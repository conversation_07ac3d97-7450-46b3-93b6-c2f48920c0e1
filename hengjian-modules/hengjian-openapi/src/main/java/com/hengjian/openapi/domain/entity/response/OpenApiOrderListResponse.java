package com.hengjian.openapi.domain.entity.response;

import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import lombok.Data;
import org.apache.ibatis.type.JdbcType;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class OpenApiOrderListResponse {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 订单扩展id(非LTL业务与orderNo一致)
     */
    private String orderExtendId;
    /**
     * 是否需要贴标签 0:是 1:否
     */
    private Integer isNeedLabeling;
    /**
     * 是否完成分割
     */
    @TableField(value = "is_split", jdbcType = JdbcType.BOOLEAN)
    private boolean isSplit;
//    private boolean ed;
    /**
     * 渠道接收状态
     */
    private String channelReceiptStatus;
    /**
     * 主订单类型
     */
    private String orderType;

    /**
     * 物流类型：PickUp-自提，DropShipping-代发
     */
    private String logisticsType;

    /**
     * 主订单编号
     */
    private String orderNo;



    /**
     * 渠道类型
     */
    private String channelType;

    /**
     * 渠道店铺主键
     */
    private Long channelId;

    /**
     * 渠道别名
     */
    private String channelAlias;

    /**
     * 销售渠道订单编号（如果有则记录）
     */
    private String channelOrderNo;

    /**
     * 销售渠道订单号（如果有则记录，展示用）
     */
    private String channelOrderName;

    /**
     * 三方-行订单项目id
     */
    private String lineOrderItemId;

    /**
     * 渠道订单时间
     */
    private Date channelOrderTime;

    /**
     * 主订单履约进度（未发货、已发货、已履约等）
     */
    private String fulfillmentProgress;

    /**
     * 主订单状态：待支付，已支付等
     */
    private String orderState ;

    /**
     * 支付时间
     */
    private Date payTime;

    /**
     * 支付错误信息
     */
    @TableField(typeHandler = JacksonTypeHandler.class, updateStrategy = FieldStrategy.IGNORED)
    private JSONObject payErrorMessage;

    /**
     * 订单商品总数量
     */
    private Integer totalQuantity;
// 金额------------------------------------------
//    /**
//     * 原始商品单价总额（供应商）
//     */
//    private BigDecimal originalTotalProductAmount;
//
//    /**
//     * 原始操作费总金额（供货商）
//     */
//    private BigDecimal originalTotalOperationFee;
//
//    /**
//     * 原始尾程派送费总额（供货商）
//     */
//    private BigDecimal originalTotalFinalDeliveryFee;
//
//    /**
//     * 原始自提价总额（供货商）
//     */
//    private BigDecimal originalTotalPickUpPrice;
//
//    /**
//     * 原始代发价总额（供货商）
//     */
//    private BigDecimal originalTotalDropShippingPrice;
//
//    /**
//     * 原始应付总金额（供货商）
//     */
//    private BigDecimal originalPayableTotalAmount;
//
//    /**
//     * 原始已预付总金额（供货商）
//     */
//    private BigDecimal originalPrepaidTotalAmount;
//
//    /**
//     * 原始实际支付总金额（供应商）
//     */
//    private BigDecimal originalActualTotalAmount;
//
//    /**
//     * 原始售后可执行总金额（供货商）
//     */
//    private BigDecimal originalRefundExecutableAmount;

    /**
     * 平台商品单价总额（平台、分销商）
     */
    private BigDecimal platformTotalProductAmount;

    /**
     * 平台操作费总金额（平台、分销商）
     */
    private BigDecimal platformTotalOperationFee;

    /**
     * 平台尾程派送费总额
     */
    private BigDecimal platformTotalFinalDeliveryFee;

    /**
     * 平台自提价总额（平台、分销商）
     */
    private BigDecimal platformTotalPickUpPrice;

    /**
     * 平台代发价总额（平台、分销商）
     */
    private BigDecimal platformTotalDropShippingPrice;

    /**
     * 平台应付总金额（平台、分销商）
     */
    private BigDecimal platformPayableTotalAmount;

    /**
     * 平台已预付总金额（平台、分销商）
     */
    private BigDecimal platformPrepaidTotalAmount;

    /**
     * 平台实际支付总金额（平台、分销商）
     */
    private BigDecimal platformActualTotalAmount;

    /**
     * 平台售后可执行总金额（平台、分销商）
     */
    private BigDecimal platformRefundExecutableAmount;
//  金额--------------------------------------------
    /**
     * 归属订单导入记录主键
     */
    private Long importRecordId;

    /**
     * 订单备注
     */
    private String orderNote;

    /**
     * 币种
     */
    private String currency;

    /**
     * tracking上传标识 1：失败 2：回传成功 其他正常
     */
    private Integer trackingFlag;

    /**
     * 异常code 0:无异常 1:商品映射异常 2:订单支付异常 3:库存不足异常 5.尾程配送费异常 6.测算异常
     */
    private Integer exceptionCode;

    /**
     * 订单来源 1: 接口接入 2: excel导入 3: 商城下单 4: openApi
     */
    private Integer orderSource;

    /**
     * 是否为展示订单 null或1为非展示订单 2：为展示订单
     */
    private Integer isShow;
    /**
     * 最晚发货时间
     */
    private Date latestShipDate;

    /**
     * 仓库预计发货时间
     */
    private Date  warehouseExpectedShipDate;
    /**
     * 订单支付方式
     */
    private String payType;

    /**
     * 渠道仓库code
     */
    private String channelWarehouseCode;
    /**
     * 取消状态 0:无，1取消中，2取消成功，3取消失败 4取消异常
     */
    private Integer cancelStatus;
    /**
     * 站点
     */
    private Long siteId;

    private String countryCode;
    /**
     * 货币符号
     */
    private String currencySymbol;

    /**
     * erp 发货异常
     */
    private String shipmentException;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;
}
