create table tenant_distr_extend_log
(
    id                              bigint                       not null comment '主键',
    tenant_id                       varchar(20) default '000000' null comment '租户编号',
    push_result											TINYINT(1)								 	 null comment '推送结果',
    del_flag                        char        default '0'      null comment '删除标志（0代表存在 2代表删除）',
    create_by                       bigint                       null comment '创建者',
    create_time                     datetime                     null comment '创建时间',
    update_by                       bigint                       null comment '更新者',
    update_time                     datetime                     null comment '更新时间'
)
    comment '租户分销商-拓展信息日志表' row_format = DYNAMIC;
