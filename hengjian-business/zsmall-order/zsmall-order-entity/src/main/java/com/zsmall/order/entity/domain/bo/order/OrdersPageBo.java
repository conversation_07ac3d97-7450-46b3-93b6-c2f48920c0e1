package com.zsmall.order.entity.domain.bo.order;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/6/7 17:51
 */
@Data
public class OrdersPageBo {

    /**
     * 参数类型
     */
    private String queryType;
    private String sortValue = "orderNoDesc";

    /**
     * 参数值
     */
    private String queryValue;

    /**
     * 订单状态
     */
    private String orderState;
    /**
     * 活动类型
     */
    private String activityType;

    /**
     * 渠道类型
     */
    private String channelType;

    /**
     * 发货日期
     */
    private String startTimeGap;

    /**
     * 发货方式
     */
    private String logisticsType;
    /**
     * 订单编号
     */
    private String orderNo;
    private List<String> orderNos;
    /**
     * 渠道订单号
     */
    private String channelOrderNo;

    private List<String> channelOrderNos;
    /**
     * sku
     */
    private String sku;
    /**
     * 商品名称
     */
    private String productName;
    /**
     * itemNo
     */
    private String itemNo;
    /**
     * 跟踪单号
     */
    private String trackingNo;
    /**
     * 活动编号
     */
    private String activityCode;


    //订单状态集合
    private List<String> orderStates;

    //租户类型
    private String tenantType;

    //当前租户ID
    private String tenantId;

    //供应商id
    private String supplierTenantId;

    //分销商id
    private String distributorTenantId;

    //订单开始时间
    private String startDate;

    //订单结束时间
    private String endDate;
    //渠道订单开始时间
    private String channelOrderStartDate;
    //渠道订单结束时间
    private String channelOrderEndDate;
    //发货开始时间
    private String dispatchedStartTime;
    //发货结束时间
    private String dispatchedEndTime;

    //履约类型
    private String fulfillmentType;

    //渠道ID集合
    private List<Long> channelIds;
    /**
     * 异常code 0:无异常 1:商品映射异常 2:订单支付异常 3:库存不足异常
     */
    private  Integer exceptionCode;
    /**
     * 是否是展示订单导出是true/不是false
     */
    private Boolean isShowOrder;
    /**
     * 订单来源 1: 接口接入 2: excel导入 3: 商城下单 4: openApi
     */
    private  Integer orderSource;
    /**
     * 是否指定排序
     */
    private Boolean isAssignmentSort;
    /**
     * 币种
     */
    private String currencyCode;
    /**
     * 取消状态 0:无，1取消中，2取消成功，3取消失败
     */
    private Integer cancelStatus;
    /**
     * 是否使用es查询
     */
    private Boolean  isOrderEsSearch;
    /**
     * erp 发货异常
     */
    private String shipmentException;

    public List<String> putChannelOrderNos(List<String> channelOrderNos){
        this.channelOrderNos = channelOrderNos;
        return this.channelOrderNos;
    }
    public List<String> putOrderNos(List<String> orderNos){
        this.orderNos = orderNos;
        return this.orderNos;
    }
}
