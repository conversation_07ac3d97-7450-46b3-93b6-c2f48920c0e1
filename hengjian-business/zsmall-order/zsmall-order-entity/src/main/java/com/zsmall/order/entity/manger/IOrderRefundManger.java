package com.zsmall.order.entity.manger;

import cn.hutool.core.annotation.AnnotationUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.*;
import cn.hutool.json.JSONObject;
import com.hengjian.common.core.domain.R;
import com.hengjian.common.core.enums.LanguageType;
import com.hengjian.common.core.enums.TenantType;
import com.hengjian.common.core.exception.RStatusCodeException;
import com.hengjian.common.core.utils.MessageUtils;
import com.hengjian.common.log.annotation.InMethodLog;
import com.hengjian.common.satoken.utils.LoginHelper;
import com.hengjian.common.tenant.helper.TenantHelper;
import com.zsmall.common.annotaion.Manager;
import com.zsmall.common.annotaion.RefundRuleAnalysisAnnotation;
import com.zsmall.common.enums.BusinessCodeEnum;
import com.zsmall.common.enums.order.LogisticsProgress;
import com.zsmall.common.enums.order.OrderRefundStateType;
import com.zsmall.common.enums.order.OrderStateType;
import com.zsmall.common.enums.orderRefund.RefundAmountStateEnum;
import com.zsmall.common.enums.orderRefund.RefundApplyType;
import com.zsmall.common.enums.orderRefund.RefundDisputeStateEnum;
import com.zsmall.common.enums.statuscode.ZSMallStatusCodeEnum;
import com.zsmall.common.service.BusinessParameterService;
import com.zsmall.order.entity.domain.*;
import com.zsmall.order.entity.domain.bo.SubmitRefundApplyBo;
import com.zsmall.order.entity.iservice.*;
import com.zsmall.order.factory.RefundRuleFactory;
import com.zsmall.order.utils.ZSMallOrderRefundEventUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.aop.framework.AopContext;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2025/3/12 14:22
 */
@RequiredArgsConstructor
@Manager
@Slf4j
public class IOrderRefundManger {
    private final RefundRuleFactory refundRuleFactory;
    private final IOrderItemPriceService iOrderItemPriceService;
    private final IOrdersService iOrdersService;
    // todo 模块问题
    private final IOrderItemService iOrderItemService;
//    private final MallSystemCodeGenerator mallSystemCodeGenerator;
    private final OrderCodeGenerator orderCodeGenerator;
    private final IOrderItemProductSkuService iOrderItemProductSkuService;
    private final IOrderRefundItemService iOrderRefundItemService;
    private final IOrderRefundAttachmentService iOrderRefundAttachmentService;
    private final IOrderRefundService iOrderRefundService;
    private final IOrderRefundRuleManger iOrderRefundRuleManger;
    private final BusinessParameterService businessParameterService;
    private final IOrderRefundRuleService iOrderRefundRuleService;
    /**
     * 功能描述：提交退款申请-待发货流程内不做强制校验
     *
     * @param SubmitRefundApplyBo 提交退款申请bo
     * @return {@link R }<{@link Void }>
     * <AUTHOR>
     * @date 2025/03/12
     */
    @Transactional(rollbackFor = {Exception.class, RStatusCodeException.class},propagation = Propagation.REQUIRED)
    public R<Void> submitRefundApplyUnDispatchedByOrderNo(SubmitRefundApplyBo SubmitRefundApplyBo) {
        String orderNo = SubmitRefundApplyBo.getOrderNo();
        String tenantId = SubmitRefundApplyBo.getTenantId();
        StringBuilder errorMsg = new StringBuilder();
//        List<String> orderRefundNos = new ArrayList<>();
        List<Orders> orders = iOrdersService.getByOrderExtendId(orderNo);
        for (Orders order : orders) {
            if (order != null) {
                try{

                    if (Objects.equals(order.getOrderState(), OrderStateType.Paid)) {
                        Long orderId = order.getId();

                        List<OrderItem> orderItems = iOrderItemService.getListByOrderId(orderId);
                        String supplierTenantId = orderItems.get(0).getSupplierTenantId();
                        OrderRefund orderRefund = new OrderRefund();
                        String orderRefundNo = orderCodeGenerator.codeGenerate(BusinessCodeEnum.OrderRefundNo);
                        orderRefund.setOrderRefundNo(orderRefundNo);
//                        orderRefundNos.add(orderRefundNo);
                        SubmitRefundApplyBo.addOrderRefundNos(orderRefundNo);
                        orderRefund.setRefundType(RefundApplyType.CancelAutoRefund);
                        orderRefund.setRefundAmountState(RefundAmountStateEnum.NotRefund);
                        orderRefund.setRefundApplyTime(new Date());
                        orderRefund.setSupplierTenantId(supplierTenantId);
                        orderRefund.setOrderId(order.getId());
                        orderRefund.setOrderNo(order.getOrderNo());
                        orderRefund.setCurrencyCode(order.getCurrency());
//                        // 临时写死
                        orderRefund.setCurrencySymbol(order.getCurrencySymbol());
                        orderRefund.setRefundDisputeState(RefundDisputeStateEnum.NotDispute);

                        String description = SubmitRefundApplyBo.getDescription();
                        if (StrUtil.isNotBlank(description)) {
                            orderRefund.setRefundDescription(description);
                        }


                        List<OrderRefundItem> orderRefundItems = new ArrayList<>();
                        for (OrderItem orderItem : orderItems) {
                            // 是否符合退款标准-时效等校验
//                            Boolean canRefund = canRefund(orderItem);
//                            if (!canRefund) {
//                                return R.fail(ZSMallStatusCodeEnum.ORDER_EXCEEDED_AFTER_SALES_LIMITATIONS);
//                            }

                            OrderItemProductSku orderProductSku = iOrderItemProductSkuService.getByOrderItemId(orderItem.getId());
                            String activityCode = orderProductSku.getActivityCode();

                            OrderRefundItem orderRefundItem = new OrderRefundItem();

                            // 如果是参与活动的订单，需要判断是否退订金
                            log.info("orderItemToRefundItem activityCode = {}", activityCode);

                            orderRefundItem.setOrderId(order.getId());
                            orderRefundItem.setOrderNo(order.getOrderNo());
                            orderRefundItem.setOrderItemId(orderItem.getId());
                            orderRefundItem.setOrderItemNo(orderItem.getOrderItemNo());
                            orderRefundItem.setProductSkuCode(orderProductSku.getProductSkuCode());
                            orderRefundItem.setOrderRefundItemNo(orderCodeGenerator.codeGenerate(BusinessCodeEnum.OrderRefundItemNo));
                            unDispatchedRefunds(SubmitRefundApplyBo, order, orderItem, orderRefund, orderRefundItem);
                            orderRefund.setTenantId(tenantId);
                            orderRefundItems.add(orderRefundItem);
                        }
                        TenantHelper.ignore(()->iOrderRefundService.saveOrUpdate(orderRefund));
                        List<OrderRefundAttachment> attachments = orderRefund.getAttachments();
                        if (CollUtil.isNotEmpty(attachments)) {
                            iOrderRefundAttachmentService.saveOrUpdateBatch(attachments);
                        }
                        for (OrderRefundItem orderRefundItem : orderRefundItems) {
                            orderRefundItem.setOrderRefundId(orderRefund.getId());
                            orderRefundItem.setOrderRefundNo(orderRefund.getOrderRefundNo());
                        }
                        iOrderRefundItemService.saveOrUpdateBatch(orderRefundItems);
                        iOrderItemService.saveOrUpdateBatch(orderItems);
                        iOrdersService.saveOrUpdate(order);

                        Set<OrderStateType> orderStateTypes = iOrderItemService.queryOrderStatusTypes(order.getId());
                        if (!orderStateTypes.contains(OrderStateType.Paid) && orderStateTypes.contains(OrderStateType.Verifying)) {
                            order.setOrderState(OrderStateType.Verifying);
                            iOrdersService.saveOrUpdate(order);
                        }
                    } else {
                        errorMsg.append(MessageUtils.message(ZSMallStatusCodeEnum.ORDER_REFUNDED.getMessageCode()));
                    }
                }catch (Exception e){
                    log.error("submitRefundApplyUnDispatchedByOrderNo error = {}", e.getMessage(), e);
                }

            } else {
                errorMsg.append(MessageUtils.message(ZSMallStatusCodeEnum.ORDER_ITEM_NOT_EXIST.getMessageCode()));

            }
        }
        if (StrUtil.isNotBlank(errorMsg)) {
            return R.fail(errorMsg.toString());
        }
        return R.ok();
    }
    /**
     * 提交退款申请(主单)
     *
     * @param SubmitRefundApplyBo
     * @return
     * @throws Exception
     */
    @Transactional(rollbackFor = {Exception.class, RStatusCodeException.class},propagation = Propagation.REQUIRED)
    public R<Void> submitRefundApplyByOrderNo(SubmitRefundApplyBo SubmitRefundApplyBo) throws RStatusCodeException {

        String orderNo = SubmitRefundApplyBo.getOrderNo();
        String refundRuleNo = SubmitRefundApplyBo.getRefundRuleNo();
        if (StrUtil.hasBlank(orderNo, refundRuleNo)) {
            return R.fail(ZSMallStatusCodeEnum.REQUIRED_CANNOT_EMPTY);
        }

        Orders order = iOrdersService.getByOrderNo(orderNo);
        if (order != null) {
//            OrderType orderType = order.getOrderType();
//            if (ObjectUtil.notEqual(orderType, OrderType.Wholesale)) {
//                return R.fail(ZSMallStatusCodeEnum.CURRENT_ORDER_NOT_SUPPORTED);
//            }

            if (Objects.equals(order.getOrderState(), OrderStateType.Paid)) {
                Long orderId = order.getId();

                LogisticsProgress fulfillmentProgress = order.getFulfillmentProgress();
                String applicableFulfillment = fulfillmentProgress.name();
                if (ObjectUtil.equals(fulfillmentProgress, LogisticsProgress.Fulfilled)) {
                    applicableFulfillment = LogisticsProgress.Dispatched.name();
                }
                OrderRefundRule orderRefundRule = iOrderRefundRuleService.queryByRefundRuleNo(refundRuleNo, applicableFulfillment);
                if (orderRefundRule != null) {
                    List<OrderItem> orderItems = iOrderItemService.getListByOrderId(orderId);
                    String supplierTenantId = orderItems.get(0).getSupplierTenantId();
                    OrderRefund orderRefund = new OrderRefund();
                    String orderRefundNo = orderCodeGenerator.codeGenerate(BusinessCodeEnum.OrderRefundNo);
                    orderRefund.setOrderRefundNo(orderRefundNo);
                    SubmitRefundApplyBo.addOrderRefundNos(orderRefundNo);
                    orderRefund.setRefundAmountState(RefundAmountStateEnum.NotRefund);
                    orderRefund.setRefundApplyTime(new Date());
                    orderRefund.setSupplierTenantId(supplierTenantId);
                    orderRefund.setOrderId(order.getId());
                    orderRefund.setOrderNo(order.getOrderNo());
                    orderRefund.setRefundDisputeState(RefundDisputeStateEnum.NotDispute);

                    String description = SubmitRefundApplyBo.getDescription();
                    if (StrUtil.isNotBlank(description)) {
                        orderRefund.setRefundDescription(description);
                    }

                    String refundReasonZhCn = orderRefundRule.getRefundReasonZhCn();
                    String refundReasonEnUs = orderRefundRule.getRefundReasonEnUs();

                    JSONObject refundRuleReasonJSON = new JSONObject(true);
                    refundRuleReasonJSON.set(LanguageType.zh_CN.name(), refundReasonZhCn);
                    refundRuleReasonJSON.set(LanguageType.en_US.name(), refundReasonEnUs);
                    orderRefund.setRefundRuleReason(refundRuleReasonJSON.toString());

                    List<OrderRefundItem> orderRefundItems = new ArrayList<>();
                    for (OrderItem orderItem : orderItems) {
                        // 是否符合退款标准
                        Boolean canRefund = canRefund(orderItem);
                        if (!canRefund) {
                            return R.fail(ZSMallStatusCodeEnum.ORDER_EXCEEDED_AFTER_SALES_LIMITATIONS);
                        }

                        OrderItemPrice orderItemPrice = iOrderItemPriceService.queryByOrderItemNo(orderItem.getOrderItemNo());
                        BigDecimal originalUnitPrice = orderItemPrice.getOriginalUnitPrice();
                        BigDecimal platformUnitPrice = orderItemPrice.getPlatformUnitPrice();

                        OrderItemProductSku orderProductSku = iOrderItemProductSkuService.getByOrderItemId(orderItem.getId());
                        String activityCode = orderProductSku.getActivityCode();

                        OrderRefundItem orderRefundItem = new OrderRefundItem();

                        // 如果是参与活动的订单，需要判断是否退订金
                        log.info("orderItemToRefundItem activityCode = {}", activityCode);

                        orderRefundItem.setOrderId(order.getId());
                        orderRefundItem.setOrderNo(order.getOrderNo());
                        orderRefundItem.setOrderItemId(orderItem.getId());
                        orderRefundItem.setOrderItemNo(orderItem.getOrderItemNo());
                        orderRefundItem.setProductSkuCode(orderProductSku.getProductSkuCode());
                        orderRefundItem.setOrderRefundItemNo(orderCodeGenerator.codeGenerate(BusinessCodeEnum.OrderRefundItemNo));

                        // 去除需要工厂处理的退款规则字段，并根据排序号排好
                        List<Field> fields = Arrays.stream(ReflectUtil.getFields(OrderRefundRule.class,
                            field -> AnnotationUtil.hasAnnotation(field, RefundRuleAnalysisAnnotation.class))).sorted(
                            (field1, field2) -> {
                                int order1 = AnnotationUtil.getAnnotation(field1, RefundRuleAnalysisAnnotation.class).order();
                                int order2 = AnnotationUtil.getAnnotation(field2, RefundRuleAnalysisAnnotation.class).order();
                                return NumberUtil.compare(order1, order2);
                            }
                        ).collect(Collectors.toList());
                        for (Field field : fields) {
                            Object fieldValue = ReflectUtil.getFieldValue(orderRefundRule, field);
                            String ruleType = AnnotationUtil.getAnnotation(field, RefundRuleAnalysisAnnotation.class).ruleType();
                            refundRuleFactory.getService(ruleType).refundRuleWhetherHandle(Integer.parseInt(fieldValue.toString()), SubmitRefundApplyBo, order, orderItem, orderRefund, orderRefundItem);
                        }
                        orderRefundItems.add(orderRefundItem);
                    }


                    iOrderRefundService.saveOrUpdate(orderRefund);
                    List<OrderRefundAttachment> attachments = orderRefund.getAttachments();
                    if (CollUtil.isNotEmpty(attachments)) {
                        iOrderRefundAttachmentService.saveOrUpdateBatch(attachments);
                    }
                    for (OrderRefundItem orderRefundItem : orderRefundItems) {
                        orderRefundItem.setOrderRefundId(orderRefund.getId());
                        orderRefundItem.setOrderRefundNo(orderRefund.getOrderRefundNo());
                    }
                    iOrderRefundItemService.saveOrUpdateBatch(orderRefundItems);
                    iOrderItemService.saveOrUpdateBatch(orderItems);
                    iOrdersService.saveOrUpdate(order);

                    Set<OrderStateType> orderStateTypes = iOrderItemService.queryOrderStatusTypes(order.getId());
                    if (!orderStateTypes.contains(OrderStateType.Paid) && orderStateTypes.contains(OrderStateType.Verifying)) {
                        order.setOrderState(OrderStateType.Verifying);
                        iOrdersService.saveOrUpdate(order);
                    }
                } else {
                    return R.fail(ZSMallStatusCodeEnum.REFUND_RULE_NOT_EXIST);
                }
            } else {
                return R.fail(ZSMallStatusCodeEnum.ORDER_REFUNDED);
            }
        } else {
            return R.fail(ZSMallStatusCodeEnum.ORDER_ITEM_NOT_EXIST);
        }
        return R.ok();
    }

    /**
     * 功能描述：提交退款申请,bo内必须给订单编号不能给子订单,orderNo必填
     *  此方法有独立事务
     * @param bo
     * @return {@link R }<{@link Void }>
     * <AUTHOR>
     * @date 2025/03/13
     */
    @InMethodLog("申请退款流程")
    public R<Void> submitRefundApply(SubmitRefundApplyBo bo) {
        try {
            // 先判断待发货、已发货--老逻辑已发货时,bo内的orderNo为主单,子单为orderItemNo;代发货时因为走的是改造后的流程,需要给orderExtendId,走的是一次性取消所有子单的业务.具体老的提交退款是否要一次性取消所有子单,需要看后续产品逻辑.
            String orderNo = bo.getOrderNo();
            if(ObjectUtil.isEmpty(orderNo)){
                throw new RuntimeException("orderNo为行id,不可为空");
            }
            LogisticsProgress fulfillmentProgress = getFulfillmentProgress(bo);
            if(ObjectUtil.isNotEmpty(fulfillmentProgress)&&LogisticsProgress.UnDispatched.equals(fulfillmentProgress)){

                if (StrUtil.isNotBlank(orderNo)) {
                    return TenantHelper.ignore(()->((IOrderRefundManger) AopContext.currentProxy()).submitRefundApplyUnDispatchedByOrderNo(bo));
                }
            }else if(ObjectUtil.isNotEmpty(fulfillmentProgress)&&LogisticsProgress.Dispatched.equals(fulfillmentProgress)){
                if (ObjectUtil.isEmpty(bo.getDescription())){
                    return R.fail("描述为必填项");
                }
                if (CollUtil.isEmpty(bo.getAttachments())){
                    return R.fail("商品图片为必填项");
                }
                return ((IOrderRefundManger) AopContext.currentProxy()).submitRefundApplyByOrderItemNo(bo);

            }

        } catch (RStatusCodeException e) {
            log.error("RStatusCodeException error = {}", e.getStatusCode(), e);
            return R.fail(e.getStatusCode());
        } catch (Exception e) {
            log.error("Exception error = {}", e.getMessage(), e);
            return R.fail(ZSMallStatusCodeEnum.SUBMIT_REFUND_REQUEST_ERROR);
        }
        return R.ok();
    }
    /**
     * 提交退款申请(子单)
     *
     * @param bo
     * @return
     */
    @Transactional(rollbackFor = {Exception.class, RStatusCodeException.class})
    public R<Void> submitRefundApplyByOrderItemNo(SubmitRefundApplyBo bo) throws RStatusCodeException {

        TenantType tenantTypeEnum = LoginHelper.getTenantTypeEnum();
        String tenantId = LoginHelper.getTenantId();

        String orderItemNo = bo.getOrderItemNo();
        String refundRuleNo = bo.getRefundRuleNo();
        if (StrUtil.hasBlank(orderItemNo, refundRuleNo)) {
            return R.fail(ZSMallStatusCodeEnum.REQUIRED_CANNOT_EMPTY);
        }

        OrderItem orderItem = iOrderItemService.getByOrderItemNo(orderItemNo);
        if (orderItem != null) {
            if (Objects.equals(orderItem.getOrderState(), OrderStateType.Paid)) {
                // 是否符合退款标准
                Boolean canRefund = canRefund(orderItem);
                if (!canRefund) {
                    return R.fail(ZSMallStatusCodeEnum.ORDER_EXCEEDED_AFTER_SALES_LIMITATIONS);
                }

                LogisticsProgress fulfillment = orderItem.getFulfillmentProgress();
                String applicableFulfillment = fulfillment.name();
                if (ObjectUtil.equals(fulfillment, LogisticsProgress.Fulfilled)) {
                    applicableFulfillment = LogisticsProgress.Dispatched.name();
                }
                OrderRefundRule orderRefundRule = iOrderRefundRuleService.queryByRefundRuleNo(refundRuleNo, applicableFulfillment);
                if (orderRefundRule != null) {
                    Orders order = iOrdersService.queryById(orderItem.getOrderId());
                    OrderItemProductSku orderProductSku = iOrderItemProductSkuService.getByOrderItemId(orderItem.getId());

                    OrderRefund orderRefund = new OrderRefund();
                    String orderRefundNo = orderCodeGenerator.codeGenerate(BusinessCodeEnum.OrderRefundNo);
                    orderRefund.setOrderRefundNo(orderRefundNo);
                    bo.addOrderRefundNos(orderRefundNo);
                    orderRefund.setRefundApplyTime(new Date());
                    orderRefund.setOrderId(order.getId());
                    orderRefund.setOrderNo(order.getOrderNo());
                    orderRefund.setCurrencySymbol(order.getCurrencySymbol());
                    orderRefund.setCurrencyCode(order.getCurrency());
                    String description = bo.getDescription();
                    if (StrUtil.isNotBlank(description)) {
                        orderRefund.setRefundDescription(description);
                    }

                    String refundReasonZhCn = orderRefundRule.getRefundReasonZhCn();
                    String refundReasonEnUs = orderRefundRule.getRefundReasonEnUs();

                    JSONObject refundRuleReasonJSON = new JSONObject(true);
                    refundRuleReasonJSON.set(LanguageType.zh_CN.name(), refundReasonZhCn);
                    refundRuleReasonJSON.set(LanguageType.en_US.name(), refundReasonEnUs);
                    orderRefund.setRefundRuleReason(refundRuleReasonJSON.toString());

                    OrderItemPrice orderItemPrice = iOrderItemPriceService.queryByOrderItemNo(orderItemNo);
                    BigDecimal platformUnitPrice = orderItemPrice.getPlatformUnitPrice();
                    BigDecimal originalUnitPrice = orderItemPrice.getOriginalUnitPrice();

                    String activityCode = orderProductSku.getActivityCode();

                    orderRefund.setRefundDisputeState(RefundDisputeStateEnum.NotDispute);
                    OrderRefundItem orderRefundItem = new OrderRefundItem();
                    orderRefundItem.setOrderRefundNo(orderRefund.getOrderRefundNo());
                    // 如果是参与活动的订单，需要判断是否退订金
                    log.info("orderItemToRefundItem activityCode = {}", activityCode);
                    if (StrUtil.isNotBlank(activityCode)) {
                        // 活动订金单价和尾款单价
                        BigDecimal platformDepositUnitPrice = orderItemPrice.getPlatformDepositUnitPrice();
                        BigDecimal originalDepositUnitPrice = orderItemPrice.getOriginalDepositUnitPrice();
                        BigDecimal platformBalanceUnitPrice = orderItemPrice.getPlatformBalanceUnitPrice();
                        BigDecimal originalBalanceUnitPrice = orderItemPrice.getOriginalBalanceUnitPrice();

                        // 已发货，单价需要加上尾款
                        if (!ObjectUtil.equals(fulfillment, LogisticsProgress.UnDispatched)) {
                            platformUnitPrice = NumberUtil.add(platformUnitPrice, platformDepositUnitPrice);
                            originalUnitPrice = NumberUtil.add(originalUnitPrice, originalDepositUnitPrice);
                        }
                    }

                    orderRefundItem.setOrderId(order.getId());
                    orderRefundItem.setOrderNo(order.getOrderNo());
                    orderRefundItem.setOrderItemId(orderItem.getId());
                    orderRefundItem.setOrderItemNo(orderItem.getOrderItemNo());
                    orderRefundItem.setProductSkuCode(orderProductSku.getProductSkuCode());
                    orderRefundItem.setActivityCode(orderItem.getActivityCode());
                    orderRefundItem.setActivityType(orderItem.getActivityType());
                    orderRefundItem.setOrderRefundItemNo(orderCodeGenerator.codeGenerate(BusinessCodeEnum.OrderRefundItemNo));
                    List<OrderRefundItem> orderRefundItems = CollUtil.newArrayList(orderRefundItem);

                    // 去除需要工厂处理的退款规则字段，并根据排序号排好
                    List<Field> fields = Arrays.stream(ReflectUtil.getFields(OrderRefundRule.class,
                        field -> AnnotationUtil.hasAnnotation(field, RefundRuleAnalysisAnnotation.class))).sorted(
                        (field1, field2) -> {
                            int order1 = AnnotationUtil.getAnnotation(field1, RefundRuleAnalysisAnnotation.class).order();
                            int order2 = AnnotationUtil.getAnnotation(field2, RefundRuleAnalysisAnnotation.class).order();
                            return NumberUtil.compare(order1, order2);
                        }
                    ).collect(Collectors.toList());
                    for (Field field : fields) {
                        Object fieldValue = ReflectUtil.getFieldValue(orderRefundRule, field);
                        String ruleType = AnnotationUtil.getAnnotation(field, RefundRuleAnalysisAnnotation.class).ruleType();
                        refundRuleFactory.getService(ruleType).refundRuleWhetherHandle(Integer.parseInt(fieldValue.toString()), bo, orderItem, orderRefund, orderRefundItem);
                    }

                    orderRefund.setRefundQuantity(orderRefundItem.getRefundQuantity());
                    // 在工厂中已经设置
                    // orderRefund.setPlatformRefundAmount(orderRefundItem.getPlatformPayableTotalAmount());
                    // orderRefund.setOriginalRefundAmount(orderRefundItem.getOriginalPayableTotalAmount());
                    orderRefund.setSupplierTenantId(orderItem.getSupplierTenantId());
                    orderRefund.setRefundAmountState(RefundAmountStateEnum.NotRefund);


                    iOrderRefundService.saveOrUpdate(orderRefund);
                    List<OrderRefundAttachment> attachments = orderRefund.getAttachments();
                    if (CollUtil.isNotEmpty(attachments)) {
                        iOrderRefundAttachmentService.saveOrUpdateBatch(attachments);
                    }
                    iOrderItemService.saveOrUpdate(orderItem);
                    orderRefundItem.setOrderRefundId(orderRefund.getId());
                    iOrderRefundItemService.saveOrUpdate(orderRefundItem);
                    iOrdersService.saveOrUpdate(order);
                    Set<OrderStateType> orderStateTypes = iOrderItemService.queryOrderStatusTypes(order.getId());
                    if (!orderStateTypes.contains(OrderStateType.Paid) && orderStateTypes.contains(OrderStateType.Verifying)) {
                        order.setOrderState(OrderStateType.Verifying);
                        iOrdersService.saveOrUpdate(order);
                    }
                } else {
                    return R.fail(ZSMallStatusCodeEnum.REFUND_RULE_NOT_EXIST);
                }
            } else {
                return R.fail(ZSMallStatusCodeEnum.ORDER_REFUNDED);
            }
        } else {
            return R.fail(ZSMallStatusCodeEnum.ORDER_ITEM_NOT_EXIST);
        }
        return R.ok();
    }
    /**
     * 功能描述：获得履约进度
     *  说明:LTL多单的情况会取任意一个订单的履约状况,实际两者的状态变更是一致的
     * @return {@link LogisticsProgress }
     * <AUTHOR>
     * @date 2025/03/12
     */
    private LogisticsProgress getFulfillmentProgress(SubmitRefundApplyBo bo) {
        LogisticsProgress fulfillmentProgress = null;
        if(ObjectUtil.isNotEmpty(bo.getOrderNo())){
            List<Orders> orders = iOrdersService.getByOrderExtendId(bo.getOrderNo());
            if(CollUtil.isNotEmpty(orders)){
                Orders order = orders.get(0);
                fulfillmentProgress = order.getFulfillmentProgress();
            }
        }else if(ObjectUtil.isNotEmpty(bo.getOrderItemNo())){
            OrderItem orderItem = iOrderItemService.getByOrderItemNo(bo.getOrderItemNo());
            String orderNo = orderItem.getOrderNo();
            Orders order = iOrdersService.getByOrderNo(orderNo);
            fulfillmentProgress = order.getFulfillmentProgress();

        }
        return fulfillmentProgress;
    }


    @Transactional(rollbackFor = {Exception.class, RStatusCodeException.class},propagation = Propagation.REQUIRED)
    public R<Void> submitRefundApplyUnDispatchedByOrderItemNo(SubmitRefundApplyBo bo) {
        return null;
    }

    /**
     * 是否符合退款标准
     *
     * @param orderItem
     * @return
     */
    private Boolean canRefund(OrderItem orderItem) {
        Integer count = iOrderRefundItemService.countByInProgress(orderItem.getOrderItemNo());
        return iOrderRefundRuleManger.compareRefundRules(orderItem, count == 0 ? true : false);
    }
    private void unDispatchedRefunds(SubmitRefundApplyBo submitRefundApplyBo, Orders order, OrderItem orderItem,
                                     OrderRefund orderRefund, OrderRefundItem orderRefundItem) {
        Integer totalNum = orderRefund.getRefundQuantity();
        Integer num = orderItem.getTotalQuantity();
        orderRefund.setRefundQuantity(NumberUtil.add(totalNum, num).intValue());
        orderRefundItem.setRefundQuantity(num);
        orderRefund.setRefundState(OrderRefundStateType.Verifying);

        BigDecimal refundExecutableAmount = order.getPlatformRefundExecutableAmount();
        BigDecimal refundExecutableAmountSup = order.getOriginalRefundExecutableAmount();

        // 退款金额（平台）不为空，说明已经设置过，不需要重复计算
        BigDecimal refundPrice = orderRefund.getPlatformRefundAmount();
        if (refundPrice != null) {
            return;
        }

        orderRefundItem.setPlatformPayableTotalAmount(orderItem.getPlatformPayableTotalAmount());
        orderRefundItem.setOriginalPayableTotalAmount(orderItem.getOriginalPayableTotalAmount());
        orderRefundItem.setPlatformPrepaidTotalAmount(orderItem.getPlatformPrepaidTotalAmount());
        orderRefundItem.setOriginalPrepaidTotalAmount(orderItem.getOriginalPrepaidTotalAmount());
        orderRefundItem.setPlatformActualTotalAmount(orderItem.getPlatformActualTotalAmount());
        orderRefundItem.setOriginalActualTotalAmount(orderItem.getOriginalActualTotalAmount());

        order.setPlatformRefundExecutableAmount(BigDecimal.ZERO);
        order.setOriginalRefundExecutableAmount(BigDecimal.ZERO);
        orderRefund.setPlatformRefundAmount(refundExecutableAmount);
        orderRefund.setOriginalRefundAmount(refundExecutableAmountSup);

        order.setOrderState(OrderStateType.Verifying);
        orderItem.setOrderState(OrderStateType.Verifying);

    }
//    /**
//     * 退款被拒时，退还金额至可执行金额
//     *
//     * @param order
//     * @param orderRefund
//     */
//    private void refundToOrderExecutableAmount(Orders order, OrderRefund orderRefund) {
//        String orderRefundNo = orderRefund.getOrderRefundNo();
//        BigDecimal platformRefundExecutableAmount_order = order.getPlatformRefundExecutableAmount();
//        BigDecimal originalRefundExecutableAmount_order = order.getOriginalRefundExecutableAmount();
//
//        BigDecimal refundPrice = orderRefund.getPlatformRefundAmount();
//        BigDecimal refundPriceSup = orderRefund.getOriginalRefundAmount();
//        log.info("refundToExecutableAmount orderRefundNo = {} platformRefundExecutableAmount_order = {}, originalRefundExecutableAmount_order = {}",
//            orderRefundNo, platformRefundExecutableAmount_order, originalRefundExecutableAmount_order);
//        log.info("refundToExecutableAmount orderRefundNo = {} refundPrice = {}, refundPriceSup = {}",
//            orderRefundNo, refundPrice, refundPriceSup);
//
//
//        platformRefundExecutableAmount_order = NumberUtil.add(NumberUtil.toBigDecimal(refundPrice), platformRefundExecutableAmount_order);
//        originalRefundExecutableAmount_order = NumberUtil.add(refundPriceSup, originalRefundExecutableAmount_order);
//        log.info("refundToExecutableAmount orderRefundNo = {} platformRefundExecutableAmount_order(New) = {}, originalRefundExecutableAmount_order(New) = {}",
//            orderRefundNo, platformRefundExecutableAmount_order, originalRefundExecutableAmount_order);
//        order.setPlatformRefundExecutableAmount(platformRefundExecutableAmount_order);
//        order.setOriginalRefundExecutableAmount(originalRefundExecutableAmount_order);
//
//    }
//    /**
//     * 退款被拒时，退还金额至子单可执行金额
//     *
//     * @param orderRefund
//     * @param orderItem
//     */
//    private void refundItemToOrderItemExecutableAmount(OrderItem orderItem, OrderRefund orderRefund) {
//        BigDecimal platformRefundAmount = orderRefund.getPlatformRefundAmount();
//        BigDecimal originalRefundAmount = orderRefund.getOriginalRefundAmount();
//        BigDecimal platformRefundExecutableAmount = orderItem.getPlatformRefundExecutableAmount();
//        BigDecimal originalRefundExecutableAmount = orderItem.getOriginalRefundExecutableAmount();
//        platformRefundExecutableAmount = NumberUtil.add(platformRefundAmount, platformRefundExecutableAmount);
//        originalRefundExecutableAmount = NumberUtil.add(originalRefundAmount, originalRefundExecutableAmount);
//        orderItem.setPlatformRefundExecutableAmount(platformRefundExecutableAmount);
//        orderItem.setOriginalRefundExecutableAmount(originalRefundExecutableAmount);
//    }
//    private void refundToWallet(OrderType orderType, OrderRefund orderRefund, List<OrderRefundItem> orderRefundItemList) {
//        // 走消息队列或者事件
//        //退款至钱包
////        try {
////            TransactionRecordEvent transactionRecordEvent = new TransactionRecordEvent();
////        } catch (WalletException e) {
////            throw new RStatusCodeException(e.getStatusCode());
////        } catch (Exception e) {
////            throw new RStatusCodeException(ZSMallStatusCodeEnum.WALLET_REFUND_ERROR);
////        }
//    }

    /**
     * 功能描述：退款申请处理
     * 以orderExtendId
     *
     * @param orderRefundNo 订单退款编号
     * @param isAuto
     * @param supplierCall
     * <AUTHOR>
     * @date 2025/03/13
     */
    public void refundApplyHandle(List<String> orderRefundNo, Boolean isAuto, Boolean supplierCall) {
        ZSMallOrderRefundEventUtils.automaticRefund(orderRefundNo,isAuto, supplierCall);

    }
}
