package com.zsmall.order.entity.domain.vo;

import cn.hutool.json.JSONObject;
import java.math.BigDecimal;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @BelongsProject: hengjian-distribution
 * @BelongsPackage: com.zsmall.order.entity.domain.vo
 * @Author: Len
 * @CreateTime: 2024-08-14  10:43
 * @Description: 订单列表页查询
 * @Version: 1.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class OrderListVo {
    //主订单编号
    private String orderId;
    //渠道订单号
    private String channelOrderId;

    private String orderState;
    //订单状态
    private String orderStatus;
    //子订单履约进度（未发货、已发货、已履约等）
    private String fulfillment;
    //订单类型
    private String orderType;
    //创建时间
    private String createTime;
    //下单时间
    private String channelOrderTime;
    //发货方式
    private String logisticsType;
    //销售渠道
    private String salesChannel;
    //货币种类
    private String currency;
    //实付金额
    private BigDecimal total;
    //平台实际支付总金额（平台、分销商）
    private BigDecimal platformActualTotalAmount;
    //原始实际支付总金额（供应商）
    private BigDecimal originalActualTotalAmount;
    private BigDecimal originalTotalDropShippingPrice;
    private BigDecimal originalTotalPickUpPrice;
    //支付失败原因
    private JSONObject payFailedMessage;
    // tracking上传标识 1：失败 其他正常
    private Integer trackingFlag;
    //异常code 0:无异常 1:商品映射异常 2:订单支付异常 3:库存不足异常 4:发货方式异常
    private Integer exceptionCode;
    //订单来源 1: 接口接入 2: excel导入 3: 商城下单 4: openApi
    private Integer orderSource;
    //2：为展示订单,其他非展示订单
    private Integer isShow;
    //渠道ID
    private Long channelId;
    //渠道类型
    private String channelType;
    //供应商ID
    private String supplierIds;
    //订单创建人姓名
    private String distributorId;
    //分销商
    private String distributor;
    //创建人
    private Long createBy;
    //币种符号
    private String currencySymbol;
    /**
     * 取消状态 0:无，1取消中，2取消成功，3取消失败 4:取消异常
     */
    private Integer cancelStatus;

    /**
     * erp 发货异常
     */
    private String shipmentException;


    //订单明细
    public List<OrderItems> orderItems;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static    class OrderItems {
        //商品图片
        private String imageShowUrl;
        // 商品名称
        private String productName;
        // 数量
        private Integer num;
        //item.no
        private String itemNo;
        private String sku;
        //活动编号
        private String activityCode;
        //小计
        private BigDecimal productTotalPrice;
        //是否可以确认收货
        private Boolean canConfirmReceipt;
        private  String orderState;
        private  String fulfillmentProgress;
        private  String refundStatus;
        private String channelSku;

    }

}
