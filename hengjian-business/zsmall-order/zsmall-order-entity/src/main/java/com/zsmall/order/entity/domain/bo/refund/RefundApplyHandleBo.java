package com.zsmall.order.entity.domain.bo.refund;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 请求信息-退款申请处理
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class RefundApplyHandleBo extends RefundBo {

    /**
     * 退款单号集合，用于批量处理
     */
    private List<String> orderRefundNoList;
    /**
     * 退款主单
     */
    private String orderRefundNo;
    /**
     * 退款子单
     */
    private String orderRefundItemNo;
    /**
     * 退款申请类型: Refund-仅退款不退货，RefundReturn-退货并且退款
     */
    private String refundApplyType;
    /**
     * 是否通过：true-通过，false-驳回
     */
    private Boolean pass;
    /**
     * 退货运费付款方
     */
    private String refundShippingPayer;
    /**
     * 审批结果
     */
    private String reason;

    /**
     * 是否自动
     */
    private Boolean isAuto;

    private Boolean supplierCall;

    public List<String> putOrderRefundNoList(List<String> list){
        return this.orderRefundNoList = list;
    }
}
