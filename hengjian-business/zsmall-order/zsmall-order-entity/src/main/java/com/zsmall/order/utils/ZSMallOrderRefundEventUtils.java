package com.zsmall.order.utils;

import com.hengjian.common.core.utils.SpringUtils;
import com.zsmall.order.event.RefundApplyHandleEvent;

import java.util.List;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2025/3/14 13:51
 */
public class ZSMallOrderRefundEventUtils {
    public static void automaticRefund(List<String> orderRefundNos, Boolean isAuto, Boolean supplierCall) {
        RefundApplyHandleEvent refundApplyHandleEvent = new RefundApplyHandleEvent();
        refundApplyHandleEvent.setOrderRefundNoList(orderRefundNos);
        refundApplyHandleEvent.setIsAuto(isAuto);
        refundApplyHandleEvent.setSupplierCall(supplierCall);
        SpringUtils.context().publishEvent(refundApplyHandleEvent);
    }
}
