package com.zsmall.order.entity.iservice;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hengjian.common.core.utils.StringUtils;
import com.hengjian.common.log.annotation.InMethodLog;
import com.hengjian.common.tenant.helper.TenantHelper;
import com.zsmall.common.enums.orderShippingRecord.ShippingStateEnum;
import com.zsmall.common.enums.product.StockManagerEnum;
import com.zsmall.common.enums.warehouse.WarehouseTypeEnum;
import com.zsmall.order.entity.domain.OrderItemShippingRecord;
import com.zsmall.order.entity.domain.bo.OrderItemShippingRecordBo;
import com.zsmall.order.entity.mapper.OrderItemShippingRecordMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * 子订单出货单Service接口
 *
 * <AUTHOR>
 * @date 2023-06-07
 */
@RequiredArgsConstructor
@Service
public class IOrderItemShippingRecordService extends ServiceImpl<OrderItemShippingRecordMapper, OrderItemShippingRecord> {

    public boolean saveNotTenant(OrderItemShippingRecord entity) {
        return TenantHelper.ignore(() -> super.save(entity));
    }

    /**
     * 根据出货单编号查询（无视租户）
     * @param shippingNo
     * @return
     */
    public OrderItemShippingRecord queryByShippingNoNotTenant(String shippingNo) {
        LambdaQueryWrapper<OrderItemShippingRecord> lqw = Wrappers.lambdaQuery();
        lqw.eq(OrderItemShippingRecord::getShippingNo, shippingNo);
        return TenantHelper.ignore(() -> super.getOne(lqw));
    }

    /**
     * 根据出货单编号批量查询（无视租户）
     * @param shippingNoList
     * @return
     */
    public List<OrderItemShippingRecord> queryByShippingNoNotTenant(List<String> shippingNoList) {
        LambdaQueryWrapper<OrderItemShippingRecord> lqw = Wrappers.lambdaQuery();
        lqw.in(OrderItemShippingRecord::getShippingNo, shippingNoList);
        return TenantHelper.ignore(() -> super.list(lqw));
    }

    /**
     * 根据订单号批量查询
     *
     * @param orderNoList
     * @return
     */
    public List<OrderItemShippingRecord> queryListByOrderNoList(List<String> orderNoList){
        LambdaQueryWrapper<OrderItemShippingRecord> lqw = Wrappers.lambdaQuery();
        lqw.in(OrderItemShippingRecord::getOrderNo, orderNoList);
        return TenantHelper.ignore(() -> super.list(lqw));
    }


    public boolean existsShippingNo(String shippingNo) {
        return baseMapper.existsShippingNo(shippingNo);
    }

    private LambdaQueryWrapper<OrderItemShippingRecord> buildQueryWrapper(OrderItemShippingRecordBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<OrderItemShippingRecord> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getSupplierTenantId()), OrderItemShippingRecord::getSupplierTenantId, bo.getSupplierTenantId());
        lqw.eq(StringUtils.isNotBlank(bo.getOrderNo()), OrderItemShippingRecord::getOrderNo, bo.getOrderNo());
        lqw.eq(StringUtils.isNotBlank(bo.getOrderItemNo()), OrderItemShippingRecord::getOrderItemNo, bo.getOrderItemNo());
        lqw.eq(StringUtils.isNotBlank(bo.getChannelType()), OrderItemShippingRecord::getChannelType, bo.getChannelType());
        lqw.eq(StringUtils.isNotBlank(bo.getWarehouseType()), OrderItemShippingRecord::getWarehouseType, bo.getWarehouseType());
        lqw.eq(StringUtils.isNotBlank(bo.getWarehouseCode()), OrderItemShippingRecord::getWarehouseCode, bo.getWarehouseCode());
        lqw.eq(StringUtils.isNotBlank(bo.getWarehouseSystemCode()), OrderItemShippingRecord::getWarehouseSystemCode, bo.getWarehouseSystemCode());
        lqw.eq(StringUtils.isNotBlank(bo.getShippingNo()), OrderItemShippingRecord::getShippingNo, bo.getShippingNo());
        lqw.eq(StringUtils.isNotBlank(bo.getShippingState()), OrderItemShippingRecord::getShippingState, bo.getShippingState());
        lqw.eq(StringUtils.isNotBlank(bo.getShippingErrorCode()), OrderItemShippingRecord::getShippingErrorCode, bo.getShippingErrorCode());
        lqw.eq(bo.getSystemManaged() != null, OrderItemShippingRecord::getSystemManaged, bo.getSystemManaged());
        return lqw;
    }

    /**
     * 根据子订单号更新发货单的状态
     * @param orderItemNo
     * @param shippingStateEnum
     * @return
     */
    public Boolean updateShippingStateByOrderItem(String orderItemNo, ShippingStateEnum shippingStateEnum) {
        LambdaUpdateWrapper<OrderItemShippingRecord> luw = Wrappers.lambdaUpdate();
        luw.set(OrderItemShippingRecord::getShippingState, shippingStateEnum);
        luw.set(OrderItemShippingRecord::getSystemManaged, false);
        luw.eq(OrderItemShippingRecord::getOrderItemNo, orderItemNo);
        return TenantHelper.ignore(() -> baseMapper.update(null, luw)) > 0;
    }

    /**
     *
     * @param orderNo
     * @param stockManager
     * @return
     */
    public List<OrderItemShippingRecord> getListByOrderNoAndType(String orderNo, StockManagerEnum stockManager) {
        return TenantHelper.ignore(() -> baseMapper.getListByOrderNoAndType(orderNo, stockManager.name()));
    }

    @InMethodLog("根据出货单是否由系统托管轮询搜索出货单")
    public List<OrderItemShippingRecord> getListBySystemManaged(WarehouseTypeEnum warehouseType, List<ShippingStateEnum> shippingStateList, Boolean systemManaged) {
        return baseMapper.getListBySystemManaged(warehouseType.name(), shippingStateList, systemManaged);
    }

    public List<OrderItemShippingRecord> queryByOrderExtendId(String orderExtendId) {
        return baseMapper.queryByOrderExtendId(orderExtendId);
    }
}
