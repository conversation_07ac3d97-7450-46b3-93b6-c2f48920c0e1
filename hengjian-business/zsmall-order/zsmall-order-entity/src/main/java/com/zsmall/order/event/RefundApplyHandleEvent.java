package com.zsmall.order.event;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2025/3/13 13:56
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class RefundApplyHandleEvent {

    /**
     * 退款单号集合，用于批量处理
     */
    private List<String> orderRefundNoList;
    /**
     * 退款主单
     */
    private String orderRefundNo;
    /**
     * 退款子单
     */
    private String orderRefundItemNo;
    /**
     * 退款申请类型: Refund-仅退款不退货，RefundReturn-退货并且退款
     */
    private String refundApplyType;
    /**
     * 是否通过：true-通过，false-驳回
     */
    private Boolean pass;
    /**
     * 退货运费付款方
     */
    private String refundShippingPayer;
    /**
     * 审批结果
     */
    private String reason;
    private Boolean isAuto;
    private String tenantId;

    private boolean supplierCall;

}
