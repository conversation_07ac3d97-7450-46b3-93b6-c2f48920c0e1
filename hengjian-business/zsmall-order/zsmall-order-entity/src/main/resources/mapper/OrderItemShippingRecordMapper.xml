<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zsmall.order.entity.mapper.OrderItemShippingRecordMapper">

    <select id="existsShippingNo" resultType="java.lang.Boolean">
        SELECT COUNT(oisr.id)
        FROM order_item_shipping_record oisr
        WHERE oisr.shipping_no = #{shippingNo}
    </select>

    <select id="getListByOrderNoAndType" resultType="com.zsmall.order.entity.domain.OrderItemShippingRecord">
        SELECT oisr.*
        FROM order_item_shipping_record oisr
                 JOIN order_item oi ON oi.order_item_no = oisr.order_item_no
        WHERE oisr.del_flag = '0'
          AND oi.del_flag = '0'
          AND oisr.order_no = #{orderNo}
          AND oisr.shipping_no IS NOT NULL
          AND oi.stock_manager = #{stockManager}
    </select>

    <select id="getListBySystemManaged" resultType="com.zsmall.order.entity.domain.OrderItemShippingRecord">
        SELECT *
        FROM order_item_shipping_record oisr
        WHERE oisr.shipping_state IN
        <foreach collection="shippingStateList" index="index" item="item" open="(" separator="," close=")">#{item}
        </foreach>
        AND oisr.warehouse_type = #{warehouseType}
        AND (oisr.system_managed IS NULL OR oisr.system_managed = #{systemManaged})
    </select>

    <select id="getAllOrderItemShippingRecord" resultType="com.zsmall.order.entity.domain.OrderItemShippingRecord">
        select * from order_item_shipping_record r where r.del_flag = 0 and r.shipping_state != 'CreateFailed'
                                     <if test="orderNo != null and orderNo.size() != 0">
                                         and  r.order_no in
                                         <foreach collection="orderNo" index="index" item="item" open="(" separator="," close=")">
                                             #{item}
                                         </foreach>
                                     </if>
    </select>

    <select id="queryByOrderExtendId" resultType="com.zsmall.order.entity.domain.OrderItemShippingRecord">
        select oisr.*
        from orders o
         inner join order_item_shipping_record oisr  on o.order_no=oisr.order_no
        where o.del_flag=0
        and oisr.del_flag=0
        and o.order_extend_id=#{orderExtendId}
    </select>
</mapper>
