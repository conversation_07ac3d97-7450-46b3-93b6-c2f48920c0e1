package com.zsmall.order.controller;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.http.Header;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hengjian.common.core.domain.R;
import com.hengjian.common.core.utils.JacksonUtils;
import com.zsmall.common.domain.dto.*;
import com.zsmall.common.domain.vo.XxlJobSearchVO;
import com.zsmall.common.enums.common.ChannelTypeEnum;
import com.zsmall.common.util.AirwallexUtil;
import com.zsmall.common.util.ImagesUtil;
import com.zsmall.order.biz.factory.ThirdOrderOperationFactory;
import com.zsmall.order.biz.service.CompensationJobService;
import com.zsmall.order.biz.service.OrderTripartiteEntryService;
import com.zsmall.order.biz.service.OrdersService;
import com.zsmall.order.biz.service.ThirdOrdersApiService;
import com.zsmall.order.biz.test.factory.ThirdOrderApiV2Factory;
import com.zsmall.order.biz.utils.TikTokUtil;
import com.zsmall.order.entity.domain.Orders;
import com.zsmall.order.entity.domain.bo.order.OrderPayBo;
import com.zsmall.order.entity.domain.vo.OrderCleanVo;
import com.zsmall.order.entity.iservice.IOrdersService;
import com.zsmall.order.entity.manger.IOrderManger;
import com.zsmall.system.entity.domain.TenantSalesChannel;
import com.zsmall.system.entity.iservice.ITenantSalesChannelService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * 三方订单服务
 *
 * <AUTHOR> Theo
 * @create 2024/1/9 14:12
 */
@SuppressWarnings("unchecked")
@Validated
@RequiredArgsConstructor
@RestController
@Slf4j
@RequestMapping("/order/tripartite")
public class OrderTripartiteEntryController {
    @Resource
    private IOrderManger iOrderManger;
    @Resource
    private ThirdOrdersApiService thirdOrdersApiService;
    @Resource
    private AirwallexUtil airwallexUtil;

    @Resource
    private IOrdersService iOrdersService;

    @Resource
    private ThirdOrderOperationFactory factory;
    @Resource
    private TikTokUtil tikTokUtil;
    private final static Logger logger = LoggerFactory.getLogger(OrderTripartiteEntryController.class);
    @Resource
    private ImagesUtil imagesUtil;
    //    @XxlConf(value = "distribution.tenant.third.flag.erp",defaultValue = "Erp-Test-01")  @Value("")
    @Value("${distribution.tenant.third.flag.erp}")
    private String thirdChannelFlag;

    //    @XxlConf(value = "distribution.tenant.id.erp",defaultValue = "DE9D45Z")   @Value("${xxl.conf.admin.address}")
    @Value("${distribution.tenant.id.erp}")
    private String tenantId;
    @Resource
    private OrdersService ordersService;

    @Resource
    private OrderTripartiteEntryService orderTripartiteEntryService;

    @Resource
    private CompensationJobService compensationJobService;

    @Resource
    private ITenantSalesChannelService salesChannelService;
    @Resource
    private ThirdOrderApiV2Factory apiFactory;

    @Value("${distribution.tiktok.appKey}")
    public String appKey;

    @Value("${distribution.tiktok.appSecret}")
    public String appSecret;
    public static final String ACCESS_TOKEN = "accessToken";
    @Resource
    private ITenantSalesChannelService iTenantSalesChannelService;

    /**
     * 功能描述：拉取商品
     *
     * @param vo vo
     * <AUTHOR>
     * @date 2024/03/06
     */
    @PostMapping("/pullProduct")
    public void pullProduct(@RequestBody XxlJobSearchVO vo,@RequestParam(required = false)List<String> allowTenantId) {
        String jsonString = JSONObject.toJSONString(vo);
        compensationJobService.pullProduct(ChannelTypeEnum.TikTok.name(), jsonString,allowTenantId);
    }



    /**
     * 三方订单,批量录入
     */
    @PostMapping("/batchEnter")
    public R tripartiteBatchEnter(@RequestBody List<OrderReceiveFromThirdDTO> dto) throws InterruptedException {
        // 临时先for循环用一下,先接接口,后续更改为线程池
        // 线程池批处理 录入失败的返回录入订单编号
        log.info("三方订单,批量录入,入参:{}", JSONObject.toJSONString(dto));
        R r = orderTripartiteEntryService.tripartiteBatchEnterForErp(dto);

        return r;

    }

    /**
     * 三方订单,批量确认收货
     */
    @PostMapping("/receipt")
    public R tripartiteReceipt(@RequestBody List<ThirdReceiptDTO> dtos) throws InterruptedException {

        R r = orderTripartiteEntryService.tripartiteBatchReceiptForErp(dtos);

        return r;

    }
    /**
     * 功能描述：支付不推送仓库
     *
     * @param bo bo
     * @return {@link R }<{@link Void }>
     * <AUTHOR>
     * @date 2024/04/02
     */
    @PostMapping("/payOrderForTest")
    public R<Void> payOrder(@RequestBody OrderPayBo bo) throws Exception {
        return ordersService.payOrderForTest(bo);
    }

    @PostMapping("/payOrderForV3")
    public R<Void> payOrderForV3(@RequestBody OrderPayBo bo) throws Exception {
        return ordersService.payOrderForV3(bo);
    }
    @PostMapping("/payOrderForV4")
    public R<Void> payOrderForV4(@RequestBody OrderPayBo bo) throws Exception {
        return ordersService.payOrderForV4(bo);
    }

    @PostMapping("/pullOrder")
    public void pullOrder(@RequestBody XxlJobSearchVO vo) {
        String jsonString = JSONObject.toJSONString(vo);
        compensationJobService.pullOrder(ChannelTypeEnum.TikTok.name(), jsonString, null);
    }

    @PostMapping("/pullOrderV2")
    public void pullOrderV2(@RequestBody XxlJobSearchVO vo,@RequestParam(required = false)List<String> allowTenantId) {
        String jsonString = JSONObject.toJSONString(vo);
        compensationJobService.pullOrderV2(ChannelTypeEnum.TikTok.name(), jsonString,allowTenantId);
    }

    /**
     * 功能描述：测试订单v2
     *
     * @param vo vo
     * <AUTHOR>
     * @date 2024/07/14
     */
    @PostMapping("/testOrderV2")
    public void testOrderV2(@RequestBody XxlJobSearchVO vo) {
        String jsonString = JSONObject.toJSONString(vo);
        compensationJobService.testOrderV2(ChannelTypeEnum.TikTok.name(), jsonString);
    }

    /**
     * 功能描述：tk 测试单入口
     *
     * @param
     * <AUTHOR>
     * @date 2024/07/14
     */
    @PostMapping("/testOrderForSpecifiedData")
    public void testOrderForSpecifiedData(@RequestBody String msg,String tenantId,String thirdChannelFlag) {
        compensationJobService.testOrderForSpecifiedData(ChannelTypeEnum.TikTok.name(),msg,tenantId,thirdChannelFlag);
    }


    /**
     * 功能描述：指定租户拉取订单,用于补偿,最好指定时间来确定唯一订单
     *
     * @param vo vo
     * <AUTHOR>
     * @date 2024/04/17
     */
    @PostMapping("/pullOrderForSpecify")
    public void pullOrderForSpecify(@RequestBody XxlJobSearchVO vo,@RequestParam(required = false)List<String> allowTenantId) {
        String jsonString = JSONObject.toJSONString(vo);
        compensationJobService.pullOrderForSpecify(ChannelTypeEnum.TikTok.name(), jsonString, allowTenantId);
    }

    /**
     * 功能描述：指定订单号拉取订单,用于补偿,可指定状态
     *
     * @param dto 到
     * <AUTHOR>
     * @date 2024/05/28
     */
    @PostMapping("/pullOrderForSpecifyOrderNo")
    public void pullOrderForSpecifyOrderNo(@RequestBody CompensationOrderDTO dto) {
        compensationJobService.pullOrderForSpecifyOrderNo(ChannelTypeEnum.TikTok.name(), dto);
    }

    /**
     * 功能描述：将订单拉至erp
     *
     * @param orderNos 订单号
     * <AUTHOR>
     * @date 2024/04/02
     */
    @PostMapping("/pullOrderToErp")
    public void pullOrderToErp(@RequestBody List<String> orderNos) {
        compensationJobService.pullOrderToErp(orderNos);
    }



    /**
     * 功能描述：清洗订单
     *
     * @param json json
     * <AUTHOR>
     * @date 2024/03/06
     */
    @PostMapping("/cleanOrders")
    public void cleanOrders(@RequestBody String json) {
        List<OrderCleanVo> list = JSON.parseArray(json, OrderCleanVo.class);
        List<Orders> orders = new ArrayList<>();
        for (OrderCleanVo orderCleanVo : list) {
            List<Orders> list1 = iOrdersService.list(new LambdaQueryWrapper<Orders>().eq(Orders::getChannelOrderNo, orderCleanVo.getBillCode()));
            for (Orders orders1 : list1) {
                orders1.setCreateTime(orderCleanVo.getBillDate());
                orders1.setPayTime(orderCleanVo.getBillDate());
                orders1.setUpdateTime(orderCleanVo.getBillDate());
                orders.add(orders1);
            }
        }
        iOrdersService.updateBatchById(orders);
    }


    /**
     * 功能描述：获取刷新令牌
     *
     * <AUTHOR>
     * @date 2024/04/02
     */
    @GetMapping("/getRefreshToken")
    public void getRefreshToken() {

        LambdaQueryWrapper<TenantSalesChannel> lambdaQueryWrapper = new LambdaQueryWrapper<TenantSalesChannel>().eq(TenantSalesChannel::getChannelType, ChannelTypeEnum.TikTok.name());
        List<TenantSalesChannel> channelList = salesChannelService.list(lambdaQueryWrapper);
        if (CollectionUtils.isNotEmpty(channelList)) {
            String url = "https://auth.tiktok-shops.com/api/v2/token/refresh?";
            for (TenantSalesChannel tenantSalesChannel : channelList) {
                try {
                    String connectStr = tenantSalesChannel.getConnectStr();
                    if (StringUtils.isEmpty(connectStr)) {
                        logger.info("channel:{}, 接口参数不存在", tenantSalesChannel.getChannelType());
                        continue;
                    }
                    JSONObject obj = JSONObject.parseObject(connectStr);
                    if (obj == null) {
                        logger.info("channel:{}, 接口参数转换有误", tenantSalesChannel.getChannelType());
                    }

                    String refreshToken = null;
                    if (obj.containsKey("refreshToken")) {
                        refreshToken = obj.getString("refreshToken");
                        if (StringUtils.isEmpty(refreshToken)) {
                            logger.info("channel:{}, refreshToken值为空", tenantSalesChannel.getChannelType());
                            continue;
                        }
                    } else {
                        logger.info("channel:{}, refreshToken不存在", tenantSalesChannel.getChannelType());
                        continue;
                    }
                    String param = "grant_type=refresh_token&app_key=" + appKey + "&app_secret=" + appSecret + "&refresh_token=" + refreshToken;

                    HttpResponse httpResponse = HttpRequest.get(url + param)
                                                           .header(Header.ACCEPT_CHARSET, "UTF-8")
                                                           .body("")
                                                           .timeout(20000)//超时，毫秒
                                                           .execute();

                    String body = httpResponse.body();

                    //打出日志
                    logger.info("请求tiktok的返回值为:{}", httpResponse);
                    logger.info("请求tiktok的返回值body为:{}", body);

                    //拿到refreshToken的值
                    String newRefreshToken = "";
                    Map<String, Object> responseMap = JacksonUtils.jsonToMap(body);
                    Map<String, Object> dataMap = (Map<String, Object>) responseMap.get("data");
                    if (dataMap.containsKey("refresh_token")) {
                        newRefreshToken = (String) dataMap.get("refresh_token");
                    } else {
                        logger.info("channel#{}请求tiktok的返回值里面没有refresh_toke的值,授权失败", tenantSalesChannel.getChannelType());
                        continue;
                    }

                    String accessToken = "";
                    if (dataMap.containsKey("access_token")) {
                        accessToken = (String) dataMap.get("access_token");
                    }

                    //找到渠道对应的参数，并更新
                    if (ObjectUtil.isNotEmpty(tenantSalesChannel)) {
                        Map<String, Object> connectMap = JacksonUtils.jsonToMap(connectStr);

                        connectMap.put("refreshToken", newRefreshToken);
                        connectMap.put("accessToken", accessToken);
                        String newConnectStr = JacksonUtils.toJson(connectMap);

                        tenantSalesChannel.setConnectStr(newConnectStr);
                        salesChannelService.saveOrUpdate(tenantSalesChannel);
                    } else {
                        logger.info("请求tiktok的渠道flag:{} 有问题,没有此账号", tenantSalesChannel.getChannelType());
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                    logger.info("channel:{} 更新refreshToken异常：{}", tenantSalesChannel.getChannelType(), e.getMessage());
                }
            }
        }
    }

    /**
     * 功能描述：面单补偿计划
     *
     * @param
     * <AUTHOR>
     * @date 2024/04/02
     */
    @GetMapping("/cancellationCompensationHandler")
    public void cancellationCompensationHandler() {
        compensationJobService.cancellationCompensationHandler(ChannelTypeEnum.TikTok.name());
    }

    /**
     * 功能描述：手动订单清洗入口
     *
     * @param orderNos 批量补偿订单
     * <AUTHOR>
     * @date 2024/03/06
     */
    @PostMapping("/updateBatchCompensateForAppoint")
    public R updateBatchCompensateForAppoint(@RequestBody List<String> orderNos) {
        return compensationJobService.updateBatchCompensateForAppoint(orderNos);
    }

    @GetMapping("/getOrderFromErp")
    public void getOrderFromErp(String order) {
        compensationJobService.getOrderFromErp(order);
    }

    @GetMapping("/testCompensationHandler")
    public void testCompensationHandler() {
        compensationJobService.compensationMethod(null);
    }


    @PostMapping("/cancelOrder")
    public void cancelOrder(@RequestBody OpenApiCancelOrderDTO dto) {
        compensationJobService.cancelOrder(dto);
    }
    @PostMapping("/getTrackingRecord")
    public R getTrackingRecord(@RequestBody List<TrackingMsgDTO> openApiRequestEntity) {

        R r;
        try {
            //查询订单

            List<TrackingMsgDTO> thirdDTO = new ArrayList<>();

            try {
                if(ObjectUtil.isEmpty(thirdDTO)){
                    return R.fail("非法的请求! 请检验参数格式是否正确");
                }
                List<String> channelNos = thirdDTO.stream().map(TrackingMsgDTO::getChannelOrderNo)
                                                  .collect(Collectors.toList());
                r = thirdOrdersApiService.getTrackingRecord(channelNos, openApiRequestEntity.get(0).getTenantId());

            } catch (Exception e) {
                log.error("open-api创建发货任务返回参数",e);
                return R.fail(e.getMessage());
            }
            return r;
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }
    /**
     * 功能描述：指定订单以取消erp,仅支持接通erp的接口
     *
     * @param dtos 按单设计服务
     * <AUTHOR>
     * @date 2025/03/27
     */
    @PostMapping("/specifyOrderToGoErpCancellation")
    public void specifyOrderToGoErpCancellation(@RequestBody List<OpenApiCancelOrderDTO> dtos) {
        compensationJobService.specifyOrderToGoErpCancellation(dtos);
    }

    /**
     * 功能描述：取消批处理作业
     *
     * <AUTHOR>
     * @date 2025/04/03
     */
    @PostMapping("/cancelBatchJob")
    public void cancelBatchJob() {
        ordersService.canceling2CancelFlow();
    }


    @PostMapping("/testSysConfig")
    public void testSysConfig(String key) {
        ordersService.testSysConfig(key);
    }

    /**
     * 功能描述：取消订单流补偿,一次一个供应商
     *
     * @param orderNo 订单号
     * <AUTHOR>
     * @date 2025/04/21
     */
    @PostMapping("/cancelOrderFlowCompensation")
    public void cancelOrderFlowCompensation(@RequestBody List<String> orderNo) {
        iOrderManger.cancelOrderFlowCompensationAfter(orderNo);
    }

}
