package com.zsmall.order.biz.service;

import com.zsmall.common.domain.airwallex.payment.intents.CreatePaymentIntentResponse;
import com.zsmall.common.exception.OrderPayException;
import com.zsmall.common.exception.ProductException;
import com.zsmall.common.exception.StockException;
import com.zsmall.extend.payment.bean.pay.PayoneerListsResponse.Redirect;
import com.zsmall.system.entity.domain.bo.pay.AirwallexOrderPayBo;

/**
 * <AUTHOR>
 * @date 2024年3月6日  10:38
 * @description:
 */
public interface PayService {

    /**
     * 订单支付 - 空中云汇
     *
     * @param airwallexOrderPayBo
     * @return
     */
    CreatePaymentIntentResponse orderPay(AirwallexOrderPayBo airwallexOrderPayBo) throws OrderPayException, StockException, ProductException;

    /**
     * 订单支付 - payoneer
     *
     * @param airwallexOrderPayBo
     * @return
     */
    Redirect orderPayByPayoneer(AirwallexOrderPayBo airwallexOrderPayBo) throws Exception;

    /**
     * 充值
     *
     * @param airwallexOrderPayBo
     * @return
     */
    CreatePaymentIntentResponse rechargePay(AirwallexOrderPayBo airwallexOrderPayBo);


    void payoneerPayTest();
}
