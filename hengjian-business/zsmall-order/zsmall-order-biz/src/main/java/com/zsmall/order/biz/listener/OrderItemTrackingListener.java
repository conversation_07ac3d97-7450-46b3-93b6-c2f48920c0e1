package com.zsmall.order.biz.listener;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.metadata.data.ReadCellData;
import com.alibaba.excel.read.listener.ReadListener;
import com.alibaba.excel.read.metadata.holder.ReadRowHolder;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hengjian.common.tenant.helper.TenantHelper;
import com.zsmall.common.domain.LocaleMessage;
import com.zsmall.common.enums.ExcelMessageEnum;
import com.zsmall.common.enums.downloadRecord.DownloadTypePlusEnum;
import com.zsmall.common.enums.downloadRecord.RecordStateEnum;
import com.zsmall.common.enums.order.LogisticsProgress;
import com.zsmall.common.enums.orderImportRecord.ImportStateEnum;
import com.zsmall.common.util.ExcelMsgBuilder;
import com.zsmall.order.biz.service.OrderItemTrackingImportRecordService;
import com.zsmall.order.entity.domain.*;
import com.zsmall.order.entity.domain.vo.tracking.OrderItemTrackingVO;
import com.zsmall.order.entity.iservice.*;
import com.zsmall.product.biz.service.ProductSkuStockService;
import com.zsmall.product.entity.domain.ProductSku;
import com.zsmall.product.entity.iservice.IProductSkuService;
import com.zsmall.system.entity.domain.DownloadRecord;
import com.zsmall.system.entity.iservice.IDownloadRecordService;
import com.zsmall.warehouse.entity.domain.Warehouse;
import com.zsmall.warehouse.entity.iservice.IWarehouseService;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2025/3/25 15:24
 */

@RequiredArgsConstructor
@Component
@Slf4j
@Setter
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
public class OrderItemTrackingListener implements ReadListener<OrderItemTrackingVO> {
    private ReadRowHolder titleRow;
    private final OrderItemTrackingImportRecordService orderItemTrackingImportRecordService;
    private final IProductSkuService iProductSkuService;
    private final IOrderAddressInfoService iOrderAddressInfoService;
    private final IOrdersService iOrdersService;
    private final IWarehouseService iWarehouseService;
    private final IOrderItemTrackingRecordService itemTrackingRecordService;

    private final IOrderItemService iOrderItemService;

    private final IOrderLogisticsInfoService iOrderLogisticsInfoService;

    private final ProductSkuStockService productSkuStockService;
    private final OrderCodeGenerator orderCodeGenerator;
    private final IDownloadRecordService iDownloadRecordService;

    private final IOrderItemProductSkuService iOrderItemProductSkuService;
    private LocaleMessage localeMessage;
    private ExcelMsgBuilder<OrderItemTrackingVO> builder;
    private String fileName;
    private List<OrderItemTrackingVO> orderItemTrackingRecords;
    private int dataRowCount = 0;
    private DataSource dataSource;
    private Connection connection;
    private static final DateTimeFormatter FORMATTER =
        DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    @Override
    public void invoke(OrderItemTrackingVO data, AnalysisContext context) {
        // 获取当前行号（Excel中的实际行号，从1开始）
        Integer rowIndex = context.readRowHolder().getRowIndex();
        // 如果跳过了表头（headRowNumber=1），则数据行号 = rowIndex + 1
        int excelRowNum = rowIndex + 1;
        // 将行号绑定到数据对象中（假设VO中添加了excelRow字段）
        data.setExcelRow(excelRowNum);
//        dataAnalysis(data,excelRowNum);
        String deliveryTime = data.getDeliveryTime();
        if (ObjectUtil.isEmpty(deliveryTime)) {
            // 定义时间格式
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            // 获取当前时间（默认系统时区）
            LocalDateTime now = LocalDateTime.now();
            // 格式化为字符串
            deliveryTime = now.format(formatter);
            data.setDeliveryTime(deliveryTime);
        }
        orderItemTrackingRecords.add(data);
        dataRowCount++;
    }
    public ReadRowHolder getTitleRow() {
        return this.titleRow;
    }
    @Override
    public void invokeHead(Map<Integer, ReadCellData<?>> headMap, AnalysisContext context) {
        Map<Integer, String> stringHeadMap = new LinkedHashMap<>();
        for (Map.Entry<Integer, ReadCellData<?>> entry : headMap.entrySet()) {
            stringHeadMap.put(entry.getKey(), entry.getValue().getStringValue());
        }
        // 获取标题行列表
        ReadRowHolder readRowHolder = context.readRowHolder();
        builder = new ExcelMsgBuilder(readRowHolder,OrderItemTrackingVO.class);
        this.titleRow = readRowHolder;
    }
    private void dataAnalysis(OrderItemTrackingVO trackingVO, int excelRowNum, Map<String, OrderItem> orderNoMap,
                              Map<String, OrderLogisticsInfo> logisticsInfoMap,
                              Map<String, OrderAddressInfo> addressInfoMap,
                              Map<String, Orders> orderInfoMap, Map<String, ProductSku> productSkuMap){
        String orderNo = trackingVO.getOrderNo();
        String trackingNo = trackingVO.getTrackingNo();
        String carrier = trackingVO.getCarrier();
        String productSkuCode = trackingVO.getProductSkuCode();
        String channelOrderNo = trackingVO.getChannelOrderNo();
        if(CharSequenceUtil.isBlank(productSkuCode)){
            localeMessage.append(builder.build(OrderItemTrackingVO::getProductSkuCode, ExcelMessageEnum.PRODUCT_SKU_CANNOT_BE_EMPTY,excelRowNum),";");
        }else {
            if(productSkuMap.get(productSkuCode) == null){
                localeMessage.append(builder.build(OrderItemTrackingVO::getProductSkuCode, ExcelMessageEnum.PRODUCT_SKU_NOT_EXIST,excelRowNum),";");
            }
        }
        if(CharSequenceUtil.isNotBlank(channelOrderNo)){
            if(channelOrderNo.length()>50){
                localeMessage.append(builder.build(OrderItemTrackingVO::getChannelOrderNo, ExcelMessageEnum.CHANNEL_ORDER_NO_TOO_LONG,excelRowNum),";");
            }
        }

        if(CharSequenceUtil.isBlank(trackingNo)){
            localeMessage.append(builder.build(OrderItemTrackingVO::getTrackingNo, ExcelMessageEnum.TRACKING_CANNOT_BE_EMPTY,excelRowNum),";");
        }else {
            if(trackingNo.length() > 50){
                localeMessage.append(builder.build(OrderItemTrackingVO::getTrackingNo, ExcelMessageEnum.TRACKING_TOO_LONG,excelRowNum),";");
            }
        }

        if(CharSequenceUtil.isBlank(orderNo)){
            localeMessage.append(builder.build(OrderItemTrackingVO::getOrderNo, ExcelMessageEnum.ORDER_NO_CANNOT_BE_EMPTY,excelRowNum),";");
        }else {
            Orders order = orderInfoMap.get(orderNo);
            if(ObjectUtil.isEmpty(order)){
                localeMessage.append(builder.build(OrderItemTrackingVO::getOrderNo, ExcelMessageEnum.ORDER_NOT_EXIST,excelRowNum),";");
            }
            OrderItem item = orderNoMap.get(orderNo);
//            if(ObjectUtil.isEmpty(item)){
//                localeMessage.append(builder.build(OrderItemTrackingVO::getOrderNo, ExcelMessageEnum.TRACKING_PICK_UP_ONLY,excelRowNum),";");
//            }
            OrderAddressInfo addressInfo = addressInfoMap.get(orderNo);
            if(ObjectUtil.isEmpty(addressInfo)){
                localeMessage.append(builder.build(OrderItemTrackingVO::getOrderNo, ExcelMessageEnum.ADDRESS_NOT_EXIST,excelRowNum),";");
            }
            OrderLogisticsInfo logisticsInfo = logisticsInfoMap.get(orderNo);
            if(ObjectUtil.isEmpty(logisticsInfo)){
                localeMessage.append(builder.build(OrderItemTrackingVO::getOrderNo, ExcelMessageEnum.LOGISTICS_NOT_EXIST,excelRowNum),";");
            }

        }

        if(CharSequenceUtil.isBlank(carrier)){
            localeMessage.append(builder.build(OrderItemTrackingVO::getTrackingNo, ExcelMessageEnum.NON_VALID_CARRIER,excelRowNum),";");
        }else {
            if(carrier.length()>50){
                localeMessage.append(builder.build(OrderItemTrackingVO::getTrackingNo, ExcelMessageEnum.CARRIER_TOO_LONG,excelRowNum),";");
            }
        }

    }
    /**
     * 功能描述：效率相对高一些,
     *
     * @param context 上下文
     * <AUTHOR>
     * @date 2025/03/25
     */
    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        if(CollUtil.isEmpty(orderItemTrackingRecords)){
            throw new RuntimeException("导入失败,空的excel");
        }
        log.info("订单Tracking导入源数据"+ JSONUtil.toJsonStr(orderItemTrackingRecords));
        DownloadRecord newRecord = new DownloadRecord();
        newRecord.setRecordState(RecordStateEnum.Generating);
        newRecord.setFileName(fileName);
        newRecord.setDownloadType(DownloadTypePlusEnum.OrderItemTrackingImport);
        iDownloadRecordService.save(newRecord);
        List<String> distinctOrderNos = orderItemTrackingRecords.stream()
                                                                .filter(Objects::nonNull) // 过滤掉元素为null的情况
                                                                .map(OrderItemTrackingVO::getOrderNo)
                                                                .filter(Objects::nonNull) // 过滤掉orderNo为null的情况
                                                                .distinct()
                                                                .collect(Collectors.toList());
        OrderItemTrackingImportRecord orderItemTrackingImportRecord = orderItemTrackingImportRecordService.initialize(newRecord,dataRowCount);
        List<OrderItem> orderItems = iOrderItemService.getByOrderNos(distinctOrderNos);


        Map<String, Date> maxDeliveryTimeMap = orderItemTrackingRecords.stream()
                                                                       .collect(Collectors.groupingBy(
                                                                           OrderItemTrackingVO::getOrderNo,
                                                                           Collectors.collectingAndThen(
                                                                               Collectors.maxBy(Comparator.comparing(r ->
                                                                                   LocalDateTime.parse(r.getDeliveryTime(), FORMATTER)
                                                                               )),
                                                                               opt -> opt.map(r ->
                                                                                   Date.from(
                                                                                       LocalDateTime.parse(r.getDeliveryTime(), FORMATTER)
                                                                                                    .atZone(ZoneId.systemDefault())
                                                                                                    .toInstant()
                                                                                   )
                                                                               ).orElse(null)
                                                                           )
                                                                       ));

        orderItems.stream()
              .filter(orderItem -> maxDeliveryTimeMap.containsKey(orderItem.getOrderNo()))  // 过滤存在关联记录的订单
              .forEach(orderItem -> {
                  orderItem.setDispatchedTime(maxDeliveryTimeMap.get(orderItem.getOrderNo()));  // 更新时间为最新值
                  orderItem.setFulfillmentProgress(LogisticsProgress.Dispatched);
                  }
              );
        orderItemTrackingImportRecordService.save(orderItemTrackingImportRecord);
        newRecord.setBusinessImportExportId(orderItemTrackingImportRecord.getId());
        try {
            connection = dataSource.getConnection();
            connection.setAutoCommit(false); // 开启事务
            // 批量插入（每批5000条）
            String sql = "INSERT INTO order_item_tracking_record (" + "sku, product_sku_code, logistics_carrier, logistics_tracking_no, " + "order_no, order_item_no, logistics_progress, warehouse_system_code, " + "quantity, create_time, update_time,dispatched_time,warehouse_code" + ") VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW(),?,?)";
            try (PreparedStatement ps = connection.prepareStatement(sql)) {
                int batchCount = 0;
                Map<String, String> skuMap = getStringStringMap();
                // 子订单信息
                Map<String, OrderItem> orderNoMap = getOrderNoMap();
                // 物流信息
                Map<String, OrderLogisticsInfo> logisticsInfoMap= getLogisticsInfo();
                // 地址信息
                Map<String, OrderAddressInfo> addressInfoMap= getAddressInfo();
                Map<String, Orders> orderInfoMap= getOrderInfo();
                Map<String, ProductSku> productSkuMap= getProductSkuMap();
                Map<String, OrderItemProductSku> orderItemProductMap = getOrderItemProductMap();
                Map<String, Warehouse> warehouseMap = getWarehouseMap();
                for (OrderItemTrackingVO recordInspect : orderItemTrackingRecords){
                    dataAnalysis(recordInspect,recordInspect.getExcelRow(),orderNoMap,logisticsInfoMap,addressInfoMap,orderInfoMap,productSkuMap);
                }

                if(localeMessage.hasData()){
                    newRecord.setRecordState(RecordStateEnum.Failed);
                    iDownloadRecordService.updateById(newRecord);

                    orderItemTrackingImportRecord.setImportState(ImportStateEnum.Failed);
                    orderItemTrackingImportRecord.setImportMessage(localeMessage.toJSON());
                    orderItemTrackingImportRecordService.updateById(orderItemTrackingImportRecord);
                }else{
                    // 先remove
                    itemTrackingRecordService.remove(new LambdaQueryWrapper<OrderItemTrackingRecord>().in(OrderItemTrackingRecord::getOrderNo, distinctOrderNos));
                   // Set<String> uniqueKeySet = new LinkedHashSet<>();
                    //对导入的Tracking进行去重
                    orderItemTrackingRecords = distinctByOrderNoAndCarrierTrackingNo(orderItemTrackingRecords);
                    List<OrderItemTrackingVO> distinctRecords = new ArrayList<>();
                    for (OrderItemTrackingVO record : orderItemTrackingRecords) {
                        // 需要去重-orderNo+logistics_tracking_no 形成唯一key,如果set内存在就跳过此条记录
//                        String uniqueKey = record.getOrderNo() + "_" + record.getTrackingNo();
//                        // 如果uniqueKey已存在，则跳过当前记录
//                        if (uniqueKeySet.contains(uniqueKey)) {
//                            continue;
//                        }
//
//                        // 否则，将唯一键加入Set，并处理当前记录（如添加到去重后的列表）
//                        uniqueKeySet.add(uniqueKey);
                        OrderItem item = orderNoMap.get(record.getOrderNo());
                        OrderItemProductSku itemProductSku = orderItemProductMap.get(record.getOrderNo());
                        String deliveryTime = record.getDeliveryTime();
                        if(ObjectUtil.isEmpty(item)){
                            continue;
                        }
//                        OrderLogisticsInfo orderLogisticsInfo = logisticsInfoMap.get(record.getOrderNo());
                        ps.setString(1, skuMap.get(record.getProductSkuCode()));
                        ps.setString(2, record.getProductSkuCode());
                        ps.setString(3, record.getCarrier());
                        ps.setString(4, record.getTrackingNo());
                        ps.setString(5, record.getOrderNo());
                        ps.setString(6, item.getOrderItemNo());
                        ps.setString(7, LogisticsProgress.Dispatched.getValue());

                        ps.setString(8, itemProductSku.getWarehouseSystemCode());
                        ps.setString(11,warehouseMap.get(itemProductSku.getWarehouseSystemCode()).getWarehouseCode());
                        ps.setInt(9, record.getQuantity());

                        ps.setString(10, deliveryTime);
                        ps.addBatch();


                        // 分批次提交到数据库（避免内存溢出）
                        if (++batchCount % 5000 == 0) {
                            ps.executeBatch();
                        }
                    }
                    // 都成功了, 插入 importRecord
                    orderItemTrackingImportRecord.setImportState(ImportStateEnum.Success);
                    orderItemTrackingImportRecordService.updateById(orderItemTrackingImportRecord);
                    newRecord.setRecordState(RecordStateEnum.Ready);
                    iDownloadRecordService.updateById(newRecord);
                    TenantHelper.ignore(()->iOrderItemService.updateBatchById(orderItems));
                    // 这个要传实体进去 批量更新
                    iOrdersService.batchUpdateTracking2Success(distinctOrderNos);
                }

                ps.executeBatch();
                connection.commit();
            } catch (SQLException e) {
                connection.rollback();
                newRecord.setRecordState(RecordStateEnum.Failed);
                iDownloadRecordService.updateById(newRecord);
                throw new RuntimeException("事务回滚，数据全部无效", e);
            }
        } catch (SQLException ex) {
            throw new RuntimeException("数据库连接失败", ex);
        } finally {
            if (connection != null) {
                try {
                    connection.close();
                } catch (SQLException e) {
                    // 记录日志
                }
            }
        }
    }

    /**
     * 功能描述：获取订单项产品图
     *
     * @return {@link Map }<{@link String }, {@link OrderItemProductSku }>
     * <AUTHOR>
     * @date 2025/03/31
     */
    private Map<String, OrderItemProductSku> getOrderItemProductMap() {
        if(CollUtil.isNotEmpty(orderItemTrackingRecords)){
            List<String> collect = orderItemTrackingRecords.stream().map(OrderItemTrackingVO::getOrderNo).distinct()
                                                           .collect(Collectors.toList());
            return TenantHelper.ignore(()->iOrderItemProductSkuService.getOrderNoAndProductSkuMap(collect));
        }else {
            return null;
        }

    }

    private Map<String, Warehouse> getWarehouseMap() {
        if(CollUtil.isNotEmpty(orderItemTrackingRecords)){
            return TenantHelper.ignore(()->iWarehouseService.list().stream().collect(Collectors.toMap(Warehouse::getWarehouseSystemCode, Function.identity())));
        }else {
            return null;
        }

    }

    /**
     * 功能描述：获取产品sku图
     *
     * @return {@link Map }<{@link String }, {@link ProductSku }>
     * <AUTHOR>
     * @date 2025/03/26
     */
    private Map<String, ProductSku> getProductSkuMap() {
        if(CollUtil.isNotEmpty(orderItemTrackingRecords)){
            List<String> collect = orderItemTrackingRecords.stream().map(OrderItemTrackingVO::getProductSkuCode).distinct()
                                                           .collect(Collectors.toList());
            return iProductSkuService.getProductSkuCodeAndProductSkuMap(collect);
        }else {
            return null;
        }

    }

    private Map<String, Orders> getOrderInfo() {
        if(CollUtil.isNotEmpty(orderItemTrackingRecords)){
            List<String> collect = orderItemTrackingRecords.stream().map(OrderItemTrackingVO::getOrderNo)
                                                           .collect(Collectors.toList());
            LambdaQueryWrapper<Orders> in = new LambdaQueryWrapper<Orders>().in(Orders::getOrderNo, collect);
            List<Orders> list = TenantHelper.ignore(()->iOrdersService.list(in));
            return list.stream().collect(Collectors.toMap(Orders::getOrderNo, Function.identity()));
        }else {
            return null;
        }

    }

    /**
     * 功能描述：获取地址信息
     *
     * @return {@link Map }<{@link String }, {@link OrderAddressInfo }>
     * <AUTHOR>
     * @date 2025/03/26
     */
    private Map<String, OrderAddressInfo> getAddressInfo() {
        if(CollUtil.isNotEmpty(orderItemTrackingRecords)){
            List<String> orderNos = orderItemTrackingRecords.stream().map(OrderItemTrackingVO::getOrderNo)
                                                            .collect(Collectors.toList());
            LambdaQueryWrapper<OrderAddressInfo> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.in(OrderAddressInfo::getOrderNo, orderNos);
            List<OrderAddressInfo> list = TenantHelper.ignore(()->iOrderAddressInfoService.list(queryWrapper));
            return list.stream().collect(Collectors.toMap(OrderAddressInfo::getOrderNo, Function.identity()));
        }else {
            return null;
        }

    }

    private Map<String, OrderLogisticsInfo> getLogisticsInfo() {
        if(CollUtil.isNotEmpty(orderItemTrackingRecords)){
            return TenantHelper.ignore(()->iOrderLogisticsInfoService.getByOrderNos(orderItemTrackingRecords.stream().map(OrderItemTrackingVO::getOrderNo).collect(Collectors.toList())));

        }else {
            return null;
        }
    }

    private Map<String, OrderItem> getOrderNoMap() {
        if(CollUtil.isNotEmpty(orderItemTrackingRecords)){
            List<String> orderNos = orderItemTrackingRecords.stream().map(OrderItemTrackingVO::getOrderNo)
                                                            .collect(Collectors.toList());

            return TenantHelper.ignore(()->iOrderItemService.getOrderNoAndOrderItemNoMap(orderNos));
        }else {
            return null;
        }

    }

    private Map<String, String> getStringStringMap() {
        if(CollUtil.isNotEmpty(orderItemTrackingRecords)){
            List<String> productSkuCodes = orderItemTrackingRecords.stream().map(OrderItemTrackingVO::getProductSkuCode)
                                                                   .collect(Collectors.toList());


            return iProductSkuService.getProductSkuCodeAndSkuMap(productSkuCodes);
        }else {
            return null;
        }

    }


    public void setFileName(String fileName) {
        this.fileName = ObjectUtil.isEmpty(fileName) ? "orderItemTrackingImport.xlsx" : fileName;
    }


    /**
     * 根据orderNo和(carrier+trackingNo)组合去重
     * 确保同一个订单下，物流商和物流单号的组合是唯一的
     * @param list 原始列表
     * @return 去重后的列表
     */
    public static List<OrderItemTrackingVO> distinctByOrderNoAndCarrierTrackingNo(List<OrderItemTrackingVO> list) {
        if (list == null || list.isEmpty()) {
            return new ArrayList<>();
        }

        return list.stream()
                   .collect(Collectors.groupingBy(OrderItemTrackingVO::getOrderNo))
                   .values()
                   .stream()
                   .flatMap(orderItems -> orderItems.stream()
                                                    .collect(Collectors.toMap(
                                                        item -> item.getCarrier() + "_" + item.getTrackingNo(),
                                                        item -> item,
                                                        (existing, replacement) -> existing
                                                    ))
                                                    .values()
                                                    .stream())
                   .collect(Collectors.toList());
    }
}
