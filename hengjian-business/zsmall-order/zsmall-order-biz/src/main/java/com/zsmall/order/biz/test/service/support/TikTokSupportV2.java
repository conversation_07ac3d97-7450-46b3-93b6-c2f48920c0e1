package com.zsmall.order.biz.test.service.support;

import com.alibaba.fastjson.JSONObject;
import com.hengjian.common.log.enums.BusinessType;
import com.zsmall.common.domain.vo.XxlJobSearchVO;
import com.zsmall.common.enums.common.ChannelTypeEnum;
import com.zsmall.common.enums.tiktok.TikTokApiEnums;
import com.zsmall.common.enums.tiktok.TikTokEnum;
import com.zsmall.order.biz.test.factory.ThirdOrderApiV2Factory;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2024/2/4 14:07
 */
@SuppressWarnings("unchecked")
@Slf4j
@Component
@RequiredArgsConstructor
public class TikTokSupportV2 {


    @Resource
    private ThirdOrderApiV2Factory apiFactory;


    /**
     * 功能描述：获取所有订单
     *  json 里包含query和body和shopId
     * @param vo          VO
     * @param targetClass 目标类
     * @return {@link T }
     * <AUTHOR>
     * @date 2024/02/02
     */
    public <T> T getAllOrder(XxlJobSearchVO vo, Class<T> targetClass) {
        JSONObject json = (JSONObject) JSONObject.toJSON(vo);
        // shopId 是所有的shopId 更新渠道下 就是 tenantSalesChannel 表内的所有id

        return (T) apiFactory.getInvokeStrategy(TikTokEnum.TIK_TOK_ORDER_V2.getEnumType())
                             .dataPlatformFlow(BusinessType.ORDER_V2,json , ChannelTypeEnum.TikTok, TikTokApiEnums.GET_ORDER_LIST,vo.getShopId(),targetClass );
    }

    /**
     * 功能描述：获取所有订单 多线程+先订单后库存
     *  json 里包含query和body和shopId
     * @param vo          VO
     * @param targetClass 目标类
     * @return {@link T }
     * <AUTHOR>
     * @date 2024/02/02
     */
    public <T> T getAllOrderV2(XxlJobSearchVO vo, Class<T> targetClass) {
        JSONObject json = (JSONObject) JSONObject.toJSON(vo);
        // shopId 是所有的shopId 更新渠道下 就是 tenantSalesChannel 表内的所有id
        return (T) apiFactory.getInvokeStrategy(TikTokEnum.TIK_TOK_ORDER_V2.getEnumType())
                             .dataPlatformFlowV2(BusinessType.ORDER_V2,json , ChannelTypeEnum.TikTok, TikTokApiEnums.GET_ORDER_LIST,vo.getShopId(),targetClass );
    }


    public <T> T  getAllProduct(XxlJobSearchVO vo, Class<T> targetClass) {
        JSONObject json = (JSONObject) JSONObject.toJSON(vo);
        return (T) apiFactory.getInvokeStrategy(TikTokEnum.TIK_TOK_ORDER_V2.getEnumType())
                             .dataPlatformFlow(BusinessType.PRODUCT_V2,json , ChannelTypeEnum.TikTok, TikTokApiEnums.SEARCH_PRODUCTS,vo.getShopId(),targetClass );
    }


    /**
     * 功能描述：获取指定所有订单
     *
     * @param vo          vo
     * @param targetClass 目标类
     * @return {@link T }
     * <AUTHOR>
     * @date 2024/04/17
     */
    public <T> T getAllOrderForSpecify(XxlJobSearchVO vo, Class<T> targetClass) {
        JSONObject json = (JSONObject) JSONObject.toJSON(vo);
        // shopId 是所有的shopId 更新渠道下 就是 tenantSalesChannel 表内的所有id

        return (T) apiFactory.getInvokeStrategy(TikTokEnum.TIK_TOK_ORDER_V2.getEnumType())
                             .dataPlatformFlowForSpecify(BusinessType.ORDER_V2,json , ChannelTypeEnum.TikTok, TikTokApiEnums.GET_ORDER_LIST,vo.getShopId(),targetClass );
    }
    public <T> T getAllOrderV2ForTest(XxlJobSearchVO vo, Class<T> targetClass, String msg) {
        JSONObject json = (JSONObject) JSONObject.toJSON(vo);
        // shopId 是所有的shopId 更新渠道下 就是 tenantSalesChannel 表内的所有id
        return (T) apiFactory.getInvokeStrategy(TikTokEnum.TIK_TOK_ORDER_V2.getEnumType())
                             .dataPlatformFlowV2ForTest(BusinessType.ORDER_V2,json , ChannelTypeEnum.TikTok, TikTokApiEnums.GET_ORDER_LIST,vo.getShopId(),targetClass ,msg);
    }

}
