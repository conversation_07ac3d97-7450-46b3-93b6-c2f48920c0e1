package com.zsmall.order.biz.handler.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hengjian.common.core.domain.R;
import com.hengjian.common.core.utils.StringUtils;
import com.hengjian.common.tenant.helper.TenantHelper;
import com.hengjian.stream.mq.constant.RabbitMqConstant;
import com.thoughtworks.xstream.XStream;
import com.zsmall.activity.entity.domain.dto.productActivity.DistributorProductActivity;
import com.zsmall.activity.entity.domain.dto.productActivity.DistributorProductActivityStock;
import com.zsmall.common.domain.LocaleMessage;
import com.zsmall.common.domain.dto.AmazonVCOrderItemMessageDTO;
import com.zsmall.common.domain.dto.AmazonVCOrderMessageDTO;
import com.zsmall.common.enums.BusinessCodeEnum;
import com.zsmall.common.enums.common.BusinessTypeMappingEnum;
import com.zsmall.common.enums.common.ChannelTypeEnum;
import com.zsmall.common.enums.order.*;
import com.zsmall.common.enums.product.SupportedLogisticsEnum;
import com.zsmall.common.enums.productMapping.SyncStateEnum;
import com.zsmall.common.enums.statuscode.OrderStatusCodeEnum;
import com.zsmall.common.enums.warehouse.WarehouseTypeEnum;
import com.zsmall.common.exception.AppRuntimeException;
import com.zsmall.common.handler.AbstractOrderOperationHandler;
import com.zsmall.common.util.RegexUtil;
import com.zsmall.common.util.UnitConverter;
import com.zsmall.extend.utils.ZSMallSystemEventUtils;
import com.zsmall.lottery.support.PriceSupportV2;
import com.zsmall.order.biz.service.OrderItemPriceService;
import com.zsmall.order.biz.service.OrderItemService;
import com.zsmall.order.biz.service.OrdersService;
import com.zsmall.order.biz.service.impl.OrderItemProductSkuThirdServiceImpl;
import com.zsmall.order.biz.service.impl.OrderItemThirdServiceImpl;
import com.zsmall.order.biz.support.OrderActivitySupport;
import com.zsmall.order.biz.support.OrderSupport;
import com.zsmall.order.entity.domain.*;
import com.zsmall.order.entity.domain.bo.order.OrderPayBo;
import com.zsmall.order.entity.domain.dto.AmazonVCOrderLogisticsAttachmentDTO;
import com.zsmall.order.entity.domain.dto.AmazonVCOrderLogisticsAttachmentItemDTO;
import com.zsmall.order.entity.domain.dto.OrderPriceCalculateDTO;
import com.zsmall.order.entity.iservice.*;
import com.zsmall.order.entity.mapper.ThirdOrderInfoItemMapper;
import com.zsmall.order.entity.mapper.ThirdOrderInfoMapper;
import com.zsmall.product.entity.domain.*;
import com.zsmall.product.entity.domain.vo.productSku.ProductSkuAdjustStockVo;
import com.zsmall.product.entity.iservice.*;
import com.zsmall.system.entity.domain.ChannelWarehouseInfo;
import com.zsmall.system.entity.domain.TenantSalesChannel;
import com.zsmall.system.entity.domain.vo.SiteCountryCurrencyVo;
import com.zsmall.system.entity.iservice.IChannelWarehouseInfoServiceImpl;
import com.zsmall.system.entity.iservice.ISiteCountryCurrencyService;
import com.zsmall.system.entity.iservice.ITenantSalesChannelService;
import com.zsmall.warehouse.entity.domain.Warehouse;
import com.zsmall.warehouse.entity.iservice.IWarehouseService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.core.MessageBuilder;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.ParseException;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

import static com.zsmall.common.enums.order.OrderFlowEnum.THIRD_CREATE_ORDER;

/**
 * <AUTHOR>
 * @date 2024年10月21日  17:28
 * @description:亚马逊VC订单处理类
 */
@Slf4j
@Lazy
@Component("amazonVCOrderOperationHandler")
public class AmazonVCOrderOperationHandler extends AbstractOrderOperationHandler<String,AmazonVCOrderMessageDTO, Map<String,Object>, Orders,Object> {

    @Resource
    private IProductMappingService iProductMappingService;
    @Value("${distribution.specify.warehouse.id.bizArk}")
    public String warehouseSystemCode;
    @Resource
    private OrderItemService orderItemService;
    @Resource
    private OrderSupport orderSupport;
    @Resource
    private IProductSkuService iProductSkuService;
    @Resource
    private IProductService iProductService;
    @Resource
    private IOrdersService iOrdersService;
    @Resource
    private OrdersService ordersService;
    @Resource
    private ITenantSalesChannelService iTenantSalesChannelService;
    @Resource
    private IProductChannelControlService iProductChannelControlService;
    @Autowired
    ApplicationContext applicationContext;
    @Resource
    private OrderCodeGenerator orderCodeGenerator;
    @Resource
    private IOrderItemService iOrderItemService;
    @Resource
    private OrderItemThirdServiceImpl iOrderItemThirdService;
    @Resource
    private IOrderItemProductSkuService iOrderItemProductSkuService;
    @Resource
    private IOrderLogisticsInfoService iOrderLogisticsInfoService;
    @Resource
    private IOrderAddressInfoService iOrderAddressInfoService;
    @Resource
    private IOrderItemPriceService iOrderItemPriceService;
    @Resource
    private OrderItemProductSkuThirdServiceImpl orderItemProductSkuThirdService;
    @Resource
    ThirdOrderInfoMapper thirdOrderInfoMapper;
    @Resource
    ThirdOrderInfoItemMapper thirdOrderInfoItemMapper;
    @Resource
    private PriceSupportV2 priceSupportV2;
    @Resource
    private OrderItemPriceService orderItemPriceService;
    @Resource
    private RabbitTemplate rabbitTemplate;
    @Resource
    private IProductSkuDetailService iProductSkuDetailService;
    @Resource
    private IChannelWarehouseInfoServiceImpl iChannelWarehouseService;
    @Resource
    private IWarehouseService iWarehouseService;
    @Resource
    private ISiteCountryCurrencyService iSiteCountryCurrencyService;
    @Resource
    private IProductSkuPriceService iProductSkuPriceService;
    @Resource
    private OrderActivitySupport orderActivitySupport;

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void initialMessageSave(AmazonVCOrderMessageDTO amazonVCOrderMessageDTO) {
        List<ThirdOrderInfo> thirdOrderInfoList = new ArrayList<>();
        List<ThirdOrderInfoItem> thirdOrderInfoItemList = new ArrayList<>();
        if(ObjectUtil.isNotEmpty(amazonVCOrderMessageDTO)){
            ThirdOrderInfo thirdOrderInfo = new ThirdOrderInfo();
            thirdOrderInfo.setAccountId(amazonVCOrderMessageDTO.getAccount_id()).setOrderNum(amazonVCOrderMessageDTO.getOrdernum()).setAmount(amazonVCOrderMessageDTO.getAmount()).setCurrencyCode(amazonVCOrderMessageDTO.getCurrency_code())
                          .setStatus(amazonVCOrderMessageDTO.getStatus()).setShippingStatus(amazonVCOrderMessageDTO.getShipping_status()).setBuyName(amazonVCOrderMessageDTO.getBuy_name()).setBuyEmail(amazonVCOrderMessageDTO.getBuy_email())
                          .setDisplaySeller(amazonVCOrderMessageDTO.getDisplay_seller()).setFulfillmentChannel(amazonVCOrderMessageDTO.getFulfillment_channel()).setShipServiceLevel(amazonVCOrderMessageDTO.getShip_service_level())
                          .setShippingName(amazonVCOrderMessageDTO.getShipping_name()).setAddressline1(amazonVCOrderMessageDTO.getAddressline1()).setAddressline2(amazonVCOrderMessageDTO.getAddressline2())
                          .setCity(amazonVCOrderMessageDTO.getCity()).setStateOrRegion(amazonVCOrderMessageDTO.getState_or_region()).setCountry(amazonVCOrderMessageDTO.getCountry()).setCountryCode(amazonVCOrderMessageDTO.getCountry_code())
                          .setPostalCode(amazonVCOrderMessageDTO.getPostal_code()).setPhone(amazonVCOrderMessageDTO.getPhone()).setCreated(amazonVCOrderMessageDTO.getCreated()).setPaymentDate(amazonVCOrderMessageDTO.getPayment_date())
                          .setUpdated(amazonVCOrderMessageDTO.getUpdated()).setLatestShipDate(amazonVCOrderMessageDTO.getLatest_ship_date()).setEarliestShipDate(amazonVCOrderMessageDTO.getEarliest_ship_date())
                          .setIsBusinessOrder(amazonVCOrderMessageDTO.getIs_business_order()).setIsPrime(amazonVCOrderMessageDTO.getIs_prime())
                          .setSales_channel(amazonVCOrderMessageDTO.getSales_channel()).setPurchase_order_state(amazonVCOrderMessageDTO.getPurchase_order_state()).setCustomer_refer_no(amazonVCOrderMessageDTO.getCustomer_refer_no())
                          .setShip_to_party(amazonVCOrderMessageDTO.getShip_to_party()).setFob_shipment_payment_method(amazonVCOrderMessageDTO.getFob_shipment_payment_method()).setAddress_code(amazonVCOrderMessageDTO.getAddress_code())
                          .setCreateTime(LocalDateTime.now());
            thirdOrderInfo.setTenantId(amazonVCOrderMessageDTO.getTenantId());
            thirdOrderInfoList.add(thirdOrderInfo);
            if(CollUtil.isNotEmpty(amazonVCOrderMessageDTO.getItems())){
                List<AmazonVCOrderItemMessageDTO> items = amazonVCOrderMessageDTO.getItems();
                for(AmazonVCOrderItemMessageDTO amazonVCOrderItemMessageDTO : items){
                    ThirdOrderInfoItem thirdOrderInfoItem = new ThirdOrderInfoItem();
                    thirdOrderInfoItem.setAccountId(amazonVCOrderMessageDTO.getAccount_id()).setOrderNum(amazonVCOrderMessageDTO.getOrdernum()).setCreateTime(LocalDateTime.now());
                    thirdOrderInfoItem.setSku(amazonVCOrderItemMessageDTO.getSku()).setAsin(amazonVCOrderItemMessageDTO.getAsin()).setTitle(amazonVCOrderItemMessageDTO.getTitle()).setQuantity(amazonVCOrderItemMessageDTO.getQuantity())
                                      .setSubtotal(amazonVCOrderItemMessageDTO.getSubtotal()).setCurrencyCode(amazonVCOrderItemMessageDTO.getCurrency_code()).setShipedQuantity(amazonVCOrderItemMessageDTO.getShiped_quantity()).setShippingPrice(amazonVCOrderItemMessageDTO.getShipping_price())
                                      .setShippingTaxPrice(amazonVCOrderItemMessageDTO.getShipping_tax_price()).setShippingDiscountPrice(amazonVCOrderItemMessageDTO.getShipping_discount_price()).setTaxPrice(amazonVCOrderItemMessageDTO.getTax_price()).setPromotionDiscountPrice(amazonVCOrderItemMessageDTO.getPromotion_discount_price())
                                      .setIs_back_order_allowed(amazonVCOrderItemMessageDTO.getIs_back_order_allowed()).setUnit_of_measure(amazonVCOrderItemMessageDTO.getUnit_of_measure()).setUnit_size(amazonVCOrderItemMessageDTO.getUnit_size())
                                      .setGiftWrapPrice(amazonVCOrderItemMessageDTO.getGift_wrap_price()).setGiftWrapTaxPrice(amazonVCOrderItemMessageDTO.getGift_wrap_tax_price()).setChannelOrderItemId(amazonVCOrderItemMessageDTO.getChannel_order_item_id())
                                      .setTaxCurrencyCode(amazonVCOrderItemMessageDTO.getTax_currency_code()).setListingPrice(amazonVCOrderItemMessageDTO.getListing_price());
                    thirdOrderInfoItem.setTenantId(amazonVCOrderMessageDTO.getTenantId());
                    thirdOrderInfoItemList.add(thirdOrderInfoItem);
                }
            }
            if(CollUtil.isNotEmpty(thirdOrderInfoList)){
                thirdOrderInfoMapper.insertBatch(thirdOrderInfoList);
            }
            if (CollUtil.isNotEmpty(thirdOrderInfoItemList)){
                thirdOrderInfoItemMapper.insertBatch(thirdOrderInfoItemList);
            }
        }
    }

    @Override
    public AmazonVCOrderMessageDTO parseThirdData(String json) {
        XStream xStream = new XStream();
        xStream.alias("OrderHeader", AmazonVCOrderMessageDTO.class);
        xStream.alias("OrderItem", AmazonVCOrderItemMessageDTO.class);
        AmazonVCOrderMessageDTO amazonVCOrderMessageDTO = (AmazonVCOrderMessageDTO) xStream.fromXML(json);
        if (ObjectUtil.isEmpty(amazonVCOrderMessageDTO)) {
            throw new AppRuntimeException("订单数据不能为null");
        }
        // 保存报文信息
        try {
            initialMessageSave(amazonVCOrderMessageDTO);
        }catch (Exception e){
            log.error("持久化amazonVC订单信息报错:{}",e.getMessage());
        }

        if(StringUtils.isNotEmpty(amazonVCOrderMessageDTO.getShipping_status()) && !amazonVCOrderMessageDTO.getShipping_status().equals("unshipped")){
            return null;
        }
//        if(null == temuOrderDTO.getAmount() || temuOrderDTO.getAmount().compareTo(BigDecimal.ZERO) == 0){
//            throw new AppRuntimeException("订单金额为0!");
//        }

        TenantSalesChannel tenantSalesChannelByChannelName = iTenantSalesChannelService.getTenantSalesChannelByChannelName(amazonVCOrderMessageDTO.getAccount_id(),ChannelTypeEnum.Amazon_VC.name());
        if(ObjectUtil.isEmpty(tenantSalesChannelByChannelName)){
            throw new AppRuntimeException("没有对应的渠道信息");
        }
        if(StringUtils.isEmpty(tenantSalesChannelByChannelName.getTenantId())){
            throw new AppRuntimeException("渠道:"+tenantSalesChannelByChannelName.getTenantId()+" 没有租户信息");
        }
        amazonVCOrderMessageDTO.setTenantId(tenantSalesChannelByChannelName.getTenantId());
        amazonVCOrderMessageDTO.setChannelType(ChannelTypeEnum.valueOf(tenantSalesChannelByChannelName.getChannelType()));
        amazonVCOrderMessageDTO.setLogisticsType(tenantSalesChannelByChannelName.getLogisticsType());
        amazonVCOrderMessageDTO.setChannelId(tenantSalesChannelByChannelName.getId());
        amazonVCOrderMessageDTO.setOrderSource(OrderSourceEnum.INTERFACE_ORDER.getValue());
        if(StringUtils.isEmpty(amazonVCOrderMessageDTO.getAddressline1())){
            return null;
        }
        if(CollUtil.isNotEmpty(amazonVCOrderMessageDTO.getItems()) && amazonVCOrderMessageDTO.getItems().size() > 1){
            amazonVCOrderMessageDTO.setIsMultiple(Boolean.TRUE);
        }else {
            amazonVCOrderMessageDTO.setIsMultiple(Boolean.FALSE);
        }
        return amazonVCOrderMessageDTO;
    }

    @Override
    public Boolean msgVerify(AmazonVCOrderMessageDTO amazonVCOrderMessageDTO) {
        List<AmazonVCOrderItemMessageDTO> items = amazonVCOrderMessageDTO.getItems();
        // 订单物流信息
        LogisticsTypeEnum orderLogisticsType = amazonVCOrderMessageDTO.getLogisticsType();
        for (AmazonVCOrderItemMessageDTO amazonVCOrderItemMessageDTO : items) {
            ProductMapping productMapping = iProductMappingService.getProductMappingByChannelSkuAndSyncStateAndCountry(amazonVCOrderMessageDTO.getTenantId(), amazonVCOrderMessageDTO.getChannelId(), amazonVCOrderItemMessageDTO.getSku(), SyncStateEnum.Mapped,amazonVCOrderMessageDTO.getCountry());
            if(null != productMapping && StringUtils.isNotEmpty(productMapping.getProductSkuCode())){
                ProductSku productSku = iProductSkuService.queryByProductSkuCode(productMapping.getProductSkuCode());
                if (productSku == null) {
                    continue;
                } else {
                    // 如果商品被管控，则报商品不存在
                    String tenantId = "1";
                    boolean checkUserAllow = iProductChannelControlService.checkUserAllow(tenantId, productMapping.getProductSkuCode(), ChannelTypeEnum.Amazon_VC.name());

                    if (!checkUserAllow) {
                        // 日志记录商品不存在
                        return Boolean.FALSE;
                    }
                }
                Product product = iProductService.queryByProductSkuCode(productMapping.getProductSkuCode());
                // 产品的物流信息
                SupportedLogisticsEnum supportedLogistics = product.getSupportedLogistics();
                // 物流类型校验
                if(SupportedLogisticsEnum.PickUpOnly.equals(supportedLogistics)){
                    if(orderLogisticsType.equals(LogisticsTypeEnum.DropShipping)){
                        log.error("订单物流类型: {} 和产品支持的物流类型: {} 不匹配,无法生成订单",supportedLogistics,LogisticsTypeEnum.DropShipping);
                        amazonVCOrderMessageDTO.setExceptionCode(OrderExceptionEnum.Delivery_exception.getValue());
                    }
                }
                if(SupportedLogisticsEnum.DropShippingOnly.equals(supportedLogistics)){
                    if(orderLogisticsType.equals(LogisticsTypeEnum.PickUp)){
                        log.error("订单物流类型: {} 和产品支持的物流类型: {} 不匹配,无法生成订单",supportedLogistics,LogisticsTypeEnum.PickUp);
                        amazonVCOrderMessageDTO.setExceptionCode(OrderExceptionEnum.Delivery_exception.getValue());
                    }
                }
            }
//            if (StrUtil.isNotBlank(amazonVCOrderMessageDTO.getOrdernum())) {
//                if (channelOrderNoSet.contains(amazonVCOrderMessageDTO.getOrdernum())) {
//                    // ?
//                } else {
//                    // 查询此账户所有订单判断是否有重复的，排除Canceled的
//                    boolean orderExists = iOrdersService.existsChannelOrderNo(amazonVCOrderMessageDTO.getOrdernum(), OrderStateType.Canceled);
//                    // 查询导入缓存表
//                    if (orderExists) {
//                        return Boolean.FALSE;
//                        // 存在同一个订单
//                    } else {
//                        channelOrderNoSet.add(amazonVCOrderMessageDTO.getOrdernum());
//                    }
//                }
//            }
            // 规则校验
            if (!RegexUtil.matchQuantity(amazonVCOrderItemMessageDTO.getQuantity().toString())) {
                // 数量异常-正则表达式
                return Boolean.FALSE;
            }
        }
        return Boolean.TRUE;
    }

    @Override
    public Orders formalOrderAndItemEntry(Map<String, List> map, Orders orders) {
        log.info("订单操作完成,进行保存操作");
        List<OrderItem> orderItems = (List<OrderItem>) map.get("orderItems");
        List<OrderItemProductSku> skus = (List<OrderItemProductSku>) map.get("orderItemProductsSku");
        iOrdersService.save(orders);
        orderItems.forEach(item -> item.setOrderId(orders.getId()));
        iOrderItemService.saveBatch(orderItems);
        if(CollUtil.isNotEmpty(skus)){
            for (OrderItem item : orderItems) {
                for (OrderItemProductSku sku : skus) {
                    if (item.getOrderItemNo().equals(sku.getOrderItemNo())) {
                        sku.setOrderItemId(item.getId());
                        sku.setOrderNo(item.getOrderNo());
                    }
                }
            }
            iOrderItemProductSkuService.saveBatch(skus);
        }
        log.info("检查渠道仓库映射");
        try{
            // 检查渠道仓库映射
            if(null == orders.getExceptionCode() || orders.getExceptionCode().equals(OrderExceptionEnum.normal.getValue()) || orders.getExceptionCode().equals(OrderExceptionEnum.out_of_stock_exception.getValue())){
                ChannelWarehouseInfo channelWarehouseInfo = iChannelWarehouseService.queryByTenantSaleChannelIdAndChannelWarehouseCode(orders.getChannelId(), orders.getChannelWarehouseCode());
                if(ObjectUtil.isEmpty(channelWarehouseInfo)){
                    orders.setExceptionCode(OrderExceptionEnum.warehouse_mapping_exception.getValue());
                    iOrdersService.updateById(orders);
                }else {
                    // 查询供应商仓库是否存在
                    for(OrderItem orderItem : orderItems){
                        Warehouse warehouse = iWarehouseService.queryByTenantIdAndWarehouseCodeAndWarehouseType(orderItem.getSupplierTenantId(), channelWarehouseInfo.getWarehouseCode(), WarehouseTypeEnum.BizArk);
                        if(ObjectUtil.isEmpty(warehouse)){
                            orders.setExceptionCode(OrderExceptionEnum.warehouse_mapping_exception.getValue());
                            iOrdersService.updateById(orders);
                        }else {
                            List<OrderItemProductSku> orderItemProductSkus = iOrderItemProductSkuService.queryListByOrderNo(orders.getOrderNo());
                            if(CollUtil.isNotEmpty(orderItemProductSkus)){
                                for (OrderItemProductSku orderItemProductSku : orderItemProductSkus){
                                    orderItemProductSku.setWarehouseSystemCode(warehouse.getWarehouseSystemCode());
                                }
                                iOrderItemProductSkuService.updateBatchById(orderItemProductSkus);
                            }
                        }
                    }
                }
            }
        } catch (Exception e){
            log.error("检查渠道仓库映射报错:{}",e.getMessage());
        }

        return orders;
    }


    /**
     * 发送订单信息到物流附件队列
     * Added by Buddy @20241115
     * 拆分后单条获取面单会导致只能获取到一个面单需要整合后多明细获取多条面单
     */
    @Override
    public void getShippingLabels(List<Map<String, List>> orderData) {
        try{
            AmazonVCOrderLogisticsAttachmentDTO amazonVCOrderLogisticsAttachmentDTO = new AmazonVCOrderLogisticsAttachmentDTO();
            List<AmazonVCOrderLogisticsAttachmentItemDTO> itemDTOList = new ArrayList<>();
            for (Map<String, List> orderDatum : orderData) {

                Orders orders = (Orders) orderDatum.get("orders").get(0);
                //发货类型为自提才获取面单
                if(!LogisticsTypeEnum.PickUp.equals(orders.getLogisticsType())){
                    log.info("发货类型为非自提，不获取面单");
                    return;
                }
                //List<OrderItem> orderItems = (List<OrderItem>) orderDatum.get("orderItems");
                List<OrderItemProductSku> skus = (List<OrderItemProductSku>) orderDatum.get("orderItemProductsSku");

                if(null == orders.getExceptionCode() || orders.getExceptionCode().equals(OrderExceptionEnum.normal.getValue())
                    || orders.getExceptionCode().equals(OrderExceptionEnum.out_of_stock_exception.getValue())){
                    log.info("发送订单信息到物流附件队列");
                    itemDTOList.addAll(createShippingLabels(orders, skus));

                }

            }
            Orders orderHead = (Orders)orderData.get(0).get("orders").get(0);
            if (CollectionUtil.isEmpty(itemDTOList)) {
                log.info("未获取到订单明细数据，单号：{}",orderHead.getChannelOrderNo());
                return;
            }
            amazonVCOrderLogisticsAttachmentDTO.setOrderNo(orderHead.getChannelOrderNo()).setChannel(orderHead.getChannelAlias().replaceAll("^[^:]*:", "")).setContainerType("Carton");
            amazonVCOrderLogisticsAttachmentDTO.setItems(itemDTOList);
            String str = JSON.toJSONString(amazonVCOrderLogisticsAttachmentDTO);
            String messageId = IdUtil.fastSimpleUUID();
            Message message = MessageBuilder.withBody(str.getBytes()).setMessageId(messageId).build();
            log.info("[发送订单信息到物流附件队列]成功，发送参数：{}",JSON.toJSON(amazonVCOrderLogisticsAttachmentDTO));
            rabbitTemplate.convertAndSend(RabbitMqConstant.MULTICHANNEL_SEND_EXCHANGE,RabbitMqConstant.ORDER_AMAZON_VC_LOGISTICS_ATTACHMENT_ROUTING_KEY,message);

        }catch (Exception e){
            log.error("发送订单信息到物流附件队列报错:{}",e.getMessage());
        }
    }

    private List<AmazonVCOrderLogisticsAttachmentItemDTO> createShippingLabels(Orders orders, List<OrderItemProductSku> skus) {
            List<AmazonVCOrderLogisticsAttachmentItemDTO> itemDTOList = new ArrayList<>();
            for(OrderItemProductSku orderItemProductSku : skus){
                AmazonVCOrderLogisticsAttachmentItemDTO itemDTO = new AmazonVCOrderLogisticsAttachmentItemDTO();
                itemDTO.setDeliverOrderNo(orders.getOrderNo()).setDeliverOrderId(orders.getId()).setChannelOrderItemId(orders.getLineOrderItemId()).setQuantity(1);
                ProductSkuDetail productSkuDetail = iProductSkuDetailService.queryByProductSkuCode(orderItemProductSku.getProductSkuCode());
                if(null != productSkuDetail){
                    // 设置长宽高
                    switch (productSkuDetail.getPackLengthUnit())      {
                        case foot:
                            itemDTO.setLength(productSkuDetail.getPackLength());
                            itemDTO.setWidth(productSkuDetail.getPackWidth());
                            itemDTO.setHeight(productSkuDetail.getPackHeight());
                            break;
                        case inch:
                            itemDTO.setLength(UnitConverter.inchesToFeet(productSkuDetail.getPackLength()));
                            itemDTO.setWidth(UnitConverter.inchesToFeet(productSkuDetail.getPackWidth()));
                            itemDTO.setHeight(UnitConverter.inchesToFeet(productSkuDetail.getPackHeight()));
                            break;
                        case m:
                            itemDTO.setLength(UnitConverter.metersToFeet(productSkuDetail.getPackLength()));
                            itemDTO.setWidth(UnitConverter.metersToFeet(productSkuDetail.getPackWidth()));
                            itemDTO.setHeight(UnitConverter.metersToFeet(productSkuDetail.getPackHeight()));
                            break;
                        case cm:
                            itemDTO.setLength(UnitConverter.metersToFeet(UnitConverter.centimetersToMeters(productSkuDetail.getPackLength())));
                            itemDTO.setWidth(UnitConverter.metersToFeet(UnitConverter.centimetersToMeters(productSkuDetail.getPackWidth())));
                            itemDTO.setHeight(UnitConverter.metersToFeet(UnitConverter.centimetersToMeters(productSkuDetail.getPackHeight())));
                            break;
                    }
                    // 设置重量
                    switch (productSkuDetail.getPackWeightUnit()){
                        case lb:
                            itemDTO.setWeight(productSkuDetail.getPackWeight());
                            break;
                        case kg:
                            itemDTO.setWeight(UnitConverter.kilogramsToPounds(productSkuDetail.getPackWeight()));
                            break;
                        case g:
                            itemDTO.setWeight(UnitConverter.gramsToPounds(productSkuDetail.getPackWeight()));
                            break;
                        case t:
                            itemDTO.setWeight(UnitConverter.kilogramsToPounds(UnitConverter.tonsToKilograms(productSkuDetail.getPackWeight())));
                            break;
                    }
                }
                itemDTOList.add(itemDTO);
            }

            return itemDTOList;
    }


@Override
public void formalOrderAboutEntry(Map<String, Object> map) {
    List<OrderLogisticsInfo> logisticsInfo = (List<OrderLogisticsInfo>) map.get("logisticsInfo");
    List<OrderAddressInfo> address = (List<OrderAddressInfo>) map.get("addressInfo");
    Orders orders = (Orders) map.get("orders");
    String tenantId = orders.getTenantId();
    iOrderLogisticsInfoService.saveBatch(logisticsInfo);
    iOrderAddressInfoService.saveBatch(address);
    String warehouseCode = null;
//      address转换为map,key为orderNo,value为zipCode
    Map<String, String> addressMap = address.stream()
                                            .collect(Collectors.toMap(OrderAddressInfo::getOrderNo, OrderAddressInfo::getZipCode));
    String logisticsCompanyName = null;
    if(CollUtil.isNotEmpty(logisticsInfo)){
        OrderLogisticsInfo orderLogisticsInfo = logisticsInfo.get(0);
        if (ObjectUtil.isNotEmpty(orderLogisticsInfo)){
            logisticsCompanyName = orderLogisticsInfo.getLogisticsCompanyName();
        }
    }
    // 此处已经生成了itemProductSku需要重新选仓库
    String warehouseSystemCode = null ;
    if(null == orders.getExceptionCode() || !orders.getExceptionCode().equals(OrderExceptionEnum.product_mapping_exception.getValue())){
        // 重新计算订单价格信息
        List<OrderItemPrice> itemPrices = new ArrayList<>();
        List<OrderItem> orderItemUpdateList = new ArrayList<>();
        List<OrderItem> listByOrderId = iOrderItemService.getListByOrderId(orders.getId());
        //查询明细商品关联
        HashMap<String,List<String> >stashMap = orderSupport.getStashList(listByOrderId);
        if(CollUtil.isNotEmpty(listByOrderId)){
            for(OrderItem orderItem : listByOrderId){
                OrderPriceCalculateDTO orderPriceCalculateDTO = new OrderPriceCalculateDTO();
                orderPriceCalculateDTO.setOrderItem(orderItem);
                orderPriceCalculateDTO.setActivityCode(orderItem.getActivityCode());
                orderPriceCalculateDTO.setLogisticsType(orders.getLogisticsType());
                List<String> stashList = stashMap.get(orderItem.getOrderItemNo());
                // 仓库编号入口
                LocaleMessage calculationOrderItemPriceExceptionMessage = priceSupportV2.calculationOrderItemPrice(orderPriceCalculateDTO,tenantId,addressMap.get(orderItem.getOrderNo()),stashList, orders, THIRD_CREATE_ORDER, logisticsCompanyName);
                warehouseSystemCode = orderPriceCalculateDTO.getWarehouseSystemCode();
                warehouseCode = orderPriceCalculateDTO.getWarehouseCode();
                if(ObjectUtil.isNotEmpty(calculationOrderItemPriceExceptionMessage) && StringUtils.isNotEmpty(calculationOrderItemPriceExceptionMessage.getEn_US())){
                    orders.setPayErrorMessage(calculationOrderItemPriceExceptionMessage.toJSON());
                }
                OrderItemPrice orderItemPrice1 = orderPriceCalculateDTO.getOrderItemPrice();
                OrderItemPrice orderItemPrice = new OrderItemPrice();
                BeanUtils.copyProperties(orderItemPrice1, orderItemPrice);
                orderItemPrice.setTotalQuantity(orderItem.getTotalQuantity()).setOrderItemId(orderItem.getId()).setOrderItemNo(orderItem.getOrderItemNo()).setProductSkuCode(orderItem.getProductSkuCode()).setLogisticsType(orderItem.getLogisticsType());
                itemPrices.add(orderItemPrice);
                OrderItem orderItemUpdate = new OrderItem();
                BeanUtils.copyProperties(orderItem, orderItemUpdate);
                orderItemUpdateList.add(orderItemUpdate);
            }
            // 重新计算主订单数据
            priceSupportV2.recalculateOrderAmount(orders,itemPrices);
        }
        // 更新订单明细信息
        orderItemPriceService.saveOrSetNUll(itemPrices,orders.getExceptionCode());
        orderItemService.updateOrSetNUll(orderItemUpdateList,orders.getExceptionCode());
//      orders 需要二次更新,第一次更新基础信息,第二次更新价格信息
        iOrdersService.saveOrUpdate(orders);
        ordersService.updateOrSetNull(orders);
        iOrderItemProductSkuService.updateOrSetNull(orders.getOrderNo(),warehouseSystemCode,warehouseSystemCode );
        log.info(orders.toString());
    }
}

    @Override
    public Map<String, Object> msgForLogistics(AmazonVCOrderMessageDTO amazonVCOrderMessageDTO, Orders orders,
                                               Map<String, List> itemMap) {
        HashMap<String, Object> map = new HashMap<>();
        List<OrderItem> orderItems = (List<OrderItem>) itemMap.get("orderItems");
        OrderLogisticsInfo orderLogisticsInfo = new OrderLogisticsInfo();
        ArrayList<OrderAddressInfo> addressInfos = new ArrayList<>();
        ArrayList<OrderLogisticsInfo> orderLogisticsInfos = new ArrayList<>();
        // 订单物流信息
        orderLogisticsInfo.setOrderId(orders.getId());
        orderLogisticsInfo.setOrderNo(orders.getOrderNo());
        orderLogisticsInfo.setShippingLabelExist(true);
        orderLogisticsInfo.setLogisticsZipCode(amazonVCOrderMessageDTO.getPostal_code());
        orderLogisticsInfo.setLogisticsType(LogisticsTypeEnum.getLogisticsTypeEnumByName(orders.getLogisticsType()
                                                                                               .name()));
        orderLogisticsInfo.setLogisticsCompanyName(amazonVCOrderMessageDTO.getCarrier());
        orderLogisticsInfo.setLogisticsCarrierCode(amazonVCOrderMessageDTO.getCarrier());
//        orderLogisticsInfo.setLogisticsServiceName(details.getCarrier());
        orderLogisticsInfo.setZipCode(amazonVCOrderMessageDTO.getPostal_code());
        orderLogisticsInfo.setLogisticsCountryCode(amazonVCOrderMessageDTO.getCountry_code());
        orderLogisticsInfos.add(orderLogisticsInfo);

        // 订单地址信息
        OrderAddressInfo orderAddressInfo = new OrderAddressInfo();
        orderAddressInfo.setOrderId(orders.getId());
        orderAddressInfo.setOrderNo(orders.getOrderNo());
        // 拿默认模版里面
        orderAddressInfo.setRecipient(amazonVCOrderMessageDTO.getBuy_name());

        orderAddressInfo.setPhoneNumber(amazonVCOrderMessageDTO.getPhone());
        // 这三个信息需要调用包裹接口拿到详细的包裹信息
        orderAddressInfo.setCountry(amazonVCOrderMessageDTO.getCountry());
        orderAddressInfo.setCountryCode(amazonVCOrderMessageDTO.getCountry_code());
        String zipCode = amazonVCOrderMessageDTO.getPostal_code();
        String state = amazonVCOrderMessageDTO.getState_or_region();
        orderAddressInfo.setState(state);
        orderAddressInfo.setStateCode(state);

        orderAddressInfo.setCity(amazonVCOrderMessageDTO.getCity());
        orderAddressInfo.setAddress1(amazonVCOrderMessageDTO.getAddressline1());
        orderAddressInfo.setAddress2(amazonVCOrderMessageDTO.getAddressline2());

        orderAddressInfo.setZipCode(zipCode);
//        orderAddressInfo.setEmail(address.getBuyerEmail());
        orderAddressInfo.setAddressType(OrderAddressType.ShipAddress);

        addressInfos.add(orderAddressInfo);

        List<OrderItemPrice> itemPrices = new ArrayList<>();
        if(null == orders.getExceptionCode() || !orders.getExceptionCode().equals(OrderExceptionEnum.product_mapping_exception.getValue())){
            OrderPriceCalculateDTO paramDTO = new OrderPriceCalculateDTO();
            // 订单明细价格
            for (OrderItem orderItem : orderItems) {
                paramDTO.setOrderItem(orderItem);
                paramDTO.setLogisticsType(amazonVCOrderMessageDTO.getLogisticsType());
                paramDTO.setCountry(amazonVCOrderMessageDTO.getCountry_code());
                OrderPriceCalculateDTO calculateDTO = orderSupport.calculationOrderItemPriceForTemu(paramDTO);
                OrderItemPrice itemPrice = calculateDTO.getOrderItemPrice();
                itemPrice.setOrderItemId(orderItem.getId());
                itemPrices.add(itemPrice);
            }
        }
        map.put("logisticsInfo", orderLogisticsInfos);
        map.put("addressInfo", addressInfos);
        map.put("orderItemPrice", itemPrices);
        map.put("orders", orders);
        log.info("amazonVC订单物流信息:{}", JSONUtil.toJsonStr(orderLogisticsInfo));
        return map;
    }

    @Override
    public Map<String, List> msgForItems(AmazonVCOrderMessageDTO amazonVCOrderMessageDTO, Orders orders) {
        List<AmazonVCOrderItemMessageDTO> items = amazonVCOrderMessageDTO.getItems();
        List<OrderItem> orderItems = new ArrayList();
        List<OrderItemProductSku> orderItemProductSkus = new ArrayList<>();
        HashMap<String, List> hashMap = new HashMap<>();
        for (AmazonVCOrderItemMessageDTO amazonVCOrderItemMessageDTO : items) {
            OrderItem orderItem = new OrderItem();
            orderItem.setChannelType(orders.getChannelType());
            orderItem.setChannelSku(amazonVCOrderItemMessageDTO.getSku());
            orderItem.setLogisticsType(amazonVCOrderMessageDTO.getLogisticsType());
            orderItemService.setOrderBusinessFieldForAmazonVc(orderItem, amazonVCOrderMessageDTO, orders, amazonVCOrderItemMessageDTO);
            orderItemService.setChannelTagForAmazonVc(orderItem, amazonVCOrderMessageDTO, orders, amazonVCOrderItemMessageDTO);
            iOrderItemThirdService.setOrderTagSystemForAmazonVc(orderItem, amazonVCOrderMessageDTO, orders, amazonVCOrderItemMessageDTO);
            // 设置币种
            SiteCountryCurrencyVo siteCountryCurrencyVo = iSiteCountryCurrencyService.queryByCountryCode(amazonVCOrderMessageDTO.getCountry_code());
            if(null != siteCountryCurrencyVo){
                orderItem.setCurrency(siteCountryCurrencyVo.getCurrencyCode());
                orderItem.setCurrencySymbol(siteCountryCurrencyVo.getCurrencySymbol());
                orderItem.setCountryCode(siteCountryCurrencyVo.getCountryCode());
                orderItem.setSiteId(siteCountryCurrencyVo.getId());
            }
            orderItems.add(orderItem);
            // 获取商品相关的活动信息
            DistributorProductActivity distributorProductActivity = orderActivitySupport.getDistributorActivityByStock(amazonVCOrderMessageDTO.getTenantId(), amazonVCOrderItemMessageDTO.getProductSkuCode(), amazonVCOrderMessageDTO.getCountry(),orderActivitySupport.logisticsTypeConvert(orders.getLogisticsType()));
            // 产品没有映射不生成 orderItemProductsSku 数据
            if(null == orders.getExceptionCode() || !orders.getExceptionCode().equals(OrderExceptionEnum.product_mapping_exception.getValue())){
                OrderItemProductSku orderItemProductSku = new OrderItemProductSku();
                // 通过 自订单编号进行关联,
                orderItemProductSkuThirdService.setBusinessFieldForAmazonVc(orderItemProductSku, orderItem, amazonVCOrderMessageDTO, orders, amazonVCOrderItemMessageDTO);
                // 活动订单设置为活动仓库
                if (null != distributorProductActivity) {
                    DistributorProductActivityStock activityWarehouseInfo = orderActivitySupport.getActivityWarehouseInfo(distributorProductActivity);
                    if(null != activityWarehouseInfo && null != activityWarehouseInfo.getWarehouseSystemCode()){
                        orderItemProductSku.setSpecifyWarehouse(activityWarehouseInfo.getWarehouseSystemCode());
                        orderItemProductSku.setWarehouseSystemCode(activityWarehouseInfo.getWarehouseSystemCode());
                    }

                }
                orderItemProductSkus.add(orderItemProductSku);
            }
            // 判断活动订单
            if (null != distributorProductActivity) {
                orderItem.setActivityCode(distributorProductActivity.getDistributorActivityCode());
                orderItem.setActivityType(distributorProductActivity.getActivityType());
            }
        }
        hashMap.put("orderItems", orderItems);
        hashMap.put("orderItemProductsSku", orderItemProductSkus);
        log.info("订单项信息:{}", JSONUtil.toJsonStr(orderItems));
        return hashMap;
    }

    @Override
    public Boolean isNeedPay() {
        return Boolean.FALSE;
    }

    @Override
    public String attachmentsFlow(AmazonVCOrderMessageDTO amazonVCOrderMessageDTO, Orders orders) {
        LogisticsTypeEnum logisticsType = orders.getLogisticsType();
// 目前全是代发
//        if(LogisticsTypeEnum.PickUp.equals(logisticsType)){
//            SaleOrderDetailDTO saleOrderDetails = orderReceiveFromThirdDTO.getSaleOrderDetails();
//            List<AttachInfo> attachInfoItems = saleOrderDetails.getAttachInfoItems();
//            for (AttachInfo attachInfoItem : attachInfoItems) {
//                SysOssVo sysOssVo = sysOssService.downloadPdfNotAsync(attachInfoItem.getUrl(), String.valueOf(orders.getId()), saleOrderDetails.getLogisticsTrackingNo());
//                OrderUpdateBo bo = new OrderUpdateBo(orders.getOrderNo(), null, true, sysOssVo);
//                // 实际上传完了以后已经有了文件了 不需要再走上传的逻辑了
//                distributorOrderService.uploadShippingLabel(bo);
//            }
//
//        }

        return orders.getOrderNo();
    }

    @Override
    public Boolean isNeedPay(AmazonVCOrderMessageDTO amazonVCOrderMessageDTO) {
        Boolean isPay;
        isPay = ZSMallSystemEventUtils.checkWalletAutoPaymentEvent(amazonVCOrderMessageDTO.getTenantId(),amazonVCOrderMessageDTO.getCurrency_code());
        if(isPay){
            // 异常订单不进行支付
            if(null != amazonVCOrderMessageDTO.getExceptionCode() && !amazonVCOrderMessageDTO.getExceptionCode().equals(OrderExceptionEnum.normal.getValue())){
                isPay = false;
            }
        }
        return isPay;
    }

    @Override
    public Boolean payOrder(AmazonVCOrderMessageDTO amazonVCOrderMessageDTO) throws Exception {
        // 是否开启自动支付
        if (true) {
            LambdaQueryWrapper<Orders> lqw = new LambdaQueryWrapper<Orders>().eq(Orders::getChannelOrderNo, amazonVCOrderMessageDTO.getOrdernum());

            List<String> orderNos = TenantHelper.ignore(() -> iOrdersService.list(lqw)).stream().map(Orders::getOrderNo)
                                                .collect(Collectors.toList());
            OrderPayBo bo = new OrderPayBo();
            bo.addOrderNoList(orderNos);
            bo.setPaymentPassword(null);
            if(CollUtil.isEmpty(orderNos)){
                return false;
            }
            try {
                TenantHelper.ignore(() -> {
                    try {
                        return ordersService.payOrderForDistribution(bo, amazonVCOrderMessageDTO.getTenantId(), true, true);
                    } catch (Exception e) {
                        log.error(e.getMessage());
                        throw new RuntimeException(e.getMessage());
                    }
                });
            } catch (Exception e) {
                log.error("支付失败:{}", e.getMessage());
            }
        }
        return true;
    }

    @Override
    public Boolean payOrderForAsync(AmazonVCOrderMessageDTO amazonVCOrderMessageDTO, Boolean isAsync) throws Exception {
        // 是否开启自动支付
        if (true) {
            LambdaQueryWrapper<Orders> lqw = new LambdaQueryWrapper<Orders>().eq(Orders::getChannelOrderNo, amazonVCOrderMessageDTO.getOrdernum());
            List<Orders> list = iOrdersService.list(lqw);
            if(orderSupport.isExpressSheetUpload(list)){
                List<String> orderNos = TenantHelper.ignore(() -> list).stream().map(Orders::getOrderNo)
                                                    .collect(Collectors.toList());
                OrderPayBo bo = new OrderPayBo();
                bo.addOrderNoList(orderNos);
                bo.setPaymentPassword(null);
                if(CollUtil.isEmpty(orderNos)){
                    return false;
                }
                try {
                    TenantHelper.ignore(() -> {
                        try {
                            return ordersService.payOrderForErp(bo, amazonVCOrderMessageDTO.getTenantId(), true, true);
                        } catch (Exception e) {
                            log.error(e.getMessage());
                            throw new RuntimeException(e.getMessage());
                        }
                    });
                } catch (Exception e) {
                    log.error("支付失败:{}", e.getMessage());
                }
            }
        }
        return true;
    }

    @Override
    public Orders thirdToDistribution(AmazonVCOrderMessageDTO amazonVCOrderMessageDTO, Orders orders) throws ParseException {
        Orders orders1 = new Orders();
        String orderNo = orderCodeGenerator.codeGenerate(BusinessCodeEnum.OrderNo);
        orders1.setOrderNo(orderNo);
        orders1.setOrderExtendId(orderNo);
        if(null != amazonVCOrderMessageDTO.getExceptionCode()){
            orders1.setExceptionCode(amazonVCOrderMessageDTO.getExceptionCode());
        }
        if(null != amazonVCOrderMessageDTO.getPayErrorMessage()){
            orders1.setPayErrorMessage(amazonVCOrderMessageDTO.getPayErrorMessage());
        }
        // 业务属性
        orders1 = ordersService.setOrderBusinessFieldForAmazonVc(amazonVCOrderMessageDTO, orders1);
        orders1 = ordersService.setOrderTagSystemByAmazonVc(amazonVCOrderMessageDTO, orders1);
        orders1 = ordersService.setChannelTagForAmazonVc(amazonVCOrderMessageDTO, orders1);
        // 币种
        SiteCountryCurrencyVo siteCountryCurrencyVo = iSiteCountryCurrencyService.queryByCountryCode(amazonVCOrderMessageDTO.getCountry_code());
        if(null != siteCountryCurrencyVo){
            orders1.setCurrency(siteCountryCurrencyVo.getCurrencyCode());
            orders1.setCurrencySymbol(siteCountryCurrencyVo.getCurrencySymbol());
            orders1.setCountryCode(siteCountryCurrencyVo.getCountryCode());
            orders1.setSiteId(siteCountryCurrencyVo.getId());
        }
        return orders1;
    }

    @Override
    public List<AmazonVCOrderMessageDTO> ordersDisassemble(AmazonVCOrderMessageDTO amazonVCOrderMessageDTO) {
        List<AmazonVCOrderItemMessageDTO> amazonVCOrderItemMessageDTOList = amazonVCOrderMessageDTO.getItems();
        List<AmazonVCOrderMessageDTO> amazonVCOrderMessageDTOS = new ArrayList<>();
        // 把订单项拆开
        for (AmazonVCOrderItemMessageDTO amazonVCOrderItemMessageDTO : amazonVCOrderItemMessageDTOList) {
            if(amazonVCOrderItemMessageDTO.getQuantity() == 1){
                // 需要通过itemOrderId进行搜索
                LambdaQueryWrapper<Orders> lqw = new LambdaQueryWrapper<Orders>().eq(Orders::getChannelOrderNo, amazonVCOrderMessageDTO.getOrdernum())
                                                                                 .eq(Orders::getLineOrderItemId,amazonVCOrderItemMessageDTO.getChannel_order_item_id())
                                                                                 .eq(Orders::getDelFlag, 0).last("limit 1");
                Orders order = iOrdersService.getOne(lqw);
                if (ObjectUtil.isNotEmpty(order)) {
                    continue;
                }
                List<AmazonVCOrderItemMessageDTO> amazonVCOrderItemMessageDTOList1 = new ArrayList<>();
                AmazonVCOrderMessageDTO amazonVCOrderMessageDTO1 = new AmazonVCOrderMessageDTO();
                ProductMapping productMapping = iProductMappingService.getProductMappingByChannelSkuAndSyncStateAndCountry(amazonVCOrderMessageDTO.getTenantId(), amazonVCOrderMessageDTO.getChannelId(), amazonVCOrderItemMessageDTO.getSku(), SyncStateEnum.Mapped,amazonVCOrderMessageDTO.getCountry());
                TenantSalesChannel tenantSalesChannel = iTenantSalesChannelService.getTenantSalesChannelByChannelName(amazonVCOrderMessageDTO.getAccount_id(),ChannelTypeEnum.Amazon_VC.name());
                if(ObjectUtil.isEmpty(tenantSalesChannel)){
                    throw new AppRuntimeException("没有对应的渠道信息");
                }
                if(null != productMapping && StringUtils.isNotEmpty(productMapping.getProductSkuCode())){
                    amazonVCOrderItemMessageDTO.setProductSkuCode(productMapping.getProductSkuCode());
                    // 判断渠道和产品的发货方式是否一致
                    Product product = iProductService.queryByProductSkuCode(productMapping.getProductSkuCode());
                    //校验渠道店铺发货方式和商品发货方式是否不一致
                    if (ObjectUtil.isNotNull(product) && ObjectUtil.isNotNull(tenantSalesChannel) ){
                        boolean isSupport = product.getSupportedLogistics()
                                                   .allowShipping(LogisticsTypeEnum.getLogisticsTypeEnumByName(String.valueOf(tenantSalesChannel.getLogisticsType())));
                        if (!isSupport){
                            amazonVCOrderMessageDTO.setExceptionCode(OrderExceptionEnum.Delivery_exception.getValue());
                        }
                    }
                    // 判断库存是否足够
                    ProductSkuAdjustStockVo adjustStockVo = iProductSkuService.queryAdjustStockVo(productMapping.getProductSkuCode());
                    Integer stockTotal = adjustStockVo.getStockTotal();
                    if (NumberUtil.compare(amazonVCOrderItemMessageDTO.getQuantity(), stockTotal) > 0) {
                        // todo 测算放开
                        amazonVCOrderMessageDTO.setExceptionCode(OrderExceptionEnum.out_of_stock_exception.getValue());
                    }
                }
                amazonVCOrderItemMessageDTOList1.add(amazonVCOrderItemMessageDTO);
                BeanUtils.copyProperties(amazonVCOrderMessageDTO, amazonVCOrderMessageDTO1);
                // 判断产品是否映射
                if (ObjectUtil.isEmpty(productMapping)) {
                    // 判断商品价格是否存在
                    List<ProductMapping> productMappingList = iProductMappingService.getProductMappingByChannelSkuAndSyncState(amazonVCOrderMessageDTO.getTenantId(), amazonVCOrderMessageDTO.getChannelId(), amazonVCOrderItemMessageDTO.getSku(), SyncStateEnum.Mapped);
                    String productSkuCode = null;
                    if(CollUtil.isNotEmpty(productMappingList)){
                        productSkuCode = productMappingList.get(0).getProductSkuCode();
                    }
                    ProductSkuPrice productSkuPrice;
                    if(StringUtils.isNotEmpty(productSkuCode)){
                        productSkuPrice = iProductSkuPriceService.queryByProductSkuCodeAndCountryCode(productSkuCode, amazonVCOrderMessageDTO.getCountry());
                        if(null == productSkuPrice){
                            LocaleMessage localeMessage = LocaleMessage.byStatusCode(OrderStatusCodeEnum.SKU_REGION_PRICE_NOT_MAINTAINED.args(productSkuCode));
                            amazonVCOrderMessageDTO1.setPayErrorMessage(localeMessage.toJSON());
                            amazonVCOrderMessageDTO.setPayErrorMessage(localeMessage.toJSON());
                        }
                    }
                    amazonVCOrderMessageDTO1.setExceptionCode(OrderExceptionEnum.product_mapping_exception.getValue());
                    amazonVCOrderMessageDTO.setExceptionCode(OrderExceptionEnum.product_mapping_exception.getValue());
                }
                amazonVCOrderMessageDTO1.pushAmazonVCOrderItemDTOList(amazonVCOrderItemMessageDTOList1);
                amazonVCOrderMessageDTO1.setChannel_order_item_id(amazonVCOrderItemMessageDTO.getChannel_order_item_id());
                amazonVCOrderMessageDTOS.add(amazonVCOrderMessageDTO1);
            }

            if(amazonVCOrderItemMessageDTO.getQuantity() > 1){
                // 需要通过itemOrderId进行搜索
                LambdaQueryWrapper<Orders> lqw = new LambdaQueryWrapper<Orders>().eq(Orders::getChannelOrderNo, amazonVCOrderMessageDTO.getOrdernum())
                                                                                 .eq(Orders::getLineOrderItemId,amazonVCOrderItemMessageDTO.getChannel_order_item_id())
                                                                                 .eq(Orders::getDelFlag, 0).last("limit 1");
                Orders order = iOrdersService.getOne(lqw);
                if (ObjectUtil.isNotEmpty(order)) {
                    continue;
                }
                List<AmazonVCOrderItemMessageDTO> amazonVCOrderItemMessageDTOList1 = new ArrayList<>();
                AmazonVCOrderMessageDTO amazonVCOrderMessageDTO1 = new AmazonVCOrderMessageDTO();
                ProductMapping productMapping = iProductMappingService.getProductMappingByChannelSkuAndSyncStateAndCountry(amazonVCOrderMessageDTO.getTenantId(), amazonVCOrderMessageDTO.getChannelId(), amazonVCOrderItemMessageDTO.getSku(), SyncStateEnum.Mapped,amazonVCOrderMessageDTO.getCountry());
                TenantSalesChannel tenantSalesChannel = iTenantSalesChannelService.getTenantSalesChannelByChannelName(amazonVCOrderMessageDTO.getAccount_id(),ChannelTypeEnum.Amazon_VC.name());
                if(ObjectUtil.isEmpty(tenantSalesChannel)){
                    throw new AppRuntimeException("没有对应的渠道信息");
                }

                if(null != productMapping && StringUtils.isNotEmpty(productMapping.getProductSkuCode())){
                    amazonVCOrderItemMessageDTO.setProductSkuCode(productMapping.getProductSkuCode());
                    // 判断渠道和产品的发货方式是否一致
                    Product product = iProductService.queryByProductSkuCode(productMapping.getProductSkuCode());
                    //校验渠道店铺发货方式和商品发货方式是否不一致
                    if (ObjectUtil.isNotNull(product) && ObjectUtil.isNotNull(tenantSalesChannel) ){
                        boolean isSupport = product.getSupportedLogistics()
                                                   .allowShipping(LogisticsTypeEnum.getLogisticsTypeEnumByName(String.valueOf(tenantSalesChannel.getLogisticsType())));
                        if (!isSupport){
                            amazonVCOrderMessageDTO.setExceptionCode(OrderExceptionEnum.Delivery_exception.getValue());
                        }
                    }
                    // 判断库存是否足够
                    ProductSkuAdjustStockVo adjustStockVo = iProductSkuService.queryAdjustStockVo(productMapping.getProductSkuCode());
                    Integer stockTotal = adjustStockVo.getStockTotal();
                    if (NumberUtil.compare(amazonVCOrderItemMessageDTO.getQuantity(), stockTotal) > 0) {
                        // todo 测算放开
                        amazonVCOrderMessageDTO.setExceptionCode(OrderExceptionEnum.out_of_stock_exception.getValue());
                    }
                }
                amazonVCOrderItemMessageDTOList1.add(amazonVCOrderItemMessageDTO);
                BeanUtils.copyProperties(amazonVCOrderMessageDTO, amazonVCOrderMessageDTO1);
                // 判断产品是否映射
                if (ObjectUtil.isEmpty(productMapping)) {
                    // 判断商品价格是否存在
                    List<ProductMapping> productMappingList = iProductMappingService.getProductMappingByChannelSkuAndSyncState(amazonVCOrderMessageDTO.getTenantId(), amazonVCOrderMessageDTO.getChannelId(), amazonVCOrderItemMessageDTO.getSku(), SyncStateEnum.Mapped);
                    String productSkuCode = null;
                    if(CollUtil.isNotEmpty(productMappingList)){
                        productSkuCode = productMappingList.get(0).getProductSkuCode();
                    }
                    ProductSkuPrice productSkuPrice;
                    if(StringUtils.isNotEmpty(productSkuCode)){
                        productSkuPrice = iProductSkuPriceService.queryByProductSkuCodeAndCountryCode(productSkuCode, amazonVCOrderMessageDTO.getCountry());
                        if(null == productSkuPrice){
                            LocaleMessage localeMessage = LocaleMessage.byStatusCode(OrderStatusCodeEnum.SKU_REGION_PRICE_NOT_MAINTAINED.args(productSkuCode));
                            amazonVCOrderMessageDTO1.setPayErrorMessage(localeMessage.toJSON());
                            amazonVCOrderMessageDTO.setPayErrorMessage(localeMessage.toJSON());
                        }
                    }
                    amazonVCOrderMessageDTO1.setExceptionCode(OrderExceptionEnum.product_mapping_exception.getValue());
                    amazonVCOrderMessageDTO.setExceptionCode(OrderExceptionEnum.product_mapping_exception.getValue());
                }
                amazonVCOrderMessageDTO1.pushAmazonVCOrderItemDTOList(amazonVCOrderItemMessageDTOList1);
                amazonVCOrderMessageDTO1.setChannel_order_item_id(amazonVCOrderItemMessageDTO.getChannel_order_item_id());
                // 拆分数量
                Integer forI = amazonVCOrderItemMessageDTO.getQuantity();
                amazonVCOrderItemMessageDTO.setQuantity(1);
                amazonVCOrderItemMessageDTO.setSubtotal(amazonVCOrderItemMessageDTO.getSubtotal().divide(new BigDecimal(forI)));
                for (int i = 0; i < forI; i++){
                    amazonVCOrderMessageDTOS.add(amazonVCOrderMessageDTO1);
                }
            }
        }
        return amazonVCOrderMessageDTOS;
    }

    @Override
    public R tripartiteUpdate(Object o) {
        return null;
    }

    @Override
    public Boolean tripartiteDeliverGoods(Object o) {
        return null;
    }

    @Override
    public Boolean tripartiteReceiptGoods(Object o) {
        return null;
    }

    @Override
    public R<Void> test() {
        return null;
    }

    @Override
    public void orderOperationHandler(AmazonVCOrderMessageDTO i, ConcurrentHashMap<String, List<Object>> businessMap,
                                      ConcurrentHashMap<String, ConcurrentHashMap<String, String>> businessNos,
                                      ChannelTypeEnum channelTypeEnum, BusinessTypeMappingEnum mappingEnum) {

    }

    @Override
    public void priceOperation(ConcurrentHashMap<String, List<Object>> businessMap,
                               ConcurrentHashMap<String, ConcurrentHashMap<String, String>> businessNos,
                               ChannelTypeEnum channelTypeEnum, BusinessTypeMappingEnum mappingEnum) {

    }

    @Override
    public void orderOperationHandlerSave(ChannelTypeEnum channelTypeEnum,
                                          ConcurrentHashMap<String, List<Object>> businessMap) {

    }

    @Override
    public List<AmazonVCOrderMessageDTO> ordersDisassembleForList(
        List<AmazonVCOrderMessageDTO> amazonVCOrderMessageDTOS, BusinessTypeMappingEnum mappingEnum) {
        return null;
    }

    @Override
    public List<AmazonVCOrderMessageDTO> parseThirdDataForList(String s) {
        return null;
    }
}
