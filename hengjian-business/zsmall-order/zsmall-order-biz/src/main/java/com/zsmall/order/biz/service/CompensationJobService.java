package com.zsmall.order.biz.service;

import com.hengjian.common.core.domain.R;
import com.zsmall.common.domain.dto.CompensationOrderDTO;
import com.zsmall.common.domain.dto.OpenApiCancelOrderDTO;
import com.zsmall.order.entity.domain.Orders;

import java.util.List;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2024/2/4 14:01
 */
public interface CompensationJobService {
    void pullOrder(String name, String jsonString, String tenantId);

    void pullProduct(String name, String jsonString);
    void pullProduct(String name, String jsonString , List<String> tenantId);

    void pullOrderToErp(List<String> orderNos);
    void pullOrderToErpByOrders(List<Orders> orderNos);

    void pullOrderForSpecify(String name, String jsonString, List<String> tenantId);

    void pullOrderForSpecifyOrderNo(String name, CompensationOrderDTO dto);

    void cancellationCompensationHandler(String type);

    void pullOrderV2(String name, String jsonString);

    void testOrderV2(String name, String jsonString);

    void testOrderForSpecifiedData(String name, String msg, String thirdChannelFlag, String channelFlag);

    /**
     * 功能描述：更新批量补偿预约
     *
     * @param orderNos 订单编号
     * <AUTHOR>
     * @date 2024/08/23
     */
    R updateBatchCompensateForAppoint(List<String> orderNos);

    void pullOrderV2(String name, String jsonString, List<String> allowTenantId);

    void abnormalOrderCompensationHandler(Integer otherDay);

    void getOrderFromErp(String order);

    void updateBatchNeedCompensationOrderNos(List<String> needCompensationOrderNos);

    void compensationMethod (Integer otherDay);

    void cancelOrder(OpenApiCancelOrderDTO dto);

    void specifyOrderToGoErpCancellation(List<OpenApiCancelOrderDTO> dtos);
}
