package com.zsmall.order.biz.service;

import com.hengjian.common.core.domain.R;
import com.zsmall.common.domain.dto.OpenApiOrderCancelDTO;
import com.zsmall.common.domain.dto.OpenApiOrderReviewDTO;
import com.zsmall.common.domain.dto.OrderReceiveAttachmentDTO;
import com.zsmall.common.domain.dto.OrderReceiveFromThirdDTO;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2024/5/11 11:52
 */
public interface ThirdOrdersApiService {

//    R tripartiteBatchEnterForOpenApi(List<OrderReceiveFromThirdDTO> dto);
    R tripartiteBatchEnterForOpenApi(OrderReceiveFromThirdDTO dto,String tenantId);

    R receiveOrderAttachment(List<OrderReceiveAttachmentDTO> dtos, @NotNull(message = "租户ID不能为空") String tenantId);
    R cancelOrderFlow(List<String> dtos, @NotNull(message = "租户ID不能为空") String tenantId);
    R getTrackingRecord(List<String> channelNos, String tenantId);

    R receiveAllOrderAttachment(List<OrderReceiveAttachmentDTO> dtos, String tenantId);

    R getCancelOrderStatus(OpenApiOrderCancelDTO thirdDTO, String tenantId);

    R orderReviewFlowForSupplier(OpenApiOrderReviewDTO openApiOrderReviewDTO, String tenantId) throws Exception;
}
