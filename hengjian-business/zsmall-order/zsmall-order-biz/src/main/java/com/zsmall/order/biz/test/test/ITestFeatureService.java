package com.zsmall.order.biz.test.test;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hengjian.common.core.utils.SpringUtils;
import com.hengjian.common.tenant.helper.TenantHelper;
import com.zsmall.common.domain.resp.tiktok.fulfillment.SplittableGroupBody;
import com.zsmall.common.domain.tiktok.domain.dto.extend.resp.TikTokPackageResp;
import com.zsmall.common.enums.common.ChannelTypeEnum;
import com.zsmall.common.enums.order.LogisticsTypeEnum;
import com.zsmall.order.biz.service.IProductAboutManger;
import com.zsmall.order.biz.utils.TikTokUtil;
import com.zsmall.order.entity.anno.annotaion.PayLimit;
import com.zsmall.order.entity.domain.OrderItem;
import com.zsmall.order.entity.domain.Orders;
import com.zsmall.order.entity.domain.bo.order.OrderPayBo;
import com.zsmall.order.entity.iservice.IOrderItemService;
import com.zsmall.order.entity.iservice.IOrdersService;
import com.zsmall.product.biz.service.RuleLevelProductPriceService;
import com.zsmall.product.entity.domain.Product;
import com.zsmall.product.entity.domain.ProductSku;
import com.zsmall.product.entity.domain.ProductSkuPrice;
import com.zsmall.product.entity.domain.RuleLevelProductPrice;
import com.zsmall.product.entity.iservice.IProductService;
import com.zsmall.product.entity.iservice.IProductSkuPriceService;
import com.zsmall.product.entity.iservice.IProductSkuService;
import com.zsmall.system.entity.iservice.ITenantSalesChannelService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.zsmall.common.enums.tiktok.TikTokApiEnums.SPLIT_ORDERS;

/**
 * 功能描述：
 *
 * <AUTHOR>
 * @date 2024/03/08
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class ITestFeatureService {
    @Resource
    private ITenantSalesChannelService tenantSalesChannelService;
    @Resource
    private TikTokUtil tikTokUtil;

    @Resource
    private IOrdersService ordersService;
    @Resource
    private IOrderItemService iOrderItemService;
    @Resource
    private IProductSkuPriceService iProductSkuPriceService;
    @Resource
    private IProductSkuService iProductSkuService;
    @Resource
    private IProductAboutManger productAboutManger;
    @Resource
    private IProductService iProductService;
    @Resource
    private IOrdersService iOrdersService;

    @Resource
    private RuleLevelProductPriceService ruleLevelProductPriceService;
    @PayLimit(timeUnit = TimeUnit.SECONDS,interval = 10)
    public void testLimitIsAble(OrderPayBo vo) {
        System.out.println(1);
    }

    public void testSpilt(SplittableGroupBody vo) {
        String shopId = "7495557786822805740";

        HashMap hashMap = new HashMap<>();
        hashMap.put("shop_id", shopId);

        String body = JSONObject.toJSONString(vo);
        //拆单
        TikTokPackageResp tikTokPackageResp = tikTokUtil.postTikTokShopReturnV2(body, SPLIT_ORDERS.getUrl(), SPLIT_ORDERS.formatPath("576614013998043800", "{order_id}"), TikTokPackageResp.class, hashMap);
    }

    public void pullOrder(String name, String jsonString) {
    }

//    public void testDataCleaning(String tenantId) {
//        // 历史 的tiktok订单进行清洗
//        LambdaQueryWrapper<Orders> eq = new LambdaQueryWrapper<Orders>().eq(Orders::getDelFlag, 0)
//                                                                        .eq(Orders::getTenantId, tenantId);
////        LambdaQueryWrapper<Orders> eq = new LambdaQueryWrapper<Orders>().eq(Orders::getOrderNo, "Z135426240402181");
//        List<Orders> ordersList = TenantHelper.ignore(() -> ordersService.list(eq));
//
//        List<String> orderNos = ordersList.stream().map(Orders::getOrderNo).collect(Collectors.toList());
//        LambdaQueryWrapper<OrderItem> eq2 = new LambdaQueryWrapper<OrderItem>().eq(OrderItem::getDelFlag, 0)
//                                                                              .in(OrderItem::getOrderNo, orderNos);
//        List<OrderItem> orderItems = TenantHelper.ignore(() -> iOrderItemService.list(eq2));
//        List<String> itemNos = orderItems.stream().map(OrderItem::getProductSkuCode).collect(Collectors.toList());
//        Map<String, String> itemNoMap = orderItems.stream()
//                                                  .filter(item -> item.getOrderNo() != null && item.getProductSkuCode() != null)
//                                                  .collect(Collectors.toMap(OrderItem::getOrderNo, OrderItem::getProductSkuCode));
//
//        List<ProductSkuPrice> productSkuPrices = TenantHelper.ignore(() -> iProductSkuPriceService.list(new LambdaQueryWrapper<ProductSkuPrice>().in(ProductSkuPrice::getProductSkuCode, itemNos)));
//        // productSkuPrices转换成ProductSkuCode value是对应元素
//        Map<String, ProductSkuPrice> productSkuPriceMap = productSkuPrices.stream().collect(Collectors.toMap(ProductSkuPrice::getProductSkuCode, Function.identity()));
//
//        List<ProductSku> productSkus = TenantHelper.ignore(() -> iProductSkuService.list(new LambdaQueryWrapper<ProductSku>().in(ProductSku::getProductSkuCode, itemNos)));
//
//        Map<String, ProductSku> productSkuMap = productSkus.stream().collect(Collectors.toMap(ProductSku::getProductSkuCode, Function.identity()));
//        List<Long> productIds = productSkus.stream().map(ProductSku::getProductId).collect(Collectors.toList());
//        LambdaQueryWrapper<Product> in = new LambdaQueryWrapper<Product>().in(Product::getId, productIds);
//
//        List<Product> products = TenantHelper.ignore(() -> iProductService.list(in));
//
//        Map<Long, Product> productMap = products.stream().collect(Collectors.toMap(Product::getId, Function.identity()));
//        // 通过item no 能拿到 productSkuPrice
//
//        for (Orders orders : ordersList) {
//            ProductSkuPrice price ;
//            // 数据清洗 统一更改价格
//            // 考虑会员价
//            BigDecimal originalProductAmount = BigDecimal.ZERO;
//
//            BigDecimal platformProductAmount = BigDecimal.ZERO;
//
//            BigDecimal originalDropShippingAmount = BigDecimal.ZERO;
//            BigDecimal originalPickAmount = BigDecimal.ZERO;
//
//            BigDecimal platformDropShippingAmount = BigDecimal.ZERO;
//            BigDecimal platformPickAmount = BigDecimal.ZERO;
//            BigDecimal originalRefundExecutableAmount =BigDecimal.ZERO;
//            BigDecimal originalActualTotalAmount = BigDecimal.ZERO;
//            String itemNo = itemNoMap.get(orders.getOrderNo());
//            if(ObjectUtil.isNotEmpty(itemNo)) {
//                if(ObjectUtil.isEmpty(productSkuPriceMap.get(itemNo))){
//                    continue;
//                }
//                price = productSkuPriceMap.get(itemNo);
//                if(ObjectUtil.isEmpty(productSkuMap.get(itemNo))){
//                    continue;
//
//                }
//                ProductSku sku = productSkuMap.get(itemNo);
//                Product product;
//                if(ObjectUtil.isNotEmpty(productMap.get(sku.getProductId()))){
//                    product = productMap.get(sku.getProductId());
//                    originalProductAmount = BigDecimal.valueOf(orders.getTotalQuantity())
//                                                      .multiply(price.getOriginalUnitPrice());
//                    // 郭琪:定价公式 单价+操作费+配送费
//                    // 有会员定价优先使用会员定价
//                    RuleLevelProductPrice memberPrice = ruleLevelProductPriceService.getMemberPrice(product.getTenantId(), orders.getTenantId(), price.getProductSkuId());
//                    if (ObjectUtil.isNotEmpty(memberPrice)) {
//                        originalDropShippingAmount = BigDecimal.valueOf(orders.getTotalQuantity())
//                                                               .multiply(memberPrice.getOriginalUnitPrice()
//                                                                                    .add(memberPrice.getOriginalOperationFee())
//                                                                                    .add(memberPrice.getOriginalFinalDeliveryFee()));
//                        originalPickAmount = BigDecimal.valueOf(orders.getTotalQuantity())
//                                                       .multiply(memberPrice.getOriginalUnitPrice()
//                                                                            .add(memberPrice.getOriginalOperationFee()));
//                        platformProductAmount = BigDecimal.valueOf(orders.getTotalQuantity())
//                                                          .multiply(memberPrice.getPlatformUnitPrice());
//                        platformDropShippingAmount = BigDecimal.valueOf(orders.getTotalQuantity())
//                                                               .multiply(memberPrice.getPlatformUnitPrice()
//                                                                                    .add(memberPrice.getPlatformOperationFee())
//                                                                                    .add(memberPrice.getPlatformFinalDeliveryFee()));
//
//                        platformPickAmount = BigDecimal.valueOf(orders.getTotalQuantity())
//                                                       .multiply(memberPrice.getPlatformUnitPrice()
//                                                                            .add(memberPrice.getPlatformOperationFee()));
//                    } else {
//                        originalDropShippingAmount = BigDecimal.valueOf(orders.getTotalQuantity())
//                                                               .multiply(price.getOriginalUnitPrice()
//                                                                              .add(price.getOriginalOperationFee())
//                                                                              .add(price.getOriginalFinalDeliveryFee()));
//                        originalPickAmount = BigDecimal.valueOf(orders.getTotalQuantity()).multiply(price.getOriginalUnitPrice()
//                                                                                                         .add(price.getOriginalOperationFee()));
//                        platformProductAmount = BigDecimal.valueOf(orders.getTotalQuantity())
//                                                          .multiply(price.getPlatformUnitPrice());
//                        platformDropShippingAmount = BigDecimal.valueOf(orders.getTotalQuantity())
//                                                               .multiply(price.getPlatformUnitPrice()
//                                                                              .add(price.getPlatformOperationFee())
//                                                                              .add(price.getPlatformFinalDeliveryFee()));
//
//                        platformPickAmount = BigDecimal.valueOf(orders.getTotalQuantity()).multiply(price.getPlatformUnitPrice()
//                                                                                                         .add(price.getPlatformOperationFee()));
//                    }
//                    BigDecimal platformActuralTotalAmount = getPlatformActuralTotalAmount( orders,orders.getPlatformActualTotalAmount());
//                    orders.setPlatformTotalPickUpPrice(platformPickAmount);
//                    orders.setPlatformTotalProductAmount(platformProductAmount);
//                    orders.setPlatformTotalDropShippingPrice(platformDropShippingAmount);
////                    orders.setPlatformActualTotalAmount(platformActuralTotalAmount);
//                    orders.setPlatformRefundExecutableAmount(platformActuralTotalAmount);
//                    orders.setPlatformPrepaidTotalAmount(BigDecimal.ZERO);
//                    // 平台应付总金额
//                    orders.setPlatformPayableTotalAmount(platformActuralTotalAmount);
//                    // 平台实付总金额
////                    orders.setPlatformActualTotalAmount(platformActuralTotalAmount);
//                    // 商品数量*尾程派送费
//                    orders.setPlatformTotalFinalDeliveryFee(BigDecimal.valueOf(orders.getTotalQuantity()).multiply(price.getPlatformFinalDeliveryFee()));
//                    orders.setPlatformTotalOperationFee(BigDecimal.valueOf(orders.getTotalQuantity()).multiply(price.getPlatformOperationFee()));
//                    // 分销商金额 供应商金额
//                    orders.setOriginalTotalFinalDeliveryFee(BigDecimal.valueOf(orders.getTotalQuantity()).multiply(price.getOriginalFinalDeliveryFee()));
//                    orders.setOriginalTotalOperationFee(BigDecimal.valueOf(orders.getTotalQuantity()).multiply(price.getOriginalOperationFee()));
//
//                    orders.setOriginalTotalProductAmount(originalProductAmount);
//
//                    orders.setOriginalTotalPickUpPrice(originalPickAmount);
//                    // 订单类型 drop就拿drop pick就拿pick
//                    if(LogisticsTypeEnum.DropShipping.equals(orders.getLogisticsType())){
//                        originalActualTotalAmount = originalDropShippingAmount;
//                        originalRefundExecutableAmount = originalActualTotalAmount;
//                    }
//                    if(LogisticsTypeEnum.PickUp.equals(orders.getLogisticsType())){
//                        originalActualTotalAmount = originalPickAmount;
//                        originalRefundExecutableAmount = originalPickAmount;
//                    }
//                    orders.setOriginalPayableTotalAmount(originalActualTotalAmount);
//                    orders.setOriginalActualTotalAmount(originalActualTotalAmount);
//                    orders.setOriginalRefundExecutableAmount(originalRefundExecutableAmount);
//                    orders.setOriginalTotalDropShippingPrice(originalDropShippingAmount);
//                    // 定金暂定0
//                    orders.setOriginalPrepaidTotalAmount(BigDecimal.ZERO);
//                }
//
//
//
//                // 拿的是订单价格,如果两者不一样 需要问erp是否录入了正确的产品价格
////        BigDecimal totalAmount = dto.getUnitPrice().multiply(BigDecimal.valueOf(dto.getQuantity()));
//
//            }
//        }
//        TenantHelper.ignore(()->iOrdersService.updateBatchById(ordersList));
//    }

    /**
     * 功能描述：获取平台实际总金额
     *
     * @param orders    订单
     * @param amount 单价 拿的商品的单价
     * @return {@link BigDecimal }
     * <AUTHOR>
     * @date 2024/07/17
     */
    private BigDecimal getPlatformActuralTotalAmount( Orders orders,BigDecimal amount) {
        BigDecimal platformActuralTotalAmount= BigDecimal.ZERO;

        if(ChannelTypeEnum.TikTok.equals(orders.getChannelType())||ChannelTypeEnum.Temu.equals(orders.getChannelType())){
            platformActuralTotalAmount = amount;
        }
        return platformActuralTotalAmount;
    }

    public void testIgonre(int i) {
        ThreadPoolExecutor executor = SpringUtils.getBean("ioThreadPoolExecutor", ThreadPoolExecutor.class);
        List<Integer> list = new ArrayList<>();
        for (int j = 0; j < i; j++) {
            list.add(j);
        }
        CountDownLatch downLatch = new CountDownLatch(list.size());
        for (Integer integer : list) {
            executor.submit(()->{
                try{
                    ordersService.getByOrderNo("Z135426240402181"+integer);
                }catch (Exception e){
                    log.info("error");
                }finally {
                    downLatch.countDown();
                }

            });
        }
        try {
            downLatch.await();
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }

    }
}
