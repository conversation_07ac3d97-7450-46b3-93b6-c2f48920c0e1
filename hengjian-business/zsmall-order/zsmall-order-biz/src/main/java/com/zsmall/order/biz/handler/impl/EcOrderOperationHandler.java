package com.zsmall.order.biz.handler.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hengjian.common.core.domain.R;
import com.hengjian.common.core.utils.StringUtils;
import com.hengjian.common.tenant.helper.TenantHelper;
import com.hengjian.stream.mq.constant.RabbitMqConstant;
import com.thoughtworks.xstream.XStream;
import com.zsmall.activity.entity.domain.dto.productActivity.DistributorProductActivity;
import com.zsmall.activity.entity.domain.dto.productActivity.DistributorProductActivityStock;
import com.zsmall.common.domain.LocaleMessage;
import com.zsmall.common.domain.dto.EcOrderItemMessageDTO;
import com.zsmall.common.domain.dto.EcOrderMessageDTO;
import com.zsmall.common.domain.dto.EcOrderMessageDTO;
import com.zsmall.common.enums.BusinessCodeEnum;
import com.zsmall.common.enums.common.BusinessTypeMappingEnum;
import com.zsmall.common.enums.common.ChannelTypeEnum;
import com.zsmall.common.enums.order.*;
import com.zsmall.common.enums.product.SupportedLogisticsEnum;
import com.zsmall.common.enums.productMapping.SyncStateEnum;
import com.zsmall.common.enums.statuscode.OrderStatusCodeEnum;
import com.zsmall.common.enums.warehouse.WarehouseTypeEnum;
import com.zsmall.common.exception.AppRuntimeException;
import com.zsmall.common.handler.AbstractOrderOperationHandler;
import com.zsmall.common.util.RegexUtil;
import com.zsmall.common.util.UnitConverter;
import com.zsmall.extend.utils.ZSMallSystemEventUtils;
import com.zsmall.lottery.support.PriceSupportV2;
import com.zsmall.order.biz.service.OrderItemPriceService;
import com.zsmall.order.biz.service.OrderItemService;
import com.zsmall.order.biz.service.OrdersService;
import com.zsmall.order.biz.service.impl.OrderItemProductSkuThirdServiceImpl;
import com.zsmall.order.biz.service.impl.OrderItemThirdServiceImpl;
import com.zsmall.order.biz.support.OrderActivitySupport;
import com.zsmall.order.biz.support.OrderSupport;
import com.zsmall.order.entity.domain.*;
import com.zsmall.order.entity.domain.bo.order.OrderPayBo;
import com.zsmall.order.entity.domain.dto.AmazonVCOrderLogisticsAttachmentDTO;
import com.zsmall.order.entity.domain.dto.AmazonVCOrderLogisticsAttachmentItemDTO;
import com.zsmall.order.entity.domain.dto.OrderPriceCalculateDTO;
import com.zsmall.order.entity.iservice.*;
import com.zsmall.order.entity.mapper.ThirdOrderInfoItemMapper;
import com.zsmall.order.entity.mapper.ThirdOrderInfoMapper;
import com.zsmall.product.entity.domain.*;
import com.zsmall.product.entity.domain.vo.productSku.ProductSkuAdjustStockVo;
import com.zsmall.product.entity.iservice.*;
import com.zsmall.system.entity.domain.ChannelWarehouseInfo;
import com.zsmall.system.entity.domain.TenantSalesChannel;
import com.zsmall.system.entity.domain.vo.SiteCountryCurrencyVo;
import com.zsmall.system.entity.iservice.IChannelWarehouseInfoServiceImpl;
import com.zsmall.system.entity.iservice.ISiteCountryCurrencyService;
import com.zsmall.system.entity.iservice.ITenantSalesChannelService;
import com.zsmall.warehouse.entity.domain.Warehouse;
import com.zsmall.warehouse.entity.iservice.IWarehouseService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.core.MessageBuilder;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.ParseException;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024年11月6日  15:47
 * @description: 易仓订单处理类
 */
@Slf4j
@Lazy
@Component("ecOrderOperationHandler")
public class EcOrderOperationHandler extends AbstractOrderOperationHandler<String, EcOrderMessageDTO, Map<String,Object>, Orders,Object> {

    @Resource
    private IProductMappingService iProductMappingService;
    @Value("${distribution.specify.warehouse.id.bizArk}")
    public String warehouseSystemCode;
    @Resource
    private OrderItemService orderItemService;
    @Resource
    private OrderSupport orderSupport;
    @Resource
    private IProductSkuService iProductSkuService;
    @Resource
    private IProductService iProductService;
    @Resource
    private IOrdersService iOrdersService;
    @Resource
    private OrdersService ordersService;
    @Resource
    private ITenantSalesChannelService iTenantSalesChannelService;
    @Resource
    private IProductChannelControlService iProductChannelControlService;
    @Autowired
    ApplicationContext applicationContext;
    @Resource
    private OrderCodeGenerator orderCodeGenerator;
    @Resource
    private IOrderItemService iOrderItemService;
    @Resource
    private OrderItemThirdServiceImpl iOrderItemThirdService;
    @Resource
    private IOrderItemProductSkuService iOrderItemProductSkuService;
    @Resource
    private IOrderLogisticsInfoService iOrderLogisticsInfoService;
    @Resource
    private IOrderAddressInfoService iOrderAddressInfoService;
    @Resource
    private IOrderItemPriceService iOrderItemPriceService;
    @Resource
    private OrderItemProductSkuThirdServiceImpl orderItemProductSkuThirdService;
    @Resource
    ThirdOrderInfoMapper thirdOrderInfoMapper;
    @Resource
    ThirdOrderInfoItemMapper thirdOrderInfoItemMapper;
    @Resource
    private PriceSupportV2 priceSupportV2;
    @Resource
    private OrderItemPriceService orderItemPriceService;
    @Resource
    private RabbitTemplate rabbitTemplate;
    @Resource
    private IProductSkuDetailService iProductSkuDetailService;
    @Resource
    private IChannelWarehouseInfoServiceImpl iChannelWarehouseService;
    @Resource
    private IWarehouseService iWarehouseService;
    @Resource
    private ISiteCountryCurrencyService iSiteCountryCurrencyService;
    @Resource
    private IProductSkuPriceService iProductSkuPriceService;
    @Resource
    private OrderActivitySupport orderActivitySupport;


    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void initialMessageSave(EcOrderMessageDTO ecOrderMessageDTO) {
        List<ThirdOrderInfo> thirdOrderInfoList = new ArrayList<>();
        List<ThirdOrderInfoItem> thirdOrderInfoItemList = new ArrayList<>();
        if(ObjectUtil.isNotEmpty(ecOrderMessageDTO)){
            ThirdOrderInfo thirdOrderInfo = new ThirdOrderInfo();
            thirdOrderInfo.setAccountId(ecOrderMessageDTO.getAccount_id()).setOrderNum(ecOrderMessageDTO.getOrdernum()).setAmount(ecOrderMessageDTO.getAmount()).setCurrencyCode(ecOrderMessageDTO.getCurrency_code())
                          .setShippingStatus(ecOrderMessageDTO.getShipping_status()).setBuyEmail(ecOrderMessageDTO.getBuy_email())
                          .setFulfillmentChannel(ecOrderMessageDTO.getFulfillment_channel())
                          .setShippingName(ecOrderMessageDTO.getShipping_name()).setAddressline1(ecOrderMessageDTO.getAddressline1()).setAddressline2(ecOrderMessageDTO.getAddressline2())
                          .setCity(ecOrderMessageDTO.getCity()).setStateOrRegion(ecOrderMessageDTO.getState_or_region()).setCountry(ecOrderMessageDTO.getCountry()).setCountryCode(ecOrderMessageDTO.getCountry_code())
                          .setPostalCode(ecOrderMessageDTO.getPostal_code()).setPhone(ecOrderMessageDTO.getPhone()).setCreated(ecOrderMessageDTO.getCreated()).setPaymentDate(ecOrderMessageDTO.getPayment_date())
                          .setCreateTime(LocalDateTime.now());
            thirdOrderInfo.setTenantId(ecOrderMessageDTO.getTenantId());
            thirdOrderInfoList.add(thirdOrderInfo);
            if(CollUtil.isNotEmpty(ecOrderMessageDTO.getItems())){
                List<EcOrderItemMessageDTO> items = ecOrderMessageDTO.getItems();
                for(EcOrderItemMessageDTO ecOrderItemMessageDTO : items){
                    ThirdOrderInfoItem thirdOrderInfoItem = new ThirdOrderInfoItem();
                    thirdOrderInfoItem.setAccountId(ecOrderMessageDTO.getAccount_id()).setOrderNum(ecOrderMessageDTO.getOrdernum()).setCreateTime(LocalDateTime.now());
                    thirdOrderInfoItem.setSku(ecOrderItemMessageDTO.getSku()).setTitle(ecOrderItemMessageDTO.getTitle()).setQuantity(ecOrderItemMessageDTO.getQuantity())
                                      .setSubtotal(ecOrderItemMessageDTO.getSubtotal()).setCurrencyCode(ecOrderItemMessageDTO.getCurrency_code()).setChannelOrderItemId(ecOrderItemMessageDTO.getChannel_order_item_id());
                    thirdOrderInfoItem.setTenantId(ecOrderMessageDTO.getTenantId());
                    thirdOrderInfoItemList.add(thirdOrderInfoItem);
                }
            }
            if(CollUtil.isNotEmpty(thirdOrderInfoList)){
                thirdOrderInfoMapper.insertBatch(thirdOrderInfoList);
            }
            if (CollUtil.isNotEmpty(thirdOrderInfoItemList)){
                thirdOrderInfoItemMapper.insertBatch(thirdOrderInfoItemList);
            }
        }
    }

    @Override
    public EcOrderMessageDTO parseThirdData(String json) {
        XStream xStream = new XStream();
        xStream.alias("OrderHeader", EcOrderMessageDTO.class);
        xStream.alias("OrderItem", EcOrderItemMessageDTO.class);
        EcOrderMessageDTO ecOrderMessageDTO = (EcOrderMessageDTO) xStream.fromXML(json);
        if (ObjectUtil.isEmpty(ecOrderMessageDTO)) {
            throw new AppRuntimeException("订单数据不能为null");
        }
        // 保存报文信息
        try {
            initialMessageSave(ecOrderMessageDTO);
        }catch (Exception e){
            log.error("持久化EC订单信息报错:{}",e.getMessage());
        }

        if(StringUtils.isNotEmpty(ecOrderMessageDTO.getShipping_status()) && !ecOrderMessageDTO.getShipping_status().equals("unshipped")){
            return null;
        }
//        if(null == temuOrderDTO.getAmount() || temuOrderDTO.getAmount().compareTo(BigDecimal.ZERO) == 0){
//            throw new AppRuntimeException("订单金额为0!");
//        }

        TenantSalesChannel tenantSalesChannelByChannelName = iTenantSalesChannelService.getTenantSalesChannelByChannelName(ecOrderMessageDTO.getAccount_id(), ChannelTypeEnum.EC.name());
        if(ObjectUtil.isEmpty(tenantSalesChannelByChannelName)){
            throw new AppRuntimeException("ec订单没有对应的渠道信息");
        }
        if(StringUtils.isEmpty(tenantSalesChannelByChannelName.getTenantId())){
            throw new AppRuntimeException("渠道:"+tenantSalesChannelByChannelName.getTenantId()+" 没有租户信息");
        }
        ecOrderMessageDTO.setTenantId(tenantSalesChannelByChannelName.getTenantId());
        ecOrderMessageDTO.setChannelType(ChannelTypeEnum.valueOf(tenantSalesChannelByChannelName.getChannelType()));
        if(StringUtils.isNotEmpty(ecOrderMessageDTO.getRemark())){
            ecOrderMessageDTO.setLogisticsType(LogisticsTypeEnum.valueOf(ecOrderMessageDTO.getRemark()));
        }else {
            throw new AppRuntimeException("ec订单没有对应的物流类型");
        }

        ecOrderMessageDTO.setChannelId(tenantSalesChannelByChannelName.getId());
        ecOrderMessageDTO.setOrderSource(OrderSourceEnum.INTERFACE_ORDER.getValue());
        if(StringUtils.isEmpty(ecOrderMessageDTO.getAddressline1())){
            return null;
        }
        if(CollUtil.isNotEmpty(ecOrderMessageDTO.getItems()) && ecOrderMessageDTO.getItems().size() > 1){
            ecOrderMessageDTO.setIsMultiple(Boolean.TRUE);
            // 易仓暂不支持多件
            log.error("易仓订单多sku不保存,{}",ecOrderMessageDTO);
            return null;
        }else {
            ecOrderMessageDTO.setIsMultiple(Boolean.FALSE);
        }
        return ecOrderMessageDTO;
    }

    @Override
    public Boolean msgVerify(EcOrderMessageDTO ecOrderMessageDTO) {
        List<EcOrderItemMessageDTO> items = ecOrderMessageDTO.getItems();
        // 订单物流信息
        LogisticsTypeEnum orderLogisticsType = ecOrderMessageDTO.getLogisticsType();
        for (EcOrderItemMessageDTO ecOrderItemMessageDTO : items) {
            ProductMapping productMapping = iProductMappingService.getProductMappingByChannelSkuAndSyncStateAndCountry(ecOrderMessageDTO.getTenantId(), ecOrderMessageDTO.getChannelId(), ecOrderItemMessageDTO.getSku(), SyncStateEnum.Mapped,ecOrderMessageDTO.getCountry_code());
            if(null != productMapping && StringUtils.isNotEmpty(productMapping.getProductSkuCode())){
                ProductSku productSku = iProductSkuService.queryByProductSkuCode(productMapping.getProductSkuCode());
                if (productSku == null) {
                    continue;
                } else {
                    // 如果商品被管控，则报商品不存在
                    String tenantId = "1";
                    boolean checkUserAllow = iProductChannelControlService.checkUserAllow(tenantId, productMapping.getProductSkuCode(), ChannelTypeEnum.EC.name());

                    if (!checkUserAllow) {
                        // 日志记录商品不存在
                        return Boolean.FALSE;
                    }
                }
                Product product = iProductService.queryByProductSkuCode(productMapping.getProductSkuCode());
                // 产品的物流信息
                SupportedLogisticsEnum supportedLogistics = product.getSupportedLogistics();
                // 物流类型校验
                if(SupportedLogisticsEnum.PickUpOnly.equals(supportedLogistics)){
                    if(orderLogisticsType.equals(LogisticsTypeEnum.DropShipping)){
                        log.error("订单物流类型: {} 和产品支持的物流类型: {} 不匹配,无法生成订单",supportedLogistics,LogisticsTypeEnum.DropShipping);
                        ecOrderMessageDTO.setExceptionCode(OrderExceptionEnum.Delivery_exception.getValue());
                    }
                }
                if(SupportedLogisticsEnum.DropShippingOnly.equals(supportedLogistics)){
                    if(orderLogisticsType.equals(LogisticsTypeEnum.PickUp)){
                        log.error("订单物流类型: {} 和产品支持的物流类型: {} 不匹配,无法生成订单",supportedLogistics,LogisticsTypeEnum.PickUp);
                        ecOrderMessageDTO.setExceptionCode(OrderExceptionEnum.Delivery_exception.getValue());
                    }
                }
            }
//            if (StrUtil.isNotBlank(ecOrderMessageDTO.getOrdernum())) {
//                if (channelOrderNoSet.contains(ecOrderMessageDTO.getOrdernum())) {
//                    // ?
//                } else {
//                    // 查询此账户所有订单判断是否有重复的，排除Canceled的
//                    boolean orderExists = iOrdersService.existsChannelOrderNo(ecOrderMessageDTO.getOrdernum(), OrderStateType.Canceled);
//                    // 查询导入缓存表
//                    if (orderExists) {
//                        return Boolean.FALSE;
//                        // 存在同一个订单
//                    } else {
//                        channelOrderNoSet.add(ecOrderMessageDTO.getOrdernum());
//                    }
//                }
//            }
            // 规则校验
            if (!RegexUtil.matchQuantity(ecOrderItemMessageDTO.getQuantity().toString())) {
                // 数量异常-正则表达式
                return Boolean.FALSE;
            }
        }
        return Boolean.TRUE;
    }

    @Override
    public Orders formalOrderAndItemEntry(Map<String, List> map, Orders orders) {
        log.info("订单操作完成,进行保存操作");
        List<OrderItem> orderItems = (List<OrderItem>) map.get("orderItems");
        List<OrderItemProductSku> skus = (List<OrderItemProductSku>) map.get("orderItemProductsSku");
        iOrdersService.save(orders);
        orderItems.forEach(item -> item.setOrderId(orders.getId()));
        iOrderItemService.saveBatch(orderItems);
        if(CollUtil.isNotEmpty(skus)){
            for (OrderItem item : orderItems) {
                for (OrderItemProductSku sku : skus) {
                    if (item.getOrderItemNo().equals(sku.getOrderItemNo())) {
                        sku.setOrderItemId(item.getId());
                        sku.setOrderNo(item.getOrderNo());
                    }
                }
            }
            iOrderItemProductSkuService.saveBatch(skus);
        }
        log.info("检查渠道仓库映射");
        try{
            // 检查渠道仓库映射
            if(null == orders.getLogisticsType() && orders.getLogisticsType().equals(LogisticsTypeEnum.PickUp)) {
                if (null == orders.getExceptionCode() || orders.getExceptionCode()
                                                               .equals(OrderExceptionEnum.normal.getValue())) {
                    ChannelWarehouseInfo channelWarehouseInfo = iChannelWarehouseService.queryByTenantSaleChannelIdAndChannelWarehouseCode(orders.getChannelId(), orders.getChannelWarehouseCode());
                    if (ObjectUtil.isEmpty(channelWarehouseInfo)) {
                        orders.setExceptionCode(OrderExceptionEnum.warehouse_mapping_exception.getValue());
                        iOrdersService.updateById(orders);
                    } else {
                        // 查询供应商仓库是否存在
                        for (OrderItem orderItem : orderItems) {
                            Warehouse warehouse = iWarehouseService.queryByTenantIdAndWarehouseCodeAndWarehouseType(orderItem.getSupplierTenantId(), channelWarehouseInfo.getWarehouseCode(), WarehouseTypeEnum.BizArk);
                            if (ObjectUtil.isEmpty(warehouse)) {
                                orders.setExceptionCode(OrderExceptionEnum.warehouse_mapping_exception.getValue());
                                iOrdersService.updateById(orders);
                            } else {
                                List<OrderItemProductSku> orderItemProductSkus = iOrderItemProductSkuService.queryListByOrderNo(orders.getOrderNo());
                                if (CollUtil.isNotEmpty(orderItemProductSkus)) {
                                    for (OrderItemProductSku orderItemProductSku : orderItemProductSkus) {
                                        orderItemProductSku.setWarehouseSystemCode(warehouse.getWarehouseSystemCode());
                                    }
                                    iOrderItemProductSkuService.updateBatchById(orderItemProductSkus);
                                }
                            }
                        }
                    }
                }
            }
        } catch (Exception e){
            log.error("检查渠道仓库映射报错:{}",e.getMessage());
        }
        return orders;
    }

    @Override
    public void formalOrderAboutEntry(Map<String, Object> map) {
        List<OrderLogisticsInfo> logisticsInfo = (List<OrderLogisticsInfo>) map.get("logisticsInfo");
        List<OrderAddressInfo> address = (List<OrderAddressInfo>) map.get("addressInfo");
        Orders orders = (Orders) map.get("orders");
        String tenantId = orders.getTenantId();
        iOrderLogisticsInfoService.saveBatch(logisticsInfo);
        iOrderAddressInfoService.saveBatch(address);
        String warehouseCode = null;
        //      address转换为map,key为orderNo,value为zipCode
        Map<String, String> addressMap = address.stream()
                                                .collect(Collectors.toMap(OrderAddressInfo::getOrderNo, OrderAddressInfo::getZipCode));
        String logisticsCompanyName = null;
        if(CollUtil.isNotEmpty(logisticsInfo)){
            OrderLogisticsInfo orderLogisticsInfo = logisticsInfo.get(0);
            if (ObjectUtil.isNotEmpty(orderLogisticsInfo)){
                logisticsCompanyName = orderLogisticsInfo.getLogisticsCompanyName();
            }
        }
        // 此处已经生成了itemProductSku需要重新选仓库
        String warehouseSystemCode = null ;
        if(null == orders.getExceptionCode() || !orders.getExceptionCode().equals(OrderExceptionEnum.product_mapping_exception.getValue())){
            // 重新计算订单价格信息
            List<OrderItemPrice> itemPrices = new ArrayList<>();
            List<OrderItem> orderItemUpdateList = new ArrayList<>();
            List<OrderItem> listByOrderId = iOrderItemService.getListByOrderId(orders.getId());
            //查询明细商品关联
            HashMap<String,List<String> >stashMap = orderSupport.getStashList(listByOrderId);
            if(CollUtil.isNotEmpty(listByOrderId)){
                for(OrderItem orderItem : listByOrderId){
                    OrderPriceCalculateDTO orderPriceCalculateDTO = new OrderPriceCalculateDTO();
                    orderPriceCalculateDTO.setOrderItem(orderItem);
                    orderPriceCalculateDTO.setLogisticsType(orders.getLogisticsType());
                    orderPriceCalculateDTO.setActivityCode(orderItem.getActivityCode());
                    List<String> stashList = stashMap.get(orderItem.getOrderItemNo());
                    // 仓库编号入口
                    LocaleMessage calculationOrderItemPriceExceptionMessage = priceSupportV2.calculationOrderItemPrice(orderPriceCalculateDTO,tenantId,addressMap.get(orderItem.getOrderNo()),stashList, orders, OrderFlowEnum.THIRD_CREATE_ORDER, logisticsCompanyName);
                    warehouseSystemCode = orderPriceCalculateDTO.getWarehouseSystemCode();
                    warehouseCode = orderPriceCalculateDTO.getWarehouseCode();
                    if(ObjectUtil.isNotEmpty(calculationOrderItemPriceExceptionMessage) && StringUtils.isNotEmpty(calculationOrderItemPriceExceptionMessage.getEn_US())){
                        orders.setPayErrorMessage(calculationOrderItemPriceExceptionMessage.toJSON());
                    }
                    OrderItemPrice orderItemPrice1 = orderPriceCalculateDTO.getOrderItemPrice();
                    OrderItemPrice orderItemPrice = new OrderItemPrice();
                    BeanUtils.copyProperties(orderItemPrice1, orderItemPrice);
                    orderItemPrice.setTotalQuantity(orderItem.getTotalQuantity()).setOrderItemId(orderItem.getId()).setOrderItemNo(orderItem.getOrderItemNo()).setProductSkuCode(orderItem.getProductSkuCode()).setLogisticsType(orderItem.getLogisticsType());
                    itemPrices.add(orderItemPrice);
                    OrderItem orderItemUpdate = new OrderItem();
                    BeanUtils.copyProperties(orderItem, orderItemUpdate);
                    orderItemUpdateList.add(orderItemUpdate);
                }
                // 重新计算主订单数据
                priceSupportV2.recalculateOrderAmount(orders,itemPrices);
            }
            // 更新订单明细信息
            orderItemPriceService.saveOrSetNUll(itemPrices,orders.getExceptionCode());
            orderItemService.updateOrSetNUll(orderItemUpdateList,orders.getExceptionCode());
        //      orders 需要二次更新,第一次更新基础信息,第二次更新价格信息
            iOrdersService.saveOrUpdate(orders);
            ordersService.updateOrSetNull(orders);
            iOrderItemProductSkuService.updateOrSetNull(orders.getOrderNo(),warehouseSystemCode,warehouseSystemCode );
            log.info(orders.toString());
        }
    }

    @Override
    public Map<String, Object> msgForLogistics(EcOrderMessageDTO ecOrderMessageDTO, Orders orders,
                                               Map<String, List> itemMap) {
        HashMap<String, Object> map = new HashMap<>();
        List<OrderItem> orderItems = (List<OrderItem>) itemMap.get("orderItems");
        OrderLogisticsInfo orderLogisticsInfo = new OrderLogisticsInfo();
        ArrayList<OrderAddressInfo> addressInfos = new ArrayList<>();
        ArrayList<OrderLogisticsInfo> orderLogisticsInfos = new ArrayList<>();
        // 订单物流信息
        orderLogisticsInfo.setOrderId(orders.getId());
        orderLogisticsInfo.setOrderNo(orders.getOrderNo());
        orderLogisticsInfo.setShippingLabelExist(true);
        orderLogisticsInfo.setLogisticsZipCode(ecOrderMessageDTO.getPostal_code());
        orderLogisticsInfo.setLogisticsType(LogisticsTypeEnum.getLogisticsTypeEnumByName(orders.getLogisticsType()
                                                                                               .name()));
        if(StringUtils.isNotEmpty(ecOrderMessageDTO.getCarrier())){
            orderLogisticsInfo.setLogisticsCompanyName(ecOrderMessageDTO.getCarrier());
            orderLogisticsInfo.setLogisticsCarrierCode(ecOrderMessageDTO.getCarrier());
        }
        if(StringUtils.isNotEmpty(ecOrderMessageDTO.getShipping_method_code())){
            orderLogisticsInfo.setLogisticsCompanyName(ecOrderMessageDTO.getShipping_method_code());
            orderLogisticsInfo.setLogisticsCarrierCode(ecOrderMessageDTO.getShipping_method_code());
        }

//        orderLogisticsInfo.setLogisticsServiceName(details.getCarrier());
        orderLogisticsInfo.setZipCode(ecOrderMessageDTO.getPostal_code());
        orderLogisticsInfo.setLogisticsCountryCode(ecOrderMessageDTO.getCountry_code());
        orderLogisticsInfos.add(orderLogisticsInfo);

        // 订单地址信息
        OrderAddressInfo orderAddressInfo = new OrderAddressInfo();
        orderAddressInfo.setOrderId(orders.getId());
        orderAddressInfo.setOrderNo(orders.getOrderNo());
        // 拿默认模版里面
//        orderAddressInfo.setRecipient(ecOrderMessageDTO.getBuy_name());

        orderAddressInfo.setPhoneNumber(ecOrderMessageDTO.getPhone());
        // 这三个信息需要调用包裹接口拿到详细的包裹信息
        orderAddressInfo.setCountry(ecOrderMessageDTO.getCountry());
        orderAddressInfo.setCountryCode(ecOrderMessageDTO.getCountry_code());
        String zipCode = ecOrderMessageDTO.getPostal_code();
        String state = ecOrderMessageDTO.getState_or_region();
        orderAddressInfo.setState(state);
        orderAddressInfo.setStateCode(state);

        orderAddressInfo.setCity(ecOrderMessageDTO.getCity());
        orderAddressInfo.setAddress1(ecOrderMessageDTO.getAddressline1());
        orderAddressInfo.setAddress2(ecOrderMessageDTO.getAddressline2());

        orderAddressInfo.setZipCode(zipCode);
//        orderAddressInfo.setEmail(address.getBuyerEmail());
        orderAddressInfo.setAddressType(OrderAddressType.ShipAddress);

        addressInfos.add(orderAddressInfo);

        List<OrderItemPrice> itemPrices = new ArrayList<>();
        if(null == orders.getExceptionCode() || !orders.getExceptionCode().equals(OrderExceptionEnum.product_mapping_exception.getValue())){
            OrderPriceCalculateDTO paramDTO = new OrderPriceCalculateDTO();
            // 订单明细价格
            for (OrderItem orderItem : orderItems) {
                paramDTO.setOrderItem(orderItem);
                paramDTO.setLogisticsType(ecOrderMessageDTO.getLogisticsType());
                paramDTO.setCountry(ecOrderMessageDTO.getCountry_code());
                OrderPriceCalculateDTO calculateDTO = orderSupport.calculationOrderItemPriceForTemu(paramDTO);
                OrderItemPrice itemPrice = calculateDTO.getOrderItemPrice();
                itemPrice.setOrderItemId(orderItem.getId());
                itemPrices.add(itemPrice);
            }
        }
        map.put("logisticsInfo", orderLogisticsInfos);
        map.put("addressInfo", addressInfos);
        map.put("orderItemPrice", itemPrices);
        map.put("orders", orders);
        log.info("ec订单物流信息:{}", JSONUtil.toJsonStr(orderLogisticsInfo));
        return map;
    }

    @Override
    public Map<String, List> msgForItems(EcOrderMessageDTO ecOrderMessageDTO, Orders orders) {
        List<EcOrderItemMessageDTO> items = ecOrderMessageDTO.getItems();
        List<OrderItem> orderItems = new ArrayList();
        List<OrderItemProductSku> orderItemProductSkus = new ArrayList<>();
        HashMap<String, List> hashMap = new HashMap<>();
        for (EcOrderItemMessageDTO ecOrderItemMessageDTO : items) {
            OrderItem orderItem = new OrderItem();
            orderItem.setChannelType(orders.getChannelType());
            orderItem.setChannelSku(ecOrderItemMessageDTO.getSku());
            orderItem.setLogisticsType(ecOrderMessageDTO.getLogisticsType());
            orderItemService.setOrderBusinessFieldForEc(orderItem, ecOrderMessageDTO, orders, ecOrderItemMessageDTO);
            orderItemService.setChannelTagForAmazonEc(orderItem, ecOrderMessageDTO, orders, ecOrderItemMessageDTO);
            iOrderItemThirdService.setOrderTagSystemForEc(orderItem, ecOrderMessageDTO, orders, ecOrderItemMessageDTO);
            // 设置币种
            SiteCountryCurrencyVo siteCountryCurrencyVo = iSiteCountryCurrencyService.queryByCountryCode(ecOrderMessageDTO.getCountry_code());
            if(null != siteCountryCurrencyVo){
                orderItem.setCurrency(siteCountryCurrencyVo.getCurrencyCode());
                orderItem.setCurrencySymbol(siteCountryCurrencyVo.getCurrencySymbol());
                orderItem.setCountryCode(siteCountryCurrencyVo.getCountryCode());
                orderItem.setSiteId(siteCountryCurrencyVo.getId());
            }
            orderItems.add(orderItem);
            // 获取商品相关的活动信息
            DistributorProductActivity distributorProductActivity = orderActivitySupport.getDistributorActivityByStock(ecOrderMessageDTO.getTenantId(), ecOrderItemMessageDTO.getProductSkuCode(), ecOrderMessageDTO.getCountry(),orderActivitySupport.logisticsTypeConvert(orders.getLogisticsType()));
            // 产品没有映射不生成 orderItemProductsSku 数据
            if(null == orders.getExceptionCode() || !orders.getExceptionCode().equals(OrderExceptionEnum.product_mapping_exception.getValue())){
                OrderItemProductSku orderItemProductSku = new OrderItemProductSku();
                // 通过 自订单编号进行关联,
                orderItemProductSkuThirdService.setBusinessFieldForEc(orderItemProductSku, orderItem, ecOrderMessageDTO, orders, ecOrderItemMessageDTO);
                // 活动订单设置为活动仓库
                if (null != distributorProductActivity) {
                    DistributorProductActivityStock activityWarehouseInfo = orderActivitySupport.getActivityWarehouseInfo(distributorProductActivity);
                    if(null != activityWarehouseInfo && null != activityWarehouseInfo.getWarehouseSystemCode()){
                        orderItemProductSku.setSpecifyWarehouse(activityWarehouseInfo.getWarehouseSystemCode());
                        orderItemProductSku.setWarehouseSystemCode(activityWarehouseInfo.getWarehouseSystemCode());
                    }

                }
                orderItemProductSkus.add(orderItemProductSku);
            }
            // 判断活动订单
            if (null != distributorProductActivity) {
                orderItem.setActivityCode(distributorProductActivity.getDistributorActivityCode());
                orderItem.setActivityType(distributorProductActivity.getActivityType());
            }
        }
        hashMap.put("orderItems", orderItems);
        hashMap.put("orderItemProductsSku", orderItemProductSkus);
        log.info("订单项信息:{}", JSONUtil.toJsonStr(orderItems));
        return hashMap;
    }

    @Override
    public void getShippingLabels(List<Map<String, List>> orderData) {
        try{
            AmazonVCOrderLogisticsAttachmentDTO amazonVCOrderLogisticsAttachmentDTO = new AmazonVCOrderLogisticsAttachmentDTO();
            List<AmazonVCOrderLogisticsAttachmentItemDTO> itemDTOList = new ArrayList<>();
            for (Map<String, List> orderDatum : orderData) {

                Orders orders = (Orders) orderDatum.get("orders").get(0);
                //发货类型为自提才获取面单
                if(!LogisticsTypeEnum.PickUp.equals(orders.getLogisticsType())){
                    log.info("发货类型为非自提，不获取面单");
                    return;
                }
                //List<OrderItem> orderItems = (List<OrderItem>) orderDatum.get("orderItems");
                List<OrderItemProductSku> skus = (List<OrderItemProductSku>) orderDatum.get("orderItemProductsSku");

                if(null == orders.getExceptionCode() || orders.getExceptionCode().equals(OrderExceptionEnum.normal.getValue())
                    || orders.getExceptionCode().equals(OrderExceptionEnum.out_of_stock_exception.getValue())){
                    log.info("发送订单信息到物流附件队列");
                    itemDTOList.addAll(createShippingLabels(orders, skus));

                }

            }
            Orders orderHead = (Orders)orderData.get(0).get("orders").get(0);
            if (CollUtil.isEmpty(itemDTOList)) {
                log.info("未获取到订单明细数据，单号：{}",orderHead.getChannelOrderNo());
                return;
            }
            amazonVCOrderLogisticsAttachmentDTO.setOrderNo(orderHead.getChannelOrderNo()).setChannel(orderHead.getChannelAlias().replaceAll("^[^:]*:", "")).setContainerType("Carton").setWarehouseCode(orderHead.getChannelWarehouseCode());
            amazonVCOrderLogisticsAttachmentDTO.setItems(itemDTOList);
            String str = JSON.toJSONString(amazonVCOrderLogisticsAttachmentDTO);
            String messageId = IdUtil.fastSimpleUUID();
            Message message = MessageBuilder.withBody(str.getBytes()).setMessageId(messageId).build();
            log.info("[发送订单信息到物流附件队列]成功，发送参数：{}",JSON.toJSON(amazonVCOrderLogisticsAttachmentDTO));
            rabbitTemplate.convertAndSend(RabbitMqConstant.MULTICHANNEL_SEND_EXCHANGE,RabbitMqConstant.ORDER_AMAZON_VC_LOGISTICS_ATTACHMENT_ROUTING_KEY,message);

        }catch (Exception e){
            log.error("发送订单信息到物流附件队列报错:{}",e.getMessage());
        }
    }

    private List<AmazonVCOrderLogisticsAttachmentItemDTO> createShippingLabels(Orders orders, List<OrderItemProductSku> skus) {
        List<AmazonVCOrderLogisticsAttachmentItemDTO> itemDTOList = new ArrayList<>();
        for(OrderItemProductSku orderItemProductSku : skus){
            AmazonVCOrderLogisticsAttachmentItemDTO itemDTO = new AmazonVCOrderLogisticsAttachmentItemDTO();
            itemDTO.setDeliverOrderNo(orders.getOrderNo()).setDeliverOrderId(orders.getId()).setChannelOrderItemId(orders.getLineOrderItemId()).setQuantity(1);
            ProductSkuDetail productSkuDetail = iProductSkuDetailService.queryByProductSkuCode(orderItemProductSku.getProductSkuCode());
            if(null != productSkuDetail){
                // 设置长宽高
                switch (productSkuDetail.getPackLengthUnit())      {
                    case foot:
                        itemDTO.setLength(productSkuDetail.getPackLength());
                        itemDTO.setWidth(productSkuDetail.getPackWidth());
                        itemDTO.setHeight(productSkuDetail.getPackHeight());
                        break;
                    case inch:
                        itemDTO.setLength(UnitConverter.inchesToFeet(productSkuDetail.getPackLength()));
                        itemDTO.setWidth(UnitConverter.inchesToFeet(productSkuDetail.getPackWidth()));
                        itemDTO.setHeight(UnitConverter.inchesToFeet(productSkuDetail.getPackHeight()));
                        break;
                    case m:
                        itemDTO.setLength(UnitConverter.metersToFeet(productSkuDetail.getPackLength()));
                        itemDTO.setWidth(UnitConverter.metersToFeet(productSkuDetail.getPackWidth()));
                        itemDTO.setHeight(UnitConverter.metersToFeet(productSkuDetail.getPackHeight()));
                        break;
                    case cm:
                        itemDTO.setLength(UnitConverter.metersToFeet(UnitConverter.centimetersToMeters(productSkuDetail.getPackLength())));
                        itemDTO.setWidth(UnitConverter.metersToFeet(UnitConverter.centimetersToMeters(productSkuDetail.getPackWidth())));
                        itemDTO.setHeight(UnitConverter.metersToFeet(UnitConverter.centimetersToMeters(productSkuDetail.getPackHeight())));
                        break;
                }
                // 设置重量
                switch (productSkuDetail.getPackWeightUnit()){
                    case lb:
                        itemDTO.setWeight(productSkuDetail.getPackWeight());
                        break;
                    case kg:
                        itemDTO.setWeight(UnitConverter.kilogramsToPounds(productSkuDetail.getPackWeight()));
                        break;
                    case g:
                        itemDTO.setWeight(UnitConverter.gramsToPounds(productSkuDetail.getPackWeight()));
                        break;
                    case t:
                        itemDTO.setWeight(UnitConverter.kilogramsToPounds(UnitConverter.tonsToKilograms(productSkuDetail.getPackWeight())));
                        break;
                }
            }
            itemDTOList.add(itemDTO);
        }

        return itemDTOList;
}

    @Override
    public Boolean isNeedPay() {
        return Boolean.FALSE;
    }

    @Override
    public String attachmentsFlow(EcOrderMessageDTO ecOrderMessageDTO, Orders orders) {
        LogisticsTypeEnum logisticsType = orders.getLogisticsType();
// 目前全是代发
//        if(LogisticsTypeEnum.PickUp.equals(logisticsType)){
//            SaleOrderDetailDTO saleOrderDetails = orderReceiveFromThirdDTO.getSaleOrderDetails();
//            List<AttachInfo> attachInfoItems = saleOrderDetails.getAttachInfoItems();
//            for (AttachInfo attachInfoItem : attachInfoItems) {
//                SysOssVo sysOssVo = sysOssService.downloadPdfNotAsync(attachInfoItem.getUrl(), String.valueOf(orders.getId()), saleOrderDetails.getLogisticsTrackingNo());
//                OrderUpdateBo bo = new OrderUpdateBo(orders.getOrderNo(), null, true, sysOssVo);
//                // 实际上传完了以后已经有了文件了 不需要再走上传的逻辑了
//                distributorOrderService.uploadShippingLabel(bo);
//            }
//
//        }

        return orders.getOrderNo();
    }

    @Override
    public Boolean isNeedPay(EcOrderMessageDTO ecOrderMessageDTO) {
        Boolean isPay;
        isPay = ZSMallSystemEventUtils.checkWalletAutoPaymentEvent(ecOrderMessageDTO.getTenantId(),ecOrderMessageDTO.getCurrency_code());
        if(isPay){
            // 异常订单不进行支付
            if(null != ecOrderMessageDTO.getExceptionCode() && !ecOrderMessageDTO.getExceptionCode().equals(OrderExceptionEnum.normal.getValue())){
                isPay = false;
            }
        }
        return isPay;
    }

    @Override
    public Boolean payOrder(EcOrderMessageDTO ecOrderMessageDTO) throws Exception {
        // 是否开启自动支付
        if (true) {
            LambdaQueryWrapper<Orders> lqw = new LambdaQueryWrapper<Orders>().eq(Orders::getChannelOrderNo, ecOrderMessageDTO.getOrdernum());

            List<String> orderNos = TenantHelper.ignore(() -> iOrdersService.list(lqw)).stream().map(Orders::getOrderNo)
                                                .collect(Collectors.toList());
            OrderPayBo bo = new OrderPayBo();
            bo.addOrderNoList(orderNos);
            bo.setPaymentPassword(null);
            if(CollUtil.isEmpty(orderNos)){
                return false;
            }
            try {
                TenantHelper.ignore(() -> {
                    try {
                        return ordersService.payOrderForDistribution(bo, ecOrderMessageDTO.getTenantId(), true, true);
                    } catch (Exception e) {
                        log.error(e.getMessage());
                        throw new RuntimeException(e.getMessage());
                    }
                });
            } catch (Exception e) {
                log.error("支付失败:{}", e.getMessage());
            }
        }
        return true;
    }

    @Override
    public Boolean payOrderForAsync(EcOrderMessageDTO ecOrderMessageDTO, Boolean isAsync) throws Exception {
        // 是否开启自动支付
        if (true) {
            LambdaQueryWrapper<Orders> lqw = new LambdaQueryWrapper<Orders>().eq(Orders::getChannelOrderNo, ecOrderMessageDTO.getOrdernum());
            List<Orders> list = iOrdersService.list(lqw);
            // 校验自提订单是否有标签
            if (orderSupport.isExpressSheetUpload(list)) {
                List<String> orderNos = TenantHelper.ignore(() -> list).stream().map(Orders::getOrderNo)
                                                    .collect(Collectors.toList());
                OrderPayBo bo = new OrderPayBo();
                bo.addOrderNoList(orderNos);
                bo.setPaymentPassword(null);
                if (CollUtil.isEmpty(orderNos)) {
                    return false;
                }
                try {
                    TenantHelper.ignore(() -> {
                        try {
                            return ordersService.payOrderForErp(bo, ecOrderMessageDTO.getTenantId(), true, true);
                        } catch (Exception e) {
                            log.error(e.getMessage());
                            throw new RuntimeException(e.getMessage());
                        }
                    });
                } catch (Exception e) {
                    log.error("支付失败:{}", e.getMessage());
                }
            }
        }
        return true;
    }

    @Override
    public Orders thirdToDistribution(EcOrderMessageDTO ecOrderMessageDTO, Orders orders) throws ParseException {
        Orders orders1 = new Orders();
        String orderNo = orderCodeGenerator.codeGenerate(BusinessCodeEnum.OrderNo);
        orders1.setOrderNo(orderNo);
        orders1.setOrderExtendId(orderNo);
        if(null != ecOrderMessageDTO.getExceptionCode()){
            orders1.setExceptionCode(ecOrderMessageDTO.getExceptionCode());
        }
        if(null != ecOrderMessageDTO.getPayErrorMessage()){
            orders1.setPayErrorMessage(ecOrderMessageDTO.getPayErrorMessage());
        }
        // 业务属性
        orders1 = ordersService.setOrderBusinessFieldForEc(ecOrderMessageDTO, orders1);
        orders1 = ordersService.setOrderTagSystemByEc(ecOrderMessageDTO, orders1);
        orders1 = ordersService.setChannelTagForEc(ecOrderMessageDTO, orders1);
        // 币种
        SiteCountryCurrencyVo siteCountryCurrencyVo = iSiteCountryCurrencyService.queryByCountryCode(ecOrderMessageDTO.getCountry_code());
        if(null != siteCountryCurrencyVo){
            orders1.setCurrency(siteCountryCurrencyVo.getCurrencyCode());
            orders1.setCurrencySymbol(siteCountryCurrencyVo.getCurrencySymbol());
            orders1.setCountryCode(siteCountryCurrencyVo.getCountryCode());
            orders1.setSiteId(siteCountryCurrencyVo.getId());
        }
        return orders1;
    }

    @Override
    public List<EcOrderMessageDTO> ordersDisassemble(EcOrderMessageDTO ecOrderMessageDTO) {
        List<EcOrderItemMessageDTO> ecOrderItemMessageDTOList = ecOrderMessageDTO.getItems();
        List<EcOrderMessageDTO> ecOrderMessageDTOS = new ArrayList<>();
        // 把订单项拆开
        for (EcOrderItemMessageDTO ecOrderItemMessageDTO : ecOrderItemMessageDTOList) {
            if(ecOrderItemMessageDTO.getQuantity() == 1){
                // 需要通过itemOrderId进行搜索
                LambdaQueryWrapper<Orders> lqw = new LambdaQueryWrapper<Orders>().eq(Orders::getChannelOrderNo, ecOrderMessageDTO.getOrdernum())
                                                                                 .eq(Orders::getLineOrderItemId,ecOrderItemMessageDTO.getChannel_order_item_id())
                                                                                 .eq(Orders::getDelFlag, 0).last("limit 1");
                Orders order = iOrdersService.getOne(lqw);
                if (ObjectUtil.isNotEmpty(order)) {
                    continue;
                }
                List<EcOrderItemMessageDTO> ecOrderItemMessageDTOList1 = new ArrayList<>();
                EcOrderMessageDTO ecOrderMessageDTO1 = new EcOrderMessageDTO();
                ProductMapping productMapping = iProductMappingService.getProductMappingByChannelSkuAndSyncStateAndCountry(ecOrderMessageDTO.getTenantId(), ecOrderMessageDTO.getChannelId(), ecOrderItemMessageDTO.getSku(), SyncStateEnum.Mapped,ecOrderMessageDTO.getCountry_code());
                TenantSalesChannel tenantSalesChannel = iTenantSalesChannelService.getTenantSalesChannelByChannelName(ecOrderMessageDTO.getAccount_id(),ChannelTypeEnum.EC.name());
                if(ObjectUtil.isEmpty(tenantSalesChannel)){
                    throw new AppRuntimeException("没有对应的渠道信息");
                }
                if(null != productMapping && StringUtils.isNotEmpty(productMapping.getProductSkuCode())){
                    ecOrderItemMessageDTO.setProductSkuCode(productMapping.getProductSkuCode());
                    // 判断渠道和产品的发货方式是否一致
                    Product product = iProductService.queryByProductSkuCode(productMapping.getProductSkuCode());
                    //校验渠道店铺发货方式和商品发货方式是否不一致
                    if (ObjectUtil.isNotNull(product) && ObjectUtil.isNotNull(tenantSalesChannel) ){
                        boolean isSupport = product.getSupportedLogistics()
                                                   .allowShipping(LogisticsTypeEnum.getLogisticsTypeEnumByName(String.valueOf(tenantSalesChannel.getLogisticsType())));
                        if (!isSupport){
                            ecOrderMessageDTO.setExceptionCode(OrderExceptionEnum.Delivery_exception.getValue());
                        }
                    }
                    // 判断库存是否足够
                    ProductSkuAdjustStockVo adjustStockVo = iProductSkuService.queryAdjustStockVo(productMapping.getProductSkuCode());
                    Integer stockTotal = adjustStockVo.getStockTotal();
                    if (NumberUtil.compare(ecOrderItemMessageDTO.getQuantity(), stockTotal) > 0) {
                        // todo 测算放开
                        ecOrderMessageDTO.setExceptionCode(OrderExceptionEnum.out_of_stock_exception.getValue());
                    }
                }
                ecOrderItemMessageDTOList1.add(ecOrderItemMessageDTO);
                BeanUtils.copyProperties(ecOrderMessageDTO, ecOrderMessageDTO1);
                // 判断产品是否映射
                if (ObjectUtil.isEmpty(productMapping)) {
                    // 判断商品价格是否存在
                    List<ProductMapping> productMappingList = iProductMappingService.getProductMappingByChannelSkuAndSyncState(ecOrderMessageDTO.getTenantId(), ecOrderMessageDTO.getChannelId(), ecOrderItemMessageDTO.getSku(), SyncStateEnum.Mapped);
                    String productSkuCode = null;
                    if(CollUtil.isNotEmpty(productMappingList)){
                        productSkuCode = productMappingList.get(0).getProductSkuCode();
                    }
                    ProductSkuPrice productSkuPrice;
                    if(StringUtils.isNotEmpty(productSkuCode)){
                        productSkuPrice = iProductSkuPriceService.queryByProductSkuCodeAndCountryCode(productSkuCode, ecOrderMessageDTO.getCountry());
                        if(null == productSkuPrice){
                            LocaleMessage localeMessage = LocaleMessage.byStatusCode(OrderStatusCodeEnum.SKU_REGION_PRICE_NOT_MAINTAINED.args(productSkuCode));
                            ecOrderMessageDTO1.setPayErrorMessage(localeMessage.toJSON());
                            ecOrderMessageDTO.setPayErrorMessage(localeMessage.toJSON());
                        }
                    }
                    ecOrderMessageDTO1.setExceptionCode(OrderExceptionEnum.product_mapping_exception.getValue());
                    ecOrderMessageDTO.setExceptionCode(OrderExceptionEnum.product_mapping_exception.getValue());
                }
                ecOrderMessageDTO1.pushEcOrderItemDTOList(ecOrderItemMessageDTOList1);
                ecOrderMessageDTO1.setChannel_order_item_id(ecOrderItemMessageDTO.getChannel_order_item_id());
                ecOrderMessageDTOS.add(ecOrderMessageDTO1);
            }

            if(ecOrderItemMessageDTO.getQuantity() > 1){
                // 需要通过itemOrderId进行搜索
                LambdaQueryWrapper<Orders> lqw = new LambdaQueryWrapper<Orders>().eq(Orders::getChannelOrderNo, ecOrderMessageDTO.getOrdernum())
                                                                                 .eq(Orders::getLineOrderItemId,ecOrderItemMessageDTO.getChannel_order_item_id())
                                                                                 .eq(Orders::getDelFlag, 0).last("limit 1");
                Orders order = iOrdersService.getOne(lqw);
                if (ObjectUtil.isNotEmpty(order)) {
                    continue;
                }
                List<EcOrderItemMessageDTO> ecOrderItemMessageDTOList1 = new ArrayList<>();
                EcOrderMessageDTO ecOrderMessageDTO1 = new EcOrderMessageDTO();
                ProductMapping productMapping = iProductMappingService.getProductMappingByChannelSkuAndSyncStateAndCountry(ecOrderMessageDTO.getTenantId(), ecOrderMessageDTO.getChannelId(), ecOrderItemMessageDTO.getSku(), SyncStateEnum.Mapped,ecOrderMessageDTO.getCountry_code());
                TenantSalesChannel tenantSalesChannel = iTenantSalesChannelService.getTenantSalesChannelByChannelName(ecOrderMessageDTO.getAccount_id(),ChannelTypeEnum.EC.name());
                if(ObjectUtil.isEmpty(tenantSalesChannel)){
                    throw new AppRuntimeException("没有对应的渠道信息");
                }

                if(null != productMapping && StringUtils.isNotEmpty(productMapping.getProductSkuCode())){
                    ecOrderItemMessageDTO.setProductSkuCode(productMapping.getProductSkuCode());
                    // 判断渠道和产品的发货方式是否一致
                    Product product = iProductService.queryByProductSkuCode(productMapping.getProductSkuCode());
                    //校验渠道店铺发货方式和商品发货方式是否不一致
                    if (ObjectUtil.isNotNull(product) && ObjectUtil.isNotNull(tenantSalesChannel) ){
                        boolean isSupport = product.getSupportedLogistics()
                                                   .allowShipping(LogisticsTypeEnum.getLogisticsTypeEnumByName(String.valueOf(tenantSalesChannel.getLogisticsType())));
                        if (!isSupport){
                            ecOrderMessageDTO.setExceptionCode(OrderExceptionEnum.Delivery_exception.getValue());
                        }
                    }
                    // 判断库存是否足够
                    ProductSkuAdjustStockVo adjustStockVo = iProductSkuService.queryAdjustStockVo(productMapping.getProductSkuCode());
                    Integer stockTotal = adjustStockVo.getStockTotal();
                    if (NumberUtil.compare(ecOrderItemMessageDTO.getQuantity(), stockTotal) > 0) {
                        // todo 测算放开
                        ecOrderMessageDTO.setExceptionCode(OrderExceptionEnum.out_of_stock_exception.getValue());
                    }
                }
                ecOrderItemMessageDTOList1.add(ecOrderItemMessageDTO);
                BeanUtils.copyProperties(ecOrderMessageDTO, ecOrderMessageDTO1);
                // 判断产品是否映射
                if (ObjectUtil.isEmpty(productMapping)) {
                    // 判断商品价格是否存在
                    List<ProductMapping> productMappingList = iProductMappingService.getProductMappingByChannelSkuAndSyncState(ecOrderMessageDTO.getTenantId(), ecOrderMessageDTO.getChannelId(), ecOrderItemMessageDTO.getSku(), SyncStateEnum.Mapped);
                    String productSkuCode = null;
                    if(CollUtil.isNotEmpty(productMappingList)){
                        productSkuCode = productMappingList.get(0).getProductSkuCode();
                    }
                    ProductSkuPrice productSkuPrice;
                    if(StringUtils.isNotEmpty(productSkuCode)){
                        productSkuPrice = iProductSkuPriceService.queryByProductSkuCodeAndCountryCode(productSkuCode, ecOrderMessageDTO.getCountry());
                        if(null == productSkuPrice){
                            LocaleMessage localeMessage = LocaleMessage.byStatusCode(OrderStatusCodeEnum.SKU_REGION_PRICE_NOT_MAINTAINED.args(productSkuCode));
                            ecOrderMessageDTO1.setPayErrorMessage(localeMessage.toJSON());
                            ecOrderMessageDTO.setPayErrorMessage(localeMessage.toJSON());
                        }
                    }
                    ecOrderMessageDTO1.setExceptionCode(OrderExceptionEnum.product_mapping_exception.getValue());
                    ecOrderMessageDTO.setExceptionCode(OrderExceptionEnum.product_mapping_exception.getValue());
                }
                ecOrderMessageDTO1.pushEcOrderItemDTOList(ecOrderItemMessageDTOList1);
                ecOrderMessageDTO1.setChannel_order_item_id(ecOrderItemMessageDTO.getChannel_order_item_id());
                // 拆分数量
                Integer forI = ecOrderItemMessageDTO.getQuantity();
                ecOrderItemMessageDTO.setQuantity(1);
                ecOrderItemMessageDTO.setSubtotal(ecOrderItemMessageDTO.getSubtotal().divide(new BigDecimal(forI)));
                for (int i = 0; i < forI; i++){
                    ecOrderMessageDTOS.add(ecOrderMessageDTO1);
                }
            }
        }
        return ecOrderMessageDTOS;
    }

    @Override
    public R tripartiteUpdate(Object o) {
        return null;
    }

    @Override
    public Boolean tripartiteDeliverGoods(Object o) {
        return null;
    }

    @Override
    public Boolean tripartiteReceiptGoods(Object o) {
        return null;
    }

    @Override
    public R<Void> test() {
        return null;
    }

    @Override
    public void orderOperationHandler(EcOrderMessageDTO i, ConcurrentHashMap<String, List<Object>> businessMap,
                                      ConcurrentHashMap<String, ConcurrentHashMap<String, String>> businessNos,
                                      ChannelTypeEnum channelTypeEnum, BusinessTypeMappingEnum mappingEnum) {

    }

    @Override
    public void priceOperation(ConcurrentHashMap<String, List<Object>> businessMap,
                               ConcurrentHashMap<String, ConcurrentHashMap<String, String>> businessNos,
                               ChannelTypeEnum channelTypeEnum, BusinessTypeMappingEnum mappingEnum) {

    }

    @Override
    public void orderOperationHandlerSave(ChannelTypeEnum channelTypeEnum,
                                          ConcurrentHashMap<String, List<Object>> businessMap) {

    }

    @Override
    public List<EcOrderMessageDTO> ordersDisassembleForList(
        List<EcOrderMessageDTO> amazonVCOrderMessageDTOS, BusinessTypeMappingEnum mappingEnum) {
        return null;
    }

    @Override
    public List<EcOrderMessageDTO> parseThirdDataForList(String s) {
        return null;
    }
}
