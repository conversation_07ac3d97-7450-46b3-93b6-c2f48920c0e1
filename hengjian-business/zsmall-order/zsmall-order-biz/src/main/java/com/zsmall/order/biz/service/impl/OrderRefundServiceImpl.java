package com.zsmall.order.biz.service.impl;

import cn.hutool.core.annotation.AnnotationUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.*;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hengjian.common.core.domain.R;
import com.hengjian.common.core.enums.LanguageType;
import com.hengjian.common.core.enums.TenantType;
import com.hengjian.common.core.exception.RStatusCodeException;
import com.hengjian.common.log.annotation.InMethodLog;
import com.hengjian.common.mybatis.core.page.PageQuery;
import com.hengjian.common.mybatis.core.page.TableDataInfo;
import com.hengjian.common.satoken.utils.LoginHelper;
import com.hengjian.common.tenant.helper.TenantHelper;
import com.hengjian.openapi.service.ISysApiService;
import com.hengjian.system.domain.SysTenant;
import com.hengjian.system.service.ISysTenantService;
import com.zsmall.common.annotaion.RefundRuleAnalysisAnnotation;
import com.zsmall.common.domain.bo.TrackingNoBo;
import com.zsmall.common.domain.dto.stock.AdjustStockDTO;
import com.zsmall.common.domain.vo.IntactAddressInfoVo;
import com.zsmall.common.enums.BusinessCodeEnum;
import com.zsmall.common.enums.BusinessParameterType;
import com.zsmall.common.enums.common.ChannelTypeEnum;
import com.zsmall.common.enums.order.*;
import com.zsmall.common.enums.orderRefund.RefundAmountStateEnum;
import com.zsmall.common.enums.orderRefund.RefundApplyType;
import com.zsmall.common.enums.orderRefund.RefundDisputeStateEnum;
import com.zsmall.common.enums.orderRefund.ReturnLogisticsType;
import com.zsmall.common.enums.statuscode.ZSMallStatusCodeEnum;
import com.zsmall.common.enums.transaction.TransactionStateEnum;
import com.zsmall.common.enums.transaction.TransactionSubTypeEnum;
import com.zsmall.common.enums.transaction.TransactionTypeEnum;
import com.zsmall.common.exception.StockException;
import com.zsmall.common.exception.WalletException;
import com.zsmall.common.service.BusinessParameterService;
import com.zsmall.extend.utils.ZSMallActivityEventUtils;
import com.zsmall.order.biz.service.OrderRefundRuleService;
import com.zsmall.order.biz.service.OrderRefundService;
import com.zsmall.order.biz.support.OrderSupport;
import com.zsmall.order.entity.domain.*;
import com.zsmall.order.entity.domain.bo.RefundApplyBo;
import com.zsmall.order.entity.domain.bo.SubmitRefundApplyBo;
import com.zsmall.order.entity.domain.bo.refund.RefundApplyHandleBo;
import com.zsmall.order.entity.domain.bo.refund.RefundBo;
import com.zsmall.order.entity.domain.bo.refund.SupplementRefundInfoBo;
import com.zsmall.order.entity.domain.dto.OrderRefundMsgDTO;
import com.zsmall.order.entity.domain.dto.OrderRefundMsgDetailDTO;
import com.zsmall.order.entity.domain.dto.RefundItemToDTO;
import com.zsmall.order.entity.domain.dto.RefundStageDTO;
import com.zsmall.order.entity.domain.export.OrderRefundExportDTO;
import com.zsmall.order.entity.domain.vo.*;
import com.zsmall.order.entity.domain.vo.refundOrder.RefundReasonListVo;
import com.zsmall.order.entity.domain.vo.refundOrder.RefundReasonVo;
import com.zsmall.order.entity.iservice.*;
import com.zsmall.order.entity.manger.IOrderRefundManger;
import com.zsmall.order.factory.RefundRuleFactory;
import com.zsmall.product.biz.service.ProductSkuStockService;
import com.zsmall.product.entity.domain.ProductSku;
import com.zsmall.product.entity.iservice.IProductSkuService;
import com.zsmall.system.biz.service.TenantWalletService;
import com.zsmall.system.biz.support.BillSupport;
import com.zsmall.system.entity.domain.TransactionRecord;
import com.zsmall.system.entity.iservice.ITransactionsOrderRefundService;
import com.zsmall.system.entity.util.MallSystemCodeGenerator;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 售后申请主单Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-06-08
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class OrderRefundServiceImpl implements OrderRefundService {
    private final IOrderRefundManger iOrderRefundManger;
    private final ISysApiService iSysApiService;
    private final String dateFormat = "yyyy-MM-dd HH:mm";
    private final IOrderRefundService iOrderRefundService;
    private final IOrderRefundAttachmentService iOrderRefundAttachmentService;
    private final IOrderRefundItemService iOrderRefundItemService;
    private final IOrdersService iOrdersService;
    private final IOrderItemTrackingRecordService iOrderItemTrackingRecordService;
    private final IOrderItemProductSkuService iOrderItemProductSkuService;
    private final IProductSkuService iProductSkuService;
    private final IOrderRefundLogisticsService iOrderRefundLogisticsService;
    private final IOrderAddressInfoService iOrderAddressInfoService;
    private final OrderSupport orderSupport;
    private final IOrderItemService iOrderItemService;
    private final IOrderItemPriceService iOrderItemPriceService;
    private final ITransactionsOrderRefundService iTransactionsOrderRefundService;

    private final OrderCodeGenerator orderCodeGenerator;
    private final MallSystemCodeGenerator mallSystemCodeGenerator;
    private final OrderRefundRuleService orderRefundRuleService;
    private final BusinessParameterService businessParameterService;
    private final IOrderRefundRuleService iOrderRefundRuleService;
    private final ProductSkuStockService productSkuStockService;
    private final TenantWalletService tenantWalletService;
    private final BillSupport billSupport;
    private final IWholesaleIntentionOrderService iWholesaleIntentionOrderService;
    private final ISysTenantService sysTenantService;

    private final RefundRuleFactory refundRuleFactory;

    /**
     * 查询售后申请主单
     */
    @Override
    public R<RefundDetailVo> queryByRefundNo(String orderRefundNo) {
        TenantType tenantType = LoginHelper.getTenantTypeEnum();
        String tenantId = LoginHelper.getTenantId();

        OrderRefund orderRefund = iOrderRefundService.queryByRefundNo(orderRefundNo);
        if (orderRefund == null) {
            return R.fail(ZSMallStatusCodeEnum.REFUND_REQUEST_NOT_EXIST);
        }
        Orders orders = iOrdersService.getByOrderNo(orderRefund.getOrderNo());
        if (ObjectUtil.equals(orders.getOrderType(), OrderType.Wholesale)) {
            return R.ok(getRefundItemDetailWholesale(orders, orderRefund, tenantType));
        }
        if (ObjectUtil.equals(orders.getOrderType(), OrderType.Normal)) {
            return R.ok(getRefundItemDetailNormal(orderRefund, tenantType));
        }
        return null;
    }

    /**
     * 查询售后申请主单列表
     */
    @Override
    public TableDataInfo<RefundApplyVo> queryPageList(RefundApplyBo bo, PageQuery pageQuery) {
        TenantType tenantType = LoginHelper.getTenantTypeEnum();
        String tenantId = LoginHelper.getTenantId();
        bo.setTenantType(tenantType.name());
        bo.setTenantId(tenantId);
        Integer page = pageQuery.getPageNum();
        Integer limit = pageQuery.getPageSize();


        String refundState = bo.getRefundState();
        List<String> refundStates = new ArrayList<>();
        if (StrUtil.isNotBlank(refundState)) {
            refundStates.add(refundState);
        }
        bo.setRefundStates(refundStates);
        // 如果是查询审核中的，那么退款进行中的也要一起查出来
        if (StrUtil.equals(refundState, OrderRefundStateType.Verifying.name())) {
            refundStates.add(OrderRefundStateType.Refunding.name());

            if (ObjectUtil.equals(tenantType, TenantType.Manager) || ObjectUtil.equals(tenantType, TenantType.Distributor)) {
                refundStates.add(OrderRefundStateType.ManagerVerifying.name());
            }
        }
        Page<OrderRefund> refundPage = new Page<>(page, limit);
        com.baomidou.mybatisplus.core.metadata.OrderItem orderItem = com.baomidou.mybatisplus.core.metadata.OrderItem.desc("create_time");
        refundPage.addOrder(orderItem);

        IPage<RefundApplyVo> orderRefundPage = iOrderRefundService.queryPageList(bo, refundPage);
        List<RefundApplyVo> records = orderRefundPage.getRecords();
        List<String> tenantIds = records.stream().map(RefundApplyVo::getTenantId).distinct().collect(Collectors.toList());
        Map<String, SysTenant> tenantMapByTenantIds = sysTenantService.getTenantMapByTenantIds(tenantIds);

        for (RefundApplyVo record : records) {
            String supplierTenantId = record.getSupplierTenantId();
            String refundApplyTime = record.getRefundApplyTime();
            String format = DateUtil.format(DateUtil.parseDate(refundApplyTime), "yyyy-MM-dd HH:mm");
            record.setRefundApplyTime(format);
            if (ObjectUtil.equals(tenantType, TenantType.Supplier)) {
                record.setTotal(record.getOriginalRefundAmount());
            }
            record.setSupplierName(supplierTenantId);
            //赋值 店铺标识
            if (ObjectUtil.isNotNull(tenantMapByTenantIds)){
                SysTenant sysTenant = tenantMapByTenantIds.get(record.getTenantId());
                if (ObjectUtil.isNotNull(sysTenant)){
                    record.setThirdChannelFlag(sysTenant.getThirdChannelFlag());
                }
            }
        }
        return TableDataInfo.build(records, orderRefundPage.getTotal());
    }

    /**
     * 获取退款单列表
     */
    @Override
    public List<RefundApplyVo> getOrderRefunds(RefundApplyBo bo) {
        if (ObjectUtil.isNull(bo.getTenantType())){
            bo.setTenantType(LoginHelper.getTenantType());
        }
        if (ObjectUtil.isNull(bo.getTenantId())){
            bo.setTenantId(LoginHelper.getTenantId());
        }

        // 根据日期分解成一天的起始时间和结束时间
        String refundState = bo.getRefundState();
        List<String> refundStates = new ArrayList<>();
        if (StrUtil.isNotBlank(refundState)) {
            refundStates.add(refundState);
        }
        bo.setRefundStates(refundStates);
        // 如果是查询审核中的，那么退款进行中的也要一起查出来
        if (StrUtil.equals(refundState, OrderRefundStateType.Verifying.name())) {
            refundStates.add(OrderRefundStateType.Refunding.name());
            refundStates.add(OrderRefundStateType.ManagerVerifying.name());
        }
        List<RefundApplyVo> records = iOrderRefundService.queryList(bo);
        for (RefundApplyVo record : records) {
            String supplierTenantId = record.getSupplierTenantId();
            String refundApplyTime = record.getRefundApplyTime();
//            String format = DateUtil.format(DateUtil.parseDateTime(refundApplyTime), "yyyy-MM-dd HH:mm:ss");
            record.setRefundApplyTime(refundApplyTime);
            if (ObjectUtil.equals(bo.getTenantType(), TenantType.Supplier.name())) {
                record.setTotal(record.getOriginalRefundAmount());
            }
            record.setSupplierName(supplierTenantId);
        }

        return records;
    }

    /**
     * 提交退款申请
     *
     * @param bo
     * @return
     */
    @Override
    public R<Void> submitRefundApply(SubmitRefundApplyBo bo) {
        return iOrderRefundManger.submitRefundApply(bo);
    }

    /**
     * 提交退款申请(子单)
     *
     * @param bo
     * @return
     */
    @Transactional(rollbackFor = {Exception.class, RStatusCodeException.class})
    public R<Void> submitRefundApplyByOrderItemNo(SubmitRefundApplyBo bo) throws RStatusCodeException {

        TenantType tenantTypeEnum = LoginHelper.getTenantTypeEnum();
        String tenantId = LoginHelper.getTenantId();

        String orderItemNo = bo.getOrderItemNo();
        String refundRuleNo = bo.getRefundRuleNo();
        if (StrUtil.hasBlank(orderItemNo, refundRuleNo)) {
            return R.fail(ZSMallStatusCodeEnum.REQUIRED_CANNOT_EMPTY);
        }

        OrderItem orderItem = iOrderItemService.getByOrderItemNo(orderItemNo);
        if (orderItem != null) {
            if (Objects.equals(orderItem.getOrderState(), OrderStateType.Paid)) {
                // 是否符合退款标准
                Boolean canRefund = canRefund(orderItem);
                if (!canRefund) {
                    return R.fail(ZSMallStatusCodeEnum.ORDER_EXCEEDED_AFTER_SALES_LIMITATIONS);
                }

                LogisticsProgress fulfillment = orderItem.getFulfillmentProgress();
                String applicableFulfillment = fulfillment.name();
                if (ObjectUtil.equals(fulfillment, LogisticsProgress.Fulfilled)) {
                    applicableFulfillment = LogisticsProgress.Dispatched.name();
                }
                OrderRefundRule orderRefundRule = iOrderRefundRuleService.queryByRefundRuleNo(refundRuleNo, applicableFulfillment);
                if (orderRefundRule != null) {
                    Orders order = iOrdersService.queryById(orderItem.getOrderId());
                    OrderItemProductSku orderProductSku = iOrderItemProductSkuService.getByOrderItemId(orderItem.getId());

                    OrderRefund orderRefund = new OrderRefund();
                    orderRefund.setOrderRefundNo(orderCodeGenerator.codeGenerate(BusinessCodeEnum.OrderRefundNo));
                    orderRefund.setRefundRuleNo(refundRuleNo);
                    orderRefund.setRefundApplyTime(new Date());
                    orderRefund.setOrderId(order.getId());
                    orderRefund.setOrderNo(order.getOrderNo());
                    orderRefund.setCurrencyCode(order.getCurrency());
                    orderRefund.setCurrencySymbol(order.getCurrencySymbol());
                    String description = bo.getDescription();
                    if (StrUtil.isNotBlank(description)) {
                        orderRefund.setRefundDescription(description);
                    }

                    String refundReasonZhCn = orderRefundRule.getRefundReasonZhCn();
                    String refundReasonEnUs = orderRefundRule.getRefundReasonEnUs();

                    JSONObject refundRuleReasonJSON = new JSONObject(true);
                    refundRuleReasonJSON.set(LanguageType.zh_CN.name(), refundReasonZhCn);
                    refundRuleReasonJSON.set(LanguageType.en_US.name(), refundReasonEnUs);
                    orderRefund.setRefundRuleReason(refundRuleReasonJSON.toString());

                    OrderItemPrice orderItemPrice = iOrderItemPriceService.queryByOrderItemNo(orderItemNo);
                    BigDecimal platformUnitPrice = orderItemPrice.getPlatformUnitPrice();
                    BigDecimal originalUnitPrice = orderItemPrice.getOriginalUnitPrice();

                    String activityCode = orderProductSku.getActivityCode();

                    orderRefund.setRefundDisputeState(RefundDisputeStateEnum.NotDispute);
                    OrderRefundItem orderRefundItem = new OrderRefundItem();
                    orderRefundItem.setOrderRefundNo(orderRefund.getOrderRefundNo());
                    // 如果是参与活动的订单，需要判断是否退订金
                    log.info("orderItemToRefundItem activityCode = {}", activityCode);
                    if (StrUtil.isNotBlank(activityCode)) {
                        // 活动订金单价和尾款单价
                        BigDecimal platformDepositUnitPrice = orderItemPrice.getPlatformDepositUnitPrice();
                        BigDecimal originalDepositUnitPrice = orderItemPrice.getOriginalDepositUnitPrice();
                        BigDecimal platformBalanceUnitPrice = orderItemPrice.getPlatformBalanceUnitPrice();
                        BigDecimal originalBalanceUnitPrice = orderItemPrice.getOriginalBalanceUnitPrice();

                        // 已发货，单价需要加上尾款
                        if (!ObjectUtil.equals(fulfillment, LogisticsProgress.UnDispatched)) {
                            platformUnitPrice = NumberUtil.add(platformUnitPrice, platformDepositUnitPrice);
                            originalUnitPrice = NumberUtil.add(originalUnitPrice, originalDepositUnitPrice);
                        }
                    }

                    orderRefundItem.setOrderId(order.getId());
                    orderRefundItem.setOrderNo(order.getOrderNo());
                    orderRefundItem.setOrderItemId(orderItem.getId());
                    orderRefundItem.setOrderItemNo(orderItem.getOrderItemNo());
                    orderRefundItem.setProductSkuCode(orderProductSku.getProductSkuCode());
                    orderRefundItem.setActivityCode(orderItem.getActivityCode());
                    orderRefundItem.setActivityType(orderItem.getActivityType());
                    orderRefundItem.setOrderRefundItemNo(orderCodeGenerator.codeGenerate(BusinessCodeEnum.OrderRefundItemNo));
                    List<OrderRefundItem> orderRefundItems = CollUtil.newArrayList(orderRefundItem);

                    // 去除需要工厂处理的退款规则字段，并根据排序号排好
                    List<Field> fields = Arrays.stream(ReflectUtil.getFields(OrderRefundRule.class,
                        field -> AnnotationUtil.hasAnnotation(field, RefundRuleAnalysisAnnotation.class))).sorted(
                        (field1, field2) -> {
                            int order1 = AnnotationUtil.getAnnotation(field1, RefundRuleAnalysisAnnotation.class).order();
                            int order2 = AnnotationUtil.getAnnotation(field2, RefundRuleAnalysisAnnotation.class).order();
                            return NumberUtil.compare(order1, order2);
                        }
                    ).collect(Collectors.toList());
                    for (Field field : fields) {
                        Object fieldValue = ReflectUtil.getFieldValue(orderRefundRule, field);
                        String ruleType = AnnotationUtil.getAnnotation(field, RefundRuleAnalysisAnnotation.class).ruleType();
                        refundRuleFactory.getService(ruleType).refundRuleWhetherHandle(Integer.parseInt(fieldValue.toString()), bo, orderItem, orderRefund, orderRefundItem);
                    }

                    orderRefund.setRefundQuantity(orderRefundItem.getRefundQuantity());
                    // 在工厂中已经设置
                    // orderRefund.setPlatformRefundAmount(orderRefundItem.getPlatformPayableTotalAmount());
                    // orderRefund.setOriginalRefundAmount(orderRefundItem.getOriginalPayableTotalAmount());
                    orderRefund.setSupplierTenantId(orderItem.getSupplierTenantId());
                    orderRefund.setRefundAmountState(RefundAmountStateEnum.NotRefund);


                    iOrderRefundService.saveOrUpdate(orderRefund);
                    List<OrderRefundAttachment> attachments = orderRefund.getAttachments();
                    if (CollUtil.isNotEmpty(attachments)) {
                        iOrderRefundAttachmentService.saveOrUpdateBatch(attachments);
                    }
                    iOrderItemService.saveOrUpdate(orderItem);
                    orderRefundItem.setOrderRefundId(orderRefund.getId());
                    iOrderRefundItemService.saveOrUpdate(orderRefundItem);
                    iOrdersService.saveOrUpdate(order);
                    Set<OrderStateType> orderStateTypes = iOrderItemService.queryOrderStatusTypes(order.getId());
                    if (!orderStateTypes.contains(OrderStateType.Paid) && orderStateTypes.contains(OrderStateType.Verifying)) {
                        order.setOrderState(OrderStateType.Verifying);
                        iOrdersService.saveOrUpdate(order);
                    }
                } else {
                    return R.fail(ZSMallStatusCodeEnum.REFUND_RULE_NOT_EXIST);
                }
            } else {
                return R.fail(ZSMallStatusCodeEnum.ORDER_REFUNDED);
            }
        } else {
            return R.fail(ZSMallStatusCodeEnum.ORDER_ITEM_NOT_EXIST);
        }
        return R.ok();
    }



    /**
     * 是否符合退款标准
     *
     * @param orderItem
     * @return
     */
    private Boolean canRefund(OrderItem orderItem) {
        Integer count = iOrderRefundItemService.countByInProgress(orderItem.getOrderItemNo());
        return orderRefundRuleService.compareRefundRules(orderItem, count == 0 ? true : false);
    }


    /**
     * 补全退货物流信息
     *
     * @param bo
     * @return
     */
    @Override
    public R<Void> supplementRefundInfo(SupplementRefundInfoBo bo) {

        //当前用户ID（也就是改退款单的申请人）
        Long userId = LoginHelper.getUserId();
        String returnType = bo.getReturnType();
        String orderRefundNo = bo.getOrderRefundNo();

        if (StrUtil.isNotBlank(orderRefundNo)) {
            OrderRefund orderRefund = iOrderRefundService.queryByRefundNo(orderRefundNo);
            OrderRefundStateType refundState = orderRefund.getRefundState();
            if (orderRefund != null) {
                List<OrderRefundItem> orderRefundItems = iOrderRefundItemService.queryByOrderRefundNo(orderRefundNo);
                List<TrackingNoBo> trackingInfoList = bo.getTrackingInfoList();

                List<OrderRefundLogistics> saveLogistics = new ArrayList<>();
                for (OrderRefundItem orderRefundItem : orderRefundItems) {
                    if (ObjectUtil.notEqual(refundState, OrderRefundStateType.Refunding)) {
                        return R.fail(ZSMallStatusCodeEnum.REFUND_ITEM_NOT_REFUNDING);
                    }
                    OrderRefundLogistics refundLogistics = iOrderRefundLogisticsService.getByOrderRefundItemId(orderRefundItem.getId());
                    if (refundLogistics == null) {
                        refundLogistics = new OrderRefundLogistics();
                        refundLogistics.setOrderRefundItemId(orderRefundItem.getId());
                        refundLogistics.setOrderRefundId(orderRefundItem.getOrderRefundId());
                    }

                    refundLogistics.setLogisticsReturnType(ReturnLogisticsType.valueOf(returnType));
                    if (StringUtils.equals(returnType, "Myself")) {
                        String carrier = bo.getCarrier();
                        String trackingNo = bo.getTrackingNo();

                        List<String> carrierList = new ArrayList<>();
                        List<String> trackingNoList = new ArrayList<>();
                        if (CollUtil.isNotEmpty(trackingInfoList)) {
                            for (TrackingNoBo reqTrackingNoBody : trackingInfoList) {
                                carrierList.add(reqTrackingNoBody.getCarrier());
                                trackingNoList.add(reqTrackingNoBody.getTrackingNo());
                            }
                        }

                        refundLogistics.setLogisticsCarrier(CollUtil.join(carrierList, ","));
                        refundLogistics.setLogisticsTrackingNo(CollUtil.join(trackingNoList, ","));

                    } else if (StringUtils.equals(returnType, ReturnLogisticsType.Manager.name())) {
                        // String address = requestBody.getPickupAddress();
                        // refundLogistics.setPickupAddress(address);
                    } else {
                        return R.fail(ZSMallStatusCodeEnum.RETURN_TYPE_UNKNOWN);
                    }
                    saveLogistics.add(refundLogistics);
                }
                iOrderRefundLogisticsService.saveOrUpdateBatch(saveLogistics);
            } else {
                return R.fail(ZSMallStatusCodeEnum.REFUND_REQUEST_NOT_EXIST);
            }
        } else {
            String orderRefundItemNo = bo.getOrderRefundItemNo();
            OrderRefundItem refundItem = iOrderRefundItemService.getByOrderRefundItemNo(orderRefundItemNo);
            OrderRefund orderRefund = iOrderRefundService.queryByRefundNo(refundItem.getOrderRefundNo());
            OrderRefundStateType refundState = orderRefund.getRefundState();
            if (refundItem == null) {
                return R.fail(ZSMallStatusCodeEnum.REFUND_REQUEST_NOT_EXIST);
            }

            OrderRefundLogistics refundLogistics = iOrderRefundLogisticsService.getByOrderRefundItemId(refundItem.getOrderItemId());
            if (refundLogistics == null) {
                refundLogistics = new OrderRefundLogistics();
                refundLogistics.setOrderRefundItemId(refundItem.getId());
                refundLogistics.setOrderRefundId(refundItem.getOrderRefundId());
            }

            ReturnLogisticsType logisticsType = ReturnLogisticsType.valueOf(returnType);
            refundLogistics.setLogisticsReturnType(logisticsType);

            if (refundItem != null) {
                if (StringUtils.equals(returnType, ReturnLogisticsType.Myself.name())) {
                    List<TrackingNoBo> trackingInfoList = bo.getTrackingInfoList();
                    String carrier = bo.getCarrier();
                    String trackingNo = bo.getTrackingNo();

                    List<String> carrierList = new ArrayList<>();
                    List<String> trackingNoList = new ArrayList<>();
                    if (CollUtil.isNotEmpty(trackingInfoList)) {
                        for (TrackingNoBo reqTrackingNoBody : trackingInfoList) {
                            carrierList.add(reqTrackingNoBody.getCarrier());
                            trackingNoList.add(reqTrackingNoBody.getTrackingNo());
                        }
                    }

                    refundLogistics.setLogisticsCarrier(CollUtil.join(carrierList, ","));
                    refundLogistics.setLogisticsTrackingNo(CollUtil.join(trackingNoList, ","));
                } else if (StringUtils.equals(returnType, ReturnLogisticsType.Manager.name())) {
                    // String address = requestBody.getPickupAddress();
                    // refundLogistics.setPickupAddress(address);
                } else {
                    return R.fail(ZSMallStatusCodeEnum.RETURN_TYPE_UNKNOWN);
                }

                if (Objects.equals(refundState, OrderRefundStateType.Refunding)) {
                    iOrderRefundLogisticsService.saveOrUpdate(refundLogistics);
                } else {
                    return R.fail(ZSMallStatusCodeEnum.REFUND_ITEM_NOT_REFUNDING);
                }
            } else {
                return R.fail(ZSMallStatusCodeEnum.ORDER_REFUND_NOT_EXIST);
            }
        }
        return R.ok();
    }

    /**
     * 确认收到退货
     *
     * @param bo
     * @return
     */
    @Transactional(rollbackFor = {RStatusCodeException.class, Exception.class})
    @Override
    public R<Void> confirmReceipt(RefundBo bo) throws RStatusCodeException {
        String orderRefundNo = bo.getOrderRefundNo();
        String refundItemNo = bo.getOrderRefundItemNo();

        if (StrUtil.isNotBlank(orderRefundNo)) {  // 主单模式
            OrderRefund orderRefund = iOrderRefundService.queryByRefundNo(orderRefundNo);
            if (orderRefund == null) {
                return R.fail(ZSMallStatusCodeEnum.REFUND_REQUEST_NOT_EXIST);
            }

            Orders order = iOrdersService.getByOrderNo(orderRefund.getOrderNo());
            orderRefund.setRefundState(OrderRefundStateType.Refunded);
            orderRefund.setRefundAmountState(RefundAmountStateEnum.Refunded);

            List<OrderRefundItem> refundItemList = iOrderRefundItemService.queryByOrderRefundNo(orderRefundNo);
            if (CollUtil.isNotEmpty(refundItemList)) {
                Long refundItemId = refundItemList.get(0).getId();
                OrderRefundLogistics refundLogistics = iOrderRefundLogisticsService.getByOrderRefundItemId(refundItemId);
                if (refundLogistics == null) {
                    return R.fail(ZSMallStatusCodeEnum.RETURN_LOGISTICS_INFORMATION_NOT_EXIST);
                }

                // 保存数据
                TenantHelper.ignore(() -> iOrderRefundService.saveOrUpdate(orderRefund));
                TenantHelper.ignore(() -> iOrderRefundItemService.saveOrUpdateBatch(refundItemList));
                // 退款至钱包
                refundToWallet(order.getOrderType(), orderRefund, refundItemList);
            } else {
                return R.fail(ZSMallStatusCodeEnum.CURRENT_REFUND_CANNOT_BE_PROCESSED);
            }
        } else {  // 子单模式
            OrderRefundItem orderRefundItem = iOrderRefundItemService.getByOrderRefundItemNoAndState(refundItemNo, OrderRefundStateType.Refunding);

            if (orderRefundItem != null) {
                OrderRefundLogistics refundLogistics = iOrderRefundLogisticsService.getByOrderRefundItemId(orderRefundItem.getId());
                if (refundLogistics != null) {
                    OrderRefund orderRefund = iOrderRefundService.queryByRefundNo(orderRefundItem.getOrderRefundNo());

                    Orders order = iOrdersService.getByOrderNo(orderRefund.getOrderNo());
                    orderRefund.setRefundState(OrderRefundStateType.Refunded);
                    orderRefund.setRefundAmountState(RefundAmountStateEnum.Refunded);

                    // 保存数据
                    TenantHelper.ignore(() -> iOrderRefundService.saveOrUpdate(orderRefund));
                    // 退款至钱包
                    refundToWallet(order.getOrderType(), orderRefund, CollUtil.newArrayList(orderRefundItem));
                } else {
                    return R.fail(ZSMallStatusCodeEnum.RETURN_LOGISTICS_INFORMATION_NOT_EXIST);
                }
            } else {
                return R.fail(ZSMallStatusCodeEnum.CURRENT_REFUND_CANNOT_BE_PROCESSED);
            }
        }
        return R.ok();
    }

    @InMethodLog(value = "查询售后原因列表")
    @Override
    public R<RefundReasonListVo> queryRefundReasonList() {

        Map<String, List<RefundReasonVo>> reasonMap = new HashMap<>();
        List<String> applicableFulfillment = iOrderRefundRuleService.queryApplicableFulfillment();
        for (String fulfillment : applicableFulfillment) {

            List<OrderRefundRule> orderRefundRuleList =
                iOrderRefundRuleService.queryByApplicableFulfillment(fulfillment);
            if (CollUtil.isNotEmpty(orderRefundRuleList)) {
                List<RefundReasonVo> reasonBodyList =
                    BeanUtil.copyToList(orderRefundRuleList, RefundReasonVo.class);
                reasonMap.put(fulfillment, reasonBodyList);
            }
        }

        RefundReasonListVo respBody = new RefundReasonListVo();
        respBody.setReasonMap(reasonMap);

        return R.ok(respBody);
    }

    /**
     * 获取退款相关选项
     *
     * @return
     */
    @Override
    public R<JSONObject> getRefundReasonOptions() {
        String refundRejectOptions = businessParameterService.getValueFromString(BusinessParameterType.REFUND_OPTIONS);
        JSONObject optionsJson = JSONUtil.parseObj(refundRejectOptions);
        return R.ok(optionsJson);
    }

    /**
     * 退款申请处理
     *
     * @param bo
     * @return
     */
    @Override
    @Transactional(rollbackFor = {StockException.class, RStatusCodeException.class, Exception.class})
    public R<Void> refundApplyHandle(RefundApplyHandleBo bo) throws Exception {
        TenantType tenantType = LoginHelper.getTenantTypeEnum();
        if(ObjectUtil.isNotEmpty(bo.getSupplierCall())&&bo.getSupplierCall()){
            tenantType = TenantType.Supplier;
        }
        // 如果订单未支付
        if (ObjectUtil.isNotEmpty(bo) &&Boolean.TRUE.equals( bo.getIsAuto())) {
            return TenantHelper.ignore(()-> {
                try {
                    return refundApplyHandleSupplierAuto(bo);
                } catch (StockException e) {
                    throw new RuntimeException(e);
                }
            });
        }else {
            // 管理员处理入口,走默认
            if (ObjectUtil.isNotEmpty(tenantType) && ObjectUtil.equals(tenantType, TenantType.Supplier)) {
                List<String> orderRefundNoList = bo.getOrderRefundNoList();
                String orderRefundNo = bo.getOrderRefundNo();
                if(CollUtil.isNotEmpty(orderRefundNoList)){
                    for (String s : orderRefundNoList) {
                        bo.setOrderRefundNo(s);
                        refundApplyHandleSupplier(bo);
                    }
                    return R.ok();
                }else if(CharSequenceUtil.isNotBlank(orderRefundNo)||CharSequenceUtil.isNotBlank(bo.getOrderRefundItemNo())){
                    refundApplyHandleSupplier(bo);
                    return R.ok();
                }
                return R.ok();
            } else {// 管理員处理
                List<String> orderRefundNoList = bo.getOrderRefundNoList();
                String orderRefundNo = bo.getOrderRefundNo();
                String orderRefundItemNo = bo.getOrderRefundItemNo();
                if(CollUtil.isNotEmpty(orderRefundNoList)){
                    for (String s : orderRefundNoList) {
                        bo.setOrderRefundNo(s);
                        refundApplyHandleManager(bo);
                    }
                    return R.ok();
                }else if(CharSequenceUtil.isNotBlank(orderRefundNo)||CharSequenceUtil.isNotBlank(orderRefundItemNo)){
                    refundApplyHandleManager(bo);
                    return R.ok();
                }

                return R.ok();
            }
        }
    }

    /**
     * 功能描述：退款申请处理自动化
     *
     * @param bo
     * @return {@link R }<{@link Void }>
     * <AUTHOR>
     * @date 2025/04/07
     */
    private R<Void> refundApplyHandleSupplierAuto(RefundApplyHandleBo bo) throws StockException {
        String orderRefundNo = bo.getOrderRefundNo();
        String orderRefundItemNo = bo.getOrderRefundItemNo();
        Boolean pass = bo.getPass();
        String reason = bo.getReason();
        List<String> orderRefundNoList = bo.getOrderRefundNoList();
        RefundApplyType refundApplyType;
        refundApplyType = RefundApplyType.CancelAutoRefund;
        if (CollUtil.isNotEmpty(orderRefundNoList)) {
            for (String autoOrderRefundNo : orderRefundNoList) {
                TenantHelper.ignore(()-> {
                    try {
                        refundSupplierAuto(autoOrderRefundNo, orderRefundNo, reason, refundApplyType, pass);
                    } catch (StockException e) {
                        log.error("错误信息",e);
                        throw new RuntimeException(e);
                    }
                });
            }
        } else {
            // 退款主单审批,批发订单退款审批
            if (StrUtil.isNotBlank(orderRefundNo)) {
                log.info("退款主单审批审批: {}", orderRefundNo);
                OrderRefund orderRefund = iOrderRefundService.queryByRefundNo(orderRefundNo);
                if (orderRefund == null) {
                    return R.fail(ZSMallStatusCodeEnum.REFUND_REQUEST_NOT_EXIST);
                }
                Orders order = iOrdersService.getByOrderNo(orderRefund.getOrderNo());
//                OrderType orderType = order.getOrderType();
//                if (ObjectUtil.notEqual(orderType, OrderType.Wholesale)) {
//                    return R.fail(ZSMallStatusCodeEnum.REFUND_REQUEST_NOT_EXIST);
//                }
                OrderRefundStateType refundState = orderRefund.getRefundState();
                OrderStateType orderState = OrderStateType.Paid;
                List<OrderRefundItem> orderRefundItems = iOrderRefundItemService.queryByOrderRefundNo(orderRefundNo);

                OrderRefundStateType refundToWalletOrderRefund = null;
                List<OrderItem> orderItemList = new ArrayList<>();
                List<AdjustStockDTO> adjustStockDTOS = new ArrayList<>();
                if (Objects.equals(refundState, OrderRefundStateType.Verifying)) {
                    orderRefund.setSupplierUserId(LoginHelper.getTenantId());
                    orderRefund.setSupplierReviewOpinion(reason);
                    orderRefund.setSupplierReviewTime(new Date());
                    orderRefund.setRefundType(refundApplyType);
                    if (pass) {
                        orderState = OrderStateType.Refunded;
                        Integer num = orderRefund.getRefundQuantity();
                        orderRefund.setSupplierReviewOpinion("Accept");
                        orderRefund.setRefundState(OrderRefundStateType.Refunding);
                        for (OrderRefundItem orderRefundItem : orderRefundItems) {
                            OrderItem orderItem = iOrderItemService.getByOrderItemNo(orderRefundItem.getOrderItemNo());
                            Integer numItem = orderRefundItem.getRefundQuantity();
                            if (Objects.equals(refundApplyType, RefundApplyType.Refund)) {  // 仅退费的退款单直接发起退款
                                orderRefund.setRefundState(OrderRefundStateType.Refunded);
                                orderRefund.setRefundAmountState(RefundAmountStateEnum.Refunded);
                            }
                            if (numItem > 0) {
                                // 同意退款，并且满足需要退归还库存的条件时，退还库存
                                String productSkuCode = orderRefundItem.getProductSkuCode();
                                Long orderItemId = orderRefundItem.getOrderItemId();
                                OrderItemProductSku orderProductSku = iOrderItemProductSkuService.getByOrderItemId(orderItemId);
                                ProductSku supProductSku = iProductSkuService.queryByProductSkuCode(productSkuCode);
                                if (supProductSku != null) {
                                    AdjustStockDTO adjustStockDTO = new AdjustStockDTO();
                                    adjustStockDTO.setAdjustQuantity(numItem);
                                    adjustStockDTO.setProductSkuCode(supProductSku.getProductSkuCode());
                                    adjustStockDTO.setSpecifyWarehouse(orderProductSku.getWarehouseSystemCode());
                                    adjustStockDTOS.add(adjustStockDTO);

                                    orderItem.setRestockQuantity(numItem);
                                }
                            }
                            orderItemList.add(orderItem);
                        }
                        order.setOrderState(OrderStateType.Refunded);
                    } else {
                        order.setCancelStatus(OrderCancelStateEnum.Failed.getValue());
                        orderRefund.setRefundState(OrderRefundStateType.Reject);
                    }

                    refundToWalletOrderRefund = orderRefund.getRefundState();
                    // 订单状态设置
//                    if (Objects.equals(order.getOrderState(), OrderStateType.Verifying)) {
//                        order.setOrderState(orderState);
//                        for (OrderItem orderItem : orderItemList) {
//                            orderItem.setOrderState(orderState);
//                        }
//                    }

                } else {
                    throw new RStatusCodeException(ZSMallStatusCodeEnum.CURRENT_REFUND_CANNOT_BE_PROCESSED);
                }

                if (pass) {
                    if (CollUtil.isNotEmpty(adjustStockDTOS)) {
                        //退还库存
                        productSkuStockService.adjustStock(adjustStockDTOS);
                    }
                } else {
                    //退款被拒时，退还金额至可执行金额
                    refundToOrderExecutableAmount(order, orderRefund);
                    for (OrderRefundItem orderRefundItem : orderRefundItems) {
                        OrderItem orderItem = iOrderItemService.getByOrderItemNo(orderRefundItem.getOrderItemNo());
                        orderItem.setOrderState(OrderStateType.Paid);
                        refundItemToOrderItemExecutableAmount(orderItem, orderRefund);
                        orderItemList.add(orderItem);
                    }
                    order.setCancelStatus(OrderCancelStateEnum.Failed.getValue());
                }

                TenantHelper.ignore(() -> {
                    iOrdersService.saveOrUpdate(order);
                });
                TenantHelper.ignore(() -> {
                    iOrderItemService.saveOrUpdateBatch(orderItemList);
                });
                TenantHelper.ignore(() -> {
                    iOrderRefundService.updateById(orderRefund);
                });

                //退款到钱包
                if (pass && OrderRefundStateType.Refunded.equals(refundToWalletOrderRefund)) {
                    refundToWallet(order.getOrderType(), orderRefund, orderRefundItems);
                }
            } else if (StringUtils.isNotBlank(orderRefundItemNo)) {
                log.info("单个退款子单审批: {}", orderRefundItemNo);
                OrderRefundItem refundItem = iOrderRefundItemService.getByOrderRefundItemNo(orderRefundItemNo);
                if (refundItem == null) {
                    return R.fail(ZSMallStatusCodeEnum.REFUND_REQUEST_NOT_EXIST);
                }

                Integer refundQuantity = refundItem.getRefundQuantity();
                OrderRefund orderRefund = iOrderRefundService.queryByRefundNo(refundItem.getOrderRefundNo());

                orderRefund.setSupplierUserId(LoginHelper.getTenantId());
                orderRefund.setSupplierReviewOpinion(reason);
                orderRefund.setSupplierReviewTime(new Date());
                orderRefund.setRefundType(refundApplyType);

                OrderItem orderItem = iOrderItemService.getByOrderItemNo(refundItem.getOrderItemNo());
                Orders order = iOrdersService.getByOrderNo(refundItem.getOrderNo());
                OrderItemProductSku orderItemProductSku = iOrderItemProductSkuService.getByOrderItemId(orderItem.getId());
                OrderStateType orderState = OrderStateType.Paid;

                OrderRefundStateType refundState = orderRefund.getRefundState();
                // 退款子单状态未审核中才能继续处理
                if (Objects.equals(refundState, OrderRefundStateType.Verifying)) {
                    if (pass) {
                        orderRefund.setSupplierReviewOpinion("Accept");
                        AdjustStockDTO adjustStockDTO = new AdjustStockDTO();
                        adjustStockDTO.setAdjustQuantity(refundQuantity);
                        adjustStockDTO.setActivityCode(orderItem.getActivityCode());
                        adjustStockDTO.setSpecifyWarehouse(orderItemProductSku.getWarehouseSystemCode());
                        adjustStockDTO.setProductSkuCode(orderItemProductSku.getProductSkuCode());
                        orderState = OrderStateType.Refunded;
                        if (Objects.equals(refundApplyType, RefundApplyType.Refund)) {  // 仅退费的退款单直接发起退款
                            // 若子单未履约，则退款子单直接改为已退，准备退款
                            orderRefund.setRefundState(OrderRefundStateType.Refunded);
                            orderRefund.setRefundAmountState(RefundAmountStateEnum.Refunded);
                        } else {
                            orderRefund.setRefundState(OrderRefundStateType.Refunding);
                        }

                        if (refundQuantity > 0) {
                            // 同意退款，并且满足需要退归还库存的条件时，退还库存
                            adjustStock(adjustStockDTO);
                            orderItem.setRestockQuantity(refundQuantity);
                        }
                    } else {
                        order.setCancelStatus(OrderCancelStateEnum.Failed.getValue());
                        orderRefund.setRefundState(OrderRefundStateType.Reject);
                        refundToOrderExecutableAmount(order, orderRefund);
                        orderItem.setOrderState(OrderStateType.Paid);
                        refundItemToOrderItemExecutableAmount(orderItem, orderRefund);
                    }

                    //订单状态设置
                    if (Objects.equals(order.getOrderState(), OrderStateType.Verifying)) {
                        order.setOrderState(orderState);
                    }
                    if (Objects.equals(orderItem.getOrderState(), OrderStateType.Verifying)) {
                        orderItem.setOrderState(orderState);
                    }

                    TenantHelper.ignore(() -> {
                        iOrdersService.updateById(order);
                    });
                    TenantHelper.ignore(() -> {
                        iOrderItemService.updateById(orderItem);
                    });
                    TenantHelper.ignore(() -> {
                        iOrderRefundService.updateById(orderRefund);
                    });

                    // 判断能否发起退款
                    if (pass && ObjectUtil.equals(orderRefund.getRefundState(), OrderRefundStateType.Refunded)) {  // 可以发起退款
                        refundToWallet(order.getOrderType(), orderRefund, CollUtil.newArrayList(refundItem));
                    }
                } else {
                    return R.fail(ZSMallStatusCodeEnum.CURRENT_REFUND_CANNOT_BE_PROCESSED);
                }
            } else {
                return R.fail(ZSMallStatusCodeEnum.REFUND_REQUEST_NOT_EXIST);
            }
        }


        return null;
    }

    private void refundSupplierAuto(String autoOrderRefundNo, String orderRefundNo, String reason,
                           RefundApplyType refundApplyType, Boolean pass) throws StockException {
        log.info("退款主单审批: {}", autoOrderRefundNo);
        OrderRefund orderRefund = iOrderRefundService.queryByRefundNo(autoOrderRefundNo);
        if (orderRefund == null) {
            throw new RStatusCodeException(ZSMallStatusCodeEnum.REFUND_REQUEST_NOT_EXIST);
        }
        Orders order = iOrdersService.getByOrderNo(orderRefund.getOrderNo());
        OrderRefundStateType refundState = orderRefund.getRefundState();
        OrderStateType orderState = OrderStateType.Paid;
        List<OrderRefundItem> orderRefundItems = iOrderRefundItemService.queryByOrderRefundNo(autoOrderRefundNo);

        OrderRefundStateType refundToWalletOrderRefund = null;
        List<OrderItem> orderItemList = new ArrayList<>();
        List<AdjustStockDTO> adjustStockDTOS = new ArrayList<>();
        if (Objects.equals(refundState, OrderRefundStateType.Verifying)) {

            orderRefund.setSupplierReviewOpinion(reason);
            orderRefund.setSupplierReviewTime(new Date());
            orderRefund.setRefundType(refundApplyType);
            if (pass) {
                orderState = OrderStateType.Refunded;
                Integer num = orderRefund.getRefundQuantity();
                orderRefund.setSupplierReviewOpinion("Accept");

                orderRefund.setRefundState(OrderRefundStateType.Refunding);
                for (OrderRefundItem orderRefundItem : orderRefundItems) {
                    OrderItem orderItem = iOrderItemService.getByOrderItemNo(orderRefundItem.getOrderItemNo());
                    orderRefund.setSupplierUserId(orderItem.getSupplierTenantId());
                    Integer numItem = orderRefundItem.getRefundQuantity();
//                    if (Objects.equals(refundApplyType, RefundApplyType.Refund)) {  // 仅退费的退款单直接发起退款
//                        orderRefund.setRefundState(OrderRefundStateType.Refunded);
//                        orderRefund.setRefundAmountState(RefundAmountStateEnum.Refunded);
//                    }
                    orderRefund.setRefundState(OrderRefundStateType.Refunded);
                    orderRefund.setRefundAmountState(RefundAmountStateEnum.Refunded);
                    if (numItem > 0) {
                        // 同意退款，并且满足需要退归还库存的条件时，退还库存
                        String productSkuCode = orderRefundItem.getProductSkuCode();
                        Long orderItemId = orderRefundItem.getOrderItemId();
                        OrderItemProductSku orderProductSku = iOrderItemProductSkuService.getByOrderItemId(orderItemId);
                        ProductSku supProductSku = iProductSkuService.queryByProductSkuCode(productSkuCode);
                        if (supProductSku != null) {
                            AdjustStockDTO adjustStockDTO = new AdjustStockDTO();
                            adjustStockDTO.setAdjustQuantity(numItem);
                            adjustStockDTO.setProductSkuCode(supProductSku.getProductSkuCode());
                            adjustStockDTO.setSpecifyWarehouse(orderProductSku.getWarehouseSystemCode());
                            adjustStockDTOS.add(adjustStockDTO);

                            orderItem.setRestockQuantity(numItem);
                        }
                    }
                    orderItemList.add(orderItem);
                }
                order.setOrderState(OrderStateType.Refunded);
            } else {
                order.setCancelStatus(OrderCancelStateEnum.Failed.getValue());
                orderRefund.setRefundState(OrderRefundStateType.Reject);
            }

            refundToWalletOrderRefund = orderRefund.getRefundState();
            // 订单状态设置
            if (Objects.equals(order.getOrderState(), OrderStateType.Verifying)) {
                order.setOrderState(orderState);
                for (OrderItem orderItem : orderItemList) {
                    orderItem.setOrderState(orderState);
                }
            }

        } else {
            throw new RStatusCodeException(ZSMallStatusCodeEnum.CURRENT_REFUND_CANNOT_BE_PROCESSED);
        }

        if (pass) {
            if (CollUtil.isNotEmpty(adjustStockDTOS)) {
                //退还库存
                productSkuStockService.adjustStock(adjustStockDTOS);
            }
        } else {
            //退款被拒时，退还金额至可执行金额
            refundToOrderExecutableAmount(order, orderRefund);
            for (OrderRefundItem orderRefundItem : orderRefundItems) {
                OrderItem orderItem = iOrderItemService.getByOrderItemNo(orderRefundItem.getOrderItemNo());
                orderItem.setOrderState(OrderStateType.Paid);
                orderRefund.setSupplierUserId(orderItem.getSupplierTenantId());
                refundItemToOrderItemExecutableAmount(orderItem, orderRefund);
                orderItemList.add(orderItem);
            }
            order.setCancelStatus(OrderCancelStateEnum.Failed.getValue());
        }

        TenantHelper.ignore(() -> {
            iOrdersService.saveOrUpdate(order);
        });
        TenantHelper.ignore(() -> {
            iOrderItemService.saveOrUpdateBatch(orderItemList);
        });
        TenantHelper.ignore(() -> {
            iOrderRefundService.updateById(orderRefund);
        });

        //退款到钱包
        if (pass && OrderRefundStateType.Refunded.equals(refundToWalletOrderRefund)) {
            refundToWallet(order.getOrderType(), orderRefund, orderRefundItems);
//            throw new RuntimeException("测试事务");
        }
    }

    /**
     * 供应商售后申请处理
     *
     * @param bo
     * @return
     */
    public R<Void> refundApplyHandleSupplier(RefundApplyHandleBo bo) throws Exception {
        String orderRefundNo = bo.getOrderRefundNo();
        String orderRefundItemNo = bo.getOrderRefundItemNo();
        Boolean pass = bo.getPass();
        String reason = bo.getReason();
        String reqRefundApplyType = bo.getRefundApplyType();

        RefundApplyType refundApplyType = null;
        if (StringUtils.isNotBlank(reqRefundApplyType)) {
            refundApplyType = RefundApplyType.valueOf(reqRefundApplyType);
        }

        // 退款主单审批,批发订单退款审批
        if (StrUtil.isNotBlank(orderRefundNo)) {
            log.info("退款主单审批审批: {}", orderRefundNo);
            OrderRefund orderRefund = iOrderRefundService.queryByRefundNo(orderRefundNo);
            if (orderRefund == null) {
                return R.fail(ZSMallStatusCodeEnum.REFUND_REQUEST_NOT_EXIST);
            }
            Orders order = iOrdersService.getByOrderNo(orderRefund.getOrderNo());
            OrderRefundStateType refundState = orderRefund.getRefundState();
            OrderStateType orderState = OrderStateType.Paid;
            List<OrderRefundItem> orderRefundItems = iOrderRefundItemService.queryByOrderRefundNo(orderRefundNo);

            OrderRefundStateType refundToWalletOrderRefund = null;
            List<OrderItem> orderItemList = new ArrayList<>();
            List<AdjustStockDTO> adjustStockDTOS = new ArrayList<>();
            if (Objects.equals(refundState, OrderRefundStateType.Verifying)) {
                orderRefund.setSupplierUserId(LoginHelper.getTenantId());
                orderRefund.setSupplierReviewOpinion(reason);
                orderRefund.setSupplierReviewTime(new Date());
                orderRefund.setRefundType(refundApplyType);
                if (pass) {
                    orderRefund.setSupplierReviewOpinion("Accept");
                    orderRefund.setRefundState(OrderRefundStateType.Refunding);
                    for (OrderRefundItem orderRefundItem : orderRefundItems) {
                        OrderItem orderItem = iOrderItemService.getByOrderItemNo(orderRefundItem.getOrderItemNo());
                        Integer numItem = orderRefundItem.getRefundQuantity();
                        if (Objects.equals(refundApplyType, RefundApplyType.Refund)) {  // 仅退费的退款单直接发起退款
                            orderRefund.setRefundState(OrderRefundStateType.Refunded);
                            orderRefund.setRefundAmountState(RefundAmountStateEnum.Refunded);
                        }
                        if (Objects.equals(refundApplyType, RefundApplyType.CancelAutoRefund)) {  // 仅退费的退款单直接发起退款
                            orderRefund.setRefundState(OrderRefundStateType.Refunded);
                            orderRefund.setRefundAmountState(RefundAmountStateEnum.Refunded);
                        }
                        if (numItem > 0) {
                            // 同意退款，并且满足需要退归还库存的条件时，退还库存
                            String productSkuCode = orderRefundItem.getProductSkuCode();
                            Long orderItemId = orderRefundItem.getOrderItemId();
                            OrderItemProductSku orderProductSku = iOrderItemProductSkuService.getByOrderItemId(orderItemId);
                            ProductSku supProductSku = iProductSkuService.queryByProductSkuCode(productSkuCode);
                            if (supProductSku != null) {
                                AdjustStockDTO adjustStockDTO = new AdjustStockDTO();
                                adjustStockDTO.setAdjustQuantity(numItem);
                                adjustStockDTO.setProductSkuCode(supProductSku.getProductSkuCode());
                                adjustStockDTO.setSpecifyWarehouse(orderProductSku.getWarehouseSystemCode());
                                adjustStockDTOS.add(adjustStockDTO);

                                orderItem.setRestockQuantity(numItem);
                            }
                        }
                        orderItemList.add(orderItem);
                    }
                    order.setOrderState(OrderStateType.Refunded);
                } else {
                    order.setCancelStatus(OrderCancelStateEnum.Failed.getValue());
                    order.setOrderState(OrderStateType.Paid);
                    orderRefund.setRefundState(OrderRefundStateType.Reject);
                }

                refundToWalletOrderRefund = orderRefund.getRefundState();
            } else {
                throw new RStatusCodeException(ZSMallStatusCodeEnum.CURRENT_REFUND_CANNOT_BE_PROCESSED);
            }

            if (pass) {
                if (CollUtil.isNotEmpty(adjustStockDTOS)) {
                    //退还库存
                    productSkuStockService.adjustStock(adjustStockDTOS);
                }
            } else {
                //退款被拒时，退还金额至可执行金额
                refundToOrderExecutableAmount(order, orderRefund);
                for (OrderRefundItem orderRefundItem : orderRefundItems) {
                    OrderItem orderItem = iOrderItemService.getByOrderItemNo(orderRefundItem.getOrderItemNo());
                    refundItemToOrderItemExecutableAmount(orderItem, orderRefund);
                    orderItem.setOrderState(OrderStateType.Paid);
                    orderItemList.add(orderItem);
                }
                order.setCancelStatus(OrderCancelStateEnum.Failed.getValue());
            }

            TenantHelper.ignore(() -> {
                iOrdersService.saveOrUpdate(order);
            });
            TenantHelper.ignore(() -> {
                iOrderItemService.saveOrUpdateBatch(orderItemList);
            });
            TenantHelper.ignore(() -> {
                iOrderRefundService.updateById(orderRefund);
            });

            //退款到钱包
            if (pass && OrderRefundStateType.Refunded.equals(refundToWalletOrderRefund)) {
                refundToWallet(order.getOrderType(), orderRefund, orderRefundItems);
            }
        } else if (StringUtils.isNotBlank(orderRefundItemNo)) {
            log.info("单个退款子单审批: {}", orderRefundItemNo);
            OrderRefundItem refundItem = iOrderRefundItemService.getByOrderRefundItemNo(orderRefundItemNo);
            if (refundItem == null) {
                return R.fail(ZSMallStatusCodeEnum.REFUND_REQUEST_NOT_EXIST);
            }

            Integer refundQuantity = refundItem.getRefundQuantity();
            OrderRefund orderRefund = iOrderRefundService.queryByRefundNo(refundItem.getOrderRefundNo());

            orderRefund.setSupplierUserId(LoginHelper.getTenantId());
            orderRefund.setSupplierReviewOpinion(reason);
            orderRefund.setSupplierReviewTime(new Date());
            orderRefund.setRefundType(refundApplyType);

            OrderItem orderItem = iOrderItemService.getByOrderItemNo(refundItem.getOrderItemNo());
            Orders order = iOrdersService.getByOrderNo(refundItem.getOrderNo());
            OrderItemProductSku orderItemProductSku = iOrderItemProductSkuService.getByOrderItemId(orderItem.getId());
            OrderStateType orderState = OrderStateType.Paid;

            OrderRefundStateType refundState = orderRefund.getRefundState();
            // 退款子单状态未审核中才能继续处理
            if (Objects.equals(refundState, OrderRefundStateType.Verifying)) {
                if (pass) {
                    orderRefund.setSupplierReviewOpinion("Accept");
                    AdjustStockDTO adjustStockDTO = new AdjustStockDTO();
                    adjustStockDTO.setAdjustQuantity(refundQuantity);
                    adjustStockDTO.setActivityCode(orderItem.getActivityCode());
                    adjustStockDTO.setSpecifyWarehouse(orderItemProductSku.getWarehouseSystemCode());
                    adjustStockDTO.setProductSkuCode(orderItemProductSku.getProductSkuCode());
                    orderState = OrderStateType.Refunded;
                    if (Objects.equals(refundApplyType, RefundApplyType.Refund)) {  // 仅退费的退款单直接发起退款
                        // 若子单未履约，则退款子单直接改为已退，准备退款
                        orderRefund.setRefundState(OrderRefundStateType.Refunded);
                        orderRefund.setRefundAmountState(RefundAmountStateEnum.Refunded);
                    } else {
                        orderRefund.setRefundState(OrderRefundStateType.Refunding);
                    }

                    if (refundQuantity > 0) {
                        // 同意退款，并且满足需要退归还库存的条件时，退还库存
                        adjustStock(adjustStockDTO);
                        orderItem.setRestockQuantity(refundQuantity);
                    }
                } else {
                    order.setCancelStatus(OrderCancelStateEnum.Failed.getValue());
                    orderRefund.setRefundState(OrderRefundStateType.Reject);
                    orderItem.setOrderState(OrderStateType.Paid);
                    refundToOrderExecutableAmount(order, orderRefund);
                    refundItemToOrderItemExecutableAmount(orderItem, orderRefund);
                }

                //订单状态设置
                if (Objects.equals(order.getOrderState(), OrderStateType.Verifying)) {
                    order.setOrderState(orderState);
                }
                if (Objects.equals(orderItem.getOrderState(), OrderStateType.Verifying)) {
                    orderItem.setOrderState(orderState);
                }

                TenantHelper.ignore(() -> {
                    iOrdersService.updateById(order);
                });
                TenantHelper.ignore(() -> {
                    iOrderItemService.updateById(orderItem);
                });
                TenantHelper.ignore(() -> {
                    iOrderRefundService.updateById(orderRefund);
                });

                // 判断能否发起退款
                if (pass && ObjectUtil.equals(orderRefund.getRefundState(), OrderRefundStateType.Refunded)) {  // 可以发起退款
                    refundToWallet(order.getOrderType(), orderRefund, CollUtil.newArrayList(refundItem));
                }
            } else {
                return R.fail(ZSMallStatusCodeEnum.CURRENT_REFUND_CANNOT_BE_PROCESSED);
            }
        } else {
            return R.fail(ZSMallStatusCodeEnum.REFUND_REQUEST_NOT_EXIST);
        }
        return R.ok();
    }

    /**
     * 平台售后申请处理
     *
     * @param bo
     * @return
     */
    public R<Void> refundApplyHandleManager(RefundApplyHandleBo bo) {
        String orderRefundNo = bo.getOrderRefundNo();
        String orderRefundItemNo = bo.getOrderRefundItemNo();
        Boolean pass = bo.getPass();
        String reason = bo.getReason();
        String reqRefundApplyType = bo.getRefundApplyType();
        RefundApplyType refundApplyType = null;
        if (StringUtils.isNotBlank(reqRefundApplyType)) {
            refundApplyType = RefundApplyType.valueOf(reqRefundApplyType);
        }
        List<String> orderRefundNoList = bo.getOrderRefundNoList();
        if(CollUtil.isNotEmpty(orderRefundNoList)){
            for (String approveOrderRefundNo : orderRefundNoList) {
                try{
                    orderRefundApprove(approveOrderRefundNo,reason,pass);
                }catch (Exception e){
                    log.error("退款单审批失败",e);
                    throw new RuntimeException();
                }
            }
        }else {
            if (StrUtil.isNotBlank(orderRefundNo)) {  // 主单模式
                log.info("整个退款单审批: {}", orderRefundNo);
                OrderRefund orderRefund = iOrderRefundService.queryByRefundNo(orderRefundNo);
                if (orderRefund == null) {
                    return R.fail(ZSMallStatusCodeEnum.REFUND_REQUEST_NOT_EXIST);
                }
                Orders order = iOrdersService.getByOrderNo(orderRefund.getOrderNo());
                OrderStateType orderState = order.getOrderState();
                OrderRefundStateType refundState = orderRefund.getRefundState();

                List<OrderRefundItem> orderRefundItemList = iOrderRefundItemService.queryByOrderRefundNo(orderRefundNo);
                orderRefund.setManagerReviewOpinion(reason);
                orderRefund.setManagerReviewTime(new Date());
                orderRefund.setManagerUserId(LoginHelper.getTenantId());
                List<OrderItem> orderItems = new ArrayList<>();
                // 退款子单状态员工审核中的状态才能继续处理
                if (Objects.equals(refundState, OrderRefundStateType.ManagerVerifying)) {
                    if (pass) {
                        order.setOrderState(OrderStateType.Refunded);
                        order.setCancelStatus(OrderCancelStateEnum.Canceled.getValue());
                        orderRefund.setManagerReviewOpinion("Accept");
                        orderRefund.setRefundState(OrderRefundStateType.Refunded);
                    } else {
                        for (OrderRefundItem refundItem : orderRefundItemList) {
                            OrderItem orderItem = iOrderItemService.getByOrderItemNo(refundItem.getOrderItemNo());
                            OrderStateType orderItemState = orderItem.getOrderState();
                            if (Objects.equals(orderItemState, OrderStateType.Verifying)) {
                                orderItem.setOrderState(OrderStateType.Paid);
                                orderItems.add(orderItem);
                            }
                        }
                        orderRefund.setRefundState(OrderRefundStateType.Reject);
                        if (Objects.equals(orderState, OrderStateType.Verifying)) {
                            order.setOrderState(OrderStateType.Paid);
                        }
                        order.setCancelStatus(OrderCancelStateEnum.Failed.getValue());
                    }
                } else {
                    return R.fail(ZSMallStatusCodeEnum.CURRENT_REFUND_CANNOT_BE_PROCESSED);
                }
                if (!pass) {
                    refundToOrderExecutableAmount(order, orderRefund);
                    for (OrderRefundItem orderRefundItem : orderRefundItemList) {
                        OrderItem orderItem = iOrderItemService.getByOrderItemNo(orderRefundItem.getOrderItemNo());
                        refundItemToOrderItemExecutableAmount(orderItem, orderRefund);
                        orderItems.add(orderItem);
                    }
                }
                extracted(orderRefund, orderItems, order);
            } else {
                /**子单模式*/
                log.info("单个退款子单审批: {}", orderRefundItemNo);
                OrderRefundItem refundItemOne = iOrderRefundItemService.getByOrderRefundItemNo(orderRefundItemNo);
                OrderRefund orderRefund = iOrderRefundService.queryByRefundNo(refundItemOne.getOrderRefundNo());
                orderRefund.setManagerUserId(LoginHelper.getTenantId());
                orderRefund.setManagerReviewOpinion(reason);
                orderRefund.setManagerReviewTime(new Date());

                Orders order = iOrdersService.getByOrderNo(orderRefund.getOrderNo());
                OrderItem orderItem = iOrderItemService.getByOrderItemNo(refundItemOne.getOrderItemNo());

                OrderRefundStateType refundState = orderRefund.getRefundState();
                // 退款子单状态员工审核中的状态才能继续处理
                if (Objects.equals(refundState, OrderRefundStateType.ManagerVerifying)) {
                    if (pass) {
                        orderRefund.setManagerReviewOpinion("Accept");
                        orderRefund.setRefundState(OrderRefundStateType.Verifying);
                    } else {
                        order.setCancelStatus(OrderCancelStateEnum.Failed.getValue());
                        orderRefund.setRefundState(OrderRefundStateType.Reject);
                        orderItem.setOrderState(OrderStateType.Paid);
                        if (Objects.equals(order.getOrderState(), OrderStateType.Verifying)) {
                            order.setOrderState(OrderStateType.Paid);
                        }
                        if (Objects.equals(orderItem.getOrderState(), OrderStateType.Verifying)) {
                            orderItem.setOrderState(OrderStateType.Paid);
                        }
                        refundToOrderExecutableAmount(order, orderRefund);
                        refundItemToOrderItemExecutableAmount(orderItem, orderRefund);
                    }
                    TenantHelper.ignore(() -> iOrderRefundService.saveOrUpdate(orderRefund));
                    TenantHelper.ignore(() -> iOrderItemService.saveOrUpdate(orderItem));
                    TenantHelper.ignore(() -> iOrdersService.saveOrUpdate(order));
                } else {
                    return R.fail(ZSMallStatusCodeEnum.CURRENT_REFUND_CANNOT_BE_PROCESSED);
                }
            }
        }

        return R.ok();
    }

    private void extracted(OrderRefund orderRefund, List<OrderItem> orderItems, Orders order) {
        TenantHelper.ignore(() -> iOrderRefundService.saveOrUpdate(orderRefund));
        TenantHelper.ignore(() -> iOrderItemService.updateBatchById(orderItems));
        TenantHelper.ignore(() -> iOrdersService.saveOrUpdate(order));
    }

    /**
     * 功能描述：订单退款批准
     *
     * @param orderRefundNo 订单退款编号
     * @param reason        原因
     * @param pass          通过
     * <AUTHOR>
     * @date 2025/03/14
     */
    private void orderRefundApprove(String orderRefundNo,String reason,boolean pass) {
        log.info("整个退款单审批: {}", orderRefundNo);
        OrderRefund orderRefund = iOrderRefundService.queryByRefundNo(orderRefundNo);
        if (orderRefund == null) {
            throw new RStatusCodeException(ZSMallStatusCodeEnum.REFUND_REQUEST_NOT_EXIST);
        }
        Orders order = iOrdersService.getByOrderNo(orderRefund.getOrderNo());
        OrderStateType orderState = order.getOrderState();
//        OrderType orderType = order.getOrderType();
//        if (ObjectUtil.notEqual(orderType, OrderType.Wholesale)) {
//            throw new RStatusCodeException(ZSMallStatusCodeEnum.REFUND_REQUEST_NOT_EXIST);}

        Long orderRefundId = orderRefund.getId();
        OrderRefundStateType refundState = orderRefund.getRefundState();

        List<OrderRefundItem> orderRefundItemList = iOrderRefundItemService.queryByOrderRefundNo(orderRefundNo);
        orderRefund.setManagerReviewOpinion(reason);
        orderRefund.setManagerReviewTime(new Date());
        orderRefund.setManagerUserId(LoginHelper.getTenantId());
        List<OrderItem> orderItems = new ArrayList<>();
        // 退款子单状态员工审核中的状态才能继续处理
        if (Objects.equals(refundState, OrderRefundStateType.ManagerVerifying)) {
            if (pass) {
                orderRefund.setManagerReviewOpinion("Accept");
                orderRefund.setRefundState(OrderRefundStateType.Verifying);
            } else {
                for (OrderRefundItem refundItem : orderRefundItemList) {
                    OrderItem orderItem = iOrderItemService.getByOrderItemNo(refundItem.getOrderItemNo());
                    OrderStateType orderItemState = orderItem.getOrderState();
                    if (Objects.equals(orderItemState, OrderStateType.Verifying)) {
                        orderItem.setOrderState(OrderStateType.Paid);
                        orderItems.add(orderItem);
                    }
                }
                orderRefund.setRefundState(OrderRefundStateType.Reject);
                if (Objects.equals(orderState, OrderStateType.Verifying)) {
                    order.setOrderState(OrderStateType.Paid);
                }

                order.setCancelStatus(OrderCancelStateEnum.Failed.getValue());
            }
        } else {
            throw new RStatusCodeException(ZSMallStatusCodeEnum.CURRENT_REFUND_CANNOT_BE_PROCESSED);
        }
        if (!pass) {
            refundToOrderExecutableAmount(order, orderRefund);
            for (OrderRefundItem orderRefundItem : orderRefundItemList) {
                OrderItem orderItem = iOrderItemService.getByOrderItemNo(orderRefundItem.getOrderItemNo());
                orderItem.setOrderState(OrderStateType.Paid);
                refundItemToOrderItemExecutableAmount(orderItem, orderRefund);
                orderItems.add(orderItem);
            }
        }
        TenantHelper.ignore(() -> iOrderRefundService.saveOrUpdate(orderRefund));
        TenantHelper.ignore(() -> iOrderItemService.updateBatchById(orderItems));
        TenantHelper.ignore(() -> iOrdersService.saveOrUpdate(order));
    }

    /**
     * 退款被拒时，退还金额至子单可执行金额
     *
     * @param orderRefund
     * @param orderItem
     */
    private void refundItemToOrderItemExecutableAmount(OrderItem orderItem, OrderRefund orderRefund) {
        BigDecimal platformRefundAmount = orderRefund.getPlatformRefundAmount();
        BigDecimal originalRefundAmount = orderRefund.getOriginalRefundAmount();
        BigDecimal platformRefundExecutableAmount = orderItem.getPlatformRefundExecutableAmount();
        BigDecimal originalRefundExecutableAmount = orderItem.getOriginalRefundExecutableAmount();
        platformRefundExecutableAmount = NumberUtil.add(platformRefundAmount, platformRefundExecutableAmount);
        originalRefundExecutableAmount = NumberUtil.add(originalRefundAmount, originalRefundExecutableAmount);
        orderItem.setPlatformRefundExecutableAmount(platformRefundExecutableAmount);
        orderItem.setOriginalRefundExecutableAmount(originalRefundExecutableAmount);
    }


    /**
     * 退款被拒时，退还金额至可执行金额
     *
     * @param order
     * @param orderRefund
     */
    private void refundToOrderExecutableAmount(Orders order, OrderRefund orderRefund) {
        String orderRefundNo = orderRefund.getOrderRefundNo();
        BigDecimal platformRefundExecutableAmount_order = order.getPlatformRefundExecutableAmount();
        BigDecimal originalRefundExecutableAmount_order = order.getOriginalRefundExecutableAmount();

        BigDecimal refundPrice = orderRefund.getPlatformRefundAmount();
        BigDecimal refundPriceSup = orderRefund.getOriginalRefundAmount();
        log.info("refundToExecutableAmount orderRefundNo = {} platformRefundExecutableAmount_order = {}, originalRefundExecutableAmount_order = {}",
            orderRefundNo, platformRefundExecutableAmount_order, originalRefundExecutableAmount_order);
        log.info("refundToExecutableAmount orderRefundNo = {} refundPrice = {}, refundPriceSup = {}",
            orderRefundNo, refundPrice, refundPriceSup);


        platformRefundExecutableAmount_order = NumberUtil.add(NumberUtil.toBigDecimal(refundPrice), platformRefundExecutableAmount_order);
        originalRefundExecutableAmount_order = NumberUtil.add(refundPriceSup, originalRefundExecutableAmount_order);
        log.info("refundToExecutableAmount orderRefundNo = {} platformRefundExecutableAmount_order(New) = {}, originalRefundExecutableAmount_order(New) = {}",
            orderRefundNo, platformRefundExecutableAmount_order, originalRefundExecutableAmount_order);
        order.setPlatformRefundExecutableAmount(platformRefundExecutableAmount_order);
        order.setOriginalRefundExecutableAmount(originalRefundExecutableAmount_order);

    }


    @InMethodLog(value = "普通订单退款单详情数据构建")
    private RefundDetailVo getRefundItemDetailNormal(OrderRefund orderRefund, TenantType tenantType) {
        RefundItemToDTO itemToDTO = refundItemToVo(orderRefund);
        RefundDetailVo refundDetailVo = BeanUtil.copyProperties(itemToDTO, RefundDetailVo.class);
        if (ObjectUtil.equals(tenantType, TenantType.Supplier)) {
            refundDetailVo.setRefundPrice(orderRefund.getOriginalRefundAmount());
        } else {
            refundDetailVo.setRefundPrice(orderRefund.getPlatformRefundAmount());
        }

        Orders order = itemToDTO.getOrders();

        String orderNo = order.getOrderNo();
        OrderAddressInfo orderAddressInfo = iOrderAddressInfoService.getByOrderNoAndAddressType(orderNo, OrderAddressType.ShipAddress);

        String recipient = orderAddressInfo.getRecipient();
        String phoneNumber = orderAddressInfo.getPhoneNumber();
        String orderRemark = order.getOrderNote();

        IntactAddressInfoVo intactAddressInfoVo = orderSupport.addressToBody(orderAddressInfo);

        RefundUserInfoVo userInfoVo = new RefundUserInfoVo();
        userInfoVo.setCustomerName(recipient);
        userInfoVo.setPhoneNumber(phoneNumber);
        OrderAddressType addressType = orderAddressInfo.getAddressType();
        if (ObjectUtil.equals(addressType, OrderAddressType.BillAddress)) {
            userInfoVo.setBillingAddress(intactAddressInfoVo);
        } else {
            userInfoVo.setShippingAddress(intactAddressInfoVo);
        }

        List<OrderRefundItem> orderRefundItems = itemToDTO.getOrderRefundItems();

        // 准备记录总金额、订金总金额、退款总金额
        RefundPriceVo refundPriceVo = new RefundPriceVo();
        List<RefundItemDetailVo> refundItemDetail = new ArrayList<>();

        for (OrderRefundItem orderRefundItem : orderRefundItems) {

            RefundItemDetailVo refundItemDetailVo = new RefundItemDetailVo();
            OrderItemProductSku orderProductSku = iOrderItemProductSkuService.getByOrderItemId(orderRefundItem.getOrderItemId());
            // 处理退款子订单信息
            dealRefundItemVo(tenantType, refundItemDetailVo, orderRefund
                , orderRefundItem, orderProductSku, refundPriceVo);

            // 处理退款轨迹
            List<RefundStageDTO> stages = generateStages(orderRefund);
            Collections.reverse(stages);
            refundItemDetailVo.setStages(stages);

            refundItemDetail.add(refundItemDetailVo);

        }
        RefundItemDetailVo detailVo = refundItemDetail.get(0);
        List<RefundStageDTO> stages = detailVo.getStages();

        for (RefundStageDTO stage : stages) {
            if("Distributor".equals(stage.getRole())){
                //
                String textEnUs = stage.getText_en_US();
                String textZhCn = stage.getText_zh_CN();
                if((StrUtil.isEmpty(textEnUs)&&StrUtil.isEmpty(textZhCn))||
                    ("null".equals(textEnUs)&&"null".equals(textZhCn))){
                    refundDetailVo.setIsNeedShow(Boolean.FALSE);
                }else {
                    refundDetailVo.setIsNeedShow(Boolean.TRUE);
                }
            }
        }

        // 如果当前用户是员工，则需要显示退货率相关信息
        if (Objects.equals(tenantType, TenantType.Manager)) {
            // 供货商名称
            userInfoVo.setSupplierName(itemToDTO.getSupplierName());
        }

        refundDetailVo.setTotalPrice(refundPriceVo.getTotalPrice());
        refundDetailVo.setRefundPrice(refundPriceVo.getRefundTotalPrice());

        boolean isSupplier = false;
        if (ObjectUtil.equals(tenantType, TenantType.Supplier)) {
            isSupplier = true;
        }
        BigDecimal sumByRefundPrice = iOrderRefundService.sumByRefundPrice(order.getId(), isSupplier);
        refundDetailVo.setCumulativeRefundPrice(sumByRefundPrice);
        refundDetailVo.setDepositTotalPrice(refundPriceVo.getDepositTotalPrice());

        refundDetailVo.setUserInfo(userInfoVo);
        refundDetailVo.setRefundItemDetail(refundItemDetail);

        return refundDetailVo;
    }

    /**
     * 处理子订单详情
     */
    private void dealRefundItemVo(TenantType tenantType, RefundItemDetailVo refundItemDetailVo, OrderRefund orderRefund
        , OrderRefundItem orderRefundItem, OrderItemProductSku orderItemProductSku, RefundPriceVo refundPriceVo) {
        BigDecimal refundTotalPrice = refundPriceVo.getRefundTotalPrice();
        BigDecimal totalPrice = refundPriceVo.getTotalPrice();
        BigDecimal depositTotalPrice = refundPriceVo.getDepositTotalPrice();
        String orderRefundItemNo = orderRefundItem.getOrderRefundItemNo();
        OrderRefundStateType refundState = orderRefund.getRefundState();

        String orderItemNo = orderRefundItem.getOrderItemNo();

        OrderItem orderItem = iOrderItemService.getByOrderItemNo(orderItemNo);
        LogisticsProgress fulfillmentProgress = orderItem.getFulfillmentProgress();
        String name = orderItemProductSku.getProductName();
        String description = orderItemProductSku.getDescription();
        String imageShowUrl = orderItemProductSku.getImageShowUrl();
        String warehouseSystemCode = orderItemProductSku.getWarehouseSystemCode();
        String sku = orderItemProductSku.getSku();
        String productSkuCode = orderItemProductSku.getProductSkuCode();

        // 统计退款金额与订单总金额
        BigDecimal totalPriceBD = NumberUtil.toBigDecimal(orderItem.getPlatformPayableTotalAmount());
        BigDecimal refundPriceBD = NumberUtil.toBigDecimal(orderRefund.getPlatformRefundAmount());
        OrderItemPrice orderItemPrice = iOrderItemPriceService.queryByOrderItemNo(orderItemNo);

        BigDecimal platformDepositUnitPrice = orderItemPrice.getPlatformDepositUnitPrice();
        if (ObjectUtil.equals(tenantType, TenantType.Supplier)) {
            totalPriceBD = NumberUtil.toBigDecimal(orderItem.getOriginalPayableTotalAmount());
            refundPriceBD = NumberUtil.toBigDecimal(orderRefund.getOriginalRefundAmount());
            platformDepositUnitPrice = orderItemPrice.getOriginalDepositUnitPrice();
        }
        totalPrice = NumberUtil.add(totalPrice, totalPriceBD);
        refundTotalPrice = NumberUtil.add(refundTotalPrice, refundPriceBD);

        if (platformDepositUnitPrice != null) {
            Integer num = orderItem.getTotalQuantity();
            BigDecimal depositTotalPriceItem = NumberUtil.mul(platformDepositUnitPrice, num);
            depositTotalPrice = NumberUtil.add(depositTotalPrice, depositTotalPriceItem);
        }

        refundPriceVo.setRefundTotalPrice(refundTotalPrice);
        refundPriceVo.setTotalPrice(totalPrice);
        refundPriceVo.setDepositTotalPrice(depositTotalPrice);
        refundItemDetailVo.setSku(sku);
        refundItemDetailVo.setOrderItemNo(orderItemNo);
        refundItemDetailVo.setOrderRefundItemNo(orderRefundItemNo);
        refundItemDetailVo.setItemNo(productSkuCode);
        refundItemDetailVo.setImageShowUrl(imageShowUrl);
        refundItemDetailVo.setFulfillment(fulfillmentProgress.name());
        refundItemDetailVo.setProductName(name);

        List<OrderItemTrackingRecord> trackingList = iOrderItemTrackingRecordService.getListByOrderItemNo(orderItemNo);
        if (CollUtil.isNotEmpty(trackingList)) {
            List<String> trackingNoList = new ArrayList<>();
            List<TrackingNoBo> trackingInfoList = new ArrayList<>();

            for (OrderItemTrackingRecord trackingRecord : trackingList) {
                String carrier = trackingRecord.getLogisticsCarrier();
                String trackingNo = trackingRecord.getLogisticsTrackingNo();
                refundItemDetailVo.setCarrier(carrier);
                trackingNoList.add(trackingNo);
                trackingInfoList.add(new TrackingNoBo(carrier, trackingNo));
            }
            refundItemDetailVo.setTrackingNo(CollUtil.join(trackingNoList, "\n"));
            refundItemDetailVo.setTrackingInfoList(trackingInfoList);
        }
        if (refundState != null) {
            refundItemDetailVo.setRefundItemStatus(OrderRefundStateType.valueOf(refundState.name()).getShowName());
        }
        OrderRefundLogistics refundLogistics = iOrderRefundLogisticsService.getByOrderRefundItemId(orderRefundItem.getId());
        if (Objects.equals(refundState, OrderRefundStateType.Refunding)) {
            if (refundLogistics != null) {
                refundItemDetailVo.setNeedSupplement(false);
            } else {
                refundItemDetailVo.setNeedSupplement(true);
            }
        }

        if (refundLogistics != null) {
            orderRefund.setOrderRefundLogistics(refundLogistics);
            ReturnLogisticsType returnLogisticsType = refundLogistics.getLogisticsReturnType();
            if (Objects.equals(returnLogisticsType, ReturnLogisticsType.Myself)) {
                refundItemDetailVo.setLogisticsType("Return by Myself");
            } else if (Objects.equals(returnLogisticsType, ReturnLogisticsType.Manager)) {
                refundItemDetailVo.setLogisticsType("Return by Manager");
            }
        }
    }

    @InMethodLog(value = "批发订单退款单详情数据构建")
    private RefundDetailVo getRefundItemDetailWholesale(Orders order, OrderRefund orderRefund, TenantType tenantType) {
        RefundItemToDTO itemToDTO = refundItemToVo(orderRefund);
        RefundDetailVo refundDetailVo = BeanUtil.copyProperties(itemToDTO, RefundDetailVo.class);
        String orderNo = order.getOrderNo();
        WholesaleIntentionOrder wiOrder = iWholesaleIntentionOrderService.queryByOrderNo(orderNo);

        BigDecimal orderDepositAmount = BigDecimal.ZERO;
        BigDecimal finalOperationFee = BigDecimal.ZERO;
        BigDecimal finalShippingFee = BigDecimal.ZERO;
        if (ObjectUtil.equals(tenantType, TenantType.Supplier)) {
            orderDepositAmount = wiOrder.getOrderDepositAmount();
            refundDetailVo.setRefundPrice(orderRefund.getOriginalRefundAmount());
            finalOperationFee = wiOrder.getFinalOperationFee();
            finalShippingFee = wiOrder.getFinalShippingFee();
        } else {
            orderDepositAmount = wiOrder.getOrderDepositAmountPlatform();
            refundDetailVo.setRefundPrice(orderRefund.getPlatformRefundAmount());
            finalOperationFee = wiOrder.getFinalOperationFeePlatform();
            finalShippingFee = wiOrder.getFinalShippingFeePlatform();
        }
        OrderRefundStateType refundState = orderRefund.getRefundState();
        refundDetailVo.setRefundState(refundState.name());

        OrderAddressInfo orderAddressInfo = iOrderAddressInfoService.getByOrderNoAndAddressType(orderNo, OrderAddressType.ShipAddress);
        IntactAddressInfoVo intactAddressInfoVo = orderSupport.addressToBody(orderAddressInfo);
        String recipient = orderAddressInfo.getRecipient();
        String phoneNumber = orderAddressInfo.getPhoneNumber();
        RefundUserInfoVo userInfoVo = new RefundUserInfoVo();

        userInfoVo.setCustomerName(recipient);
        userInfoVo.setPhoneNumber(phoneNumber);
        OrderAddressType addressType = orderAddressInfo.getAddressType();
        if (ObjectUtil.equals(addressType, OrderAddressType.BillAddress)) {
            userInfoVo.setBillingAddress(intactAddressInfoVo);
        } else {
            userInfoVo.setShippingAddress(intactAddressInfoVo);
        }

        List<OrderRefundItem> orderRefundItems = itemToDTO.getOrderRefundItems();
        OrderItemProductSku orderItemProductSku = itemToDTO.getOrderItemProductSku();
        // 准备记录总金额、订金总金额、退款总金额
        RefundPriceVo refundPriceVo = new RefundPriceVo();
        List<RefundItemDetailVo> refundItemDetail = new ArrayList<>();
        // 处理退款子订单信息
        for (OrderRefundItem orderRefundItem : orderRefundItems) {
            RefundItemDetailVo refundItemDetailVo = new RefundItemDetailVo();
            OrderItemProductSku orderProductSku = iOrderItemProductSkuService.getByOrderItemId(orderRefundItem.getOrderItemId());
            // 处理退款子订单信息
            dealRefundItemVo(tenantType, refundItemDetailVo, orderRefund
                , orderRefundItem, orderProductSku, refundPriceVo);

            // 处理退款轨迹
            List<RefundStageDTO> stages = generateStages(orderRefund);
            Collections.reverse(stages);
            refundItemDetailVo.setStages(stages);

            refundItemDetail.add(refundItemDetailVo);
        }
        RefundItemDetailVo detailVo = refundItemDetail.get(0);
        List<RefundStageDTO> stages = detailVo.getStages();
        for (RefundStageDTO stage : stages) {
            if("Distributor".equals(stage.getRole())){
                String textEnUs = stage.getText_en_US();
                String textZhCn = stage.getText_zh_CN();
                if((StrUtil.isEmpty(textEnUs)&&StrUtil.isEmpty(textZhCn))||
                    ("null".equals(textEnUs)&&"null".equals(textZhCn))){
                    refundDetailVo.setIsNeedShow(Boolean.FALSE);
                }else {
                    refundDetailVo.setIsNeedShow(Boolean.TRUE);
                }
            }
        }
        // 如果当前用户是员工，则需要显示退货率相关信息
        if (Objects.equals(tenantType, TenantType.Manager)) {
            // 供货商名称
            userInfoVo.setSupplierName(itemToDTO.getSupplierName());
        }

        refundDetailVo.setTotalPrice(refundPriceVo.getTotalPrice().add(finalOperationFee).add(finalShippingFee));
        if (TenantType.Supplier.equals(tenantType)) {
            refundDetailVo.setRefundPrice(orderRefund.getOriginalRefundAmount());
        } else {
            refundDetailVo.setRefundPrice(orderRefund.getPlatformRefundAmount());
        }

        boolean isSupplier = false;
        if (ObjectUtil.equals(tenantType, TenantType.Supplier)) {
            isSupplier = true;
        }
        BigDecimal sumByRefundPrice = iOrderRefundService.sumByRefundPrice(order.getId(), isSupplier);
        refundDetailVo.setCumulativeRefundPrice(sumByRefundPrice);


        refundDetailVo.setDepositTotalPrice(orderDepositAmount);
        RefundItemDetailVo refundItemDetailVo = refundItemDetail.get(0);
        List<RefundStageDTO> stages1 = refundItemDetailVo.getStages();
        String refundItemStatus = refundItemDetailVo.getRefundItemStatus();
        Boolean needSupplement = refundItemDetailVo.getNeedSupplement();
        refundDetailVo.setStages(stages1);
        refundDetailVo.setUserInfo(userInfoVo);
        refundDetailVo.setRefundItemDetail(refundItemDetail);
        refundDetailVo.setRefundState(refundItemStatus);
        refundDetailVo.setNeedSupplement(needSupplement);
        refundDetailVo.setLogisticsType(refundItemDetailVo.getLogisticsType());

        return refundDetailVo;

    }

    /**
     * 退款单实体转详细实体
     *
     * @param orderRefund
     * @return
     */
    private RefundItemToDTO refundItemToVo(OrderRefund orderRefund) {
        RefundItemToDTO detailVo = new RefundItemToDTO();

        OrderRefundStateType refundState = orderRefund.getRefundState();
        String orderNo = orderRefund.getOrderNo();
        String orderRefundNo = orderRefund.getOrderRefundNo();
        Orders order = iOrdersService.getByOrderNo(orderNo);
        detailVo.setOrders(order);
        Integer num = orderRefund.getRefundQuantity();
        String refundNo = orderRefund.getOrderRefundNo();
        ChannelTypeEnum _channelType = order.getChannelType();
        List<OrderRefundItem> orderRefundItems = iOrderRefundItemService.queryByOrderRefundNo(orderRefundNo);
        detailVo.setOrderRefundItems(orderRefundItems);
        Date applyTime = orderRefund.getRefundApplyTime();
        LogisticsProgress fulfillmentProgress = order.getFulfillmentProgress();

        detailVo.setDistributor(orderRefund.getTenantId());
        detailVo.setSupplierName(orderRefund.getSupplierTenantId());
        detailVo.setOrderNo(orderNo);
        detailVo.setOrderType(order.getOrderType().name());
        detailVo.setOrderRefundNo(refundNo);
        detailVo.setRefundApplyTime(DateUtil.format(applyTime, "yyyy-MM-dd HH:mm"));
        detailVo.setNum(num);
        detailVo.setFulfillmentStatus(fulfillmentProgress.name());
        detailVo.setRefundAmountState(orderRefund.getRefundAmountState().name());
        detailVo.setChannelType(_channelType.name());
        detailVo.setOrderType(EnumUtil.toString(order.getOrderType()));
        detailVo.setRefundState(refundState.name());
        detailVo.setCurrencyCode(orderRefund.getCurrencyCode());
        detailVo.setCurrencySymbol(orderRefund.getCurrencySymbol());
        return detailVo;
    }


    /**
     * 处理退款步骤
     *
     * @param orderRefund
     * @return
     */
    private List<RefundStageDTO> generateStages(OrderRefund orderRefund) {

        // 处理退款步骤
        List<RefundStageDTO> stages = new ArrayList<>();


        String orderRefundNo = orderRefund.getOrderRefundNo();
        Date refundApplyTime = orderRefund.getRefundApplyTime();
        OrderRefundStateType refundState = orderRefund.getRefundState();
        String refundRuleReason = orderRefund.getRefundRuleReason();
        String refundDescription = orderRefund.getRefundDescription();
        List<String> imageShowUrls = new ArrayList<>();
        List<OrderRefundAttachment> orderRefundAttachments = iOrderRefundAttachmentService.queryListByRefundNo(orderRefundNo);
        for (OrderRefundAttachment attachment : orderRefundAttachments) {
            imageShowUrls.add(attachment.getAttachmentShowUrl());
        }

        JSONObject refundRuleReasonJSON = JSONUtil.parseObj(refundRuleReason);
        List<String> zh_CN = CollUtil.newArrayList(refundRuleReasonJSON.getStr("zh_CN"));
        List<String> en_US = CollUtil.newArrayList(refundRuleReasonJSON.getStr("en_US"));

        if (StrUtil.isNotBlank(refundDescription)) {
            zh_CN.add(refundDescription);
            en_US.add(refundDescription);
        }

        RefundStageDTO stageDTO = new RefundStageDTO();
        Integer stage = 1;
        stageDTO.setRole("Distributor");
//        stageDTO.setTime(DateUtil.formatToEST(refundApplyTime));
        stageDTO.setTime(DateUtil.format(refundApplyTime, dateFormat));
        stageDTO.setSort(stage++);
        stageDTO.setText_zh_CN(CollUtil.join(zh_CN, "; "));
        stageDTO.setText_en_US(CollUtil.join(en_US, "; "));
        stageDTO.setImageShowUrls(imageShowUrls);
        stages.add(stageDTO);


        // 管理员审核阶段
        Date managerReviewTime = orderRefund.getManagerReviewTime();
        if (managerReviewTime != null) {
            String managerReviewOpinion = orderRefund.getManagerReviewOpinion();
            RefundStageDTO managerStageDTO = new RefundStageDTO();
            managerStageDTO.setRole("Platform Review");
//            managerStageDTO.setTime(DateUtil.formatToEST(managerReviewTime));
            managerStageDTO.setTime(DateUtil.format(managerReviewTime, dateFormat));
            managerStageDTO.setSort(stage++);
            if (StrUtil.equals(managerReviewOpinion, "Accept")) {
                managerStageDTO.setText_zh_CN("同意");
            } else {
                managerStageDTO.setText_zh_CN(managerReviewOpinion);
            }
            managerStageDTO.setText_en_US(managerReviewOpinion);
            stages.add(managerStageDTO);
        }

        // 商家审核阶段
        Date supplierReviewTime = orderRefund.getSupplierReviewTime();
        if (supplierReviewTime != null) {
            String supplierReviewOpinion = orderRefund.getSupplierReviewOpinion();
            RefundStageDTO supplierStageDTO = new RefundStageDTO();
            supplierStageDTO.setRole("Supplier Review");
            supplierStageDTO.setTime(DateUtil.format(supplierReviewTime, dateFormat));
            supplierStageDTO.setSort(stage++);
            if (StrUtil.equals(supplierReviewOpinion, "Accept")) {
                supplierStageDTO.setText_zh_CN("同意");
            } else {
                supplierStageDTO.setText_zh_CN(supplierReviewOpinion);
            }
            supplierStageDTO.setText_en_US(supplierReviewOpinion);


            if (ObjectUtil.notEqual(refundState, OrderRefundStateType.Reject)) {
                // 拼接退款类型
                RefundApplyType refundApplyType = orderRefund.getRefundType();
                if (refundApplyType != null) {
                    if (ObjectUtil.equals(refundApplyType, RefundApplyType.Refund)) {
                        supplierStageDTO.setText_zh_CN(supplierStageDTO.getText_zh_CN() + "；仅退款");
                        supplierStageDTO.setText_en_US(supplierStageDTO.getText_en_US() + "; Refunds only");
                    } else if (ObjectUtil.equals(refundApplyType, RefundApplyType.RefundReturn)) {
                        supplierStageDTO.setText_zh_CN(supplierStageDTO.getText_zh_CN() + "；退款退货");
                        supplierStageDTO.setText_en_US(supplierStageDTO.getText_en_US() + "; Refunds and Returns");
                    }
                }
            }
            stages.add(supplierStageDTO);
        }


        OrderRefundLogistics refundLogistics = orderRefund.getOrderRefundLogistics();
        if (refundLogistics != null) {
            RefundStageDTO returnStageDTO = new RefundStageDTO();
            Date createDateTime = refundLogistics.getCreateTime();
            ReturnLogisticsType logisticsReturnType = refundLogistics.getLogisticsReturnType();
            returnStageDTO.setRole(stageDTO.getRole());
            returnStageDTO.setSort(stage++);
            returnStageDTO.setTime(DateUtil.format(createDateTime, dateFormat));

            if (Objects.equals(logisticsReturnType, ReturnLogisticsType.Manager)) {
                returnStageDTO.setText_zh_CN("通过Manager取回");
                returnStageDTO.setText_en_US("Return By Manager");
            } else {
                String carrier = refundLogistics.getLogisticsCarrier();
                String trackingNo = refundLogistics.getLogisticsTrackingNo();
                returnStageDTO.setTrackingNo(trackingNo);
                returnStageDTO.setCarrier(carrier);

                List<String> carrierList = StrUtil.split(carrier, ",", true, true);
                List<String> trackingList = StrUtil.split(trackingNo, ",", true, true);
                List<TrackingNoBo> trackingInfoList = new ArrayList<>();
                for (int i = 0; i < trackingList.size(); i++) {
                    String item_trackingNo = trackingList.get(i);
                    String item_carrier = CollUtil.get(carrierList, i);
                    if (StrUtil.isBlank(item_carrier)) {
                        item_carrier = CollUtil.getFirst(carrierList);
                    }
                    TrackingNoBo reqTrackingNoBody = new TrackingNoBo(item_carrier, item_trackingNo);
                    trackingInfoList.add(reqTrackingNoBody);
                }
                returnStageDTO.setTrackingInfoList(trackingInfoList);
            }
            stages.add(returnStageDTO);
        }

        return stages;
    }

    /**
     * 根据时间判断是否符合退货规则
     *
     * @param dateTime
     * @return
     */
    private Boolean compareRefundRules(Date dateTime) {
        if (dateTime != null) {
            Integer refundDays = businessParameterService.getValueFromInteger(BusinessParameterType.REFUND_DAYS);
            int between = (int) DateUtil.between(dateTime, new Date(), DateUnit.DAY);
            return refundDays.compareTo(between) >= 0;
        } else {
            return false;
        }
    }

    /**
     * 获取主订单剩余数量
     *
     * @param order
     * @return
     */
    private int getOrderSurplusNum(Orders order) {
        Long orderId = order.getId();
        int totalNum = order.getTotalQuantity();

        Integer refundSuccess = iOrderRefundItemService.getOrderTotalRefundNum(orderId);
        refundSuccess = refundSuccess == null ? 0 : refundSuccess;
        return totalNum - refundSuccess;
    }


    /**
     * 获取子订单剩余的可以退款的数量
     *
     * @param orderItem
     * @return
     */
    private int getOrderItemSurplusNum(OrderItem orderItem) {
        Long orderId = orderItem.getId();
        int num = orderItem.getTotalQuantity();

        Integer refundSuccess = iOrderRefundItemService.getOrderItemTotalRefundNum(orderId);
        refundSuccess = refundSuccess == null ? 0 : refundSuccess;
        return num - refundSuccess;
    }

    private void refundToWallet(OrderType orderType, OrderRefund orderRefund, List<OrderRefundItem> orderRefundItemList) {
        //退款至钱包
        try {
            TransactionRecord transactionRecord = new TransactionRecord(mallSystemCodeGenerator.codeGenerate(BusinessCodeEnum.TransactionNo));
            transactionRecord.setTransactionAmount(orderRefund.getPlatformRefundAmount());
            transactionRecord.setTransactionType(TransactionTypeEnum.Income);
            transactionRecord.setTransactionSubType(TransactionSubTypeEnum.OrderRefund);
            transactionRecord.setTransactionState(TransactionStateEnum.Processing);
            transactionRecord.setCurrency(orderRefund.getCurrencyCode());
            transactionRecord.setCurrencySymbol(orderRefund.getCurrencySymbol());
            orderRefund.setRefundCompletionTime(new Date());
            TenantHelper.ignore(() -> iOrderRefundService.updateById(orderRefund));

            tenantWalletService.walletChanges(orderRefund.getTenantId(), transactionRecord, true);
            // 保存关联关系
            iTransactionsOrderRefundService.saveRelation(transactionRecord.getId(), orderRefund.getId());

            if (OrderType.Wholesale.equals(orderType)) {
                // 计入账单
                billSupport.generateBillDTOByWholesaleOrderRefund(orderRefund, orderRefundItemList);
            } else {
                // 计入账单
                billSupport.generateBillDTOByOrderRefundItem(orderRefundItemList);
            }
        } catch (WalletException e) {
            throw new RStatusCodeException(e.getStatusCode());
        } catch (Exception e) {
            throw new RStatusCodeException(ZSMallStatusCodeEnum.WALLET_REFUND_ERROR);
        }
    }

    /**
     * 根据商品是否参加活动调用调整库存方法
     *
     * @param adjustStockDTO
     * @throws StockException
     */
    private void adjustStock(AdjustStockDTO adjustStockDTO) throws StockException {
        String activityCode = adjustStockDTO.getActivityCode();

        if (StrUtil.isNotBlank(activityCode)) {
            ZSMallActivityEventUtils.activityStockAdjust(adjustStockDTO);
        } else {
            productSkuStockService.adjustStock(adjustStockDTO, null);
        }
    }

    @Override
    public List<OrderRefundExportDTO> excelExport(RefundApplyBo bo, HttpServletResponse response) {
        List<RefundApplyVo> orderRefundsList =getOrderRefunds(bo);
        List<String> tenantIds = orderRefundsList.stream().map(RefundApplyVo::getTenantId).distinct().collect(Collectors.toList());
        Map<String, SysTenant> tenantMapByTenantIds = sysTenantService.getTenantMapByTenantIds(tenantIds);
        orderRefundsList.parallelStream().forEach(refundApplyVo -> {
            SysTenant sysTenant = tenantMapByTenantIds.get(refundApplyVo.getTenantId());
            if (ObjectUtil.isNotNull(sysTenant)){
                refundApplyVo.setThirdChannelFlag(sysTenant.getThirdChannelFlag());
            }
        });
        List<OrderRefundExportDTO> orderRefundExportDTOS = BeanUtil.copyToList(orderRefundsList, OrderRefundExportDTO.class);
        return orderRefundExportDTOS;
    }
    // todo 退款订单信息和erp打通的需求,不上线
    @Override
    public List<OrderRefundMsgDTO> getOrderRefundInformationForRefundable(List<OrderRefundMsgReq> orderRefundMsgReqs) {
        List<String> channelOrderNos = orderRefundMsgReqs.stream().map(OrderRefundMsgReq::getChannelOrderNo)
                                                 .collect(Collectors.toList());
        // todo channelOrderNos 找到对应的订单记录和退款记录,其中如果一个channelOrderNo得到了多条记录,需要状态一致才行


        List<Orders> orders = iOrdersService.findByChannelOrderNoIn(channelOrderNos);
        // orders根据渠道订单号分组转换成key为channelOrderNo,value为订单列表的map
        Map<String, List<Orders>> ordersMap = orders.stream().collect(Collectors.groupingBy(Orders::getChannelOrderNo));
        for (Map.Entry<String, List<Orders>> channelOrderMap : ordersMap.entrySet()) {
            OrderRefundMsgDTO orderRefundMsgDTO = new OrderRefundMsgDTO();
            List<Orders> ordersList = channelOrderMap.getValue();
            List<String> orderNos = ordersList.stream().map(Orders::getOrderNo).collect(Collectors.toList());
            List<OrderItemTrackingRecord> itemTrackingRecords = iOrderItemTrackingRecordService.getListByOrderNo(orderNos);
            String key = channelOrderMap.getKey();
            Date createTime =new Date();
            String currency = null;
            BigDecimal refundAllAmount = BigDecimal.ZERO;
            ChannelTypeEnum channelType =null;
            Integer totalQuantity = 0;
            String logisticsType = null;
            for (Orders o : ordersList) {
                // 这里计算总价,
                createTime = o.getCreateTime();
                currency = o.getCurrency();
                refundAllAmount = refundAllAmount.add(o.getOriginalActualTotalAmount());
                channelType = o.getChannelType();
                totalQuantity = totalQuantity+o.getTotalQuantity();
                logisticsType = o.getLogisticsType().name();
            }

            // todo 退款表
//            orderRefundMsgDTO.setStoreName();
//            orderRefundMsgDTO.setReturnDate();
//            orderRefundMsgDTO.setReturnStatus();
            orderRefundMsgDTO.setChannelOrderNo(key);
            orderRefundMsgDTO.setOrderCreateTime(createTime);
            // 发货日期要看tracking的填写情况 可能没有
            orderRefundMsgDTO.setShippingDate(null);
            orderRefundMsgDTO.setLogisticsType(logisticsType);
            // 无
//            orderRefundMsgDTO.setLabelSource();
//            orderRefundMsgDTO.setCurrency(currency);
//            // todo
//            orderRefundMsgDTO.setRefundAllAmount();
//            orderRefundMsgDTO.setOrderAllAmount(refundAllAmount);
//            orderRefundMsgDTO.setOrderQuantity(totalQuantity);
//
//            orderRefundMsgDTO.setChannelType(channelType.name());
//            // todo 确定是否是店铺名称
//            orderRefundMsgDTO.setChannelStore();
//            orderRefundMsgDTO.setBuyerName();
//            // todo 从退款表里拿
//            orderRefundMsgDTO.setRefundMethod();
//            // todo 从退款表里叠加
//            orderRefundMsgDTO.setRefundTotalAmount();
//            // todo 为订单整体状态
//            orderRefundMsgDTO.setRefundStatus();


        }
        orders.stream().collect(Collectors.toList());
        List<OrderRefundMsgDTO> orderRefundMsgDTOS = new ArrayList<>();




        List<OrderRefundMsgDetailDTO> orderRefundMsgDetailDTOS = new ArrayList<>();
        OrderRefundMsgDetailDTO orderRefundMsgDetailDTO = new OrderRefundMsgDetailDTO();
//        orderRefundMsgDetailDTO.setChannelSku();
//        orderRefundMsgDetailDTO.setErpSku();
//        orderRefundMsgDetailDTO.setSpuId();
//        orderRefundMsgDetailDTO.setCategory();
//        orderRefundMsgDetailDTO.setReceivedQuantity();
//        orderRefundMsgDetailDTO.setReturnQuantity();
//        orderRefundMsgDetailDTO.setProductName();
//        orderRefundMsgDetailDTO.setShippingTracking();
//        orderRefundMsgDetailDTO.setReturnCarrier();
//        orderRefundMsgDetailDTO.setRefundReason();
//        orderRefundMsgDetailDTO.setRefundDescription();
//        orderRefundMsgDetailDTO.setRefundNumber();
//        orderRefundMsgDTO.putOrderRefundMsgDetailDTOList();

        return null;
    }
}
