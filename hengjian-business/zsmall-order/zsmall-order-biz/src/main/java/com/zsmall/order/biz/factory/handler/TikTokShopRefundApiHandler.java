package com.zsmall.order.biz.factory.handler;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.hengjian.common.log.enums.BusinessType;
import com.hengjian.common.redis.utils.RedisUtilsPlus;
import com.xxl.conf.core.annotation.XxlConf;
import com.zsmall.common.domain.req.RejectReasonListReq;
import com.zsmall.common.domain.req.RejectReverseReq;
import com.zsmall.common.domain.req.base.TikTokRequestBaseEntity;
import com.zsmall.common.domain.resp.base.TikTokResponseBaseEntity;
import com.zsmall.common.domain.resp.tiktok.refund.*;
import com.zsmall.common.domain.tiktok.OrderMsg;
import com.zsmall.common.domain.tiktok.TikTokBusinessData;
import com.zsmall.common.enums.common.ChannelTypeEnum;
import com.zsmall.common.enums.tiktok.OrderRefundEnum;
import com.zsmall.common.enums.tiktok.TikTokApiEnums;
import com.zsmall.common.enums.tiktok.TikTokEnum;
import com.zsmall.common.handler.AbstractRefundHandler;
import com.zsmall.order.biz.utils.TikTokUtil;
import com.zsmall.system.entity.domain.TenantSalesChannel;
import com.zsmall.system.entity.iservice.ITenantSalesChannelService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.zsmall.common.enums.tiktok.TikTokApiEnums.*;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2024/1/29 13:36
 */
@Slf4j
@Lazy
@Component("tikTokShopRefundApi")
public class TikTokShopRefundApiHandler extends AbstractRefundHandler<TikTokResponseBaseEntity, TikTokRequestBaseEntity> {
    @Resource
    private TikTokUtil tikTokUtil;
    @XxlConf(value = "bizark-erp.tiktok.switch",defaultValue = "false")
    public static Boolean TIKTOK_SWITCH;
    @Resource
    private RedisUtilsPlus redisUtils;
    @Resource
    private RedisTemplate redisTemplate;

    @Resource
    private ITenantSalesChannelService tenantSalesChannelService;

    @Override
    public TikTokResponseBaseEntity confirmReverseRequest(Map query) {
        String reverseOrderId = String.valueOf(query.get("reverseOrderId"));
        String shopId = String.valueOf(query.get("shopId"));
        return tikTokUtil.getTikTokShopReturn(reverseOrderId, CONFIRM_REVERSE_REQUEST.getUrl(), CONFIRM_REVERSE_REQUEST.getPath(), ConfirmReverseResp.class, shopId);
    }

    @Override
    public List<String> getRejectReasonList(Map query) {
        // 拿出 reverseActionType 和 reasonType 等于2 然后进行属性的拼接
        RejectReasonListReq req = new RejectReasonListReq();
        req.setReverseActionType(OrderRefundEnum.GET_REJECT_REASON_LIST.getCodeType());
        req.setReasonType(OrderRefundEnum.GET_REJECT_REASON_LIST.getType());
        String shopId = String.valueOf(query.get("shopId"));
        ReverseReason reverseReason = new ReverseReason();
        List<ReverseReason> reverseReasonList = new ArrayList<>();
        if (TIKTOK_SWITCH) {
            RejectReasonResp rejectReasonList = tikTokUtil.getTikTokShopReturnByGet(req, GET_REJECT_REASON_LIST.getUrl(), GET_REJECT_REASON_LIST.getPath(), RejectReasonResp.class, shopId);
            reverseReasonList = rejectReasonList.getData().getReverseReasonList();
        } else {
            reverseReason.setReverseReason("其他");
            reverseReason.setReverseReasonKey("otherKey");
            reverseReasonList.add(reverseReason);
        }

        return reverseReasonList.stream().map(i -> {
            String jsonString = JSON.toJSONString(i);
            return JSON.parseObject(jsonString, String.class);
        }).collect(Collectors.toList());
    }

    @Override
    public TikTokResponseBaseEntity rejectReverseRequest(Map<String, Object> query) {
        RejectReverseReq req = new RejectReverseReq();
        String reasonKey = "_" + query.get("reasonKey");
        req.setReverseOrderId(String.valueOf(query.get("reverseOrderId")));
        req.setReverseRejectReasonKey(reasonKey);
        String shopId = String.valueOf(query.get("shopId"));
        return tikTokUtil.getTikTokShopReturn(req, REJECT_REVERSE_REQUEST.getUrl(), REJECT_REVERSE_REQUEST.getPath(), GeneralResp.class, shopId);
    }


    @Override
    public void insertBusinessData(JSONObject json, String channel, Map dataMap) {
        // 先拆成 webhook 然后拿到orderId和reverseId,然后调用逆向订单接口 resp shopId webhookJson
        ReverseOrderResponseMsg resp = (ReverseOrderResponseMsg) dataMap.get("resp");
        log.info("逆向订单数据:{}", JSON.toJSON(resp));
        // 拆解属性,不同属性存不同的表,同时保存关联表
        ReverseOrderResponseData data = resp.getData();
        log.info("逆向订单数据详情:{}", JSON.toJSON(data));
        // 退款请求表/退款表/明细表/附件表/ 一起保存 ,有异常一起回滚
        if (TikTokEnum.TIK_TOK_REFUND.getEnumType().equals(channel)) {
            channel = TikTokEnum.TIK_TOK_REFUND.getDesc();
        }
//        refundManger.insertByWebHook(data, channel, json);
    }

    @Override
    public void insertBusinessData(BusinessType type, ChannelTypeEnum channelTypeEnum, JSONObject json, Map dataMap) {

    }


    @Override
    public Map<String, Object> webHookDataAnalysis(JSONObject json, String channel) {
        // webhook数据分析 ,逆向json shopId webhookJson
        Map<String, Object> dataMap = new HashMap<>();

        Map<String, Object> msgMap = new HashMap<>();
        ReverseOrderResponseMsg msg = new ReverseOrderResponseMsg();
        TikTokBusinessData webHookData = JSON.toJavaObject(json, TikTokBusinessData.class);
        TikTokBusinessData tikTokData = new TikTokBusinessData();
        OrderMsg orderMsg = new OrderMsg();
        // 正式
        if (TIKTOK_SWITCH) {
            orderMsg.setOrderId((webHookData.getData().getOrderId()));
            orderMsg.setReverseOrderId(webHookData.getData().getReverseOrderId());
        } else {
            tikTokData = JSONObject.parseObject(getTestWeb(webHookData.getData().getOrderId(), webHookData.getData().getReverseOrderId()), TikTokBusinessData.class);
            orderMsg.setOrderId((tikTokData.getData().getOrderId()));
            orderMsg.setUpdateTimeFrom(tikTokData.getTimestamp());
            orderMsg.setReverseOrderId(tikTokData.getData().getReverseOrderId());
        }
        // 测试
        // tikTokData转换为 ReverseOrderResponseMsg 对象
        orderMsg.setReverseType(2);
        orderMsg.setSortType(1);
        orderMsg.setSize(100);
        orderMsg.setSortBy(1);
        orderMsg.setOffset(0);
        msgMap.put("req", orderMsg);


        if (TIKTOK_SWITCH) {
            // 正式版本
            TenantSalesChannel shopId = tenantSalesChannelService.getShopId(webHookData.getShopId());
            msgMap.put("shopId", shopId.getId());
            dataMap.put("shopId", shopId.getId());
            // 得到逆向订单数据 数据转换异常
            msg = (ReverseOrderResponseMsg) getReverseOrderMsg(msgMap);
            log.info("正式环境逆向订单数据:{}", JSON.toJSON(msg));
        } else {
            String testMsg = getTest(tikTokData.getData().getOrderId(), tikTokData.getData().getReverseOrderId());
            // 测试版本
            TenantSalesChannel shopId = tenantSalesChannelService.getShopId(webHookData.getShopId());
            msgMap.put("shopId", shopId.getId());
            msg = JSONObject.parseObject(testMsg, ReverseOrderResponseMsg.class);
        }
        dataMap.put("resp", msg);
        dataMap.put("webhookJson", json);
        return dataMap;

    }



    private String getTestWeb(String orderId, String reverseOrderId) {
        return "{\n" +
            "  \"type\": 2,\n" +
            "  \"shop_id\": \"7495138550629238920\",\n" +
            "  \"timestamp\": 1698657140,\n" +
            "  \"data\": {\n" +
            "    \"order_id\":" + orderId + ",\n" +
            "    \"reverse_type\": 2,\n" +
            "    \"reverse_user\": 1,\n" +
            "    \"reverse_order_status\": 3,\n" +
            "    \"reverse_event_type\": \"ORDER_REQUEST_CANCEL\",\n" +
            "    \"reverse_order_id\":" + reverseOrderId + ",\n" +
            "    \"update_time\": 1698657140\n" +
            "  }\n" +
            "}";
    }

    private String getTest(String orderId, String reverseOrderId) {
        return "{\n" +
            "    \"data\": {\n" +
            "        \"more\": true,\n" +
            "        \"reverse_list\": [\n" +
            "            {\n" +
            "                \"currency\": \"IDR\",\n" +
            "                \"order_id\": \"" + orderId + "\",\n" +
            "                \"refund_total\": \"5000\",\n" +
            "                \"return_item_list\": [\n" +
            "                    {\n" +
            "                        \"product_images\": \"https://pic1.zhimg.com/70/v2-d094166746ca67d2a2ed61042e2675ce_1440w.avis?source=172ae18b&biz_tag=Post\",\n" +
            "                        \"return_product_id\": \"647622\",\n" +
            "                        \"return_product_name\": \"VIVA-3822-BLK办公椅\",\n" +
            "                        \"return_quantity\": 2,\n" +
            "                        \"seller_sku\": \"C-3822-BK\",\n" +
            "                        \"sku_id\": \"2729382476852921560\",\n" +
            "                        \"sku_name\": \"椅子\"\n" +
            "                    }\n" +
            "                ],\n" +
            "                \"return_reason\": \"Package wasn't received\",\n" +
            "                \"return_tracking_id\": \"E0h1y0j4l1F8G3U6X7p8\",\n" +
            "                \"return_type\": 2,\n" +
            "                \"reverse_order_id\": \"" + reverseOrderId + "\",\n" +
            "                \"reverse_record_list\": [\n" +
            "                    {\n" +
            "                        \"additional_image_list\": \"\",\n" +
            "                        \"additional_message\": \"I want to return\",\n" +
            "                        \"description\": \"\",\n" +
            "                        \"reason_text\": \"Package wasn't received\",\n" +
            "                        \"update_time\": 1698657140\n" +
            "                    }\n" +
            "                ],\n" +
            "                \"reverse_request_time\": 1698657140,\n" +
            "                \"reverse_status_value\": 1,\n" +
            "                \"reverse_type\": 2,\n" +
            "                \"reverse_update_time\": 1698657140\n" +
            "            }\n" +
            "        ]\n" +
            "    }}";
    }


    @Override
    public boolean isNeedByChannel(JSONObject json) {
        TikTokBusinessData tikTokData = JSON.toJavaObject(json, TikTokBusinessData.class);
        // cannel = 1 refun = 2


        if (ObjectUtil.isNotNull(tikTokData)) {
            if (ObjectUtil.isNotNull(tikTokData.getData())) {
                if (ObjectUtil.isNotNull(tikTokData.getData().getReverseType())
                    &&
                    ObjectUtil.isNotNull(tikTokData.getType())) {
                }
                log.info("逆向数据:{},退款业务检查", JSON.toJSON(json));
                boolean isRefund = 2 == tikTokData.getType()
                    && "2".equals(String.valueOf(tikTokData.getData().getReverseType()))
                    && !"99".equals(String.valueOf(tikTokData.getData().getReverseOrderStatus()))                        ;
                if(isRefund){
                    String reverseOrderId = tikTokData.getData().getReverseOrderId();
                    //  以reverseOrderId记为redis key,判断redis中有没有这个值如果有返回true,如果没有返回false并将key和value塞入 设置过期10分钟,写一个lua脚本
                    List<String> keys = new ArrayList<>();
                    keys.add("tiktok_refund_reverseOrderId");
                    List<String> values = new ArrayList<>();
                    values.add(reverseOrderId);
                    if(redisUtils.isExist(keys,values)){

//                        List<OrderRefundRequest> requests = orderRefundRequestService.list(new LambdaQueryWrapper<OrderRefundRequest>().eq(OrderRefundRequest::getReverseOrderId, reverseOrderId).last("limit 1"));
//                        if(CollUtil.isEmpty(requests)){
//                            // 发送mq,1分钟后进行查询,查询scTicket表中是否已经有了此id,如果没有就记录到日志中
//                            rabbitTemplate.convertAndSend(MQDefine.TIKTOK_REFUND_EXCHANGE,MQDefine.TIKTOK_REFUND_ROUTING_KEY,json);
//                            return Boolean.TRUE;

                    }
                }
            }
        }
        return Boolean.FALSE;
    }

    @Override
    public boolean isNeedByChannel(JSONObject json, TikTokApiEnums enums) {
        return false;
    }



    @Override
    public TikTokResponseBaseEntity getReverseOrderMsg(Map<String, Object> query) {
        Object req = query.get("req");
        String shopId = String.valueOf(query.get("shopId"));
        log.info("参数检查:{}", JSON.toJSON(req));
        ReverseOrderResponseMsg tikTokShopReturn = tikTokUtil.getTikTokShopReturn(req, GET_REVERSE_ORDER_LIST_V2.getUrl(), GET_REVERSE_ORDER_LIST_V2.getPath(), ReverseOrderResponseMsg.class, shopId);
        log.info("返回数据校验:{}", JSON.toJSON(tikTokShopReturn));
        return tikTokShopReturn;
    }

    @Override
    public TikTokResponseBaseEntity jsonObjectToBusinessData(JSONObject json) {
        return JSON.toJavaObject(json, ReverseOrderResponseMsg.class);
    }
}
