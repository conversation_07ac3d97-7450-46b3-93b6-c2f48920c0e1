package com.zsmall.order.biz.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.UUID;
import cn.hutool.core.net.url.UrlBuilder;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.hengjian.common.core.domain.R;
import com.hengjian.common.core.domain.model.LoginUser;
import com.hengjian.common.core.utils.StringUtils;
import com.hengjian.common.satoken.utils.LoginHelper;
import com.zsmall.common.domain.airwallex.payment.intents.*;
import com.zsmall.common.domain.airwallex.payment.intents.Product;
import com.zsmall.common.enums.order.OrderStateType;
import com.zsmall.common.enums.order.OrderType;
import com.zsmall.common.enums.payment.PayTypeEnum;
import com.zsmall.common.enums.statuscode.ZSMallStatusCodeEnum;
import com.zsmall.common.enums.transaction.ReceiptReviewStateEnum;
import com.zsmall.common.enums.transaction.TransactionMethodEnum;
import com.zsmall.common.enums.transaction.TransactionStateEnum;
import com.zsmall.common.enums.transaction.TransactionTypeEnum;
import com.zsmall.common.exception.*;
import com.zsmall.common.service.AirwallexService;
import com.zsmall.extend.payment.bean.pay.PayoneerListsRequest.*;
import com.zsmall.extend.payment.bean.pay.PayoneerListsResponse.PayoneerListsResponse;
import com.zsmall.extend.payment.bean.pay.PayoneerListsResponse.Redirect;
import com.zsmall.extend.payment.support.PayoneerSupport;
import com.zsmall.order.biz.factory.OrderHandleInterface;
import com.zsmall.order.biz.service.OrderImportRecordService;
import com.zsmall.order.biz.support.OrderSupport;
import com.zsmall.order.entity.domain.AirwallexRequestInfo;
import com.zsmall.order.entity.domain.OrderAddressInfo;
import com.zsmall.order.entity.domain.Orders;
import com.zsmall.order.entity.domain.bo.orderImport.OrderImportRecordBo;
import com.zsmall.order.biz.service.PayService;
import com.zsmall.order.entity.domain.vo.order.OrderItemVo;
import com.zsmall.order.entity.domain.vo.order.OrderPageVo;
import com.zsmall.order.entity.domain.vo.orderImport.OrderImportRecordDetailVo;
import com.zsmall.order.entity.domain.vo.orderImport.TempOrderVo;
import com.zsmall.order.entity.iservice.IAirwallexRequestInfoService;
import com.zsmall.order.entity.iservice.IOrderAddressInfoService;
import com.zsmall.order.entity.iservice.IOrdersService;
import com.zsmall.system.entity.domain.bo.pay.AirwallexOrderPayBo;
import lombok.RequiredArgsConstructor;
import lombok.Synchronized;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static com.zsmall.common.util.AESUtil.*;

/**
 * <AUTHOR>
 * @date 2024年3月6日  10:38
 * @description:
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class PayServiceImpl implements PayService {

    @Autowired
    OrderImportRecordService orderImportRecordService;

    @Autowired
    AirwallexService airwallexService;

    @Autowired
    IAirwallexRequestInfoService iAirwallexRequestInfoService;

    @Autowired
    IOrdersService iOrdersService;

    @Autowired
    PayoneerSupport payoneerSupport;

    @Autowired
    OrderSupport orderSupport;

    private final List<OrderHandleInterface> orderHandleInterfaces;

    private final IOrderAddressInfoService orderAddressInfoService;


    @Override
    @Synchronized
    public CreatePaymentIntentResponse orderPay(AirwallexOrderPayBo airwallexOrderPayBo) throws OrderPayException, StockException, ProductException {
        if (null != airwallexOrderPayBo){
            CreatePaymentIntentRequest createPaymentIntentRequest = new CreatePaymentIntentRequest();
            List<Product> productList = new ArrayList<>();
            List<String> orderNo = new ArrayList<>();
            // 订单信息
            Order order = new Order();
            // 单个支付
            if(StringUtils.isNotEmpty(airwallexOrderPayBo.getRecordNo())){
                OrderImportRecordBo orderImportRecordBo = new OrderImportRecordBo();
                orderImportRecordBo.setRecordNo(airwallexOrderPayBo.getRecordNo());
                R<OrderImportRecordDetailVo> orderImportRecordDetailVoR = orderImportRecordService.queryImportRecordDetail(orderImportRecordBo);
                OrderImportRecordDetailVo data = orderImportRecordDetailVoR.getData();
                createPaymentIntentRequest.setAmount(new BigDecimal(data.getAllTotalPrice()));
                List<TempOrderVo> tempOrderList = data.getTempOrderList();
                List<String> orderNoList = tempOrderList.stream().map(TempOrderVo::getOrderNo).collect(Collectors.toList());
                List<Orders> ordersList = iOrdersService.queryByOrderNoList(orderNoList);
                if(CollUtil.isNotEmpty(ordersList)){
                    // 订单前置检查
                    orderSupport.orderPayForThirdCheck(LoginHelper.getLoginUser().getTenantId(),ordersList);
                }
                for(TempOrderVo tempOrderVo : tempOrderList){
                    orderNo.add(tempOrderVo.getOrderNo());
                    Product product = new Product();
                    product.setCode("");
                    product.setDesc("");
                    product.setName(tempOrderVo.getProductName());
                    product.setQuantity(Long.valueOf(tempOrderVo.getProductQuantity()));
                    product.setSku(tempOrderVo.getProductSkuCode());
                    product.setType("physical_good");
                    product.setUnit_price(tempOrderVo.getDropShippingPrice());
//                    product.setUrl(tempOrderVo.getProductImageShowUrl());
                    productList.add(product);
                }
                order.setProducts(productList);
                TempOrderVo tempOrderVo = tempOrderList.get(0);
                Shipping shipping = new Shipping();
                ShippingAddress shippingAddress = new ShippingAddress();
                shippingAddress.setCity(tempOrderVo.getCity());
                shippingAddress.setCountry_code(tempOrderVo.getCountryCode());
                shippingAddress.setPostcode(tempOrderVo.getZipCode());
                shippingAddress.setState(tempOrderVo.getState());
                shippingAddress.setStreet(tempOrderVo.getAddress1());
                shipping.setAddress(shippingAddress);
                order.setShipping(shipping);
            }
            // 批量支付
            if(CollectionUtil.isNotEmpty(airwallexOrderPayBo.getOrderNoList())){
                orderNo = airwallexOrderPayBo.getOrderNoList();
                List<Orders> ordersList = iOrdersService.queryByOrderNoListAndStateIn(airwallexOrderPayBo.getOrderNoList(), OrderStateType.UnPaid, OrderStateType.Pending, OrderStateType.Failed);
                // 订单处理
                if(CollectionUtil.isNotEmpty(ordersList)){
                    List<Orders> ordersList1 = iOrdersService.queryByOrderNoList(airwallexOrderPayBo.getOrderNoList());
                    // 订单前置检查
                    orderSupport.orderPayForThirdCheck(LoginHelper.getLoginUser().getTenantId(),ordersList1);
                    List<OrderPageVo> orderBodies = new ArrayList<>();
                    BigDecimal amount = new BigDecimal("0");
                    for (Orders orders : ordersList) {
                        Optional<OrderHandleInterface> orderHandleInterface = orderHandleInterfaces.stream().
                                                                                                   filter(oif -> oif.isThisImpl(orders.getOrderType()))
                                                                                                   .findFirst();
                        OrderPageVo orderPageVo;
                        if (orderHandleInterface.isPresent()) {
                            orderPageVo = orderHandleInterface.get().buildOrderBody(orders, null);
                        } else {
                            orderPageVo = orderHandleInterfaces.stream().
                                                               filter(oif -> oif.isThisImpl(OrderType.Normal)).findFirst().get()
                                                               .buildOrderBody(orders, null);
                        }
                        orderBodies.add(orderPageVo);
                    }
                    if(CollectionUtil.isNotEmpty(orderBodies)){
                        for(OrderPageVo orderPageVo : orderBodies){
                            amount = amount.add(orderPageVo.getTotalNumber());
                            for(OrderItemVo orderItemVo : orderPageVo.getOrderItems()){
                                Product product = new Product();
                                product.setCode("");
                                product.setDesc("");
                                product.setName(orderItemVo.getProductName());
                                product.setQuantity(Long.valueOf(orderItemVo.getNum()));
                                product.setSku(orderItemVo.getSku());
                                product.setType("physical_good");
                                if(null != orderItemVo.getProductTotalPrice() && null != orderItemVo.getNum() && 0 != orderItemVo.getNum()){
                                    BigDecimal unitPrice =  orderItemVo.getProductTotalPrice().divide(BigDecimal.valueOf(orderItemVo.getNum()));
                                    product.setUnit_price(unitPrice);
                                }
//                                product.setUrl(orderItemVo.getImageShowUrl());
                                productList.add(product);
                            }
                        }
                        OrderAddressInfo orderAddressInfo = orderAddressInfoService.getByOrderNo(orderBodies.get(0).getOrderId());
                        if(null != orderAddressInfo){
                            Shipping shipping = new Shipping();
                            ShippingAddress shippingAddress = new ShippingAddress();
                            shippingAddress.setCity(orderAddressInfo.getCity());
                            shippingAddress.setCountry_code(orderAddressInfo.getCountryCode());
                            shippingAddress.setPostcode(orderAddressInfo.getZipCode());
                            shippingAddress.setState(orderAddressInfo.getState());
                            shippingAddress.setStreet(orderAddressInfo.getAddress1());
                            shipping.setAddress(shippingAddress);
                            order.setShipping(shipping);
                        }
                    }
                    order.setProducts(productList);
                    createPaymentIntentRequest.setAmount(amount);
                }else {
                    throw new OrderPayException("没有对应的订单信息: "+ orderNo);
                }
            }
            if(StringUtils.isNotBlank(airwallexOrderPayBo.getCurrency())){
                createPaymentIntentRequest.setCurrency(airwallexOrderPayBo.getCurrency());
            }else {
                createPaymentIntentRequest.setCurrency("USD");
            }
            createPaymentIntentRequest.setMerchant_order_id(UUID.fastUUID().toString());
            createPaymentIntentRequest.setRequest_id(UUID.fastUUID().toString());
            CustomerAdditionalInfo customerAdditionalInfo = new CustomerAdditionalInfo();
            customerAdditionalInfo.setRegistration_date(LocalDate.now().toString());

            LoginUser loginUser = LoginHelper.getLoginUser();
            Customer customer = new Customer();
            customer.setAdditional_info(customerAdditionalInfo);
            customer.setFirst_name("A");
//            customer.setLast_name("B");
            customer.setLast_name(loginUser.getUsername());
            customer.setMerchant_customer_id(UUID.fastUUID().toString());
            customer.setEmail(loginUser.getEmail());
            customer.setPhone_number(loginUser.getPhoneNumber());
            createPaymentIntentRequest.setCustomer(customer);
            createPaymentIntentRequest.setOrder(order);
            CreatePaymentIntentResponse paymentIntent = airwallexService.createPaymentIntent(createPaymentIntentRequest);
            AirwallexRequestInfo airwallexRequestInfo = new AirwallexRequestInfo();
            String orderNos = String.join(",", orderNo);
            airwallexRequestInfo.setRequestId(paymentIntent.getRequestId()).setTenantId(loginUser.getTenantId()).setPayType(PayTypeEnum.Airwallex.getValue()).setType(1).setOrderNo(orderNos).setCreateTime(LocalDateTime.now()).setUpdateTime(LocalDateTime.now()).setDelFlag("0");
//            airwallexRequestInfo.setRequestId(paymentIntent.getRequestId()).setType(1).setOrderNos(orderNos).setCreateTime(LocalDateTime.now()).setUpdateTime(LocalDateTime.now()).setDelFlag("0");
            iAirwallexRequestInfoService.save(airwallexRequestInfo);
            return paymentIntent;
        }
        return null;
    }

    @Override
    @Synchronized
    public Redirect orderPayByPayoneer(AirwallexOrderPayBo airwallexOrderPayBo) throws Exception {
        LoginUser loginUser = LoginHelper.getLoginUser();
        PayoneerRequest payoneerRequest = new PayoneerRequest();
        String transactionId = UUID.fastUUID().toString();
        // 系统订单号
        List<String> orderNoList = new ArrayList<>();
        // 回调信息
        Callback callback = new Callback();
        callback.setReturnUrl(airwallexOrderPayBo.getReturnUrl());
        callback.setCancelUrl(airwallexOrderPayBo.getCancelUrl());
        callback.setNotificationUrl("https://distribution.ehengjian.com/prod-api/distributor/salesChannel/payoneer/notifications");
        // 支付信息
        PayoneerRequestPayment payoneerRequestPayment = new PayoneerRequestPayment();
        // 产品信息
        List<com.zsmall.extend.payment.bean.pay.PayoneerListsRequest.Product> productList = new ArrayList<>();
        // 消费者信息
        PayoneerRequestCustomer payoneerRequestCustomer = new PayoneerRequestCustomer();
        // 地址信息
        Billing shipping = new Billing();
        // 单次支付
        if(StringUtils.isNotEmpty(airwallexOrderPayBo.getRecordNo())){
            OrderImportRecordBo orderImportRecordBo = new OrderImportRecordBo();
            orderImportRecordBo.setRecordNo(airwallexOrderPayBo.getRecordNo());
            R<OrderImportRecordDetailVo> orderImportRecordDetailVoR = orderImportRecordService.queryImportRecordDetail(orderImportRecordBo);
            OrderImportRecordDetailVo data = orderImportRecordDetailVoR.getData();
            payoneerRequestPayment.setReference("order").setAmount(new BigDecimal(data.getAllTotalPrice())).setCurrency("USD");
            List<TempOrderVo> tempOrderList = data.getTempOrderList();
            List<String> orderNoTempList = tempOrderList.stream().map(TempOrderVo::getOrderNo).collect(Collectors.toList());
            List<Orders> ordersList2 = iOrdersService.queryByOrderNoList(orderNoTempList);
            if (CollUtil.isNotEmpty(ordersList2)) {
                // 订单前置检查
                orderSupport.orderPayForThirdCheck(LoginHelper.getLoginUser().getTenantId(), ordersList2);
            }
            List<String> orderNoSerchList = tempOrderList.stream().map(TempOrderVo::getOrderNo)
                                                         .collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(orderNoSerchList)) {
                orderNoList = orderNoSerchList;
                List<Orders> ordersList = iOrdersService.queryByOrderNoListAndStateIn(orderNoSerchList, OrderStateType.UnPaid, OrderStateType.Pending);
                List<OrderPageVo> orderBodies = new ArrayList<>();
                for (Orders orders : ordersList) {
                    Optional<OrderHandleInterface> orderHandleInterface = orderHandleInterfaces.stream().
                                                                                               filter(oif -> oif.isThisImpl(orders.getOrderType()))
                                                                                               .findFirst();
                    OrderPageVo orderPageVo;
                    if (orderHandleInterface.isPresent()) {
                        orderPageVo = orderHandleInterface.get().buildOrderBody(orders, null);
                    } else {
                        orderPageVo = orderHandleInterfaces.stream().
                                                           filter(oif -> oif.isThisImpl(OrderType.Normal)).findFirst().get()
                                                           .buildOrderBody(orders, null);
                    }
                    orderBodies.add(orderPageVo);
                }
                if(CollectionUtil.isNotEmpty(orderBodies)){
                    for(OrderPageVo orderPageVo : orderBodies){
                        for(OrderItemVo orderItemVo : orderPageVo.getOrderItems()){
                            com.zsmall.extend.payment.bean.pay.PayoneerListsRequest.Product product = new com.zsmall.extend.payment.bean.pay.PayoneerListsRequest.Product();
                            product.setType("PHYSICAL").setName(orderItemVo.getProductName()).setQuantity(Long.valueOf(orderItemVo.getNum()));
                            if(StringUtils.isNotEmpty(orderPageVo.getCurrency())){
                                product.setCurrency(orderPageVo.getCurrency());
                            }else {
                                product.setCurrency("USD");
                            }
                            if(null != orderItemVo.getProductTotalPrice() && null != orderItemVo.getNum() && 0 != orderItemVo.getNum()){
                                BigDecimal amount =  orderItemVo.getProductTotalPrice().divide(BigDecimal.valueOf(orderItemVo.getNum()));
                                product.setAmount(amount);
                            }
                            productList.add(product);
                        }
                    }
                }
            }
            TempOrderVo tempOrderVo = tempOrderList.get(0);
            shipping.setStreet(tempOrderVo.getAddress1()).setZip(tempOrderVo.getZipCode()).setCity(tempOrderVo.getCity()).setState(tempOrderVo.getState()).setCountry(tempOrderVo.getCountryCode());
        }
        // 批量支付
        if(CollectionUtil.isNotEmpty(airwallexOrderPayBo.getOrderNoList())){
            orderNoList = airwallexOrderPayBo.getOrderNoList();
            List<Orders> ordersList1 = iOrdersService.queryByOrderNoList(airwallexOrderPayBo.getOrderNoList());
            // 订单前置检查
            orderSupport.orderPayForThirdCheck(LoginHelper.getLoginUser().getTenantId(), ordersList1);

            List<Orders> ordersList = iOrdersService.queryByOrderNoListAndStateIn(airwallexOrderPayBo.getOrderNoList(), OrderStateType.UnPaid, OrderStateType.Pending, OrderStateType.Failed);
            if (CollectionUtil.isNotEmpty(ordersList)) {
                List<OrderPageVo> orderBodies = new ArrayList<>();
                BigDecimal amount = new BigDecimal("0");
                for (Orders orders : ordersList) {
                    Optional<OrderHandleInterface> orderHandleInterface = orderHandleInterfaces.stream().
                                                                                               filter(oif -> oif.isThisImpl(orders.getOrderType()))
                                                                                               .findFirst();
                    OrderPageVo orderPageVo;
                    if (orderHandleInterface.isPresent()) {
                        orderPageVo = orderHandleInterface.get().buildOrderBody(orders, null);
                    } else {
                        orderPageVo = orderHandleInterfaces.stream().
                                                           filter(oif -> oif.isThisImpl(OrderType.Normal)).findFirst().get()
                                                           .buildOrderBody(orders, null);
                    }
                    orderBodies.add(orderPageVo);
                }
                if(CollectionUtil.isNotEmpty(orderBodies)){
                    for(OrderPageVo orderPageVo : orderBodies){
                        amount = amount.add(orderPageVo.getTotalNumber());
                        for(OrderItemVo orderItemVo : orderPageVo.getOrderItems()){
                            com.zsmall.extend.payment.bean.pay.PayoneerListsRequest.Product product = new com.zsmall.extend.payment.bean.pay.PayoneerListsRequest.Product();
                            product.setType("PHYSICAL").setName(orderItemVo.getProductName()).setQuantity(Long.valueOf(orderItemVo.getNum()));
                            if(StringUtils.isNotEmpty(orderPageVo.getCurrency())){
                                product.setCurrency(orderPageVo.getCurrency());
                            }else {
                                product.setCurrency("USD");
                            }
                            if(null != orderItemVo.getProductTotalPrice() && null != orderItemVo.getNum() && 0 != orderItemVo.getNum()){
                                BigDecimal unitPrice =  orderItemVo.getProductTotalPrice().divide(BigDecimal.valueOf(orderItemVo.getNum()));
                                product.setAmount(unitPrice);
                            }
                            productList.add(product);
                        }
                    }
                    OrderAddressInfo orderAddressInfo = orderAddressInfoService.getByOrderNo(orderBodies.get(0).getOrderId());
                    if(null != orderAddressInfo){
                        shipping.setStreet(orderAddressInfo.getAddress1()).setZip(orderAddressInfo.getZipCode()).setCity(orderAddressInfo.getCity()).setState(orderAddressInfo.getState()).setCountry(orderAddressInfo.getCountryCode());
                    }
                }
                payoneerRequestPayment.setReference("order").setAmount(amount).setCurrency("USD");
            }
        }

        payoneerRequestCustomer.setEmail(loginUser.getEmail());
//        payoneerRequestCustomer.setEmail("<EMAIL>");
        Addresses addresses = new Addresses();
        addresses.setShipping(shipping).setBilling(shipping);
        payoneerRequestCustomer.setAddresses(addresses);
        // 托管页面样式
        Style style = new Style();
        style.setLanguage("cn").setHostedVersion("v4").setDisplayName("Sweet furniture").setPrimaryColor("#2196F3").setLogoUrl("https://hengjian-distribution.oss-cn-hangzhou.aliyuncs.com/2024/04/11/11.png").setBackgroundType("BACKGROUND_IMAGE");
        // 自定义参数加密
        String key = bytesToHex(generateAESKey(128).getEncoded());
        Header header = new Header();
        header.setName("Authorization").setValue(encrypt(transactionId, key));
        List<Header> headerList = new ArrayList<>();
        headerList.add(header);
        callback.setNotificationHeaders(headerList);
        // 构建请求参数
        payoneerRequest.setIntegration("HOSTED").setTransactionId(transactionId).setCountry("CN").setChannel("WEB_ORDER")
                       .setCallback(callback).setCustomer(payoneerRequestCustomer).setStyle(style).setPayment(payoneerRequestPayment).setProducts(productList);
        PayoneerListsResponse payRequest = payoneerSupport.createPayRequest(payoneerRequest);
        if (ObjectUtil.isNull(payRequest)){
            List<Orders> ordersList = iOrdersService.queryByOrderNoListAndStateIn(orderNoList, OrderStateType.Pending);
            com.alibaba.fastjson.JSONObject jsonObject = new com.alibaba.fastjson.JSONObject();
            jsonObject.put("payonnner pay error","调用第三方接口出现错误");
            orderSupport.orderPayFailThird(ordersList, jsonObject.toJSONString());
            throw new OrderException(jsonObject.toJSONString());
        }
        AirwallexRequestInfo airwallexRequestInfo = new AirwallexRequestInfo();
        String orderNos = String.join(",", orderNoList);
        airwallexRequestInfo.setRequestId(transactionId).setTenantId(loginUser.getTenantId()).setPayType(PayTypeEnum.Payonner.getValue()).setType(1).setOrderNo(orderNos).setCreateTime(LocalDateTime.now()).setUpdateTime(LocalDateTime.now()).setDelFlag("0").setAesKey(key);
//        airwallexRequestInfo.setRequestId(transactionId).setType(1).setOrderNo(orderNos).setCreateTime(LocalDateTime.now()).setUpdateTime(LocalDateTime.now()).setDelFlag("0").setAesKey(key);
        iAirwallexRequestInfoService.save(airwallexRequestInfo);
        return payRequest.getRedirect();
    }

    @Override
    @Synchronized
    public CreatePaymentIntentResponse rechargePay(AirwallexOrderPayBo airwallexOrderPayBo) {
        if (null != airwallexOrderPayBo && null != airwallexOrderPayBo.getRechargeAmount()){
            CreatePaymentIntentRequest createPaymentIntentRequest = new CreatePaymentIntentRequest();
            if(StringUtils.isNotBlank(airwallexOrderPayBo.getCurrency())){
                createPaymentIntentRequest.setCurrency(airwallexOrderPayBo.getCurrency());
            }else {
                createPaymentIntentRequest.setCurrency("USD");
            }
            createPaymentIntentRequest.setMerchant_order_id(UUID.fastUUID().toString());
            createPaymentIntentRequest.setRequest_id(UUID.fastUUID().toString());
            createPaymentIntentRequest.setAmount(airwallexOrderPayBo.getRechargeAmount());
            CustomerAdditionalInfo customerAdditionalInfo = new CustomerAdditionalInfo();
            customerAdditionalInfo.setRegistration_date(LocalDate.now().toString());
            Order order = new Order();
            Product product = new Product();
            product.setCode("");
            product.setDesc("");
            product.setName("recharge card");
            product.setQuantity(1L);
            product.setSku("rc");
            product.setType("intangible_good");
            product.setUnit_price(airwallexOrderPayBo.getRechargeAmount());
            order.setProducts(Arrays.asList(product));
            LoginUser loginUser = LoginHelper.getLoginUser();
            Customer customer = new Customer();
            customer.setAdditional_info(customerAdditionalInfo);
            customer.setFirst_name("A");
            customer.setLast_name(loginUser.getUsername());
            customer.setMerchant_customer_id(UUID.fastUUID().toString());
            customer.setEmail(loginUser.getEmail());
            customer.setPhone_number(loginUser.getPhoneNumber());
            createPaymentIntentRequest.setCustomer(customer);
            createPaymentIntentRequest.setOrder(order);
            CreatePaymentIntentResponse paymentIntent = airwallexService.createPaymentIntent(createPaymentIntentRequest);
            AirwallexRequestInfo airwallexRequestInfo = new AirwallexRequestInfo();
            airwallexRequestInfo.setRequestId(paymentIntent.getRequestId()).setTenantId(loginUser.getTenantId()).setType(2).setCreateTime(LocalDateTime.now()).setUpdateTime(LocalDateTime.now()).setDelFlag("0");
//            airwallexRequestInfo.setRequestId(paymentIntent.getRequestId()).setType(2).setCreateTime(LocalDateTime.now()).setUpdateTime(LocalDateTime.now()).setDelFlag("0");
            iAirwallexRequestInfoService.save(airwallexRequestInfo);
            return paymentIntent;
        }
        return null;
    }

    @Override
    public void payoneerPayTest() {
        PayoneerRequest payoneerRequest = new PayoneerRequest();
        // 回调信息
        Callback callback = new Callback();
        callback.setReturnUrl("https://www.baidu.com");
        callback.setCancelUrl("https://www.baidu.com");
        callback.setNotificationUrl("https://distribution.ehengjian.com/prod-api/distributor/salesChannel/payoneer/notifications");
        // 消费者信息
        PayoneerRequestCustomer payoneerRequestCustomer = new PayoneerRequestCustomer();
        payoneerRequestCustomer.setEmail("<EMAIL>");
        Addresses addresses = new Addresses();
        Billing shipping = new Billing();
        shipping.setStreet("Ganghoferstr.").setZip("80339").setCity("Munich").setState("Bayern").setCountry("DE");
        addresses.setShipping(shipping).setBilling(shipping);
        payoneerRequestCustomer.setAddresses(addresses);
        // 支付信息
        PayoneerRequestPayment payoneerRequestPayment = new PayoneerRequestPayment();
        payoneerRequestPayment.setReference("test").setAmount(new BigDecimal("10")).setCurrency("USD");
        // 产品信息
        List<com.zsmall.extend.payment.bean.pay.PayoneerListsRequest.Product> productList = new ArrayList<>();
        com.zsmall.extend.payment.bean.pay.PayoneerListsRequest.Product product = new com.zsmall.extend.payment.bean.pay.PayoneerListsRequest.Product();
        product.setType("PHYSICAL").setName("Storage Ottoman with Thicker Foam Padded Seat, Folding Foot Rest for Living Room End of Bed Bench").setAmount(new BigDecimal("31")).setQuantity(1L).setCurrency("USD");
        productList.add(product);
        // 托管页面样式
        Style style = new Style();
        style.setLanguage("cn").setHostedVersion("v4");
        payoneerRequest.setIntegration("HOSTED").setTransactionId(UUID.fastUUID().toString()).setCountry("CN").setChannel("WEB_ORDER")
            .setCallback(callback).setCustomer(payoneerRequestCustomer).setStyle(style).setPayment(payoneerRequestPayment).setProducts(productList);
        PayoneerListsResponse payRequest = payoneerSupport.createPayRequest(payoneerRequest);
    }


}
