package com.zsmall.order.biz.service.impl;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hengjian.common.core.utils.MapstructUtils;
import com.hengjian.common.tenant.helper.TenantHelper;
import com.hengjian.system.domain.SysOss;
import com.hengjian.system.domain.vo.SysOssVo;
import com.hengjian.system.mapper.SysOssMapper;
import com.hengjian.system.service.impl.SysOssServiceImpl;
import com.zsmall.common.domain.resp.tiktok.express.TikTokApiReturnMsg;
import com.zsmall.common.domain.resp.tiktok.express.TikTokExpressSheet;
import com.zsmall.common.domain.resp.tiktok.fulfillment.TikTokPackShippingDocument;
import com.zsmall.common.domain.resp.tiktok.product.resp.PackageDetailsResp;
import com.zsmall.common.domain.tiktok.domain.dto.express.TikTokExpressRelevant;
import com.zsmall.common.domain.tiktok.domain.dto.extend.resp.TikTokCreatePackageResp;
import com.zsmall.common.domain.tiktok.domain.dto.extend.resp.TikTokSyncOrderSearchResp;
import com.zsmall.common.domain.tiktok.domain.dto.item.TikTokLineItem;
import com.zsmall.common.domain.tiktok.domain.dto.order.TikTokLineOrderCompensation;
import com.zsmall.common.domain.tiktok.domain.dto.order.TikTokOrder;
import com.zsmall.common.domain.tiktok.domain.dto.packages.TikTokCreatePackageData;
import com.zsmall.common.domain.tiktok.domain.dto.req.TikTokCreatePackagesBody;
import com.zsmall.common.enums.common.ChannelTypeEnum;
import com.zsmall.common.enums.order.LogisticsTypeEnum;
import com.zsmall.common.enums.order.TiktokLogisticsTypeEnum;
import com.zsmall.common.enums.tiktok.OrderStatusEnum;
import com.zsmall.order.biz.service.DistributorOrderService;
import com.zsmall.order.biz.service.TiktokLineOrderCompensationService;
import com.zsmall.order.biz.utils.TikTokShopApiUtil;
import com.zsmall.order.entity.domain.Orders;
import com.zsmall.order.entity.domain.bo.order.OrderUpdateBo;
import com.zsmall.order.entity.iservice.IOrdersService;
import com.zsmall.order.entity.mapper.TiktokLineOrderCompensationMapper;
import com.zsmall.system.entity.iservice.ITenantSalesChannelService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【tiktok_line_order_compensation】的数据库操作Service实现
 * @createDate 2024-06-04 17:19:19
 */
@Service(value = "tiktokLineOrderCompensationService")
@Slf4j
public class TiktokLineOrderCompensationServiceImpl extends ServiceImpl<TiktokLineOrderCompensationMapper, TikTokLineOrderCompensation>
    implements TiktokLineOrderCompensationService {
    @Resource
    private SysOssServiceImpl sysOssService;
    @Resource
    private TikTokShopApiUtil tikTokShopApiUtil;

    @Resource
    private ITenantSalesChannelService tenantSalesChannelService;

    @Resource
    private SysOssMapper sysOssMapper;
    @Resource
    private IOrdersService iOrdersService;
    @Resource
    private DistributorOrderService distributorOrderService;

    @Resource
    private ITikTokLineOrderCompensationServiceImpl iTikTokLineOrderCompensationService;
    @Override
    public void cancellationCompensationPlan(String type) {
        LambdaQueryWrapper<TikTokLineOrderCompensation> eq = new LambdaQueryWrapper<TikTokLineOrderCompensation>().eq(TikTokLineOrderCompensation::getChannelType, type)
                                                                                                                  .eq(TikTokLineOrderCompensation::getStatus, 0);
        List<TikTokLineOrderCompensation> tikTokLineOrderCompensations = baseMapper.selectList(eq);
        ThreadPoolExecutor executor = new ThreadPoolExecutor(10, 20, 120L, TimeUnit.SECONDS, new LinkedBlockingQueue<>());

        ConcurrentHashMap<String, TikTokLineOrderCompensation> compensationConcurrentHashMap = new ConcurrentHashMap<>();

        CountDownLatch downLatch = new CountDownLatch(tikTokLineOrderCompensations.size());

        for (TikTokLineOrderCompensation compensation : tikTokLineOrderCompensations) {
            executor.submit(() -> {
                try {
                    compensationOrder(compensation,compensationConcurrentHashMap);
                } catch (Exception e) {
                    e.printStackTrace();
                    log.info("获取产品映射失败:{}", e.getMessage());
                }finally {
                    downLatch.countDown();
                }
            });

        }

        try {
            downLatch.await();
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }finally {
            executor.shutdown();
        }
        ArrayList<TikTokLineOrderCompensation> compensations = new ArrayList<>(compensationConcurrentHashMap.values());
        iTikTokLineOrderCompensationService.updateBatchById(compensations);
//         面单补偿 先测试上面的
        List<TikTokLineOrderCompensation> collect = compensations.stream()
                                                                 .filter(compensation -> compensation.getStatus() == 11)
                                                                 .collect(Collectors.toList());
        if (CollUtil.isNotEmpty(collect)) {
            // 补偿成功,绑定面单
            List<Long> ids = collect.stream().filter(order -> TiktokLogisticsTypeEnum.TIKTOK.name()
                                                                                                 .equals(order.getShippingType()))
                                         .map(TikTokLineOrderCompensation::getOrderItemId).collect(Collectors.toList());
            binding(ids);
        }
    }

    private TikTokApiReturnMsg getTikTokApiReturnMsg(TikTokExpressSheet tikTokExpressSheet) {
        TikTokApiReturnMsg tikTokApiReturnMsg = new TikTokApiReturnMsg();
        tikTokApiReturnMsg.setChannel(tikTokExpressSheet.getChannel())
                          .setOrderNo(tikTokExpressSheet.getOrderNo());
        return tikTokApiReturnMsg;
    }

    /**
     * 功能描述：获取快递单
     *
     * @param order 顺序
     * @return {@link TikTokExpressSheet }
     * <AUTHOR>
     * @date 2024/04/01
     */
    private TikTokExpressSheet getExpressSheet(TikTokLineOrderCompensation order) {
        TikTokExpressSheet tikTokExpressSheet = new TikTokExpressSheet();
        tikTokExpressSheet.setSplitOrCombineTag(order.getSplitOrCombineTag());
        String shippingType = order.getShippingType();
        if (TiktokLogisticsTypeEnum.TIKTOK.name().equals(shippingType)) {
            shippingType = LogisticsTypeEnum.PickUp.name();
        }
        if (TiktokLogisticsTypeEnum.SELLER.name().equals(shippingType)) {
            shippingType = LogisticsTypeEnum.DropShipping.name();
        }
        tikTokExpressSheet.setShippingType(shippingType);
        tikTokExpressSheet.setOrderStatus(order.getOrderStatus());
        tikTokExpressSheet.setChannel(ChannelTypeEnum.TikTok.name());
        tikTokExpressSheet.setOrderNo(order.getOrderId());


        return tikTokExpressSheet;
    }

    /**
     * 功能描述：赔偿令 只能对 获取不到面单的异常信息进行补偿,如果是业务问题导致的 没有面单不进行补偿
     *
     * @param compensation                  补偿
     * @param compensationConcurrentHashMap 补偿并发散列映射
     * <AUTHOR>
     * @date 2024/06/17
     */
    private void compensationOrder(TikTokLineOrderCompensation compensation,ConcurrentHashMap<String, TikTokLineOrderCompensation> compensationConcurrentHashMap) {
        TikTokExpressSheet tikTokExpressSheet = getExpressSheet(compensation);
        TikTokApiReturnMsg tikTokApiReturnMsg = getTikTokApiReturnMsg(tikTokExpressSheet);
        // 面单url 相关信息
        TikTokExpressRelevant tikTokExpressRelevant = new TikTokExpressRelevant();
        String shopId = compensation.getShopId();
        try {

            if (tikTokApiReturnMsg != null) {
                tikTokApiReturnMsg.setChannel(tikTokExpressSheet.getChannel());
                tikTokApiReturnMsg.setOrderNo(tikTokExpressSheet.getOrderNo());
            }

            //1.1 判断订单物流模式是平台提供还是卖家自己提供，卖家自己提供不买面单
            if (LogisticsTypeEnum.DropShipping.name().equals(tikTokExpressSheet.getShippingType())) {
                log.info("channel {} getLabel orderNo {} 物流模式为卖家自提供", tikTokApiReturnMsg.getChannel(), tikTokApiReturnMsg.getOrderNo());
                return;
            }
//            拆单失败的不予以补偿
            //2.2 获取面单信息
            String status = compensation.getDisplayStatus();
            if (OrderStatusEnum.AWAITING_SHIPMENT.name()
                                                 .equals(status) || OrderStatusEnum.AWAITING_COLLECTION.name()
                                                                                                       .equals(status)) {
                if (ObjectUtil.isEmpty(compensation)) {
                    log.info("channel {} getLabel orderNo {} 订单明细行不存在", tikTokApiReturnMsg.getChannel(), tikTokApiReturnMsg.getOrderNo());
                    TikTokLineOrderCompensation tikTokLineOrderCompensation = new TikTokLineOrderCompensation();
                    tikTokLineOrderCompensation.setStatus(12);
                    compensationConcurrentHashMap.put(String.valueOf(compensation.getOrderItemId()), tikTokLineOrderCompensation);
                    return;
                }
                //2.2.1 判断是否有包裹ID，无包裹ID先创建包裹
                String packageId = compensation.getPackageId();
                TikTokCreatePackagesBody packagesBody = new TikTokCreatePackagesBody();

                packagesBody.setOrderId(compensation.getOrderId());
                packagesBody.setOrderLineItemIds(Arrays.asList(String.valueOf(compensation.getOrderItemId())));
                String body = JSONObject.toJSONString(packagesBody);
                TikTokCreatePackageResp tikTokCreatePackageResp = null;
                // 此处虽然做了判断,但是packageId一定要有,拆单后tk没有提供根据lineId获取包裹信息的接口
                if (StrUtil.isEmpty(packageId)) {
                    tikTokCreatePackageResp = tikTokShopApiUtil.createPackage(body, shopId);

                    if (ObjectUtil.isNotEmpty(tikTokCreatePackageResp)) {
                        TikTokCreatePackageData data = tikTokCreatePackageResp.getData();
                        if (ObjectUtil.isNotEmpty(data)) {
                            packageId = data.getPackageId();

                        }
                        tikTokApiReturnMsg.setMsg(null);
                        tikTokApiReturnMsg.setErrorMsg(null);

                    } else {
                        TikTokLineOrderCompensation tikTokLineOrderCompensation = new TikTokLineOrderCompensation();
                        tikTokLineOrderCompensation.setStatus(12);
                        compensationConcurrentHashMap.put(String.valueOf(compensation.getOrderItemId()), tikTokLineOrderCompensation);
                        return;
                    }
                }

                if (StrUtil.isBlank(packageId)) {
                    compensation.setStatus(12);
                    updateById(compensation);
                    log.info("channel {} getLabel orderNo {} 创建包裹失败,错误信息:{}", tikTokApiReturnMsg.getChannel(), tikTokApiReturnMsg.getOrderNo(), tikTokCreatePackageResp.getMessage());
                    if (StrUtil.isEmpty(tikTokApiReturnMsg.getMsg())) {
                        tikTokApiReturnMsg.setErrorMsg("创建包裹失败");
                    }
                    // 设置失败信息;
                    TikTokLineOrderCompensation tikTokLineOrderCompensation = new TikTokLineOrderCompensation();
                    tikTokLineOrderCompensation.setStatus(12);
                    compensationConcurrentHashMap.put(String.valueOf(compensation.getOrderItemId()), tikTokLineOrderCompensation);
                    return;
                }

                tikTokApiReturnMsg.setErrorMsg(null);

                //2.2.2 获取包裹信息
                if (StrUtil.isBlank(compensation.getTrackingNo())) {

                    ConcurrentHashMap<String, PackageDetailsResp> packageDetails = tikTokShopApiUtil.getPackageDetails(Arrays.asList(packageId), shopId);
                    if (CollUtil.isNotEmpty(packageDetails)) {
                        PackageDetailsResp packageDetailsResp = packageDetails.get(packageId);
                        Integer code = packageDetailsResp.getCode();
                        if (21001001 == code) {
                            //packageId发生变更，获取最新packageId

                            List<String> orderIdList = Arrays.asList(compensation.getOrderId());
                            List<TikTokOrder> tikTokOrders = tikTokShopApiUtil.getOrderDetail(orderIdList, shopId);
                            // tikTokOrders 中所有的 lineItems 都拿出来放入
                            Map<String, TikTokLineItem> lineMaps = processOrdersWithStreams(tikTokOrders);
                            packageId = lineMaps.get(String.valueOf(compensation.getOrderItemId())).getPackageId();
                            if (CollectionUtil.isNotEmpty(tikTokOrders)) {

                                ConcurrentHashMap<String, PackageDetailsResp> packageDetailsNew = tikTokShopApiUtil.getPackageDetails(Arrays.asList(packageId), shopId);
                                PackageDetailsResp detailsResp = packageDetailsNew.get(packageId);
                                if (ObjectUtil.isNotEmpty(detailsResp)) {

                                    String trackingNumber = detailsResp.getTrackingNumber();
                                    String shippingProviderName = detailsResp.getShippingProviderName();

                                    tikTokExpressRelevant.setTrackingNumber(trackingNumber);
                                    tikTokExpressRelevant.setShipMethod(shippingProviderName);
                                }
                            }
                        } else {

                            String trackingNumber = packageDetailsResp.getTrackingNumber();
                            String shippingProviderName = packageDetailsResp.getShippingProviderName();
                            tikTokExpressRelevant.setTrackingNumber(trackingNumber);
                            tikTokExpressRelevant.setShipMethod(shippingProviderName);
                        }
                    } else {
                        // 这里直接就根据id去更新失败就行
                        TikTokLineOrderCompensation tikTokLineOrderCompensation = new TikTokLineOrderCompensation();
                        tikTokLineOrderCompensation.setStatus(12);
                        compensationConcurrentHashMap.put(String.valueOf(compensation.getOrderItemId()), tikTokLineOrderCompensation);

                        log.info("包裹详情获取失败");
                        return;
                    }
                } else {
                    tikTokExpressRelevant.setTrackingNumber(compensation.getTrackingNo());
//                    tikTokExpressRelevant.setShipMethod(lineItem.getShippingProviderName());
                }

                if (StringUtils.isBlank(tikTokExpressRelevant.getTrackingNumber())) {

                    log.error("channel {} getLabel orderNo {} 获取TrackingNo失败", tikTokApiReturnMsg.getChannel(), tikTokApiReturnMsg.getOrderNo());
                    TikTokLineOrderCompensation tikTokLineOrderCompensation = new TikTokLineOrderCompensation();
                    tikTokLineOrderCompensation.setStatus(12);
                    compensationConcurrentHashMap.put(String.valueOf(compensation.getOrderItemId()), tikTokLineOrderCompensation);
                    return;
                }

                //2.2.3 获取面单URL
                TikTokPackShippingDocument trackingUrl = tikTokShopApiUtil.getPackageShippingDocument(packageId, shopId);
                if(ObjectUtil.isEmpty(trackingUrl)&&21023046==trackingUrl.getCode()){
                    // 未通过系统补偿已经完成发货
                    TikTokLineOrderCompensation tikTokLineOrderCompensation = new TikTokLineOrderCompensation();
                    tikTokLineOrderCompensation.setStatus(13);
                    compensationConcurrentHashMap.put(String.valueOf(compensation.getOrderItemId()), tikTokLineOrderCompensation);
                }
                tikTokExpressRelevant.setFileUrl(trackingUrl.getData().getDocUrl());

                if (ObjectUtil.isNull(trackingUrl)) {

                    log.info("channel {} getLabel orderNo {} 获取面单失败", tikTokApiReturnMsg.getChannel(), tikTokApiReturnMsg.getOrderNo());
                    tikTokApiReturnMsg.setErrorMsg("获取面单失败");
                    TikTokLineOrderCompensation tikTokLineOrderCompensation = new TikTokLineOrderCompensation();
                    tikTokLineOrderCompensation.setStatus(12);
                    compensationConcurrentHashMap.put(String.valueOf(compensation.getOrderItemId()), tikTokLineOrderCompensation);
                    return;
                } else {
                    // 上传oss
                    sysOssService.downloadPdfNotAsync(trackingUrl.getData()
                                                                 .getDocUrl(), String.valueOf(compensation.getOrderItemId()), tikTokExpressRelevant.getTrackingNumber());

                    log.info("oss上传成功");

                }
            }
            TikTokLineOrderCompensation tikTokLineOrderCompensation = new TikTokLineOrderCompensation();
            tikTokLineOrderCompensation.setStatus(11);
            compensationConcurrentHashMap.put(String.valueOf(compensation.getOrderItemId()), tikTokLineOrderCompensation);


        }catch (Exception e){
            log.info("获取面单失败:{},渠道订单号:{},自订单号:{}",e.getMessage(),compensation.getOrderId(),compensation.getOrderItemId());
        }
    }



    public static Map<String, TikTokLineItem> processOrdersWithStreams(List<TikTokOrder> tikTokOrders) {
        return tikTokOrders.stream() // 将List转换为Stream
                           .flatMap(order -> order.getLineItems().stream()) // 将每个order的lineItems展平为一个单独的Stream
                           .collect(Collectors.toMap(TikTokLineItem::getId, lineItem -> lineItem, (existing, replacement) -> existing)); // 收集到Map中，处理可能的键冲突（这里我们选择保留现有的值）
    }
    public void binding(List<Long> ids){

        HashMap<Long, Object> map = new HashMap<>();
        if (CollUtil.isNotEmpty(ids)) {
            ids.forEach(item -> {

                SysOss oss = sysOssMapper.selectOne(new LambdaQueryWrapper<SysOss>().eq(SysOss::getBusinessId, item).last("limit 1"));
                SysOssVo sysOssVo = MapstructUtils.convert(oss, SysOssVo.class);
                // 后续开异步了再用中间件
//                RedissonClient client = RedisUtils.getClient();
//                String key = "order:sys:oss:"+item;
//                SysOssVo sysOssVo = (SysOssVo) client.getBucket(key).get();
//                if(ObjectUtil.isEmpty(sysOssVo)){
//                    sysOssVo = sysOssMapper.selectByLineOrderItemId(item);
//                }
                if (ObjectUtil.isNull(sysOssVo)) {
                    log.error("子订单获取面单失败:{}", item);
                }
                if (ObjectUtil.isNotEmpty(sysOssVo)) {
                    map.put(item, sysOssVo);
                }
            });
            // 查到所有的orders
            List<Orders> ordersList = TenantHelper.ignore(() -> iOrdersService.list(new LambdaQueryWrapper<Orders>()
                .eq(Orders::getLogisticsType, LogisticsTypeEnum.PickUp)
                .in(Orders::getLineOrderItemId, ids)));
            if (ordersList.size() < ids.size()) {
                // 如果查到的订单数和lineItemIds的数量不一致,说明部分订单录入失败,以查到的订单数为准
                // 找到ordersList中没有,但lineItemIds里有的id,通过Orders.lineOrderItemId=lineItemIds
                Set<String> orderLineItemIdsSet = ordersList.stream()
                                                            .map(Orders::getLineOrderItemId)
                                                            .collect(Collectors.toSet());
                List<Long> missingIds = ids.stream()
                                                     .filter(id -> !orderLineItemIdsSet.contains(id))
                                                     .collect(Collectors.toList());
                log.error("Missing orders: {}", missingIds);
            }

            // 上传附件-面单
            List<Orders> list = new ArrayList<>();

            for (Orders order : ordersList) {

                SysOssVo sysOssVo = (SysOssVo) map.get(order.getLineOrderItemId());
                if (ObjectUtil.isNotEmpty(sysOssVo)) {
                    OrderUpdateBo bo = new OrderUpdateBo(order.getOrderNo(), null, true, sysOssVo);
                    // 实际上传完了以后已经有了文件了 不需要再走上传的逻辑了
                    distributorOrderService.uploadShippingLabel(bo);
                    list.add(order);
                }
            }
            List<Orders> updatedOrdersList = list.stream()
                                                 .map(order -> {
                                                     order.setSplit(true);
                                                     return order;
                                                 })
                                                 .collect(Collectors.toList());
            if(CollUtil.isNotEmpty(updatedOrdersList)){
                iOrdersService.updateBatchById(updatedOrdersList);
            }

        }
    }
}






