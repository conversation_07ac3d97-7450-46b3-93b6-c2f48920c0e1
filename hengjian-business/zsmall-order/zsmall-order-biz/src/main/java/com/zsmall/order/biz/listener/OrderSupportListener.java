package com.zsmall.order.biz.listener;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import com.hengjian.common.tenant.helper.TenantHelper;
import com.hengjian.openapi.service.ISysApiService;
import com.zsmall.common.enums.order.LogisticsProgress;
import com.zsmall.order.biz.service.OrderRefundService;
import com.zsmall.order.biz.support.OrderSupport;
import com.zsmall.order.entity.domain.*;
import com.zsmall.order.entity.domain.bo.refund.RefundApplyHandleBo;
import com.zsmall.order.entity.domain.dto.OrderItemDTO;
import com.zsmall.order.entity.domain.dto.OrderPriceCalculateDTO;
import com.zsmall.order.entity.domain.event.*;
import com.zsmall.order.entity.iservice.*;
import com.zsmall.order.event.RefundApplyHandleEvent;
import com.zsmall.product.entity.domain.ProductSku;
import com.zsmall.product.entity.iservice.IProductSkuService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Set;

/**
 * 订单相关支持-监听类
 *
 * <AUTHOR>
 * @date 2023/6/27
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class OrderSupportListener {
    private final IOrderItemService iOrderItemService;
    private final OrderSupport orderSupport;
    private final IProductSkuService iProductSkuService;
    private final IOrderAddressInfoService iOrderAddressInfoService;
    private final IOrderLogisticsInfoService iOrderLogisticsInfoService;
    private final OrderRefundService orderRefundService;
    private final IOrderRefundItemService iOrderRefundItemService;
    private final IOrderRefundService iOrderRefundService;
    private final ISysApiService iSysApiService;
    @EventListener
    public void listenInGenerateOrderItem(GenerateOrderItemEvent event) throws Exception {
        String productSkuCode = event.getProductSkuCode();
        ProductSku productSku = iProductSkuService.queryByProductSkuCodeIncludeDel(productSkuCode);

        OrderPriceCalculateDTO paramDTO = BeanUtil.toBean(event, OrderPriceCalculateDTO.class);

        String dTenantId = event.getDTenantId();
        Orders order = event.getOrder();
        Integer totalQuantity = event.getTotalQuantity();
        OrderAddressInfo addressInfo = iOrderAddressInfoService.getByOrderNo(order.getOrderNo());
        OrderLogisticsInfo logisticsInfo = iOrderLogisticsInfoService.getByOrderNo(order.getOrderNo());
        OrderItemDTO orderItemDTO = orderSupport.generateOrderItem(dTenantId, order, productSku, totalQuantity, paramDTO,addressInfo.getZipCode(),logisticsInfo.getLogisticsCompanyName());
        event.setOutDTO(orderItemDTO);
    }

    @EventListener
    public void listenInSetOrderFulfillmentProgressEvent(SetOrderFulfillmentProgressEvent event) throws Exception {
        Set<Long> orderIdList = event.getOrderIdList();
        orderSupport.setOrderFulfillmentProgress(orderIdList);
    }

    @EventListener
    public void listenInRecalculateOrderAmount(RecalculateOrderAmountEvent event) {
        orderSupport.recalculateOrderAmount(event.getOrder(), event.getOrderItemPriceList());
    }

    @EventListener
    public void listenInCountSoldNumber(CountSoldNumberEvent event) {
        Integer sold = orderSupport.countSoldNumber(event.getOrderState(), event.getProductCode(), event.getSku());
        event.setSold(sold);
    }

    /**
     * 统计平台订单支付退款金额
     * @param event
     */
    @EventListener
    public void listenStatsPlatformOrderPaymentRefundAmount(StatsPlatformOrderPaymentRefundAmountEvent event) {
        BigDecimal orderTotalAmount = orderSupport.statsPlatformOrderPaymentAmount(event.getInStartDate(), event.getInEndDate());
        BigDecimal orderTotalAmount4Wholesale = orderSupport.statsPlatformOrderPaymentAmount4Wholesale(event.getInStartDate(), event.getInEndDate());
        BigDecimal refundTotalAmount = orderSupport.statsPlatformOrderRefundAmount(event.getInStartDate(), event.getInEndDate());
        event.setOrderTotalAmount(NumberUtil.add(orderTotalAmount, orderTotalAmount4Wholesale));
        event.setRefundTotalAmount(refundTotalAmount);
    }

    @EventListener
    public void refundApplyHandleEvent(RefundApplyHandleEvent event) throws Exception {
        RefundApplyHandleBo bo = new RefundApplyHandleBo();
        if(ObjectUtil.isNotEmpty(event.isSupplierCall())&&event.isSupplierCall()){
            bo.setSupplierCall(true);
        }
        List<String> orderRefundNoList = event.getOrderRefundNoList();
        String orderRefundNo = orderRefundNoList.get(0);
        Orders orders = TenantHelper.ignore(()->iOrderRefundService.queryOrderByRefundNo(orderRefundNo));
        OrderRefund orderRefund = TenantHelper.ignore(()->iOrderRefundService.queryByRefundNo(orderRefundNo));
        log.info("退款申请处理事件,订单信息:{}", orders);
        String tenantId = orders.getTenantId();
        List<OrderItem> orderItems = TenantHelper.ignore(()->iOrderItemService.getListByOrderId(orders.getId()));
        if(CollUtil.isNotEmpty(orderItems)){
            String supplierTenantId = orderItems.get(0).getSupplierTenantId();
            // sysConifg
            boolean notExist = TenantHelper.ignore(()->iSysApiService.isNotExist(supplierTenantId));
            log.info("是否自动化流程退款:{}", !notExist);
            bo.putOrderRefundNoList(orderRefundNoList);

            bo.setPass(Boolean.TRUE);
//            bo.setRefundApplyType(RefundApplyType.RefundReturn.getValue());
            bo.setRefundApplyType(orderRefund.getRefundType().getValue());
            // 如果登陆用户是供应商,实际上是走了手动拒绝逻辑 拒绝逻辑要走非自动流程
            if (ObjectUtil.isNotEmpty(orders) && LogisticsProgress.UnDispatched.equals(orders.getFulfillmentProgress()) && !notExist) {
                bo.setIsAuto(Boolean.TRUE);
                Boolean isAuto = event.getIsAuto();
                if(Boolean.TRUE.equals(!isAuto)){
                    bo.setIsAuto(Boolean.FALSE);
                }
            } else {
                bo.setIsAuto(Boolean.FALSE);
            }

            // 调用原本的退款逻辑
            orderRefundService.refundApplyHandle(bo);
        }

    }
}
