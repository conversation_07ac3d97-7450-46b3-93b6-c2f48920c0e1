package com.zsmall.order.biz.test.job;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.http.Header;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hengjian.common.core.utils.JacksonUtils;
import com.hengjian.stream.mq.constant.RabbitMqConstant;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.zsmall.common.domain.vo.CancellationCompensation;
import com.zsmall.common.domain.vo.XxlJobSearchVO;
import com.zsmall.common.enums.common.ChannelTypeEnum;
import com.zsmall.order.biz.service.CompensationJobService;
import com.zsmall.order.biz.support.OrderSupport;
import com.zsmall.order.entity.domain.Orders;
import com.zsmall.order.entity.domain.vo.OrderCleanVo;
import com.zsmall.order.entity.iservice.IOrdersService;
import com.zsmall.system.entity.domain.TenantSalesChannel;
import com.zsmall.system.entity.domain.vo.salesChannel.SaleChannelCallBackVo;
import com.zsmall.system.entity.iservice.ITenantSalesChannelService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static com.zsmall.common.util.CollectionUtils.objectToJson;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2024/2/18 10:24
 */
@Slf4j
@Service
public class TikTokHandlerJob {
    private final static Logger logger = LoggerFactory.getLogger(TikTokHandlerJob.class);
    @Resource
    private CompensationJobService testJob;
    @Value("${distribution.tiktok.appKey}")
    public String appKey;
    @Resource
    private ITenantSalesChannelService salesChannelService;
    @Value("${distribution.tiktok.appSecret}")
    public String appSecret;

    @Resource
    private OrderSupport orderSupport;

    @Resource
    private IOrdersService iOrdersService;
    public static final String ACCESS_TOKEN = "accessToken";
    @Resource
    private RabbitTemplate rabbitTemplate;

    @XxlJob("pullOrderToErpHandler")
    public void pullOrderToErpHandler() throws Exception {
        List<Orders> orders = iOrdersService.list(new LambdaQueryWrapper<Orders>().eq(Orders::getChannelType, "TikTok")
                                                                                .eq(Orders::getChannelReceiptStatus, "NotReceipted"));
        testJob.pullOrderToErpByOrders(orders);
    }

    @XxlJob("cancellationCompensationHandler")
    public void cancellationCompensationHandler() throws Exception {
        //  渠道
        String type = XxlJobHelper.getJobParam();
        CancellationCompensation xxlBody = JSON.parseObject(type, CancellationCompensation.class);
        testJob.cancellationCompensationHandler(xxlBody.getType());
    }


    @XxlJob("tiktokOrderJobHandlerForUpdate")
    public void tiktokOrderJobHandlerForUpdate() throws Exception {
        String jobParam = XxlJobHelper.getJobParam();
        XxlJobSearchVO xxlBody = JSON.parseObject(jobParam, XxlJobSearchVO.class);

        String tenantId = xxlBody.getTenantId();
        xxlBody.setTenantId(null);

        XxlJobSearchVO xxlJobSearchVO = new XxlJobSearchVO();
        // 复制属性
        BeanUtils.copyProperties(xxlBody, xxlJobSearchVO);

//        xxlJobSearchVO.setPageSize("100");
        LocalDateTime currentDateTime = LocalDateTime.now();

        // 减去5天
        LocalDateTime dateTimeFiveDaysAgo = currentDateTime.minusDays(5);
        Instant nowDays = currentDateTime.atZone(ZoneId.systemDefault()).toInstant();

        // 转换为Instant对象（以UTC为基准）
        Instant instantFiveDaysAgo = dateTimeFiveDaysAgo.atZone(java.time.ZoneId.systemDefault()).toInstant();

        // 转换为秒时间戳
        long timestampInSeconds = Math.floorDiv(instantFiveDaysAgo.getEpochSecond(), 1);

        long nowTime = Math.floorDiv(nowDays.getEpochSecond(), 1);
        xxlJobSearchVO.setCreateTimeGe(timestampInSeconds);
        xxlJobSearchVO.setCreateTimeLt(nowTime);

        String jsonString = JSONObject.toJSONString(xxlJobSearchVO);
        testJob.pullOrder(ChannelTypeEnum.TikTok.name(),jsonString, tenantId);
    }
    @XxlJob("tiktokOrderJobHandlerForAll")
    public void tiktokOrderJobHandlerForAll() throws Exception {

        XxlJobSearchVO xxlJobSearchVO = new XxlJobSearchVO();

        xxlJobSearchVO.setPageSize("100");

        String jsonString = JSONObject.toJSONString(xxlJobSearchVO);

        testJob.pullOrder(ChannelTypeEnum.TikTok.name(),jsonString, null);
    }

    @XxlJob("tiktokProductJobHandler")
    public void tiktokProductJobHandler() throws Exception {
        String vo = XxlJobHelper.getJobParam();
        XxlJobSearchVO xxlBody = JSON.parseObject(vo, XxlJobSearchVO.class);
        String jsonString = JSONObject.toJSONString(xxlBody);
        testJob.pullProduct(ChannelTypeEnum.TikTok.name(),jsonString);
    }

    @XxlJob("tiktokProductJobHandlerForUpdate")
    public void tiktokProductJobHandlerForUpdate() throws Exception {
        String jobParam = XxlJobHelper.getJobParam();
        XxlJobSearchVO xxlBody = JSON.parseObject(jobParam, XxlJobSearchVO.class);


        XxlJobSearchVO xxlJobSearchVO = new XxlJobSearchVO();
        // 复制属性
        BeanUtils.copyProperties(xxlBody, xxlJobSearchVO);

//        xxlJobSearchVO.setPageSize("100");
        // 当前时间-15分钟 到 当前时间


        LocalDateTime currentDateTime = LocalDateTime.now();

        // 减去5天
        LocalDateTime dateTimeFiveDaysAgo = currentDateTime.minusDays(5);
        Instant nowDays = currentDateTime.atZone(ZoneId.systemDefault()).toInstant();

        // 转换为Instant对象（以UTC为基准）
        Instant instantFiveDaysAgo = dateTimeFiveDaysAgo.atZone(java.time.ZoneId.systemDefault()).toInstant();

        // 转换为秒时间戳
        long timestampInSeconds = Math.floorDiv(instantFiveDaysAgo.getEpochSecond(), 1);

        long nowTime = Math.floorDiv(nowDays.getEpochSecond(), 1);

        xxlJobSearchVO.setUpdateTimeGe(timestampInSeconds);
        xxlJobSearchVO.setUpdateTimeLt(nowTime);
        String jsonString = JSONObject.toJSONString(xxlJobSearchVO);
        testJob.pullProduct(ChannelTypeEnum.TikTok.name(),jsonString);
    }

    @XxlJob("tiktokRefreshToken")
    public void tiktokRefreshToken() throws Exception {
        LambdaQueryWrapper<TenantSalesChannel> lambdaQueryWrapper = new LambdaQueryWrapper<TenantSalesChannel>().eq(TenantSalesChannel::getChannelType, ChannelTypeEnum.TikTok.name());
        List<TenantSalesChannel> channelList = salesChannelService.list(lambdaQueryWrapper);
        if (CollectionUtils.isNotEmpty(channelList)) {
            String url = "https://auth.tiktok-shops.com/api/v2/token/refresh?";
            for (TenantSalesChannel tenantSalesChannel : channelList) {
                try {
                    String connectStr = tenantSalesChannel.getConnectStr();
                    if (StringUtils.isEmpty(connectStr)) {
                        logger.info("channel:{}, 接口参数不存在", tenantSalesChannel.getChannelType());
                        continue;
                    }
                    JSONObject obj = JSONObject.parseObject(connectStr);
                    if (obj == null) {
                        logger.info("channel:{}, 接口参数转换有误", tenantSalesChannel.getChannelType());
                    }

                    String refreshToken = null;
                    if (obj.containsKey("refreshToken")) {
                        refreshToken = obj.getString("refreshToken");
                        if (StringUtils.isEmpty(refreshToken)) {
                            logger.info("channel:{}, refreshToken值为空", tenantSalesChannel.getChannelType());
                            continue;
                        }
                    } else {
                        logger.info("channel:{}, refreshToken不存在", tenantSalesChannel.getChannelType());
                        continue;
                    }
                    String param = "grant_type=refresh_token&app_key=" + appKey + "&app_secret=" + appSecret + "&refresh_token=" + refreshToken;

                    HttpResponse httpResponse = HttpRequest.get(url + param)
                                                           .header(Header.ACCEPT_CHARSET, "UTF-8")
                                                           .body("")
                                                           .timeout(20000)//超时，毫秒
                                                           .execute();

                    String body = httpResponse.body();

                    //打出日志
                    logger.info("请求tiktok的返回值为:{}", httpResponse);
                    logger.info("请求tiktok的返回值body为:{}", body);

                    //拿到refreshToken的值
                    String newRefreshToken = "";
                    Map<String, Object> responseMap = JacksonUtils.jsonToMap(body);
                    Map<String, Object> dataMap = (Map<String, Object>) responseMap.get("data");
                    if (dataMap.containsKey("refresh_token")) {
                        newRefreshToken = (String) dataMap.get("refresh_token");
                    } else {
                        logger.info("channel#{}请求tiktok的返回值里面没有refresh_toke的值,授权失败", tenantSalesChannel.getChannelType());
                        continue;
                    }

                    String accessToken = "";
                    if (dataMap.containsKey("access_token")) {
                        accessToken = (String) dataMap.get("access_token");
                    }

                    //找到渠道对应的参数，并更新
                    if (ObjectUtil.isNotEmpty(tenantSalesChannel)) {
                        Map<String, Object> connectMap = JacksonUtils.jsonToMap(connectStr);

                        connectMap.put("refreshToken", newRefreshToken);
                        connectMap.put("accessToken", accessToken);
                        String newConnectStr = JacksonUtils.toJson(connectMap);

                        tenantSalesChannel.setConnectStr(newConnectStr);
                        salesChannelService.saveOrUpdate(tenantSalesChannel);
                        // 将刷新的token传到多渠道
                        try {
                            if(null != tenantSalesChannel.getState() && tenantSalesChannel.getState().equals(1)){
                                SaleChannelCallBackVo saleChannelCallBackVo = new SaleChannelCallBackVo();
                                saleChannelCallBackVo.setChannelFlag(tenantSalesChannel.getChannelName()).setType(tenantSalesChannel.getChannelType()).setSource_system("distribution").setConnectStr(tenantSalesChannel.getConnectStr());
                                log.info("tiktok店铺刷新token发送到多渠道,发送数据：{}",saleChannelCallBackVo);
                                rabbitTemplate.convertAndSend(RabbitMqConstant.MULTICHANNEL_SEND_EXCHANGE,RabbitMqConstant.TIKTOK_SHOP_ROUTING_KEY,objectToJson(saleChannelCallBackVo));
                            }
                        }catch (Exception e){
                            log.error("将回调信息发送到多渠道异常，异常信息: {}",e.getMessage());
                        }
                    } else {
                        logger.info("请求tiktok的渠道flag:{} 有问题,没有此账号", tenantSalesChannel.getChannelType());
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                    logger.info("channel:{} 更新refreshToken异常：{}", tenantSalesChannel.getChannelType(), e.getMessage());
                }
            }
        }
    }

    @XxlJob("erpCleanJob")
    public void erpCleanJob() throws Exception {
        List<OrderCleanVo> list = JSON.parseArray(XxlJobHelper.getJobParam(), OrderCleanVo.class);
        List<Orders> orders = new ArrayList<>();
        for (OrderCleanVo orderCleanVo : list) {
            List<Orders> list1 = iOrdersService.list(new LambdaQueryWrapper<Orders>().eq(Orders::getChannelOrderNo, orderCleanVo.getBillCode()));
            for (Orders orders1 : list1) {
                orders1.setCreateTime(orderCleanVo.getBillDate());
                orders1.setPayTime(orderCleanVo.getBillDate());
                orders1.setUpdateTime(orderCleanVo.getBillDate());
                orders.add(orders1);
            }
        }
        iOrdersService.updateBatchById(orders);

    }
    @XxlJob("pushToThirdWareHouseJob")
    public void pushToThirdWareHouseFollowUp() throws Exception {
        log.info("开始推送到第三方仓库");
        orderSupport.pushToThirdWareHouseFollowUp();

    }
}
