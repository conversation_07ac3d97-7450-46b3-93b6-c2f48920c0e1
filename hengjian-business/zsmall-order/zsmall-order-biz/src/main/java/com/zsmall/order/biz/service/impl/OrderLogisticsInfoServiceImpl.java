package com.zsmall.order.biz.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import com.hengjian.common.core.domain.R;
import com.hengjian.common.core.domain.model.LoginUser;
import com.hengjian.common.core.enums.TenantType;
import com.hengjian.common.core.exception.RStatusCodeException;
import com.hengjian.common.satoken.utils.LoginHelper;
import com.zsmall.common.domain.LocaleMessage;
import com.zsmall.common.enums.ExcelMessageEnum;
import com.zsmall.common.enums.common.ChannelTypeEnum;
import com.zsmall.common.enums.order.*;
import com.zsmall.common.enums.orderShippingRecord.ShippingStateEnum;
import com.zsmall.common.enums.statuscode.ZSMallStatusCodeEnum;
import com.zsmall.common.exception.ExcelMessageException;
import com.zsmall.common.util.ZExcelUtil;
import com.zsmall.order.biz.service.OrderLogisticsInfoService;
import com.zsmall.order.biz.support.OrderSupport;
import com.zsmall.order.biz.support.ThirdPartyLogisticsSupport;
import com.zsmall.order.biz.support.WholesaleOrderSupport;
import com.zsmall.order.entity.domain.*;
import com.zsmall.order.entity.domain.bo.ConfirmDispatchedBo;
import com.zsmall.order.entity.domain.dto.DispatchedImportDTO;
import com.zsmall.order.entity.iservice.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Sheet;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.web.multipart.MultipartFile;

import java.io.InputStream;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 主订单物流信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-06-07
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class OrderLogisticsInfoServiceImpl implements OrderLogisticsInfoService {

    private final IOrderLogisticsInfoService iOrderLogisticsInfoService;
    private final IOrderItemService iOrderItemService;
    private final IOrderItemShippingRecordService iOrderItemShippingRecordService;
    private final IOrdersService iOrdersService;
    private final IOrderItemProductSkuService iOrderItemProductSkuService;
    private final IOrderRefundService iOrderRefundService;
    private final IOrderItemTrackingRecordService iOrderItemTrackingRecordService;
    private final IOrderAttachmentService iOrderAttachmentService;
    private final ThirdPartyLogisticsSupport thirdPartyLogisticsSupport;
    private final IOrderItemPriceService iOrderItemPriceService;
    private final OrderSupport orderSupport;
    private final WholesaleOrderSupport wholesaleSupport;

    private final IThirdChannelFulfillmentRecordService iThirdChannelFulfillmentRecordService;

    /**
     * 确认发货
     *
     * @param bo
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public R<Void> confirmDispatched(ConfirmDispatchedBo bo) {
        LoginUser loginUser = LoginHelper.getLoginUser(TenantType.Supplier, TenantType.Manager);
        TenantType tenantType = loginUser.getTenantTypeEnum();
        String tenantId = loginUser.getTenantId();

        String orderItemNo = bo.getOrderItemNo();
        OrderItem orderItem;
        if (ObjectUtil.equals(tenantType, TenantType.Supplier)) {
            orderItem = iOrderItemService.getByOrderItemNo(orderItemNo, tenantId);
        } else {
            orderItem = iOrderItemService.getByOrderItemNo(orderItemNo);
        }

        if (orderItem != null) {
            Orders orders = iOrdersService.queryById(orderItem.getOrderId());
            if ( ObjectUtil.equals(orders.getFulfillmentProgress().name(),LogisticsProgress.UnDispatched.name())){
                if (orders.getCancelStatus()==1 || orders.getCancelStatus()==2){
                    throw new RuntimeException("订单状态为取消中或取消成功,不支持发货操作");
                }

            }
            String orderNo = orders.getOrderNo();
            OrderType orderType = orders.getOrderType();
            LogisticsTypeEnum logisticsType = orderItem.getLogisticsType();
            OrderStateType orderState = orderItem.getOrderState();
            OrderLogisticsInfo logisticsInfo = iOrderLogisticsInfoService.getByOrderNo(orderNo);
            String serviceName = null;
            if (logisticsInfo != null) {
                serviceName = logisticsInfo.getLogisticsServiceName();
            }
            ChannelTypeEnum channelType = orders.getChannelType();
            if (Objects.equals(orderState, OrderStateType.Verifying)) {
                return R.fail(ZSMallStatusCodeEnum.ORDER_REFUNDING_CANT_FULFILL);
            }

            // 存在进行中的退款单，禁止发货
            Integer inProgress = iOrderRefundService.countByInProgress(orders.getId());
            if (inProgress > 0) {
                return R.fail(ZSMallStatusCodeEnum.ORDER_REFUNDING_CANT_FULFILL);
            }

            if (ObjectUtil.equals(orderState, OrderStateType.Paid) && ObjectUtil.equals(orders.getOrderState(), OrderStateType.Paid)) {
                LogisticsProgress fulfillment = orderItem.getFulfillmentProgress();
                OrderItemProductSku orderItemProductSku = iOrderItemProductSkuService.getByOrderItemId(orderItem.getId());
                String sku = orderItemProductSku.getSku();
                String productSkuCode = orderItemProductSku.getProductSkuCode();
                String warehouseSystemCode = orderItemProductSku.getWarehouseSystemCode();

                // 未发货，或管理员账号已发货，才能继续往下走，管理员账号可以操作已发货的子订单重复发货
                if ((ObjectUtil.equals(fulfillment, LogisticsProgress.UnDispatched)
                    && ObjectUtil.equals(tenantType, TenantType.Supplier))
                    || (ObjectUtil.equals(fulfillment, LogisticsProgress.Dispatched) && ObjectUtil.equals(tenantType, TenantType.Manager))) {
                    String carrier = bo.getCarrier();
                    List<ConfirmDispatchedBo.TrackingNoDTO> trackingNoList = bo.getTrackingInfoList();

                    //创建新的跟踪单信息
                    List<OrderItemTrackingRecord> trackingRecordList = new ArrayList<>();
                    if (CollUtil.isNotEmpty(trackingNoList)) {
                        for (ConfirmDispatchedBo.TrackingNoDTO trackingNoDTO : trackingNoList) {
                            String trackingNo = trackingNoDTO.getTrackingNo();
                            // 防止误填多余空格
                            if (StrUtil.contains(trackingNo, " ")) {
                                trackingNo = StrUtil.replace(trackingNo, " ", "");
                            }
                            OrderItemTrackingRecord record = new OrderItemTrackingRecord();
                            record.setOrderNo(orders.getOrderNo());
                            record.setOrderItemNo(orderItemNo);
                            record.setSku(sku);
                            record.setProductSkuCode(productSkuCode);
                            record.setQuantity(orderItem.getTotalQuantity());
                            record.setDispatchedTime(new Date());
                            record.setLogisticsCarrier(carrier);
                            record.setLogisticsService(serviceName);
                            record.setLogisticsTrackingNo(trackingNo);
                            record.setWarehouseSystemCode(warehouseSystemCode);
                            record.setSystemManaged(true);
                            thirdPartyLogisticsSupport.queryLogistics(record);
                            trackingRecordList.add(record);
                        }

                        // 子订单强制变为已发货，只有管理员才能再次修改
                        orderItem.setFulfillmentProgress(LogisticsProgress.Dispatched);
                        // 批发、批量自提和代发的订单，发货后直接完结
                        if (ObjectUtil.equals(orderType, OrderType.Wholesale) && (
                            ObjectUtil.equals(logisticsType, LogisticsTypeEnum.PickUp) || ObjectUtil
                                .equals(logisticsType, LogisticsTypeEnum.DropShipping))) {
                            orderItem.setFulfillmentProgress(LogisticsProgress.Fulfilled);
                            orderItem.setFulfillmentTime(new Date());
                        }
                        if (orderItem.getDispatchedTime() == null) {
                            orderItem.setDispatchedTime(new Date());
                            updateRefundExecutableAmount(orderItem);
                        }

                        // 删除所有已存在的跟踪单
                        iOrderItemTrackingRecordService.trackingListDisassociate(orderItemNo);
                        // 保存新的跟踪单
                        iOrderItemTrackingRecordService.saveBatch(trackingRecordList);
                        // 更新子订单数据
                        iOrderItemService.updateNoTenant(orderItem);
                        // 更新发货单状态
                        iOrderItemShippingRecordService.updateShippingStateByOrderItem(orderItemNo, ShippingStateEnum.Shipped);
                        // 查询主订单所有子订单的物流状态，主订单需要设置履约复合状态
                        List<OrderItem> orderItemList = iOrderItemService.getListByOrderIdNotTenant(orderItem.getOrderId());
                        Set<LogisticsProgress> fulfillmentProgresses = orderItemList.stream()
                            .filter(item -> OrderStateType.Paid.equals(item.getOrderState()))
                            .map(OrderItem::getFulfillmentProgress).collect(Collectors.toSet());
                        orders.setFulfillmentProgress(LogisticsProgress.getComplexType(fulfillmentProgresses));
                        iOrdersService.updateNoTenant(orders);

                        this.channelTrackingSync(channelType, orderItem, orders);
                        iOrderItemService.updateNoTenant(orderItem);

                        //现货订单若在此完成，需要计入账单
                        if (ObjectUtil.equals(orderType, OrderType.Wholesale) && ObjectUtil
                            .equals(orders.getFulfillmentProgress(), LogisticsProgress.Fulfilled)) {
                            wholesaleSupport.wholesaleOrderAddToBill(orders);
                        }
                    } else {
                        return R.fail(ZSMallStatusCodeEnum.FILL_LEAST_ONE_TRACKING_NO);
                    }
                } else {
                    return R.fail(ZSMallStatusCodeEnum.ORDER_ALREADY_DISPATCHED);
                }
            } else {
                return R.fail(ZSMallStatusCodeEnum.ORDER_UNPAID_CANT_FULFILL);
            }
        } else {
            return R.fail(ZSMallStatusCodeEnum.ORDER_NOT_EXIST);
        }
        return R.ok();
    }


    /**
     * 批量确认发货
     *
     * @param file
     * @return
     * @throws Exception
     */
    @Transactional(rollbackFor = {ExcelMessageException.class, Exception.class})
    @Override
    public R<Void> batchConfirmDispatched(MultipartFile file) throws Exception {
        if (file == null) {
            return R.fail(ZSMallStatusCodeEnum.UPLOAD_FILE_IS_EMPTY);
        }

        //TODO 文件备份
        /*OSSUploadEvent ossUploadEvent = new OSSUploadEvent(file);
        SpringUtils.context().publishEvent(ossUploadEvent);
        SysOssVo sysOssVo = ossUploadEvent.getSysOssVo();*/

        try {

            // 渠道订单跟踪信息回传
//        List<TaskTrackingSyncEntity> trackingSyncEntityList = new ArrayList<>();
            StringBuilder errorMessage = new StringBuilder();

            //业务处理
            InputStream inputStream = file.getInputStream();
            ExcelReader reader = ExcelUtil.getReader(inputStream);
            if (reader != null) {
                Sheet sheet = reader.getSheet();
                String sheetName = sheet.getSheetName();
                log.info("uploadOrderExcel - sheetName = {}", sheetName);
                int columnCount = reader.getColumnCount();
                log.info("uploadOrderExcel - columnCount = {}", columnCount);
                String title = "Order ID,Carrier Code,Logistics Service,Tracking No.";

                // 必填的title
                String requireTitle = "Order ID,Tracking No.";
                String[] titleArray = StringUtils.split(title, ",");
                if (columnCount != titleArray.length) {
                    throw new RStatusCodeException(ZSMallStatusCodeEnum.EXCEL_COLUMN_COUNT_NOT_MATCH);
                }

                // 有效行
                int physicalRows = sheet.getPhysicalNumberOfRows();
                log.info("uploadOrderExcel - physicalRows = {}", physicalRows);

                String excelType = "dispatchedImport";
                List<DispatchedImportDTO> importDTOS = ZExcelUtil.parseFieldDTO(reader, DispatchedImportDTO.class, 1);
                log.info("importDTOS = {}", JSONUtil.toJsonStr(importDTOS));

                //已处理的orderID集合
                List<String> processedOrderIDList = new ArrayList<>();
                if (CollUtil.isNotEmpty(importDTOS)) {
                    LocaleMessage localeMessage = new LocaleMessage();
                    //初次校验数据
                    for (DispatchedImportDTO importDTO : importDTOS) {
                        int showRowIndex = importDTO.getShowRowIndex();

                        String orderID = importDTO.getOrderID();
                        String trackingNo = importDTO.getTrackingNo();
                        String carrierCode = importDTO.getCarrierCode();
                        String logisticsService = importDTO.getLogisticsService();
                        if (!StrUtil.isAllNotBlank(orderID, trackingNo)) {
                            return R.fail(ZSMallStatusCodeEnum.REQUIRED_CANNOT_EMPTY);
                        }
                        // 根据Excel每一行创建一个DTO搜集数据，并归属于一个OrderNo，全部搜集后再做下一步处理
                        if (StrUtil.equals(carrierCode.toUpperCase(), "FEDEX")) {
                            importDTO.setCarrierCode("FedEx");
                        }
                        // 防止误填中文逗号
                        if (StrUtil.contains(trackingNo, "，")) {
                            trackingNo = StrUtil.replace(trackingNo, "，", ",");
                        }
                        importDTO.setTrackingNo(StrUtil.cleanBlank(trackingNo));
                    }

                    //数据库校验数据
                    List<String> orderIDs = importDTOS.stream().map(DispatchedImportDTO::getOrderID).distinct().collect(Collectors.toList());
                    // 需要保存的子订单实体类
                    List<OrderItem> saveOrderItems = new ArrayList<>();
                    //创建新的跟踪单号
                    List<OrderItemTrackingRecord> newTrackingList = new ArrayList<>();
                    // 需要保存的跟踪单实体类
                    List<OrderItemTrackingRecord> saveTrackingOrders = new ArrayList<>();
                    // 需要保存的履约记录
                    List<ThirdChannelFulfillmentRecord> fulfillmentRecordList = new ArrayList<>();
                    Set<Long> orderIds = new HashSet<>();
                    for (String orderID : orderIDs) {
                        String orderErrorTemplate = "Order ID: []";
                        String format = String.format(orderErrorTemplate, orderID);
                        Orders orders = iOrdersService.getByOrderNoAndSupplier(orderID, LoginHelper.getTenantId());
                        // 订单号无法查询到订单，抛出错误
                        if (orders == null) {
                            throw new ExcelMessageException(ExcelMessageEnum.ORDER_NOT_EXIST.buildLocalMessage(null, format));
                        } else if (ObjectUtil.notEqual(orders.getOrderState(), OrderStateType.Paid)) {  // 订单已退款，抛出错误
                            throw new ExcelMessageException(ExcelMessageEnum.ORDER_HAS_NOT_BEEN_PAID.buildLocalMessage(null, format));
                        }
                        orderIds.add(orders.getId());
                        // 存在进行中的退款单，禁止发货
                        Integer inProgress = iOrderRefundService.countByInProgress(orders.getId());
                        if (inProgress > 0) {
                            throw new ExcelMessageException(ExcelMessageEnum.ORDER_REFUNDING_CANT_FULFILL.buildLocalMessage(null, format));
                        }

                        LogisticsTypeEnum logisticsType = orders.getLogisticsType();
                        List<OrderItem> orderItems = iOrderItemService.getAllUnDispatchedItems(orders.getOrderNo());

                        if (CollUtil.isEmpty(orderItems)) {
                            throw new ExcelMessageException(ExcelMessageEnum.NO_PRODUCT_DISPATCHED.buildLocalMessage(null, format));
                        }
                        List<DispatchedImportDTO> dtos = importDTOS.stream().filter(dto -> StrUtil.equals(orderID, dto.getOrderID())).collect(Collectors.toList());
                        // 如果批量发货的数据数量大于未发货的子订单数量，需要报错
                        if (CollUtil.size(dtos) > CollUtil.size(orderItems)) {
                            throw new ExcelMessageException(ExcelMessageEnum.CONFIRM_QUANTITY_GT_UNDISPATCHED);
                        }

                        for (int i = 0; i < dtos.size(); i++) {
                            DispatchedImportDTO batchDispatchedDTO = dtos.get(i);
                            OrderItem orderItem = orderItems.get(i);
                            String orderItemNo = orderItem.getOrderItemNo();
                            Integer totalQuantity = orderItem.getTotalQuantity();
                            Date dispatchedTime = orderItem.getDispatchedTime();
                            Long orderId = orderItem.getOrderId();

                            OrderItemProductSku orderItemProductSku = iOrderItemProductSkuService.getByOrderItemId(orderItem.getId());
                            // 将已存在的跟踪单号解除关联
                            List<OrderItemTrackingRecord> existTrackingList = iOrderItemTrackingRecordService.getListByOrderItemNo(orderItemNo);
                            for (OrderItemTrackingRecord existTracking : existTrackingList) {
                                existTracking.setDelFlag("2");
                            }

                            String carrierCode = batchDispatchedDTO.getCarrierCode();
                            String logisticsService = batchDispatchedDTO.getLogisticsService();
                            String trackingNoStr = batchDispatchedDTO.getTrackingNo();
                            List<String> trackingNoList = Arrays.asList(StrUtil.join(",", trackingNoStr));
                            // 遍历所有跟踪单号，创建新的跟踪单关联到子订单上
                            for (String trackingNo : trackingNoList) {
                                OrderItemTrackingRecord record = new OrderItemTrackingRecord();
                                record.setSku(orderItemProductSku.getSku());
                                record.setProductSkuCode(orderItemProductSku.getProductSkuCode());
                                record.setDispatchedTime(new Date());
                                record.setLogisticsCarrier(carrierCode);
                                record.setLogisticsTrackingNo(trackingNo);
                                record.setQuantity(totalQuantity);
                                record.setLogisticsService(logisticsService);
                                record.setSystemManaged(true);
                                record.setOrderItemNo(orderItemNo);
                                record.setOrderNo(orderID);
                                record.setFulfillmentTime(new Date());
                                record.setLogisticsProgress(LogisticsProgress.Dispatched);
                                record.setWarehouseSystemCode(orderItemProductSku.getWarehouseSystemCode());
//                                record.setWarehouseCode();

                                //物流注册
                                thirdPartyLogisticsSupport.queryLogistics(record);
                                if (StrUtil.isBlank(record.getLogisticsCarrier())) {
                                    throw new ExcelMessageException(ExcelMessageEnum.UNRECOGNIZABLE_TRACKING_NO.buildLocalMessage(null, format));
                                }
                                newTrackingList.add(record);

                            }
                            orderItem.setFulfillmentProgress(LogisticsProgress.Dispatched);

                            // 国外现货、批量自提和代发的订单，发货后直接完结
                            if (ObjectUtil.equals(orders.getOrderType(), OrderType.Wholesale) && (
                                ObjectUtil.equals(logisticsType, LogisticsTypeEnum.PickUp) || ObjectUtil
                                    .equals(logisticsType, LogisticsTypeEnum.DropShipping))) {
                                orderItem.setFulfillmentProgress(LogisticsProgress.Fulfilled);
                                orderItem.setFulfillmentTime(new Date());
                            }

                            if (orderItem.getDispatchedTime() == null) {
                                orderItem.setDispatchedTime(new Date());
                                updateRefundExecutableAmount(orderItem);
                            }

                            saveOrderItems.add(orderItem);
                            if (CollUtil.isNotEmpty(existTrackingList)) {
                                saveTrackingOrders.addAll(existTrackingList);
                            }
                            saveTrackingOrders.addAll(newTrackingList);
                            //创建物流信息回传任务 TODO
                            ThirdChannelFulfillmentRecord fulfillmentRecord = this.channelTrackingSyncNotSave(orders.getChannelType(), orderItem, orders);
                            if (fulfillmentRecord != null) {
                                fulfillmentRecordList.add(fulfillmentRecord);
                            }
                        }
                    }
                    iOrderItemService.batchSaveOrUpdate(saveOrderItems);
                    iOrderItemTrackingRecordService.saveOrUpdateBatch(saveTrackingOrders);
                    // 批量更新主订单的物流状态
                    orderSupport.setOrderFulfillmentProgress(orderIds);

                    if (CollUtil.isNotEmpty(fulfillmentRecordList)) {
                        iThirdChannelFulfillmentRecordService.saveBatch(fulfillmentRecordList);
                    }
                    //保存物流信息回传任务 TODO
                    /*if (CollUtil.isNotEmpty(trackingSyncEntityList)) {
                        iTaskTrackingSyncService.saveBatch(trackingSyncEntityList);
                    }*/
                }
            }
            return R.ok();
        } catch (ExcelMessageException e) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            log.error("batchConfirmDispatched 初次检查数据错误 {}", e.getLocaleMessage());
            return R.fail(e.getLocaleMessage().toString());
        } catch (RStatusCodeException e) {
            log.error("batchConfirmDispatched 状态码异常 {}", e.getMessage());
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return R.fail(e.getStatusCode());
        } catch (Exception e) {
            log.error("batchConfirmDispatched 异常 error = {}", e.getMessage(), e);
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return R.fail();
        }


    }

    /**
     * 刷新子订单的售后可执行金额
     *
     * @param orderItem
     */
    private void updateRefundExecutableAmount(OrderItem orderItem) {
        String orderItemNo = orderItem.getOrderItemNo();
        // 新版售后，活动订单发货后，需要把订金加回到可执行金额中
        OrderItemPrice orderItemPrice = iOrderItemPriceService.queryByOrderItemNo(orderItem.getOrderItemNo());
        Integer orderItemNum = orderItemPrice.getTotalQuantity();
        BigDecimal depositUnitPrice = orderItemPrice.getPlatformDepositUnitPrice();
        BigDecimal depositUnitPriceSup = orderItemPrice.getOriginalDepositUnitPrice();
        if (depositUnitPrice == null || depositUnitPriceSup == null) {
            return;
        }
        log.info("刷新可执行金额 orderItemNo = {} orderItemNum = {}, depositUnitPrice = {}, depositUnitPriceSup = {}", orderItemNo,
            orderItemNum, depositUnitPrice, depositUnitPriceSup);

        BigDecimal depositTotalPrice = NumberUtil.mul(depositUnitPrice, orderItemNum);
        BigDecimal depositTotalPriceSup = NumberUtil.mul(depositUnitPriceSup, orderItemNum);
        log.info("刷新可执行金额 orderItemNo = {} depositTotalPrice = {}, depositTotalPriceSup = {}", orderItemNo,
            depositTotalPrice, depositTotalPriceSup);

        BigDecimal refundExecutableAmount = orderItem.getPlatformRefundExecutableAmount();
        BigDecimal refundExecutableAmountSup = orderItem.getOriginalRefundExecutableAmount();

        log.info("刷新可执行金额 orderItemNo = {} refundExecutableAmount（原始值） = {}, refundExecutableAmountSup（原始值） = {}",
            orderItemNo, refundExecutableAmount, refundExecutableAmountSup);

        BigDecimal newRefundExecutableAmount = NumberUtil.add(depositTotalPrice, refundExecutableAmount);
        BigDecimal newRefundExecutableAmountSup = NumberUtil.add(depositTotalPriceSup, refundExecutableAmountSup);

        log.info("刷新可执行金额 orderItemNo = {} refundExecutableAmount（新值） = {}, refundExecutableAmountSup（新值） = {}", orderItemNo,
            newRefundExecutableAmount, newRefundExecutableAmountSup);
        orderItem.setPlatformRefundExecutableAmount(newRefundExecutableAmount);
        orderItem.setOriginalRefundExecutableAmount(newRefundExecutableAmountSup);
    }

    /**
     * 渠道物流跟踪信息回传  TODO
     *
     * @param channelType
     * @param orderItem
     * @param order
     */
    private void channelTrackingSync(ChannelTypeEnum channelType, OrderItem orderItem, Orders order) {
        // Others和OneLink不需要回传物流跟踪信息
        if (ObjectUtil.notEqual(channelType, ChannelTypeEnum.Others) || ObjectUtil
            .notEqual(channelType, ChannelTypeEnum.OneLink)) {
            // 同步物流信息至第三方渠道
            ThirdChannelFulfillmentRecord thirdChannelFulfillmentRecord = new ThirdChannelFulfillmentRecord();

            thirdChannelFulfillmentRecord.setChannelId(orderItem.getChannelId());
            thirdChannelFulfillmentRecord.setChannelType(channelType);
            thirdChannelFulfillmentRecord.setOrderNo(order.getOrderNo());
            thirdChannelFulfillmentRecord.setOrderItemNo(orderItem.getOrderItemNo());
            thirdChannelFulfillmentRecord.setChannelItemNo(orderItem.getChannelItemNo());
            thirdChannelFulfillmentRecord.setChannelOrderNo(order.getChannelOrderNo());
            thirdChannelFulfillmentRecord.setChannelOrderName(order.getChannelOrderName());
            thirdChannelFulfillmentRecord.setFulfillmentPushState(FulfillmentPushStateEnum.WaitPush);
            iThirdChannelFulfillmentRecordService.save(thirdChannelFulfillmentRecord);
        }
    }

    /**
     * 渠道物流跟踪信息回传  TODO
     *
     * @param channelType
     * @param orderItem
     * @param order
     */
    private ThirdChannelFulfillmentRecord channelTrackingSyncNotSave(ChannelTypeEnum channelType, OrderItem orderItem, Orders order) {
        // Others和OneLink不需要回传物流跟踪信息
        if (ObjectUtil.notEqual(channelType, ChannelTypeEnum.Others) || ObjectUtil
            .notEqual(channelType, ChannelTypeEnum.OneLink)) {
            // 同步物流信息至第三方渠道
            ThirdChannelFulfillmentRecord thirdChannelFulfillmentRecord = new ThirdChannelFulfillmentRecord();

            thirdChannelFulfillmentRecord.setChannelId(orderItem.getChannelId());
            thirdChannelFulfillmentRecord.setChannelType(channelType);
            thirdChannelFulfillmentRecord.setOrderNo(order.getOrderNo());
            thirdChannelFulfillmentRecord.setOrderItemNo(orderItem.getOrderItemNo());
            thirdChannelFulfillmentRecord.setChannelItemNo(orderItem.getChannelItemNo());
            thirdChannelFulfillmentRecord.setChannelOrderNo(order.getChannelOrderNo());
            thirdChannelFulfillmentRecord.setChannelOrderName(order.getChannelOrderName());
            thirdChannelFulfillmentRecord.setFulfillmentPushState(FulfillmentPushStateEnum.WaitPush);
            return thirdChannelFulfillmentRecord;
        }
        return null;
    }

}
