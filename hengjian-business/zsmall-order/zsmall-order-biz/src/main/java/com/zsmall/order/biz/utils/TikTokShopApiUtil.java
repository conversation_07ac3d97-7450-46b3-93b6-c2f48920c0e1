package com.zsmall.order.biz.utils;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.zsmall.common.domain.resp.tiktok.fulfillment.TikTokPackShippingDocument;
import com.zsmall.common.domain.resp.tiktok.product.data.PackageDetailsData;
import com.zsmall.common.domain.resp.tiktok.product.resp.PackageDetailsResp;
import com.zsmall.common.domain.tiktok.domain.dto.extend.resp.TikTokCreatePackageResp;
import com.zsmall.common.domain.tiktok.domain.dto.extend.resp.TikTokOrderDetailData;
import com.zsmall.common.domain.tiktok.domain.dto.extend.resp.TikTokOrderDetailResp;
import com.zsmall.common.domain.tiktok.domain.dto.order.TikTokOrder;
import com.zsmall.system.entity.domain.TenantSalesChannel;
import com.zsmall.system.entity.iservice.ITenantSalesChannelService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import static com.zsmall.common.enums.tiktok.TikTokApiEnums.*;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2024/4/3 16:39
 */
@Component
@Slf4j
public class TikTokShopApiUtil {
    public static final String ACCESS_TOKEN = "accessToken";
    public static final String REFRESH_TOKEN = "refreshToken";
    public static final String OPEN_ID = "openId";
    public static final String SHOP_CIPHER = "shopCipher";
    @Resource
    private ITenantSalesChannelService iTenantSalesChannelService;

    @Resource
    private TikTokUtil tikTokUtil;

    /**
     * 功能描述：获取程序包详细信息
     *
     * @param packageIds 包裹ID
     * @param shopId     店铺id
     * @return {@link ConcurrentHashMap }<{@link String }, {@link PackageDetailsResp }>
     * <AUTHOR>
     * @date 2024/04/03
     */
    public ConcurrentHashMap<String, PackageDetailsResp> getPackageDetails(List<String> packageIds,
                                                                           String shopId) throws Exception {

        ConcurrentHashMap<String, PackageDetailsResp> packageMap = new ConcurrentHashMap<>();
        HashMap<String, Object> hashMap = new HashMap<>();
        TenantSalesChannel tenantSalesChannel = iTenantSalesChannelService.getShopId(shopId);
        String connectStr = tenantSalesChannel.getConnectStr();
        JSONObject jsonObject = JSON.parseObject(connectStr);
        if (!jsonObject.containsKey(ACCESS_TOKEN)) {
            return null;
        }
        hashMap.put("shop_id", jsonObject.get("shopId"));

        String jsonString = JSON.toJSONString(hashMap);
        for (String packageId : packageIds) {
            log.info("包裹编号:{}", packageId);
            PackageDetailsData detail = tikTokUtil.getTikTokShopReturnV2(jsonString, GET_PACKAGE_DETAIL.getUrl(), GET_PACKAGE_DETAIL.getPath() + packageId, PackageDetailsData.class, hashMap);
            PackageDetailsResp detailData;
            if (ObjectUtil.isNull(detail)) {
                throw new Exception("获取包裹详情失败");
            }
            if (21001001 != detail.getCode() && 21011500 != detail.getCode()) {
                if (ObjectUtil.isNotEmpty(detail) && ObjectUtil.isNotEmpty(detail.getData())) {
                    detailData = detail.getData();
                    detailData.setCode(detail.getCode());
                    packageMap.put(detailData.getPackageId(), detailData);
                }
            }


        }

        return packageMap;
    }

    /**
     * 功能描述：获取订单详细信息
     *
     * @param orderIdList 订单id列表
     * @param shopId      店铺id
     * @return {@link List }<{@link TikTokOrder }>
     * <AUTHOR>
     * @date 2024/04/03
     */
    public List<TikTokOrder> getOrderDetail(List<String> orderIdList, String shopId) {
        HashMap<String, Object> hashMap = new HashMap<>();
        TenantSalesChannel tenantSalesChannel = iTenantSalesChannelService.getShopId(shopId);
        String connectStr = tenantSalesChannel.getConnectStr();
        JSONObject jsonObject = JSON.parseObject(connectStr);
        if (!jsonObject.containsKey(ACCESS_TOKEN)) {
            return null;
        }
        String ids = String.join(",", orderIdList);
        hashMap.put("shop_id", jsonObject.get("shopId"));
        hashMap.put("ids", ids);
        String jsonString = JSON.toJSONString(hashMap);
        TikTokOrderDetailResp orders = tikTokUtil.getTikTokShopReturnV2(jsonString, GET_ORDER_DETAIL.getUrl(), GET_ORDER_DETAIL.getPath(), TikTokOrderDetailResp.class, hashMap);

        return orders.getData().getOrders();
    }

    /**
     * 功能描述：获取订单详细信息
     *
     * @param shopId   店铺id
     * @param orderIds 订单ID
     * @return {@link Map }<{@link String }, {@link TikTokOrder }>
     * <AUTHOR>
     * @date 2024/04/08
     */
    private Map<String, TikTokOrder> getOrderDetails(String shopId, List<String> orderIds) {
        HashMap<String, Object> hashMap = new HashMap<>();
        TenantSalesChannel tenantSalesChannel = iTenantSalesChannelService.getShopId(shopId);
        String connectStr = tenantSalesChannel.getConnectStr();
        JSONObject jsonObject = JSON.parseObject(connectStr);
        if (!jsonObject.containsKey(ACCESS_TOKEN)) {
            return null;
        }
        hashMap.put("shop_id", jsonObject.get("shopId"));

        String jsonString = JSON.toJSONString(hashMap);
        ConcurrentHashMap<String, TikTokOrder> collect = new ConcurrentHashMap<>();
        for (String orderId : orderIds) {
            log.info("包裹编号:{}", orderId);
            TikTokOrderDetailResp detail = tikTokUtil.getTikTokShopReturnV2(jsonString, GET_ORDER_DETAIL.getUrl(), GET_ORDER_DETAIL.getPath() , TikTokOrderDetailResp.class, hashMap);
            TikTokOrderDetailData detailData;
            if (ObjectUtil.isNotEmpty(detail) && ObjectUtil.isNotEmpty(detail.getData())) {
                detailData = detail.getData();
                List<TikTokOrder> orders = detailData.getOrders();
                orders.forEach(order -> {
                    String tikTokOrderId = order.getTikTokOrderId();
                    collect.computeIfAbsent(tikTokOrderId, k -> order);
                });
            }

        }

        return collect;
    }

    /**
     * 功能描述：获取包裹运输单据
     *
     * @param packageId 程序包id
     * @param shopId    店铺id
     * @return {@link String }
     * <AUTHOR>
     * @date 2024/04/08
     */
    public TikTokPackShippingDocument getPackageShippingDocument(String packageId, String shopId) {
        HashMap<String, Object> hashMap = new HashMap<>();
        TenantSalesChannel tenantSalesChannel = iTenantSalesChannelService.getShopId(shopId);
        String connectStr = tenantSalesChannel.getConnectStr();
        JSONObject jsonObject = JSON.parseObject(connectStr);
        if (!jsonObject.containsKey(ACCESS_TOKEN)) {
            return null;
        }
        hashMap.put("shop_id", jsonObject.get("shopId"));
        hashMap.put("document_type", "SHIPPING_LABEL");

        String jsonString = JSON.toJSONString(hashMap);
        TikTokPackShippingDocument tikTokShopReturnV2 = tikTokUtil.getTikTokShopReturnV2(jsonString, GET_PACKAGE_SHIPPING_DOCUMENT.getUrl(), GET_PACKAGE_SHIPPING_DOCUMENT.formatPath(packageId, "{package_id}"), TikTokPackShippingDocument.class, hashMap);
        return tikTokShopReturnV2;
    }

    /**
     * 功能描述：创建包裹
     *
     * @param body   </body>
     * @param shopId 店铺id
     * @return {@link TikTokCreatePackageResp }
     * <AUTHOR>
     * @date 2024/04/10
     */
    public TikTokCreatePackageResp createPackage(String body, String shopId) {
        HashMap<String, Object> hashMap = new HashMap<>();
        TenantSalesChannel tenantSalesChannel = iTenantSalesChannelService.getShopId(shopId);
        String connectStr = tenantSalesChannel.getConnectStr();
        JSONObject jsonObject = JSON.parseObject(connectStr);
        if (!jsonObject.containsKey(ACCESS_TOKEN)) {
            return null;
        }
        hashMap.put("shop_id", jsonObject.get("shopId"));
        TikTokCreatePackageResp resp = tikTokUtil.postTikTokShopReturnV2(body, CREATE_PACKAGES.getUrl(), CREATE_PACKAGES.getPath(), TikTokCreatePackageResp.class, hashMap);
        return resp;
    }
}
