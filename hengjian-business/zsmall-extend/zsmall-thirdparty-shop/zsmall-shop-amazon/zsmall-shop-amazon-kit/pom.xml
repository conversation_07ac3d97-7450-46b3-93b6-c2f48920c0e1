<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.zsmall.extend.shop</groupId>
        <artifactId>zsmall-shop-amazon</artifactId>
        <version>${zsmall.version}</version>
    </parent>

    <artifactId>zsmall-shop-amazon-kit</artifactId>
    <name>ZS-Mall第三方店铺模块 amazon 接口工具</name>

    <dependencies>
        <dependency>
            <groupId>com.hengjian</groupId>
            <artifactId>hengjian-common-redis</artifactId>
        </dependency>

        <dependency>
            <groupId>com.amazon.sellingpartnerapi</groupId>
            <artifactId>sellingpartner-api</artifactId>
            <version>${sellingpartner-api.version}</version>
        </dependency>

        <dependency>
            <groupId>com.amazon.sellingpartnerapi</groupId>
            <artifactId>sellingpartnerapi-aa-java</artifactId>
            <version>${sellingpartner-api.version}</version>
        </dependency>

        <dependency>
            <groupId>com.google.code.gson</groupId>
            <artifactId>gson</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-crypto</artifactId>
        </dependency>

        <dependency>
            <groupId>com.squareup.okhttp3</groupId>
            <artifactId>logging-interceptor</artifactId>
        </dependency>

        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-api</artifactId>
            <optional>true</optional>
        </dependency>
    </dependencies>
</project>
