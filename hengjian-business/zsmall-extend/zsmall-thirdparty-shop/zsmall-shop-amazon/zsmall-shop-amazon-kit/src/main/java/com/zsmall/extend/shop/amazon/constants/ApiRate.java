package com.zsmall.extend.shop.amazon.constants;

/**
 * API 速率
 */
public interface ApiRate {

    interface FeedsApi {
        /**
         * getFeeds
         * requests per second：0.0222
         */
        Double getFeedsWaitTimeInSeconds = 45.5;
        /**
         * createFeed
         * requests per second：0.0083
         */
        Double createFeedWaitTimeInSeconds = 120.6;
        /**
         * getFeed
         * requests per second：2
         */
        Double getFeedWaitTimeInSeconds = 0.55;
        /**
         * cancelFeed
         * requests per second：2
         */
        Double cancelFeedWaitTimeInSeconds = 0.55;
        /**
         * createFeedDocument
         * requests per second：0.5
         */
        Double createFeedDocumentWaitTimeInSeconds = 2.1;
        /**
         * getFeedDocument
         * requests per second：0.0222
         */
        Double getFeedDocumentWaitTimeInSeconds = 45.5;

    }

    interface OrdersApi {
        /**
         * getOrders
         * requests per second：0.0167
         */
        Double getOrdersWaitTimeInSeconds = 60.0;
        /**
         * getOrder
         * requests per second：0.0167
         */
        Double getOrderWaitTimeInSeconds = 60.0;
        /**
         * getOrders
         * requests per second：0.0167
         */
        Double getOrderBuyerInfoWaitTimeInSeconds = 60.0;
        /**
         * getOrderAddress
         * requests per second：0.0167
         */
        Double getOrderAddressWaitTimeInSeconds = 60.0;
        /**
         * getOrderItems
         * requests per second：0.5
         */
        Double getOrderItemsWaitTimeInSeconds = 2.1;
        /**
         * getOrderItemsBuyerInfo
         * requests per second：0.5
         */
        Double getOrderItemsBuyerInfoWaitTimeInSeconds = 2.1;
        /**
         * updateShipmentStatus
         * requests per second：5
         */
        Double updateShipmentStatusWaitTimeInSeconds = 0.21;
        /**
         * getOrderRegulatedInfo
         * requests per second：0.5
         */
        Double getOrderRegulatedInfoWaitTimeInSeconds = 2.1;
        /**
         * updateVerificationStatus
         * requests per second：0.5
         */
        Double updateVerificationStatusWaitTimeInSeconds = 2.1;
        /**
         * confirmShipment
         * requests per second：2
         */
        Double confirmShipmentWaitTimeInSeconds = 0.51;
    }

    interface TokensApi {
        /**
         * createRestrictedDataToken 超时后等待时间
         */
        Double createRestrictedWaitTimeInSeconds = 1.1;
    }

}
