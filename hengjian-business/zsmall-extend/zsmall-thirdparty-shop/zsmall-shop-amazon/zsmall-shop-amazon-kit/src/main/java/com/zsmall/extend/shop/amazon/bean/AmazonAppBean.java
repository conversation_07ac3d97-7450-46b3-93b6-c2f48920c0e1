package com.zsmall.extend.shop.amazon.bean;

import lombok.Data;

/**
 * 亚马逊应用属性
 */
@Data
public class AmazonAppBean {
    /**
     * 应用授权信息
     */
    public AuthorizeInfo authorizeInfo;
    /**
     * 应用请求地址信息
     */
    public Endpoint endpoint;
    /**
     * 缓存文件相关目录
     */
    public FileTmpBean tmpFile;
    /**
     * 业务网站授权地址
     */
    private String websiteLoginUrl;
    /**
     * 应用授权跳转地址
     */
    private String authorizeRedirectUrl;
    /**
     * 应用Id
     */
    private String appId;

    @Data
    public static class Endpoint {
        /**
         * token请求地址
         */
        private String token;
        /**
         * api 请求地址
         */
        private String api;
        /**
         * api 请求地区
         */
        private String region;
        /**
         * 市场Id
         */
        private String marketplaceId;
    }

    @Data
    public static class AuthorizeInfo {
        /**
         * 应用ClientId
         */
        private String clientId;
        /**
         * 应用ClientSecret
         */
        private String clientSecret;
        /**
         * 业务网站登录地址
         */
        private String websiteAuthorizeUrl;
        /**
         * 应用授权参数
         */
        private String authorizeParams;
    }

}
