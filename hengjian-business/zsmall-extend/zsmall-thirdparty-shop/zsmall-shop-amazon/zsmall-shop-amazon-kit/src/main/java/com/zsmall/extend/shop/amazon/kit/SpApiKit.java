package com.zsmall.extend.shop.amazon.kit;

import com.amazon.SellingPartnerAPIAA.LWAAuthorizationCredentials;
import com.zsmall.extend.shop.amazon.bean.AmazonAppBean;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class SpApiKit {

    /**
     * 单个用户，全局参数
     */
    public static LWAAuthorizationCredentials buildLwaAuthorizationCredentials(AmazonAppBean appBean, String refreshToken) {
        AmazonAppBean.AuthorizeInfo authorizeInfo = appBean.getAuthorizeInfo();
        AmazonAppBean.Endpoint endpoint = appBean.getEndpoint();

        String appClientId = authorizeInfo.getClientId();
        String appClientSecret = authorizeInfo.getClientSecret();


        return
            LWAAuthorizationCredentials.builder().clientId(appClientId)
                .clientSecret(appClientSecret)
                .refreshToken(refreshToken)
                .endpoint(endpoint.getToken()).build();
    }


}
