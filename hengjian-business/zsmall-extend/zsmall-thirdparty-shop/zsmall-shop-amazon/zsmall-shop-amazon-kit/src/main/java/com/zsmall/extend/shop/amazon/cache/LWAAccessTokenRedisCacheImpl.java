package com.zsmall.extend.shop.amazon.cache;

import cn.hutool.core.net.URLEncodeUtil;
import com.amazon.SellingPartnerAPIAA.LWAAccessTokenCache;
import com.google.gson.Gson;
import com.hengjian.common.core.constant.GlobalConstants;
import com.hengjian.common.redis.utils.RedisUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.nio.charset.StandardCharsets;
import java.time.Duration;

/**
 * LWA AccessToken Redis实现
 */
@Slf4j
@RequiredArgsConstructor
public class LWAAccessTokenRedisCacheImpl implements LWAAccessTokenCache {

    //单位：秒，提前失效，重新获取，避免使用时失效。to avoid returning a token that would expire before or while a request is made
    private final long expiryAdjustment = 60;
    private final String EX_CACHE_DEFAULT = "SHOP:AMAZON:";

    @Override
    public String get(Object key) {
        String newKey = new Gson().toJson(key);
        log.info("gson get key = {}", newKey);
        String objectToString = URLEncodeUtil.encode(newKey, StandardCharsets.UTF_8);

        return RedisUtils.getCacheObject(getKey(objectToString));
    }

    @Override
    public void put(Object key, String accessToken, long tokenTTLInSeconds) {
        String newKey = new Gson().toJson(key);
        log.info("gson put key = {}", newKey);
        String objectToString = URLEncodeUtil.encode(newKey, StandardCharsets.UTF_8);
        log.info("LWAAccessTokenRedisCacheImpl key => {}", objectToString);
        RedisUtils.setCacheObject(objectToString, accessToken, Duration.ofSeconds(tokenTTLInSeconds - expiryAdjustment));
    }

    private String getKey(String key) {
        return GlobalConstants.GLOBAL_REDIS_KEY + EX_CACHE_DEFAULT + key;
    }

}
