package com.zsmall.extend.shop.amazon.kit;

import com.amazon.SellingPartnerAPIAA.LWAAccessTokenCache;
import com.amazon.SellingPartnerAPIAA.LWAAuthorizationCredentials;
import com.amazon.client.api.FeedsApi;
import com.amazon.client.api.OrdersV0Api;
import com.amazon.client.api.TokensApi;
import com.zsmall.extend.shop.amazon.bean.AmazonAppBean;
import com.zsmall.extend.shop.amazon.logger.OkHttpSpApiLogger;
import lombok.extern.slf4j.Slf4j;
import okhttp3.logging.HttpLoggingInterceptor;

@Slf4j
public class AmazonSpApiKit {

    private static final HttpLoggingInterceptor logInterceptor = new HttpLoggingInterceptor(new OkHttpSpApiLogger());

    /**
     * 创建FeedsApi
     *
     * @return
     */
    public static FeedsApi createFeedsApi(AmazonAppBean amazonAppBean, LWAAccessTokenCache lwaAccessTokenCache,
                                          String refreshToken) {
        log.info("lwaAccessTokenCache is null ? {}", lwaAccessTokenCache == null);

        LWAAuthorizationCredentials lwaAuthorizationCredentials = SpApiKit.buildLwaAuthorizationCredentials(amazonAppBean, refreshToken);
        return new FeedsApi.Builder().lwaAccessTokenCache(lwaAccessTokenCache)
                                     .lwaAuthorizationCredentials(lwaAuthorizationCredentials)
                                     .loggingInterceptor(logInterceptor)
                                     .endpoint(amazonAppBean.getEndpoint().getApi())
                                     .build();
    }

    /**
     * 创建OrdersV0Api
     *
     * @return
     */
    public static OrdersV0Api createOrdersV0Api(AmazonAppBean amazonAppBean, LWAAccessTokenCache lwaAccessTokenCache,
                                                String refreshToken) {
        log.info("lwaAccessTokenCache is null ? {}", lwaAccessTokenCache == null);

        LWAAuthorizationCredentials lwaAuthorizationCredentials = SpApiKit.buildLwaAuthorizationCredentials(amazonAppBean, refreshToken);
        return new OrdersV0Api.Builder().lwaAccessTokenCache(lwaAccessTokenCache)
                                        .lwaAuthorizationCredentials(lwaAuthorizationCredentials)
                                        .loggingInterceptor(logInterceptor)
                                        .endpoint(amazonAppBean.getEndpoint().getApi()).build();
    }


    /**
     * 创建OrdersV0Api
     *
     * @return
     */
    public static OrdersV0Api createOrdersV0ApiWithRestricted(AmazonAppBean amazonAppBean,
                                                              LWAAccessTokenCache lwaAccessTokenCache,
                                                              String restrictedDataToken) {
        log.info("lwaAccessTokenCache is null ? {}", lwaAccessTokenCache == null);
        log.info("restrictedDataToken = {}", restrictedDataToken);

        return new OrdersV0Api.Builder().lwaAccessTokenCache(lwaAccessTokenCache)
                                        .restrictedDataToken(restrictedDataToken)
                                        .loggingInterceptor(logInterceptor)
                                        .endpoint(amazonAppBean.getEndpoint().getApi()).build();
    }

    public static TokensApi createTokensApi(AmazonAppBean amazonAppBean, LWAAccessTokenCache lwaAccessTokenCache,
                                            String refreshToken) {
        log.info("lwaAccessTokenCache is null ? {}", lwaAccessTokenCache == null);

        LWAAuthorizationCredentials lwaAuthorizationCredentials = SpApiKit.buildLwaAuthorizationCredentials(amazonAppBean, refreshToken);
        return new TokensApi.Builder().lwaAccessTokenCache(lwaAccessTokenCache)
                                      .lwaAuthorizationCredentials(lwaAuthorizationCredentials)
                                      .loggingInterceptor(logInterceptor)
                                      .endpoint(amazonAppBean.getEndpoint().getApi())
                                      .build();
    }
}
