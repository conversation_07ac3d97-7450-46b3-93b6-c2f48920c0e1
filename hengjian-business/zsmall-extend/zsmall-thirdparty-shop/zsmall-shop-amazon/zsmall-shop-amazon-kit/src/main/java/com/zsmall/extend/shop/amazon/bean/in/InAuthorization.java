package com.zsmall.extend.shop.amazon.bean.in;

import cn.hutool.core.annotation.Alias;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 输入-授权信息
 *
 * <AUTHOR>
 * @date 2021-03-05 11:07
 */
@Data
@NoArgsConstructor
public class InAuthorization {

    @Alias("grant_type")
    private String grantType = "authorization_code";

    private String code;

    public InAuthorization(String code) {
        this.code = code;
    }
}
