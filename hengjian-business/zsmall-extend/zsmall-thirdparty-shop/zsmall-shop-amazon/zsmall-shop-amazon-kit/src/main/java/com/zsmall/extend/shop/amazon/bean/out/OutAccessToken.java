package com.zsmall.extend.shop.amazon.bean.out;

import cn.hutool.core.annotation.Alias;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 响应授权token信息
 *
 * <AUTHOR>
 * @date 2021-03-05 11:06
 */
@Data
@Accessors(chain = true)
public class OutAccessToken {

    /**
     * 当前授权应用ID
     */
    private String appId;

    @Alias("access_token")
    private String accessToken;

    @Alias("refresh_token")
    private String refreshToken;

    @Alias("token_type")
    private String tokenType;

    @Alias("expires_in")
    private Long expiresIn;

}
