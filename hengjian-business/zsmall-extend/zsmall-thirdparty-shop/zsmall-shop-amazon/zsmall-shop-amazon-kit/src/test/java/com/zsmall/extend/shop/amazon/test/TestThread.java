package com.zsmall.extend.shop.amazon.test;

import cn.hutool.core.thread.ExecutorBuilder;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.RandomUtil;

import java.util.Random;
import java.util.concurrent.*;

public class TestThread {


    static ExecutorService threadPool = Executors.newFixedThreadPool(2);
    static ThreadPoolExecutor orderExecutorService =
        ExecutorBuilder.create()
                       .setCorePoolSize(2)
                       .setMaxPoolSize(5)
                       .setKeepAliveTime(5, TimeUnit.SECONDS)
                       .setWorkQueue(new LinkedBlockingQueue<>(10))
                       .setThreadFactory(ThreadUtil.createThreadFactory("orders-"))
                       .setHandler(new ThreadPoolExecutor.AbortPolicy())
                       .build();
    //停车场同时容纳的车辆10
    private static Semaphore semaphore = new Semaphore(2);

    public static void main(String[] args) {
//        doSemaphore();

//        doTask();

        doHutoolTask();
//        testThread();
    }

    private static void doSemaphore() {
        //模拟100辆车进入停车场
        for (int i = 0; i < 20; i++) {
            Thread thread = new Thread(new Runnable() {
                public void run() {
                    try {
                        String threadName = Thread.currentThread().getName();
                        System.out.println("====" + threadName + "来到停车场");
                        if (semaphore.availablePermits() == 0) {
                            System.out.println("车位不足，" + threadName + "请耐心等待");
                        }
                        semaphore.acquire();//获取令牌尝试进入停车场
                        System.out.println(threadName + "成功进入停车场");
                        Thread.sleep(new Random().nextInt(10000));//模拟车辆在停车场停留的时间
                        System.out.println(threadName + "驶出停车场");
                        semaphore.release();//释放令牌，腾出停车场车位
                    } catch (InterruptedException e) {
                        e.printStackTrace();
                    }
                }
            }, i + "号车");
            thread.start();
        }
    }

    private static void doTask() {
        try {
            CountDownLatch countDownLatch = new CountDownLatch(10);

            System.out.println("starting.....");
            for (int i = 0; i < 10; i++) {
                final int index = i;
                threadPool.execute(() -> {
                    try {
                        System.out.println("starting... " + index + Thread.currentThread().getName());
                        Thread.sleep(1000 + ThreadLocalRandom.current().nextInt(1000));
                        System.out.println("finish" + index + Thread.currentThread().getName());
                    } catch (InterruptedException e) {
                        e.printStackTrace();
                    } finally {
                        countDownLatch.countDown();
                    }
                });
            }

            countDownLatch.await();
            System.out.println("ending.....");
            threadPool.shutdown();
        } catch (InterruptedException e) {
            e.printStackTrace();
        }

    }

    private static void doHutoolTask() {

        System.out.println("总线程数：" + orderExecutorService.getPoolSize() + "当前活跃线程数：" + orderExecutorService.getActiveCount());
        for (int index = 0; index < 30; index++) {
            int finalIndex = index;

            ThreadPoolExecutor poolExecutor = orderExecutorService;
            long taskCount1 = poolExecutor.getActiveCount();
            int size1 = poolExecutor.getQueue().size();
            System.out.println("starting... finalIndex = " + finalIndex + " === " + size1);
            if (size1 < 10) {
                orderExecutorService.execute(() -> {
                    try {
//                        System.out.println("starting... " + finalIndex + " 线程名：" + Thread.currentThread().getName() + " --- " + taskCount1);
                        ThreadUtil.sleep(RandomUtil.randomInt(1000, 1200));
                        System.out.println("finish.success.. " + finalIndex + " 线程名：" + Thread.currentThread().getName() + " --- " + poolExecutor.getQueue().size());
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                });
            } else {
                int activeCount = poolExecutor.getActiveCount();
                long taskCount = poolExecutor.getTaskCount();
                int size = poolExecutor.getQueue().size();
                System.out.println("finish.full..  " + finalIndex + "..... 队列已满。。。。。。。。。。。。。。。。" + activeCount + " -------- " + taskCount + " --- " + size);
                ThreadUtil.sleep(RandomUtil.randomInt(550, 700));
            }

//            while (true) {
                System.out.println("总线程数：" + orderExecutorService.getPoolSize() + "当前活跃线程数：" + orderExecutorService.getActiveCount());
//                try {
//                    TimeUnit.SECONDS.sleep(1);
//                } catch (InterruptedException e) {
//                    e.printStackTrace();
//                }
//            }
        }
    }


    public static void testThread() {
        ExecutorService es = new ThreadPoolExecutor(50, 100, 0L, TimeUnit.MILLISECONDS,
            new LinkedBlockingQueue<Runnable>(
                100000
            ));

        for (int i = 0; i < 100000; i++) {
            int finalI = i;
            es.execute(() -> {
                System.out.print(finalI + " " );
                try{
                    Thread.sleep(1000);
                }
                catch(InterruptedException e) {
                    e.printStackTrace();
                }
            });

        }

        ThreadPoolExecutor tpe = ((ThreadPoolExecutor) es);
        while (true) {
            System.out.println();
            int queueSize = tpe.getQueue().size();
            System.out.println("当前排队线程数："+ queueSize);

            int activeCount = tpe.getActiveCount();
            System.out.println("当前活动线程数："+ activeCount);

            long completedTaskCount = tpe.getCompletedTaskCount();
            System.out.println("执行完成线程数："+ completedTaskCount);

            long taskCount = tpe.getTaskCount();
            System.out.println("总线程数："+ taskCount);

            ThreadUtil.sleep(3000);
        }
    }
}
