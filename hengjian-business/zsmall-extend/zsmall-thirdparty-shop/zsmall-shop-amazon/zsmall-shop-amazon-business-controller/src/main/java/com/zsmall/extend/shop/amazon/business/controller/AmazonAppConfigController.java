package com.zsmall.extend.shop.amazon.business.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.hengjian.common.core.domain.R;
import com.hengjian.common.core.validate.AddGroup;
import com.hengjian.common.core.validate.EditGroup;
import com.hengjian.common.excel.utils.ExcelUtil;
import com.hengjian.common.idempotent.annotation.RepeatSubmit;
import com.hengjian.common.log.annotation.Log;
import com.hengjian.common.log.enums.BusinessType;
import com.hengjian.common.mybatis.core.page.PageQuery;
import com.hengjian.common.mybatis.core.page.TableDataInfo;
import com.hengjian.common.web.core.BaseController;
import com.zsmall.extend.shop.amazon.business.entity.domain.bo.AmazonAppConfigBo;
import com.zsmall.extend.shop.amazon.business.entity.domain.vo.AmazonAppConfigDetailVo;
import com.zsmall.extend.shop.amazon.business.entity.domain.vo.AmazonAppConfigVo;
import com.zsmall.extend.shop.amazon.business.entity.iservice.IAmazonAppConfigService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 亚马逊应用管理
 *
 * <AUTHOR> Li
 * @date 2023-10-24
 */
@Slf4j
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/staff/appConfig/amazon")
public class AmazonAppConfigController extends BaseController {

    private final IAmazonAppConfigService amazonAppConfigService;

    /**
     * 查询亚马逊应用管理列表
     */
    @SaCheckPermission("staff:appConfig:list")
    @GetMapping("/list")
    public TableDataInfo<AmazonAppConfigVo> list(AmazonAppConfigBo bo, PageQuery pageQuery) {
        return amazonAppConfigService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出亚马逊应用管理列表
     */
    @SaCheckPermission("staff:appConfig:export")
    @Log(title = "亚马逊应用管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(AmazonAppConfigBo bo, HttpServletResponse response) {
        List<AmazonAppConfigVo> list = amazonAppConfigService.queryList(bo);
        ExcelUtil.exportExcel(list, "Amazon Apps", AmazonAppConfigVo.class, response, false);
    }

    /**
     * 获取亚马逊应用管理详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("staff:appConfig:query")
    @GetMapping("/{id}")
    public R<AmazonAppConfigDetailVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable String id) {
        return R.ok(amazonAppConfigService.queryById(id));
    }

    /**
     * 新增亚马逊应用管理
     */
    @SaCheckPermission("staff:appConfig:add")
    @Log(title = "亚马逊应用管理", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody AmazonAppConfigBo bo) {
        return toAjax(amazonAppConfigService.insertByBo(bo));
    }

    /**
     * 修改亚马逊应用管理
     */
    @SaCheckPermission("staff:appConfig:edit")
    @Log(title = "亚马逊应用管理", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody AmazonAppConfigBo bo) {
        return toAjax(amazonAppConfigService.updateByBo(bo));
    }

    /**
     * 删除亚马逊应用管理
     *
     * @param ids 主键串
     */
    @SaCheckPermission("staff:appConfig:remove")
    @Log(title = "亚马逊应用管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable String[] ids) {
        return toAjax(amazonAppConfigService.deleteWithValidByIds(List.of(ids), true));
    }


    /**
     * 获取可用的亚马逊App
     * @return
     */
    @GetMapping("/getEnableApp")
    public R<String> getEnableApp() {
        return R.ok("success", amazonAppConfigService.getEnableApp());
    }

}
