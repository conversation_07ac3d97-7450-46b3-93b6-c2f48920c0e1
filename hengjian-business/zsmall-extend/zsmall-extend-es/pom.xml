<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.zsmall.extend</groupId>
        <artifactId>zsmall-extend</artifactId>
        <version>${zsmall.version}</version>
    </parent>

    <groupId>com.zsmall.extend.es</groupId>
    <artifactId>zsmall-extend-es</artifactId>
    <version>${zsmall.version}</version>
    <name>ZS-Mall 扩展ES模块</name>

    <properties>
        <maven.compiler.source>11</maven.compiler.source>
        <maven.compiler.target>11</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-json</artifactId>
        </dependency>

        <dependency>
            <groupId>com.hengjian</groupId>
            <artifactId>hengjian-common-easyes</artifactId>
            <optional>true</optional>
        </dependency>

        <!-- zsmall -->
        <dependency>
            <groupId>com.zsmall</groupId>
            <artifactId>zsmall-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.zsmall</groupId>
            <artifactId>zsmall-product-entity</artifactId>
        </dependency>
    </dependencies>

</project>
