package com.zsmall.extend.es.entity;


import lombok.Data;
import org.dromara.easyes.annotation.IndexField;
import org.dromara.easyes.annotation.IndexId;
import org.dromara.easyes.annotation.IndexName;
import org.dromara.easyes.annotation.Score;
import org.dromara.easyes.annotation.rely.Analyzer;
import org.dromara.easyes.annotation.rely.FieldStrategy;
import org.dromara.easyes.annotation.rely.FieldType;
import org.dromara.easyes.annotation.rely.IdType;

import java.math.BigDecimal;
import java.util.List;
import java.util.Set;

/**
 * ES 商品实体
 */
@Data
@IndexName(value = "product", keepGlobalPrefix = true)
public class EsProduct {

    @IndexId(type = IdType.CUSTOMIZE)
    private String id;

    /**
     * SKU编码
     */
    private String skuCode;

    /**
     * SPU编码
     */
    private String spuCode;

    /**
     * 商品名称
     * 引入TK分词器
     */
    @IndexField(fieldType = FieldType.TEXT, analyzer = Analyzer.IK_SMART, searchAnalyzer = Analyzer.IK_SMART)
    private String productName;
    /**
     * 商品类型
     */
    private String productType;
    /**
     * 原图地址
     */
    private String originImageShowUrl;
    /**
     * 压缩后图片地址
     */
    private String compressImageShowUrl;
    /**
     * 商品一级分类Id
     */
    @IndexField(fieldType = FieldType.LONG)
    private Long category1Id;
    /**
     * 商品一级分类名称
     */
    @IndexField(fieldType = FieldType.TEXT, analyzer = Analyzer.IK_SMART, searchAnalyzer = Analyzer.IK_SMART)
    private String category1Name;
    /**
     * 商品二级分类Id
     */
    @IndexField(fieldType = FieldType.LONG)
    private Long category2Id;
    /**
     * 商品二级分类名称
     */
    @IndexField(fieldType = FieldType.TEXT, strategy = FieldStrategy.NOT_EMPTY, analyzer = Analyzer.IK_SMART, searchAnalyzer = Analyzer.IK_SMART)
    private String category2Name;
    /**
     * 商品三级分类Id
     */
    @IndexField(fieldType = FieldType.LONG)
    private Long category3Id;
    /**
     * 商品三级分类名称
     */
    @IndexField(fieldType = FieldType.TEXT, strategy = FieldStrategy.NOT_EMPTY, analyzer = Analyzer.IK_SMART, searchAnalyzer = Analyzer.IK_SMART)
    private String category3Name;

    /**
     * 规格
     * 引入TK分词器
     */
    @IndexField(fieldType = FieldType.TEXT, analyzer = Analyzer.IK_SMART, searchAnalyzer = Analyzer.IK_SMART)
    private String specification;

    /**
     * 属性
     */
    @IndexField(fieldType = FieldType.NESTED, nestedClass = EsKeyValuePair.class)
    private Set<EsKeyValuePair> attributes;

    /**
     * 产品特点
     */
    @IndexField(fieldType = FieldType.NESTED, nestedClass = EsKeyValuePair.class)
    private Set<EsKeyValuePair> features;

    /**
     * sku
     */
    private String sku;

    /**
     * 供货商租户Id
     */
    private String supplierId;

    /**
     * 允许销售的分销商租户Id
     */
    private List<String> allowTenantId;

    /**
     * 活动类型，商品参加的活动的类型
     */
    private List<String> activityTypes;

    /**
     * Spu状态：0-有效，2-已删除
     */
    private Integer spuState;

    /**
     * Sku状态：0-有效，2-已删除
     */
    private Integer skuState;

    /**
     * Spu货架状态
     */
    private String shelfState;

    /**
     * SKU销售状态
     */
    private String skuShelfState;

    /**
     * 运输方式
     */
    private String transportMethod;

    /**
     * 商品标签
     */
    @IndexField(strategy = FieldStrategy.IGNORED)
    private List<String> labelNameGroup;

    /**
     * 商品标签ID
     */
    @IndexField(strategy = FieldStrategy.IGNORED)
    private List<Long> labelGroup;

    /**
     * 平台代发价
     */
    @IndexField(fieldType = FieldType.DOUBLE)
    private BigDecimal dropShippingPrice;

    /**
     * 平台自提价
     */
    @IndexField(fieldType = FieldType.DOUBLE)
    private BigDecimal pickUpPrice;

    /**
     * 建议零售价
     */
    @IndexField(fieldType = FieldType.DOUBLE)
    private BigDecimal msrp;

    /**
     * 根据商品自提类型不同，价格不同
     */
    @IndexField(fieldType = FieldType.DOUBLE)
    private BigDecimal mainPrice;

    /**
     * 库存
     */
    private Integer stockTotal;

    /**
     * 可配送国家
     */
    private List<Integer> shippingCountry;
    /**
     * 是否免运费
     */
    private Integer freeShipping;
    /**
     * 配送最快送达时间
     */
    private Integer shippingDayMin;
    /**
     * 配送最迟送达时间
     */
    private Integer shippingDayMax;

    /**
     * 支持的物流，0-仅支持代发，1-仅支持自提，2-都支持
     */
    private Integer supportedLogistics;

    /**
     * 平台订单 处理时间
     */
    private Integer processingTime;

    /**
     * 禁售渠道
     */
    private List<Integer> forbiddenChannel;

    /**
     * 销量
     */
    private Integer salesVolume;

    /**
     * 商品创建时间
     */
    private Long createTime;

    @Score
    private Float score;

}
