package com.zsmall.common.enums.order;

import com.baomidou.mybatisplus.annotation.IEnum;


/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2025/3/11 16:15
 */
public enum OrderCancelStateEnum implements IEnum<Integer> {
    None(0),
    Cancel<PERSON>(1),
    Canceled(2),
    Failed(3),
    Abnormal(4),
    ;
    OrderCancelStateEnum(Integer code) {
        this.code = code;
    }
    /**
     * 获取可以查询到的订单状态
     *
     * @return
     */
    private final int code;

    public static String getDisplayName(Integer cancelStatus) {
        for (OrderCancelStateEnum orderCancelStateEnum : OrderCancelStateEnum.values()) {
            if (orderCancelStateEnum.getValue().equals(cancelStatus)) {
                return orderCancelStateEnum.name();
            }
        }
        return null;
    }


    /**
     * 枚举数据库存储值
     */
    @Override
    public Integer getValue() {
        return this.code;
    }
}
