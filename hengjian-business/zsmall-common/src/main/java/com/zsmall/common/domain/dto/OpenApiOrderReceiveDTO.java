package com.zsmall.common.domain.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import com.hengjian.common.core.validate.TripartiteEntryGroup;
import com.zsmall.common.domain.tiktok.domain.dto.address.TikTokRecipientAddress;
import com.zsmall.common.enums.common.ChannelTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2024/6/19 10:15
 */
@Data
public class OpenApiOrderReceiveDTO {
    /**
     * 行订单项目id
     */
    // 为下面所有的类属性添加@JsonProperty("")@JSONField(name="")注解 值为转换为驼峰后的属性名
    //
    @JsonProperty("line_order_item_id")
    @JSONField(name="line_order_item_id")
    private String lineOrderItemId;
    /**
     * 租户 ID
     */
    @NotNull(message = "租户id", groups = {TripartiteEntryGroup.class})
    @ApiModelProperty(value = "租户id", required = true)

    @JsonProperty("tenant_id")
    @JSONField(name="tenant_id")
    private String tenantId;

    /**
     * 渠道店铺标识
     */
    @NotNull(message = "三方标识不能为空,渠道店铺标识", groups = {TripartiteEntryGroup.class})
    @ApiModelProperty(value = "渠道店铺标识", required = true)
    @JsonProperty("third_channel_flag")
    @JSONField(name="third_channel_flag")
    private String thirdChannelFlag;

    /**
     * 订单号
     */
    @NotNull(message = "渠道订单号不能为空", groups = {TripartiteEntryGroup.class})
    @ApiModelProperty(value = "渠道订单号", required = true)
    @JsonProperty("order_no")
    @JSONField(name="order_no")
    private String orderNo;

    /**
     * 订单状态
     */
    @NotNull(message = "订单状态", groups = {TripartiteEntryGroup.class})
    @ApiModelProperty(value = "订单状态,订单状态:", required = true)
    @JsonProperty("order_status")
    @JSONField(name="order_status")
    private String orderStatus;

    /**
     * 通道类型
     */
    @JsonProperty("channel_type")
    @JSONField(name="channel_type")
    private ChannelTypeEnum channelType;

    /**
     * 总量
     */
    @NotNull(message = "商品总数", groups = {TripartiteEntryGroup.class})
    @ApiModelProperty(value = "商品总数", required = true)
    @JsonProperty("total_quantity")
    @JSONField(name="total_quantity")
    private int totalQuantity;

    /**
     * 币种
     */
    @NotNull(message = "币种", groups = {TripartiteEntryGroup.class})
    @ApiModelProperty(value = "币种", required = false)
    @JsonProperty("currency_code")
    @JSONField(name="currency_code")
    private String currencyCode;
//
//    @NotNull(message = "总金额", groups = {TripartiteEntryGroup.class})
//    @ApiModelProperty(value = "金额", required = false)
//    private BigDecimal itemAmount;

    /**
     * 下单日期
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonProperty("create_time")
    @JSONField(name="create_time")
    private Date createTime;

    /**
     * 发货方式：PickUp-自提，DropShipping-代发
     */
    @ApiModelProperty(value = "发货方式：PickUp-自提，DropShipping-代发", required = true)
    @JsonProperty("logistics_type")
    @JSONField(name="logistics_type")
    private String logisticsType;

    /**
     * 是否需要贴标签 0:是 1:否
     */
    @JsonProperty("is_need_labeling")
    @JSONField(name="is_need_labeling")
    private Integer isNeedLabeling;
    /**
     * 调拨订单.采购订单号
     */
    @JsonProperty("remark")
    @JSONField(name="remark")
    private String remark;

    /**
     * 销售订单详细信息
     */
    @JsonProperty("sale_order_details")
    @JSONField(name="sale_order_details")
    private OpenSaleOrderDetailDTo saleOrderDetails;

    /**
     * 产品明细
     */
    @JsonProperty("sale_order_items_list")
    @JSONField(name="sale_order_items_list")
    private List<OpenApiSaleOrderItemDTO> saleOrderItemsList;

    private TikTokRecipientAddress address;

    /**
     * 税前总额
     */
    @JsonProperty("sub_total")
    @JSONField(name="sub_total")
    private String subTotal;

    /**
     * 税后
     */
    @JsonProperty("total_amount")
    @JSONField(name="total_amount")
    private String totalAmount;

//    public List<OpenApiSaleOrderItemDTO> pushSaleOrderItemsList(List<OpenApiSaleOrderItemDTO> saleOrderItemsList) {
//        this.saleOrderItemsList = saleOrderItemsList;
//        return this.saleOrderItemsList;
//    }
}
