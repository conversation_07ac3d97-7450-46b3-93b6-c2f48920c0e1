package com.zsmall.common.enums.payment;

import com.baomidou.mybatisplus.annotation.IEnum;

/**
 * <AUTHOR>
 * @date 2024年4月28日  10:48
 * @description: 支付类型
 */
public enum PayTypeEnum implements IEnum<Integer> {

    /**
     * 空中云汇收单
     */
    Airwallex(1),

    /**
     * 派安盈收单
     */
    Payonner(2),

    /**
     * 连连支付收单
     */
    LianLian(3),

    /**
     * 派安盈账户转账
     */
    Payonner_Transfer(4)
    ;

    private Integer value;

    PayTypeEnum(Integer code) {
        this.value = code;
    }

    /**
     * 枚举数据库存储值
     */
    @Override
    public Integer getValue() {
        return this.value;
    }

    public static PayTypeEnum fromValue(Integer value) {
        for (PayTypeEnum payType : PayTypeEnum.values()) {
            if (value == payType.getValue()) {
                return payType;
            }
        }
        throw new IllegalArgumentException("Enum type does not exist!");
    }
}
