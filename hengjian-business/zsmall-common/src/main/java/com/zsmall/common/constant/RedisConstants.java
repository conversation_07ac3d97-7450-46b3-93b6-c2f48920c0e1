package com.zsmall.common.constant;

import com.hengjian.common.core.constant.GlobalConstants;

/**
 * Redis用常量
 *
 * <AUTHOR>
 * @date 2023/6/14
 */
public class RedisConstants {

    /**
     * 变更钱包KEY
     */
    public static final String ZSMALL_WALLET_LOCK = GlobalConstants.GLOBAL_REDIS_KEY + "ZSMALL:WALLET:LOCK:";

    /**
     * 库存锁
     */
    public static final String ZSMALL_PRODUCT_SKU_STOCK_LOCK = GlobalConstants.GLOBAL_REDIS_KEY + "ZSMALL:PRODUCT:STOCK_LOCK:";

    /**
     * 活动锁
     */
    public static final String ZSMALL_PRODUCT_ACTIVITY_LOCK = GlobalConstants.GLOBAL_REDIS_KEY + "ZSMALL:PRODUCT:ACTIVITY_LOCK:";

    /**
     * 子活动锁
     */
    public static final String ZSMALL_PRODUCT_ACTIVITY_ITEM_LOCK = GlobalConstants.GLOBAL_REDIS_KEY + "ZSMALL:PRODUCT:ACTIVITY_ITEM_LOCK:";

    /**
     * 订单支付锁
     */
    public static final String ZSMALL_ORDER_PAY_LOCK = GlobalConstants.GLOBAL_REDIS_KEY + "ZSMALL:ORDER:PAY:LOCK:";

    /**
     * Payoneer 授权码锁
     */
    public static final String ZSMALL_PAYONEER_CODE_LOCK = GlobalConstants.GLOBAL_REDIS_KEY + "ZSMALL:PAYONEER_CODE:LOCK:";

    /**
     * 承运商转换信息KEY
     */
    public static final String ZSMALL_CARRIER_SWITCH = GlobalConstants.GLOBAL_REDIS_KEY + "ZSMALL:CARRIER_SWITCH";

    /**
     * 账单锁
     */
    public static final String ZSMALL_BILL = GlobalConstants.GLOBAL_REDIS_KEY + "ZSMALL:BILL:LOCK:";

    /**
     * 消息通知发送次数KEY
     */
    public static final String ZSMALL_NOTICE_SEND_OF_TIMES = GlobalConstants.GLOBAL_REDIS_KEY + "ZSMALL:NOTICE:SEND_OF_TIMES:";

    /**
     * 供货商仓库列表缓存Key
     */
    public static final String ZSMALL_SUPPLIER_WAREHOUSE = "WAREHOUSE_LIST";


    /**
     * 用户是否完善信息标识key
     */
    public static final String EXTRA_PERFECTION_FLAG = GlobalConstants.GLOBAL_REDIS_KEY + "EXTRA_PERFECTION_FLAG:";

    /**
     * 商品ElasticSearch数据初始化Key
     */
    public static final String ZSMALL_PRODUCT_ELASTIC_SEARCH_INIT_KEY = GlobalConstants.GLOBAL_REDIS_KEY + "ZSMALL:PRODUCT_ELASTIC_SEARCH_INIT_KEY";

    /**
     * Rakuten定时任务限流锁
     */
    public static final String ZSMALL_RAKUTEN_TIMED_EVENT_LOCK_KEY = GlobalConstants.GLOBAL_REDIS_KEY + "ZSMALL:RAKUTEN_TIMED_EVENT_LOCK_KEY";

    public static final String MESSAGE_QUEUE_LOCK = GlobalConstants.GLOBAL_REDIS_KEY + "MESSAGE:QUEUE:LOCK";
    /**
     * 取消订单锁定
     */
    public static final String CANCEL_ORDER_LOCK = "CANCEL:ORDER:LOCK" ;
}
