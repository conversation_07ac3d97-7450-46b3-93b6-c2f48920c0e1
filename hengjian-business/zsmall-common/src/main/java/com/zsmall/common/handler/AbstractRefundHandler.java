package com.zsmall.common.handler;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.hengjian.common.log.enums.BusinessType;
import com.zsmall.common.enums.common.ChannelTypeEnum;
import com.zsmall.common.enums.tiktok.TikTokApiEnums;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2024/1/29 13:33
 */
@Slf4j
public abstract class AbstractRefundHandler<T,E> {
    public abstract T getReverseOrderMsg(Map<String,Object> query);

    /**
     * 确认反向请求
     *
     * @param query
     * @return {@link T}
     */
    public abstract T confirmReverseRequest(Map<String,Object> query);


    /**
     * 获取拒绝原因列表
     *
     * @param
     * @return {@link Object}
     */
    public abstract List<String> getRejectReasonList(Map<String,Object> query);

    public abstract T jsonObjectToBusinessData(JSONObject json);

    /**
     * 拒绝反向请求
     *
     * @param query 查询
     * @return {@link T}
     */
    public abstract T rejectReverseRequest(Map<String,Object> query);

    /**
     * 插入业务数据
     *
     * @param jsonObject JSON 对象
     * @param channel    渠道
     * @param dataMap
     */
    public abstract void insertBusinessData(JSONObject jsonObject, String channel, Map dataMap);


    /**
     * 功能描述：插入商家数据
     *
     * @param type            类型
     * @param channelTypeEnum 通道类型枚举
     * @param dataMap         资料图
     * <AUTHOR>
     * @date 2024/01/29
     */
    public abstract void insertBusinessData(BusinessType type, ChannelTypeEnum channelTypeEnum ,JSONObject json, Map dataMap);

    /**
     * 逆向订单数据分析
     *
     * @param json    杰森
     * @param channel 渠道
     * @return {@link Map}<{@link String},{@link Object}>
     */
    public abstract Map<String,Object> webHookDataAnalysis(JSONObject json, String channel);


    /**
     * 功能描述：是按渠道需要
     *
     * @param json json格式
     * @return boolean
     * <AUTHOR>
     * @date 2024/01/29
     */
    public abstract boolean isNeedByChannel(JSONObject json,TikTokApiEnums enums);

    /**
     * 功能描述：是按渠道需要
     *
     * @param json json格式
     * @return boolean
     * <AUTHOR>
     * @date 2024/01/29
     */
    public abstract boolean isNeedByChannel(JSONObject json);









    /**
     * 从 Web API 接收反向顺序
     * 创建数据备份日志,在调用第三方接口后,保存调用信息和返回值 这个aop得从sendPostPro那边做细化的时候把数据保留下来,通过orderId和reverseId来生成唯一键
     *
     * @param json    杰森
     * @param channel 渠道
     */
    public final void receiveReverseOrderFromWebApi(JSONObject json,String channel){
        if(isNeedByChannel(json)){
            log.info("当前线程:{},渠道:{}---webhook数据:{},准备进入webhook数据分析------->",Thread.currentThread(),channel,json+"\n");
            // webhook数据分析 ,map内包含:逆向resp shopId webhookJson
            Map<String, Object> dataMap = webHookDataAnalysis(json, channel);
            log.info("当前线程:{},渠道:{}---逆向订单数据:{},准备数据存储------->",Thread.currentThread(),channel, JSON.toJSON(dataMap));
            // 保存明细
            insertBusinessData(json,channel, dataMap);
        }
    }
}
