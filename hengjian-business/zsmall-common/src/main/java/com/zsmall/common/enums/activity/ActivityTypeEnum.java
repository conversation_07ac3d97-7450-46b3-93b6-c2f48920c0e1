package com.zsmall.common.enums.activity;

import com.zsmall.common.enums.storageFee.FeeStateEnum;
import lombok.Data;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025年7月7日  15:21
 * @description: 活动类型
 */
@Getter
public enum ActivityTypeEnum {

    /**
     * 锁货
     */
    WAIT_CONFIRM("lock", "锁货"),
    /**
     * 圈货
     */
    CONFIRMING("circle", "圈货");

    private final String value;

    private final String name;

    ActivityTypeEnum(String value, String name) {
        this.value = value;
        this.name = name;
    }

    /**
     * 根据value获取枚举
     */
    public static ActivityTypeEnum getByValue(String value) {
        for (ActivityTypeEnum item : values()) {
            if (item.value.equals(value)) {
                return item;
            }
        }
        return null;
    }
}
