<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <artifactId>hengjian-business</artifactId>
        <groupId>com.hengjian</groupId>
        <version>${revision}</version>
    </parent>

    <artifactId>zsmall-common</artifactId>
    <groupId>com.zsmall</groupId>
    <version>${zsmall.version}</version>
    <name>ZS-Mall通用模块</name>
    <description>
        ZS-Mall通用模块
    </description>

    <properties>
        <maven.compiler.source>11</maven.compiler.source>
        <maven.compiler.target>11</maven.compiler.target>
    </properties>

    <dependencies>

<!--        <dependency>-->
<!--            <groupId>com.hengjian</groupId>-->
<!--            <artifactId>hengjian-system</artifactId>-->
<!--        </dependency>-->
        <dependency>
            <groupId>com.hengjian</groupId>
            <artifactId>hengjian-common-translation</artifactId>
        </dependency>

        <dependency>
            <groupId>com.hengjian</groupId>
            <artifactId>hengjian-common-mybatis</artifactId>
        </dependency>

        <dependency>
            <groupId>com.hengjian</groupId>
            <artifactId>hengjian-common-doc</artifactId>
        </dependency>

        <dependency>
            <groupId>com.hengjian</groupId>
            <artifactId>hengjian-common-excel</artifactId>
        </dependency>

        <dependency>
            <groupId>com.hengjian</groupId>
            <artifactId>hengjian-event-system</artifactId>
        </dependency>

        <dependency>
            <groupId>com.hengjian</groupId>
            <artifactId>hengjian-common-idempotent</artifactId>
        </dependency>

        <dependency>
            <groupId>com.hengjian</groupId>
            <artifactId>hengjian-common-log</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-core</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-http</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-extra</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-json</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-poi</artifactId>
        </dependency>

        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>

        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-boot-starter</artifactId>
        </dependency>


<!--        <dependency>-->
<!--            <groupId>com.alibaba.nacos</groupId>-->
<!--            <artifactId>nacos-api</artifactId>-->
<!--            <version>1.2.0</version>-->
<!--            <scope>compile</scope>-->
<!--            <exclusions>-->
<!--                <exclusion>-->
<!--                    <groupId>com.alibaba</groupId>-->
<!--                    <artifactId>fastjson</artifactId>-->
<!--                </exclusion>-->
<!--            </exclusions>-->
<!--        </dependency>-->

        <dependency>
            <groupId>org.gavaghan</groupId>
            <artifactId>geodesy</artifactId>
            <version>1.1.3</version>
        </dependency>

        <dependency>
            <groupId>com.itextpdf</groupId>
            <artifactId>itextpdf</artifactId>
            <version>5.5.13.2</version>
        </dependency>
        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
            <version>31.1-jre</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>io.swagger</groupId>
            <artifactId>swagger-annotations</artifactId>
            <version>1.6.2</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.squareup.okhttp3</groupId>
            <artifactId>okhttp</artifactId>
            <version>4.10.0</version>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
            <version>1.2.83</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.hengjian.openapi</groupId>
            <artifactId>hengjian-openapi</artifactId>
        </dependency>

        <!-- rest amazon -->
<!--        <dependency>-->
<!--            <groupId>com.wezone.product.rest</groupId>-->
<!--            <artifactId>wezone-rest-amazon</artifactId>-->
<!--            <version>1.0.3</version>-->
<!--            <exclusions>-->
<!--                <exclusion>-->
<!--                    <groupId>cn.hutool</groupId>-->
<!--                    <artifactId>hutool-core</artifactId>-->
<!--                </exclusion>-->
<!--                <exclusion>-->
<!--                    <groupId>cn.hutool</groupId>-->
<!--                    <artifactId>hutool-http</artifactId>-->
<!--                </exclusion>-->
<!--                <exclusion>-->
<!--                    <groupId>cn.hutool</groupId>-->
<!--                    <artifactId>hutool-json</artifactId>-->
<!--                </exclusion>-->
<!--                <exclusion>-->
<!--                    <groupId>com.alibaba</groupId>-->
<!--                    <artifactId>fastjson</artifactId>-->
<!--                </exclusion>-->
<!--            </exclusions>-->
<!--        </dependency>-->

    </dependencies>
</project>
