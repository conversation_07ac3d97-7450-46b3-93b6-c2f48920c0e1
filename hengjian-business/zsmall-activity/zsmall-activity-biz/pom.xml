<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>zsmall-activity</artifactId>
        <groupId>com.zsmall</groupId>
        <version>${zsmall.version}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>zsmall-activity-biz</artifactId>
    <groupId>com.zsmall</groupId>
    <name>ZS-Mall活动业务模块</name>

    <properties>
        <maven.compiler.source>11</maven.compiler.source>
        <maven.compiler.target>11</maven.compiler.target>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.zsmall</groupId>
            <artifactId>zsmall-system-biz</artifactId>
        </dependency>

        <dependency>
            <groupId>com.zsmall</groupId>
            <artifactId>zsmall-activity-entity</artifactId>
        </dependency>

        <dependency>
            <groupId>com.zsmall</groupId>
            <artifactId>zsmall-product-entity</artifactId>
        </dependency>

        <dependency>
            <groupId>com.zsmall</groupId>
            <artifactId>zsmall-system-entity</artifactId>
        </dependency>

        <dependency>
            <groupId>com.zsmall.extend.event</groupId>
            <artifactId>zsmall-extend-event</artifactId>
        </dependency>
        <dependency>
            <groupId>com.hengjian</groupId>
            <artifactId>hengjian-stream-mq-producer</artifactId>
        </dependency>
        <dependency>
            <groupId>com.zsmall</groupId>
            <artifactId>zsmall-warehouse-entity</artifactId>
        </dependency>
        <dependency>
            <groupId>com.zsmall.extend.es</groupId>
            <artifactId>zsmall-extend-es</artifactId>
        </dependency>
        <dependency>
            <groupId>com.zsmall</groupId>
            <artifactId>zsmall-product-biz</artifactId>
        </dependency>
    </dependencies>

</project>
