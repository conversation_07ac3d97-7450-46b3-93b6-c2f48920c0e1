package com.zsmall.activity.biz.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.TimeInterval;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.lang.RegexPool;
import cn.hutool.core.lang.UUID;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import cn.hutool.poi.excel.ExcelReader;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hengjian.common.core.domain.R;
import com.hengjian.common.core.enums.TenantType;
import com.hengjian.common.core.exception.RStatusCodeException;
import com.hengjian.common.core.utils.SpringUtils;
import com.hengjian.common.excel.utils.ExcelUtil;
import com.hengjian.common.log.annotation.InMethodLog;
import com.hengjian.common.mybatis.core.page.PageQuery;
import com.hengjian.common.mybatis.core.page.TableDataInfo;
import com.hengjian.common.satoken.utils.LoginHelper;
import com.hengjian.common.tenant.helper.TenantHelper;
import com.hengjian.extend.event.OSSDownloadEvent;
import com.hengjian.extend.event.OSSUploadEvent;
import com.hengjian.system.domain.vo.SysOssVo;
import com.zsmall.activity.biz.service.ChinaSpotProductService;
import com.zsmall.activity.entity.domain.vo.chinaSpotProduct.ChinaSpotProduct;
import com.zsmall.activity.entity.domain.vo.chinaSpotProduct.ChinaSpotProductSku;
import com.zsmall.activity.entity.domain.vo.chinaSpotProduct.ChinaSpotProductSkuAttachment;
import com.zsmall.activity.entity.domain.vo.chinaSpotProduct.ChinaSpotProductSkuStock;
import com.zsmall.activity.entity.domain.bo.chinaSpotProduct.ChinaSpotProductDetailBo;
import com.zsmall.activity.entity.domain.bo.chinaSpotProduct.ChinaSpotProductListBo;
import com.zsmall.activity.entity.domain.dto.ChinaSpotProductExcelDTO;
import com.zsmall.activity.entity.domain.dto.ChinaSpotProductManagerDTO;
import com.zsmall.activity.entity.domain.dto.ChinaSpotProductSupplierDTO;
import com.zsmall.activity.entity.domain.vo.chinaSpotProduct.ChinaSpotProductDetailVo;
import com.zsmall.activity.entity.domain.vo.chinaSpotProduct.ChinaSpotProductVo;
import com.zsmall.activity.entity.domain.vo.chinaSpotProduct.RespChinaSpotProductSkuDetailBody;
import com.zsmall.activity.entity.iservice.IChinaSpotProductService;
import com.zsmall.activity.entity.iservice.IChinaSpotProductSkuAttachmentService;
import com.zsmall.activity.entity.iservice.IChinaSpotProductSkuService;
import com.zsmall.activity.entity.iservice.IChinaSpotProductSkuStockService;
import com.zsmall.activity.entity.util.ActivityCodeGenerator;
import com.zsmall.common.constant.ChinaSpotProductConstant;
import com.zsmall.common.constant.FileNameConstants;
import com.zsmall.common.domain.LocaleMessage;
import com.zsmall.common.enums.BusinessCodeEnum;
import com.zsmall.common.enums.ExcelMessageEnum;
import com.zsmall.common.enums.common.AttachmentTypeEnum;
import com.zsmall.common.enums.downloadRecord.DownloadTypePlusEnum;
import com.zsmall.common.enums.downloadRecord.RecordStateEnum;
import com.zsmall.common.enums.product.LengthUnitEnum;
import com.zsmall.common.enums.product.WeightUnitEnum;
import com.zsmall.common.enums.statuscode.ZSMallStatusCodeEnum;
import com.zsmall.common.exception.ExcelMessageException;
import com.zsmall.common.properties.FileProperties;
import com.zsmall.common.util.DecimalUtil;
import com.zsmall.common.util.ExcelMsgBuilder;
import com.zsmall.common.util.ZExcelUtil;
import com.zsmall.product.entity.domain.ProductCategory;
import com.zsmall.product.entity.domain.vo.category.ProductCategoryVo;
import com.zsmall.product.entity.iservice.IProductCategoryService;
import com.zsmall.system.entity.domain.DownloadRecord;
import com.zsmall.system.entity.iservice.IDownloadRecordService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Sheet;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.BufferedInputStream;
import java.io.BufferedOutputStream;
import java.io.File;
import java.io.InputStream;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
@RequiredArgsConstructor
public class ChinaSpotProductServiceImpl implements ChinaSpotProductService {

    private final IChinaSpotProductSkuAttachmentService iChinaSpotProductSkuAttachmentService;
    private final IChinaSpotProductService iChinaSpotProductService;
    private final IChinaSpotProductSkuStockService iChinaSpotProductSkuStockService;
    private final IChinaSpotProductSkuService iChinaSpotProductSkuService;
    private final IProductCategoryService iProductCategoryService;
    private final IDownloadRecordService iDownloadRecordService;
    private final FileProperties fileProperties;
    private final ActivityCodeGenerator activityCodeGenerator;


    @InMethodLog(value = "分页查询国内现货商品")
    @Override
    public TableDataInfo<ChinaSpotProductVo> queryPage(ChinaSpotProductListBo bo, PageQuery pageQuery) {

        TenantType tenantType = LoginHelper.getTenantTypeEnum();

        Page page = new Page(pageQuery.getPageNum(), pageQuery.getPageSize());
        Page pageResult = getChinaSpotProductPage(bo, page);

        List<ChinaSpotProduct> records = pageResult.getRecords();
        List<ChinaSpotProductVo> results = new ArrayList<>();
        if (CollUtil.isNotEmpty(records)) {
            for (ChinaSpotProduct record : records) {
                ChinaSpotProductVo respChinaSpotProductBody = new ChinaSpotProductVo();

                Long chinaSpotProductId = record.getId();

                Date createDateTime = record.getCreateTime();
                String productCode = record.getProductCode();
                String productName = record.getProductName();
                Integer skuQuantity = record.getSkuQuantity();
                Long quantityTotal = iChinaSpotProductSkuStockService.countQuantityTotalByProduct(chinaSpotProductId);

                respChinaSpotProductBody.setProductCode(productCode);
                respChinaSpotProductBody.setProductName(productName);
                respChinaSpotProductBody.setSkuQuantity(skuQuantity);
                respChinaSpotProductBody.setInventoryQuantity(quantityTotal);
                respChinaSpotProductBody.setCreateDateTime(DateUtil.format(createDateTime, "yyyy-MM-dd HH:mm:ss"));

                List<ChinaSpotProductSkuAttachment> attachmentList =
                    iChinaSpotProductSkuAttachmentService.queryListByChinaSpotProductId(chinaSpotProductId);
                if (CollUtil.isNotEmpty(attachmentList)) {
                    ChinaSpotProductSkuAttachment spotProductSkuAttachment = attachmentList.get(0);
                    respChinaSpotProductBody.setImageShowUrl(spotProductSkuAttachment.getAttachmentShowUrl());
                }

                if (TenantType.Manager.equals(tenantType)) {
                    String creatorCode = record.getTenantId();
                    respChinaSpotProductBody.setSupCode(creatorCode);
                }

                results.add(respChinaSpotProductBody);
            }
        }
        return new TableDataInfo<>(results, pageResult.getTotal());
    }

    private Page getChinaSpotProductPage(ChinaSpotProductListBo bo, Page page) {
        String queryType = bo.getQueryType();
        String queryValue = bo.getQueryValue();

        if (StrUtil.isBlank(queryValue)) {
            queryType = null;
        } else {
            queryValue = "%" + queryValue + "%";
        }
        page = iChinaSpotProductService.queryPage(queryType, queryValue, page);
        return page;
    }


    @InMethodLog(value = "查询国内现货商品详情")
    @Override
    public R<ChinaSpotProductDetailVo> queryDetail(ChinaSpotProductDetailBo bo) {

        String req_productCode = bo.getProductCode();

        if (StrUtil.isBlank(req_productCode)) {
            return R.fail(ZSMallStatusCodeEnum.REQUIRED_CANNOT_EMPTY);
        }

        ChinaSpotProduct chinaSpotProductEntity = iChinaSpotProductService.queryByProductCode(req_productCode);
        if (chinaSpotProductEntity == null) {
            return R.fail(ZSMallStatusCodeEnum.QUERY_CHINA_SPOT_PRODUCT_NOT_EXIST);
        }

        Long chinaSpotProductId = chinaSpotProductEntity.getId();

        String productCode = chinaSpotProductEntity.getProductCode();
        String productName = chinaSpotProductEntity.getProductName();
        Integer minimumQuantity = chinaSpotProductEntity.getMinimumQuantity();

        Long belongCategoryId = chinaSpotProductEntity.getBelongCategoryId();

        ChinaSpotProductDetailVo respBody = new ChinaSpotProductDetailVo();
        respBody.setProductCode(productCode);
        respBody.setProductName(productName);
        respBody.setMinimumQuantity(minimumQuantity);

        // 获取分类
        ProductCategoryVo productCategory = iProductCategoryService.queryById(belongCategoryId);
        List<String> categoryNameEN = new LinkedList<>();
        categoryNameEN.add(productCategory.getCategoryName());

        Long parentId = productCategory.getParentId();
        while (parentId != null && !ObjectUtil.equals(parentId, 0L)) {
            productCategory = iProductCategoryService.queryById(parentId);
            categoryNameEN.add(productCategory.getCategoryName());
            parentId = productCategory.getParentId();
        }
        List<String> reverseList = CollUtil.reverse(categoryNameEN);

        respBody.setCategoryName_zh_CN(CollUtil.join(reverseList, "/"));
        respBody.setCategoryName_en_US(CollUtil.join(reverseList, "/"));

        List<ChinaSpotProductSku> chinaSpotProductSkuList =
            iChinaSpotProductSkuService.queryByChinaSpotProductId(chinaSpotProductId);

        List<RespChinaSpotProductSkuDetailBody> skuDetailBodyList = new ArrayList<>();
        if (CollUtil.isNotEmpty(chinaSpotProductSkuList)) {
            for (ChinaSpotProductSku chinaSpotProductSkuEntity : chinaSpotProductSkuList) {
                RespChinaSpotProductSkuDetailBody skuDetailBody = new RespChinaSpotProductSkuDetailBody();

                Long spotProductSkuId = chinaSpotProductSkuEntity.getId();
                String sku = chinaSpotProductSkuEntity.getSku();
                String productSkuCode = chinaSpotProductSkuEntity.getProductSkuCode();
                String fixedSpecValue = chinaSpotProductSkuEntity.getFixedSpecValue();
                BigDecimal referencePrice = chinaSpotProductSkuEntity.getReferencePrice();
                BigDecimal length = chinaSpotProductSkuEntity.getLength();
                BigDecimal width = chinaSpotProductSkuEntity.getWidth();
                BigDecimal height = chinaSpotProductSkuEntity.getHeight();
                String lengthUnit = chinaSpotProductSkuEntity.getLengthUnit();

                BigDecimal weight = chinaSpotProductSkuEntity.getWeight();
                String weightUnit = chinaSpotProductSkuEntity.getWeightUnit();

                BigDecimal packLength = chinaSpotProductSkuEntity.getPackLength();
                BigDecimal packWidth = chinaSpotProductSkuEntity.getPackWidth();
                BigDecimal packHeight = chinaSpotProductSkuEntity.getPackHeight();
                String packLengthUnit = chinaSpotProductSkuEntity.getPackLengthUnit();

                BigDecimal packWeight = chinaSpotProductSkuEntity.getPackWeight();
                String packWeightUnit = chinaSpotProductSkuEntity.getPackWeightUnit();

                String description = chinaSpotProductSkuEntity.getDescription();

                skuDetailBody.setSku(sku);
                skuDetailBody.setProductSkuCode(productSkuCode);
                skuDetailBody.setFixedSpecValue(fixedSpecValue);
                skuDetailBody.setDescription(description);
                skuDetailBody.setReferencePrice(DecimalUtil.bigDecimalToString(referencePrice));
                skuDetailBody.setLength(DecimalUtil.bigDecimalToString(length));
                skuDetailBody.setWidth(DecimalUtil.bigDecimalToString(width));
                skuDetailBody.setHeight(DecimalUtil.bigDecimalToString(height));
                skuDetailBody.setLengthUnit(lengthUnit);

                skuDetailBody.setWeight(DecimalUtil.bigDecimalToString(weight));
                skuDetailBody.setWeightUnit(weightUnit);

                skuDetailBody.setPackLength(DecimalUtil.bigDecimalToString(packLength));
                skuDetailBody.setPackWidth(DecimalUtil.bigDecimalToString(packWidth));
                skuDetailBody.setPackHeight(DecimalUtil.bigDecimalToString(packHeight));
                skuDetailBody.setPackLengthUnit(packLengthUnit);

                skuDetailBody.setPackWeight(DecimalUtil.bigDecimalToString(packWeight));
                skuDetailBody.setPackWeightUnit(packWeightUnit);

                List<ChinaSpotProductSkuStock> productSkuInventoryList =
                    iChinaSpotProductSkuStockService.queryBySpotProductSkuId(spotProductSkuId);
                if (CollUtil.isNotEmpty(productSkuInventoryList)) {
                    Integer quantity = 0;
                    String warehouseBelongCity = null;
                    for (ChinaSpotProductSkuStock chinaSpotProductSkuInventoryEntity : productSkuInventoryList) {
                        quantity += chinaSpotProductSkuInventoryEntity.getQuantity();
                        warehouseBelongCity = chinaSpotProductSkuInventoryEntity.getWarehouseBelongCity();
                    }

                    skuDetailBody.setQuantity(quantity);
                    skuDetailBody.setWarehouseBelongCity(warehouseBelongCity);
                }

                List<ChinaSpotProductSkuAttachment> skuAttachmentList =
                    iChinaSpotProductSkuAttachmentService.queryBySpotProductSkuId(spotProductSkuId);
                List<String> imageList = new ArrayList<>();
                if (CollUtil.isNotEmpty(skuAttachmentList)) {
                    for (ChinaSpotProductSkuAttachment spotProductSkuAttachment : skuAttachmentList) {
                        imageList.add(spotProductSkuAttachment.getAttachmentShowUrl());
                    }
                }
                skuDetailBody.setImageList(imageList);
                skuDetailBodyList.add(skuDetailBody);
            }
        }

        respBody.setChinaSpotProductSkuList(skuDetailBodyList);

        return R.ok(ZSMallStatusCodeEnum.REQUEST_SUCCESS, respBody);
    }


    @InMethodLog(value = "删除国内现货商品")
    @Override
    public R<Void> deleteChinaSpotProduct(ChinaSpotProductDetailBo bo) {

        String req_productCode = bo.getProductCode();

        ChinaSpotProduct chinaSpotProductEntity = iChinaSpotProductService.queryByProductCode(req_productCode);
        if (chinaSpotProductEntity == null) {
            return R.fail(ZSMallStatusCodeEnum.QUERY_CHINA_SPOT_PRODUCT_NOT_EXIST);
        }

        Long chinaSpotProductId = chinaSpotProductEntity.getId();

        List<ChinaSpotProductSku> chinaSpotProductSkuList =
            iChinaSpotProductSkuService.queryByChinaSpotProductId(chinaSpotProductId);

        List<Long> chinaSpotProductSkuIds = new ArrayList<>();
        List<Long> chinaSpotProductSkuStockIds = new ArrayList<>();
        List<Long> chinaSpotProductSkuAttachmentIds = new ArrayList<>();

        if (CollUtil.isNotEmpty(chinaSpotProductSkuList)) {
            for (ChinaSpotProductSku chinaSpotProductSkuEntity : chinaSpotProductSkuList) {
                Long spotProductSkuId = chinaSpotProductSkuEntity.getId();
                chinaSpotProductSkuIds.add(spotProductSkuId);

                List<ChinaSpotProductSkuStock> productSkuInventoryList =
                    iChinaSpotProductSkuStockService.queryBySpotProductSkuId(spotProductSkuId);
                if (CollUtil.isNotEmpty(productSkuInventoryList)) {
                    List<Long> ids = productSkuInventoryList.stream().map(ChinaSpotProductSkuStock::getId)
                        .collect(Collectors.toList());
                    chinaSpotProductSkuStockIds.addAll(ids);
                }

                List<ChinaSpotProductSkuAttachment> skuAttachmentList =
                    iChinaSpotProductSkuAttachmentService.queryBySpotProductSkuId(spotProductSkuId);
                List<String> imageList = new ArrayList<>();
                if (CollUtil.isNotEmpty(skuAttachmentList)) {
                    List<Long> ids = skuAttachmentList.stream().map(ChinaSpotProductSkuAttachment::getId)
                        .collect(Collectors.toList());
                    chinaSpotProductSkuAttachmentIds.addAll(ids);
                }
            }
        }

        if (CollUtil.isNotEmpty(chinaSpotProductSkuIds)) {
            TenantHelper.ignore(() -> iChinaSpotProductSkuService.removeByIds(chinaSpotProductSkuIds));
        }

        if (CollUtil.isNotEmpty(chinaSpotProductSkuStockIds)) {
            iChinaSpotProductSkuStockService.removeByIds(chinaSpotProductSkuStockIds);
        }

        if (CollUtil.isNotEmpty(chinaSpotProductSkuAttachmentIds)) {
            iChinaSpotProductSkuAttachmentService.removeByIds(chinaSpotProductSkuAttachmentIds);
        }

        TenantHelper.ignore(() -> iChinaSpotProductService.removeById(chinaSpotProductEntity));

        return R.ok();
    }


    @InMethodLog(value = "上传国内现货商品Excel")
    @Override
    @Transactional(rollbackFor = Exception.class)
    public R<Void> uploadChinaSpotProductExcel(MultipartFile file) {
        if (file == null) {
            return R.fail(ZSMallStatusCodeEnum.UPLOAD_FILE_IS_EMPTY);
        }
        try {
            //业务处理
            InputStream inputStream = file.getInputStream();
            ExcelReader reader = cn.hutool.poi.excel.ExcelUtil.getReader(inputStream);
            if (reader != null) {
                Sheet sheet = reader.getSheet();
                String sheetName = sheet.getSheetName();
                log.info("uploadOrderExcel - sheetName = {}", sheetName);
                int columnCount = reader.getColumnCount();
                log.info("uploadOrderExcel - columnCount = {}", columnCount);

                if (!NumberUtil.equals(23, columnCount)) {
                    throw new RStatusCodeException(ZSMallStatusCodeEnum.EXCEL_COLUMN_COUNT_NOT_MATCH);
                }
                int rowCount = reader.getRowCount();
                if (rowCount < 8) {
                    throw new RStatusCodeException(ZSMallStatusCodeEnum.PRODUCT_IMPORT_FILE_NOT_ROW);
                }
                // 有效行
                int physicalRows = sheet.getPhysicalNumberOfRows();
                log.info("uploadOrderExcel - physicalRows = {}", physicalRows);
                List<ChinaSpotProductExcelDTO> importDTOS = ZExcelUtil.parseFieldDTO(reader, ChinaSpotProductExcelDTO.class, 9, 10);

                ExcelMsgBuilder<ChinaSpotProductExcelDTO> builder = ZExcelUtil.msgBuilder(reader, 9, ChinaSpotProductExcelDTO.class);
                builder.setMsgPrefix("<p>").setMsgSuffix("</p></br>");

                log.info("importDTOS = {}", JSONUtil.toJsonStr(importDTOS));

                TimeInterval timer1 = DateUtil.timer();
                // 重复的SKU
                Set<String> repeatSkuSet = new HashSet<>();
                Map<String, ChinaSpotProduct> productGroup = new HashMap<>();
                LocaleMessage localeMessage = new LocaleMessage();
                //导入数据格式校验
                for (ChinaSpotProductExcelDTO importDTO : importDTOS) {
                    int showRowIndex = importDTO.getShowRowIndex();
                    builder.setNowShowRow(showRowIndex);

                    String spu = importDTO.getSpu();
                    ChinaSpotProduct chinaSpotProduct = productGroup.get(spu);
                    String categoryName = importDTO.getCategoryName();
                    String productName = importDTO.getProductName();
                    Integer minimumQuantity = importDTO.getMinimumQuantity();

                    // 不存在实体，说明是第一个SPU数据，需要判断SPU的共有数据是否填写
                    if (chinaSpotProduct == null) {
                        if (StrUtil.isBlank(spu)) {
                            localeMessage.append(builder.build(ChinaSpotProductExcelDTO::getSpu, ExcelMessageEnum.CHINA_SPOT_REQUIRED));
                        } else {
                            if (StrUtil.length(spu) > 1600) {
                                localeMessage.append(builder.build(ChinaSpotProductExcelDTO::getSpu, ExcelMessageEnum.CHINA_SPOT_PRODUCT_NAME_CHAR_LIMIT));
                            }
                        }
                        if (StrUtil.isBlank(productName)) {
                            localeMessage.append(builder.build(ChinaSpotProductExcelDTO::getProductName, ExcelMessageEnum.CHINA_SPOT_REQUIRED));
                        }else {
                            if (StrUtil.length(productName) > 1600) {
                                localeMessage.append(builder.build(ChinaSpotProductExcelDTO::getProductName, ExcelMessageEnum.CHINA_SPOT_PRODUCT_NAME_CHAR_LIMIT));
                            }
                        }
                        if (minimumQuantity == null) {
                            localeMessage.append(builder.build(ChinaSpotProductExcelDTO::getMinimumQuantity, ExcelMessageEnum.CHINA_SPOT_REQUIRED));
                        } else if (StrUtil.length(minimumQuantity.toString()) >= 29 || minimumQuantity <= 0) {
                            localeMessage.append(builder.build(ChinaSpotProductExcelDTO::getMinimumQuantity, ExcelMessageEnum.CHINA_SPOT_QUANTITY_ERROR));
                        }
                        ProductCategory productCategory = new ProductCategory();
                        if (StrUtil.isBlank(categoryName)) {
                            localeMessage.append(builder.build(ChinaSpotProductExcelDTO::getCategoryName, ExcelMessageEnum.CHINA_SPOT_REQUIRED));
                        } else {
                            List<ProductCategory> productCategories = iProductCategoryService.queryByCategoryName(categoryName);
                            if (CollUtil.isEmpty(productCategories)) {
                                localeMessage.append(builder.build(ChinaSpotProductExcelDTO::getCategoryName, ExcelMessageEnum.CHINA_SPOT_CATEGORY_NOT_EXIST));
                            } else {
                                productCategory = productCategories.get(0);
                            }
                        }
                        chinaSpotProduct = new ChinaSpotProduct();
                        String productCode = activityCodeGenerator.codeGenerate(BusinessCodeEnum.ChinaSpotProductCode);
                        chinaSpotProduct.setSkuQuantity(0);
                        chinaSpotProduct.setProductCode(productCode);
                        chinaSpotProduct.setProductName(productName);
                        chinaSpotProduct.setMinimumQuantity(minimumQuantity);
                        chinaSpotProduct.setBelongCategoryId(productCategory.getId());
                        chinaSpotProduct.setReviewState(ChinaSpotProductConstant.ReviewStatus.UNDER_REVIEW);
                    }

                    String sku = importDTO.getSku();
                    if (StrUtil.length(sku) > 16000) {
                        localeMessage.append(builder.build(ChinaSpotProductExcelDTO::getSku, ExcelMessageEnum.CHINA_SPOT_PRODUCT_NAME_CHAR_LIMIT));
                    }
                    // 判断SKU重复
                    if (repeatSkuSet.contains(sku)) {
                        localeMessage.append(builder.build(ChinaSpotProductExcelDTO::getSku, ExcelMessageEnum.SKU_REPEAT));
                    } else {
                        Long count = iChinaSpotProductSkuService.countBySku(sku);
                        if (count > 0) {
                            localeMessage.append(builder.build(ChinaSpotProductExcelDTO::getSku, ExcelMessageEnum.SKU_REPEAT));
                        } else {
                            repeatSkuSet.add(sku);
                        }
                    }

                    String specValue = importDTO.getSpecValue();
                    if (ReUtil.contains(RegexPool.CHINESE, specValue)) {
                        localeMessage.append(builder.build(ChinaSpotProductExcelDTO::getSpecValue, ExcelMessageEnum.CHINA_SPOT_ONLY_ENGLISH));
                    }

                    Integer quantity = importDTO.getQuantity();
                    BigDecimal referencePrice = importDTO.getReferencePrice();

                    BigDecimal length = importDTO.getLength();
                    BigDecimal width = importDTO.getWidth();
                    BigDecimal height = importDTO.getHeight();
                    BigDecimal weight = importDTO.getWeight();

                    BigDecimal packLength = importDTO.getPackLength();
                    BigDecimal packWidth = importDTO.getPackWidth();
                    BigDecimal packHeight = importDTO.getPackHeight();
                    BigDecimal packWeight = importDTO.getPackWeight();
                    if (length.precision() > 29
                        || StrUtil.length(quantity.toString()) > 29
                        || width.precision() > 29
                        || height.precision() > 29
                        || weight.precision() > 29
                        || packLength.precision() > 29
                        || packWidth.precision() > 29
                        || packHeight.precision() > 29
                        || packWeight.precision() > 29
                    ){
                        localeMessage.append(builder.buildOnlyRow(ExcelMessageEnum.CHINA_SPOT_SKU_SIZE_ERROR));
                    }
                    String warehouseBelongCity = importDTO.getWarehouseBelongCity();
                    if (StrUtil.length(warehouseBelongCity) > 1600) {
                        localeMessage.append(builder.build(ChinaSpotProductExcelDTO::getWarehouseBelongCity, ExcelMessageEnum.CHINA_SPOT_PRODUCT_NAME_CHAR_LIMIT));
                    }
                    String imageUrl1 = importDTO.getImageUrl1();
                    String imageUrl2 = importDTO.getImageUrl2();
                    String imageUrl3 = importDTO.getImageUrl3();
                    String imageUrl4 = importDTO.getImageUrl4();
                    String imageUrl5 = importDTO.getImageUrl5();
                    List<String> imageUrlList = new ArrayList<>();
                    imageUrlList.add(imageUrl1);
                    if (StrUtil.isNotBlank(imageUrl2)) {
                        imageUrlList.add(imageUrl2);
                    }
                    if (StrUtil.isNotBlank(imageUrl3)) {
                        imageUrlList.add(imageUrl3);
                    }
                    if (StrUtil.isNotBlank(imageUrl4)) {
                        imageUrlList.add(imageUrl4);
                    }
                    if (StrUtil.isNotBlank(imageUrl5)) {
                        imageUrlList.add(imageUrl5);
                    }
                    for (int i = 0; i < imageUrlList.size(); i++) {
                        String imageUrl = imageUrlList.get(i);
                        if (StrUtil.isNotBlank(imageUrl) && StrUtil.length(FileUtil.getSuffix(imageUrl)) > 500) {
                            localeMessage.append(builder.build(ExcelMessageEnum.CHINA_SPOT_PRODUCT_NAME_CHAR_LIMIT));
                        }
                    }
                    String description = importDTO.getDescription();

                    ChinaSpotProductSku chinaSpotProductSku = new ChinaSpotProductSku();
                    chinaSpotProductSku.setProductSkuCode(activityCodeGenerator.codeGenerate(BusinessCodeEnum.ChinaSpotProductSkuCode));
                    chinaSpotProductSku.setSku(sku);
                    chinaSpotProductSku.setFixedSpecValue(specValue);
                    chinaSpotProductSku.setLength(length);
                    chinaSpotProductSku.setWidth(width);
                    chinaSpotProductSku.setHeight(height);
                    chinaSpotProductSku.setLengthUnit(LengthUnitEnum.inch.getUnit());
                    chinaSpotProductSku.setWeight(weight);
                    chinaSpotProductSku.setWeightUnit(WeightUnitEnum.lb.getUnit());

                    chinaSpotProductSku.setPackLength(packLength);
                    chinaSpotProductSku.setPackWidth(packWidth);
                    chinaSpotProductSku.setPackHeight(packHeight);
                    chinaSpotProductSku.setPackLengthUnit(LengthUnitEnum.inch.getUnit());
                    chinaSpotProductSku.setPackWeight(packWeight);
                    chinaSpotProductSku.setPackWeightUnit(WeightUnitEnum.lb.getUnit());
                    chinaSpotProductSku.setDescription(description);
                    chinaSpotProductSku.setReferencePrice(referencePrice);

                    List<ChinaSpotProductSkuStock> chinaSpotProductSkuStockList = new ArrayList<>();
                    ChinaSpotProductSkuStock productSkuStock = new ChinaSpotProductSkuStock();
                    productSkuStock.setQuantity(quantity);
                    productSkuStock.setQuantityTotal(quantity);
                    productSkuStock.setWarehouseBelongCity(warehouseBelongCity);
                    chinaSpotProductSkuStockList.add(productSkuStock);

                    List<ChinaSpotProductSkuAttachment> attachments = new ArrayList<>();
                    for (int i = 0; i < imageUrlList.size(); i++) {
                        String imageUrl = imageUrlList.get(i);
                        if (StrUtil.isBlank(imageUrl)) {
                            continue;
                        }

                        String imageSuffix = FileUtil.getSuffix(imageUrl);
                        // 检查是否是非法的后缀
                        if (StrUtil.length(imageSuffix) > 5) {
                            continue;
                        }
                        OSSDownloadEvent downloadEvent = new OSSDownloadEvent(imageUrl);
                        SpringUtils.publishEvent(downloadEvent);
                        SysOssVo sysOssVo = downloadEvent.getSysOssVo();

                        if (ObjectUtil.isNotNull(sysOssVo)) {
                            ChinaSpotProductSkuAttachment attachment = new ChinaSpotProductSkuAttachment();
                            attachment.setOssId(sysOssVo.getOssId());
                            attachment.setAttachmentName(sysOssVo.getFileName());
                            attachment.setAttachmentSavePath(sysOssVo.getSavePath());
                            attachment.setAttachmentShowUrl(sysOssVo.getUrl());
                            attachment.setAttachmentSort(i + 1);
                            attachment.setAttachmentType(AttachmentTypeEnum.Image.name());
                            attachment.setAttachmentSuffix(imageSuffix);
                            attachment.setAttachmentOriginalName(sysOssVo.getOriginalName());
                            attachments.add(attachment);
                        }
                    }
                    chinaSpotProductSku.setChinaSpotProductSkuStockList(chinaSpotProductSkuStockList);
                    chinaSpotProductSku.setChinaSpotProductSkuAttachmentList(attachments);

                    List<ChinaSpotProductSku> chinaSpotProductSkuList =
                        chinaSpotProduct.getChinaSpotProductSkuList();
                    if (chinaSpotProductSkuList == null) {
                        chinaSpotProductSkuList = new ArrayList<>();
                    }
                    chinaSpotProductSkuList.add(chinaSpotProductSku);
                    chinaSpotProduct.setChinaSpotProductSkuList(chinaSpotProductSkuList);

                    Integer skuQuantity = chinaSpotProduct.getSkuQuantity();
                    chinaSpotProduct.setSkuQuantity(++skuQuantity);

                    productGroup.put(spu, chinaSpotProduct);
                }

                if (localeMessage.hasData()) {
                    throw new ExcelMessageException(localeMessage);
                }

                TimeInterval timer2 = DateUtil.timer();
                Set<String> keySet = productGroup.keySet();
                for (String key : keySet) {
                    ChinaSpotProduct chinaSpotProduct = productGroup.get(key);
                    iChinaSpotProductService.saveOrUpdate(chinaSpotProduct);
                    Long chinaSpotProductId = chinaSpotProduct.getId();

                    List<ChinaSpotProductSku> chinaSpotProductSkuList = chinaSpotProduct.getChinaSpotProductSkuList();
                    for (ChinaSpotProductSku chinaSpotProductSku : chinaSpotProductSkuList) {
                        chinaSpotProductSku.setChinaSpotProductId(chinaSpotProductId);
                        iChinaSpotProductSkuService.saveOrUpdate(chinaSpotProductSku);
                        Long chinaSpotProductSkuId = chinaSpotProductSku.getId();

                        List<ChinaSpotProductSkuStock> spotProductSkuStocks =
                            chinaSpotProductSku.getChinaSpotProductSkuStockList();
                        for (ChinaSpotProductSkuStock stock : spotProductSkuStocks) {
                            stock.setChinaSpotProductId(chinaSpotProductId);
                            stock.setChinaSpotProductSkuId(chinaSpotProductSkuId);
                        }

                        List<ChinaSpotProductSkuAttachment> chinaSpotProductSkuAttachmentList =
                            chinaSpotProductSku.getChinaSpotProductSkuAttachmentList();
                        for (ChinaSpotProductSkuAttachment attachment : chinaSpotProductSkuAttachmentList) {
                            attachment.setChinaSpotProductId(chinaSpotProductId);
                            attachment.setChinaSpotProductSkuId(chinaSpotProductSkuId);
                        }

                        iChinaSpotProductSkuStockService.saveBatch(spotProductSkuStocks);
                        iChinaSpotProductSkuAttachmentService.saveBatch(chinaSpotProductSkuAttachmentList);
                    }
                }
                log.info("保存数据，花费时间 = {}", timer2.interval());
                log.info("遍历DTO花费时间 = {}", timer1.interval());
            }
            return R.ok();
        } catch (ExcelMessageException e) {
            log.error("Exception error = {}", e.getMessage());
            return R.failHtml(e.getLocaleMessage().toMessage());
        } catch (RStatusCodeException e) {
            log.error("Exception error = {}", e.getStatusCode());
            return R.fail(e.getStatusCode());
        } catch (Exception e) {
            e.printStackTrace();
            throw new RStatusCodeException(ZSMallStatusCodeEnum.DATABASE_DATA_TOO_LONG);
        }
    }


    @InMethodLog(value = "导出国内现货商品列表")
    @Override
    public R<Void> exportChinaSpotProductList(ChinaSpotProductListBo bo) {

        TenantType tenantType = LoginHelper.getTenantTypeEnum();
        Boolean existsed = iDownloadRecordService.existsByRecordState(RecordStateEnum.Generating);
        if (existsed) {
            return R.fail(ZSMallStatusCodeEnum.DOWNLOAD_RECORD_GENERATING);
        } else {
            String fileName = StrUtil.format(FileNameConstants.CHINA_SPOT_PRODUCT_EXPORT, DateUtil.format(new Date(), "yyMMdd-HH:mm:ss.SSSS"));
            // 创建新的下载记录
            DownloadRecord newRecord = new DownloadRecord();
            newRecord.setRecordState(RecordStateEnum.Generating);
            newRecord.setFileName(fileName);
            newRecord.setDownloadType(DownloadTypePlusEnum.ChinaSpotProduct);
            iDownloadRecordService.save(newRecord);

            ThreadUtil.execute(() -> {
                try {
                    Page<ChinaSpotProduct> chinaSpotProductPage = getChinaSpotProductPage(bo, new PageQuery().build());
                    List<ChinaSpotProduct> list = chinaSpotProductPage.getRecords();
                    List<Map<String, Object>> mapList = new ArrayList<>();
                    if (CollUtil.isNotEmpty(list)) {
                        for (ChinaSpotProduct record : list) {
                            Long chinaSpotProductId = record.getId();

                            Long belongCategoryId = record.getBelongCategoryId();
                            String productCode = record.getProductCode();
                            String productName = record.getProductName();
                            Integer minimumQuantity = record.getMinimumQuantity();

                            // 获取分类
                            ProductCategoryVo productCategory = iProductCategoryService.queryById(belongCategoryId);
                            List<String> categoryNameEN = new LinkedList<>();
                            categoryNameEN.add(productCategory.getCategoryName());
                            Long parentId = productCategory.getParentId();
                            while (parentId != null && !ObjectUtil.equals(parentId, 0L)) {
                                productCategory = iProductCategoryService.queryById(parentId);
                                categoryNameEN.add(productCategory.getCategoryName());
                                parentId = productCategory.getParentId();
                            }
                            List<String> reverseList = CollUtil.reverse(categoryNameEN);

                            Map<String, Object> productMap = new LinkedHashMap<>();
                            if (TenantType.Manager.equals(tenantType)) {
                                productMap.put("tenantId", record.getTenantId());
                            }
                            productMap.put("productCode", productCode);
                            productMap.put("categoryName", CollUtil.join(reverseList, "/"));
                            productMap.put("productName", productName);
                            productMap.put("minimumQuantity", minimumQuantity);

                            List<ChinaSpotProductSku> chinaSpotProductSkuList =
                                iChinaSpotProductSkuService.queryByChinaSpotProductId(chinaSpotProductId);

                            if (CollUtil.isNotEmpty(chinaSpotProductSkuList)) {
                                for (ChinaSpotProductSku chinaSpotProductSkuEntity : chinaSpotProductSkuList) {
                                    Map<String, Object> productSkuMap = new LinkedHashMap<>();
                                    productSkuMap.putAll(productMap);

                                    Long spotProductSkuId = chinaSpotProductSkuEntity.getId();
                                    String sku = chinaSpotProductSkuEntity.getSku();
                                    String fixedSpecValue = chinaSpotProductSkuEntity.getFixedSpecValue();
                                    BigDecimal referencePrice = chinaSpotProductSkuEntity.getReferencePrice();
                                    BigDecimal length = chinaSpotProductSkuEntity.getLength();
                                    BigDecimal width = chinaSpotProductSkuEntity.getWidth();
                                    BigDecimal height = chinaSpotProductSkuEntity.getHeight();
                                    String lengthUnit = chinaSpotProductSkuEntity.getLengthUnit();

                                    BigDecimal weight = chinaSpotProductSkuEntity.getWeight();
                                    String weightUnit = chinaSpotProductSkuEntity.getWeightUnit();

                                    BigDecimal packLength = chinaSpotProductSkuEntity.getPackLength();
                                    BigDecimal packWidth = chinaSpotProductSkuEntity.getPackWidth();
                                    BigDecimal packHeight = chinaSpotProductSkuEntity.getPackHeight();
                                    String packLengthUnit = chinaSpotProductSkuEntity.getPackLengthUnit();

                                    BigDecimal packWeight = chinaSpotProductSkuEntity.getPackWeight();
                                    String packWeightUnit = chinaSpotProductSkuEntity.getPackWeightUnit();

                                    productSkuMap.put("sku", sku);
                                    productSkuMap.put("fixedSpecValue", fixedSpecValue);

                                    List<ChinaSpotProductSkuStock> productSkuInventoryList =
                                        iChinaSpotProductSkuStockService.queryBySpotProductSkuId(spotProductSkuId);
                                    if (CollUtil.isNotEmpty(productSkuInventoryList)) {
                                        Integer quantity = 0;
                                        String warehouseBelongCity = null;
                                        for (ChinaSpotProductSkuStock chinaSpotProductSkuInventoryEntity : productSkuInventoryList) {
                                            quantity += chinaSpotProductSkuInventoryEntity.getQuantity();
                                            warehouseBelongCity = chinaSpotProductSkuInventoryEntity.getWarehouseBelongCity();
                                        }

                                        productSkuMap.put("warehouseBelongCity", warehouseBelongCity);
                                        productSkuMap.put("quantity", quantity);
                                    } else {
                                        productSkuMap.put("quantity", 0);
                                    }

                                    productSkuMap.put("length", DecimalUtil.bigDecimalToString(length) + "(" + lengthUnit + ")");
                                    productSkuMap.put("width", DecimalUtil.bigDecimalToString(width) + "(" + lengthUnit + ")");
                                    productSkuMap.put("height", DecimalUtil.bigDecimalToString(height) + "(" + lengthUnit + ")");
                                    productSkuMap.put("weight", DecimalUtil.bigDecimalToString(weight) + "(" + weightUnit + ")");
                                    productSkuMap.put("packLength", DecimalUtil.bigDecimalToString(packLength) + "(" + packLengthUnit + ")");
                                    productSkuMap.put("packWidth", DecimalUtil.bigDecimalToString(packWidth) + "(" + packLengthUnit + ")");
                                    productSkuMap.put("packHeight", DecimalUtil.bigDecimalToString(packHeight) + "(" + packLengthUnit + ")");
                                    productSkuMap.put("packWeight", DecimalUtil.bigDecimalToString(packWeight) + "(" + packWeightUnit + ")");
                                    productSkuMap.put("referencePrice", DecimalUtil.bigDecimalToString(referencePrice));

                                    List<ChinaSpotProductSkuAttachment> skuAttachmentList =
                                        iChinaSpotProductSkuAttachmentService.queryBySpotProductSkuId(spotProductSkuId);
                                    for (int i = 0; i < 5; i++) {
                                        int index = i + 1;
                                        ChinaSpotProductSkuAttachment skuAttachment = CollUtil.get(skuAttachmentList, i);
                                        if (skuAttachment != null) {
                                            productSkuMap.put("image" + index, skuAttachment.getAttachmentShowUrl());
                                        } else {
                                            productSkuMap.put("image" + index, "");
                                        }
                                    }
                                    productSkuMap.put("description", chinaSpotProductSkuEntity.getDescription());
                                    mapList.add(productSkuMap);
                                }
                            }
                        }
                    }
                    List resultList = new ArrayList();
                    if (TenantType.Manager.equals(tenantType)) {
                        for (Map<String, Object> map : mapList) {
                            resultList.add(BeanUtil.toBean(map, ChinaSpotProductManagerDTO.class));
                        }
                    } else {
                        for (Map<String, Object> map : mapList) {
                            resultList.add(BeanUtil.toBean(map, ChinaSpotProductSupplierDTO.class));
                        }
                    }
                    String tempSavePath = fileProperties.getTempSavePath();
                    String tempFile = tempSavePath + File.separator + UUID.fastUUID().toString(true) + ".xlsx";
                    BufferedOutputStream outputStream = FileUtil.getOutputStream(tempFile);
                    if (TenantType.Manager.equals(tenantType)) {
                        ExcelUtil.exportExcelWithLocale(resultList, "ChinaSpotProduct", ChinaSpotProductManagerDTO.class, outputStream);
                    } else {
                        ExcelUtil.exportExcelWithLocale(resultList, "ChinaSpotProduct", ChinaSpotProductSupplierDTO.class, outputStream);
                    }
                    outputStream.close();
                    BufferedInputStream inputStream = FileUtil.getInputStream(tempFile);

                    OSSUploadEvent uploadEvent = new OSSUploadEvent(inputStream, fileName);
                    SpringUtils.publishEvent(uploadEvent);
                    SysOssVo sysOssVo = uploadEvent.getSysOssVo();

                    newRecord.setOssId(sysOssVo.getOssId());
                    newRecord.setFileSaveKey(sysOssVo.getFileName());
                    newRecord.setFileUrl(sysOssVo.getUrl());
                    newRecord.setRecordState(RecordStateEnum.Ready);
                    iDownloadRecordService.updateById(newRecord);
                    inputStream.close();
                    FileUtil.del(tempFile);
                } catch (Exception e) {
                    log.error("【导出国内现货商品】出现未知错误 {}", e.getMessage(), e);
                    newRecord.setRecordState(RecordStateEnum.Failed);
                    iDownloadRecordService.updateById(newRecord);
                }

            });
        }


        return R.ok();


    }
}
