package com.zsmall.activity.biz.support;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import co.elastic.clients.elasticsearch.ml.ValidateRequest;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hengjian.common.core.domain.RStatusCodeBase;
import com.hengjian.common.core.enums.MessageTitleEnum;
import com.hengjian.common.core.enums.TenantType;
import com.hengjian.common.core.exception.RStatusCodeException;
import com.hengjian.common.log.annotation.InMethodLog;
import com.hengjian.common.mybatis.core.page.PageQuery;
import com.hengjian.common.redis.utils.RedisUtils;
import com.hengjian.common.tenant.helper.TenantHelper;
import com.hengjian.extend.utils.SystemEventUtils;
import com.hengjian.extend.verifycode.utils.EmailSmsSendUtils;
import com.hengjian.stream.mqProducer.domain.MessageDto;
import com.hengjian.stream.mqProducer.domain.MessageDtoBuilder;
import com.hengjian.stream.mqProducer.producer.RedisProducer;
import com.hengjian.system.domain.vo.SysUserVo;
import com.zsmall.activity.biz.factory.ProductActivityFactory;
import com.zsmall.activity.entity.domain.dto.ExpiringActivityItemDTO;
import com.zsmall.activity.entity.domain.dto.productActivity.DistributorProductActivity;
import com.zsmall.activity.entity.domain.dto.productActivity.DistributorProductActivityStock;
import com.zsmall.activity.entity.domain.dto.productActivity.SupplierProductActivity;
import com.zsmall.activity.entity.domain.dto.productActivity.SupplierProductActivityStock;
import com.zsmall.activity.entity.iservice.*;
import com.zsmall.activity.entity.util.ActivityCodeGenerator;
import com.zsmall.common.constant.RedisConstants;
import com.zsmall.common.domain.LocaleMessage;
import com.zsmall.common.domain.dto.stock.AdjustStockDTO;
import com.zsmall.common.enums.BusinessCodeEnum;
import com.zsmall.common.enums.BusinessParameterType;
import com.zsmall.common.enums.order.LogisticsTypeEnum;
import com.zsmall.common.enums.order.OrderStateType;
import com.zsmall.common.enums.productActivity.*;
import com.zsmall.common.enums.statuscode.OrderStatusCodeEnum;
import com.zsmall.common.enums.statuscode.ZSMallStatusCodeEnum;
import com.zsmall.common.enums.transaction.TransactionStateEnum;
import com.zsmall.common.enums.transaction.TransactionSubTypeEnum;
import com.zsmall.common.enums.transaction.TransactionTypeEnum;
import com.zsmall.common.exception.StockException;
import com.zsmall.common.exception.WalletException;
import com.zsmall.common.service.BusinessParameterService;
import com.zsmall.extend.utils.ZSMallProductEventUtils;
import com.zsmall.order.entity.domain.Orders;
import com.zsmall.order.entity.iservice.IOrdersService;
import com.zsmall.product.entity.domain.ProductSku;
import com.zsmall.product.entity.domain.ProductSkuPrice;
import com.zsmall.product.entity.domain.ProductSkuStock;
import com.zsmall.product.entity.domain.bo.productSkuPrice.ProductSkuPriceBo;
import com.zsmall.product.entity.domain.vo.ProductSkuAttachmentVo;
import com.zsmall.product.entity.iservice.IProductSkuAttachmentService;
import com.zsmall.product.entity.iservice.IProductSkuPriceRuleService;
import com.zsmall.product.entity.iservice.IProductSkuService;
import com.zsmall.product.entity.iservice.IProductSkuStockService;
import com.zsmall.system.biz.service.TenantWalletService;
import com.zsmall.system.biz.support.BillSupport;
import com.zsmall.system.entity.domain.TransactionRecord;
import com.zsmall.system.entity.iservice.ITransactionsProductActivityItemService;
import com.zsmall.system.entity.util.MallSystemCodeGenerator;
import com.zsmall.warehouse.entity.domain.Warehouse;
import com.zsmall.warehouse.entity.iservice.IWarehouseService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Duration;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 商品活动相关支持
 *
 * <AUTHOR>
 * @date 2023/7/21
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ProductActivitySupport {


    private final IProductSkuService iProductSkuService;
    private final IProductSkuStockService iProductSkuStockService;
    private final IProductSkuPriceRuleService iProductSkuPriceRuleService;
    private final IProductSkuAttachmentService iProductSkuAttachmentService;
    private final IWarehouseService iWarehouseService;
    private final ITransactionsProductActivityItemService iTransactionsProductActivityItemService;
    private final BusinessParameterService businessParameterService;
    private final ProductActivityFactory productActivityFactory;
    private final ActivityCodeGenerator activityCodeGenerator;
    private final MallSystemCodeGenerator mallSystemCodeGenerator;
    private final BillSupport billSupport;
    private final RedisProducer redisProducer;
    private final TenantWalletService tenantWalletService;

    private final ISupplierProductActivityService supplierProductActivityService;
    private final ISupplierProductActivityStockService supplierProductActivityStockService;
    private final ISupplierProductActivityPriceService supplierProductActivityPriceService;
    private final IDistributorProductActivityService distributorProductActivityService;
    private final IDistributorProductActivityStockService distributorProductActivityStockService;
    private final IDistributorProductActivityPriceService distributorProductActivityPriceService;
    private final IOrdersService ordersService;

}
