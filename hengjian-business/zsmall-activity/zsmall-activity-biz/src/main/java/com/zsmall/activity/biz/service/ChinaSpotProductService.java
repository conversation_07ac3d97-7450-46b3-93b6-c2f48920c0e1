package com.zsmall.activity.biz.service;

import com.hengjian.common.core.domain.R;
import com.hengjian.common.mybatis.core.page.PageQuery;
import com.hengjian.common.mybatis.core.page.TableDataInfo;
import com.zsmall.activity.entity.domain.bo.chinaSpotProduct.ChinaSpotProductDetailBo;
import com.zsmall.activity.entity.domain.bo.chinaSpotProduct.ChinaSpotProductListBo;
import com.zsmall.activity.entity.domain.vo.chinaSpotProduct.ChinaSpotProductDetailVo;
import com.zsmall.activity.entity.domain.vo.chinaSpotProduct.ChinaSpotProductVo;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;

/**
 * 国内现货相关功能-Service
 * <AUTHOR>
 * @date 2023/8/15 17:03
 */
public interface ChinaSpotProductService {

    /**
     * 分页查询国内现货商品
     */
    TableDataInfo<ChinaSpotProductVo> queryPage(ChinaSpotProductListBo bo, PageQuery pageQuery);

    /**
     * 查询国内现货商品详情
     */
    R<ChinaSpotProductDetailVo> queryDetail(ChinaSpotProductDetailBo bo);

    /**
     * 删除国内现货商品
     */
    R<Void> deleteChinaSpotProduct(ChinaSpotProductDetailBo bo);

    /**
     * 上传国内现货商品Excel
     */
    R<Void> uploadChinaSpotProductExcel(MultipartFile file) throws IOException;

    /**
     * 导出国内现货商品列表
     */
    R<Void> exportChinaSpotProductList(ChinaSpotProductListBo bo);

}
