package com.zsmall.activity.controller.controller;

import cn.dev33.satoken.annotation.SaIgnore;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hengjian.common.core.domain.R;
import com.hengjian.common.core.enums.TenantType;
import com.hengjian.common.core.exception.ServiceException;
import com.hengjian.common.excel.convert.ExcelBigNumberConvert;
import com.hengjian.common.log.annotation.Log;
import com.hengjian.common.mybatis.core.page.PageQuery;
import com.hengjian.common.mybatis.core.page.TableDataInfo;
import com.hengjian.common.satoken.utils.LoginHelper;
import com.zsmall.activity.biz.service.ProductActiveService;
import com.zsmall.activity.entity.domain.dto.productActivity.*;
import com.zsmall.activity.entity.domain.dto.productActivity.export.*;
import com.zsmall.activity.entity.iservice.IDistributorProductActivityService;
import com.zsmall.activity.entity.iservice.ISupplierProductActivityReviewRecordService;
import com.zsmall.activity.entity.iservice.ISupplierProductActivityService;
import com.zsmall.common.constant.FileNameConstants;
import com.zsmall.common.enums.ProductReviewOpinion;
import com.zsmall.common.enums.downloadRecord.DownloadTypePlusEnum;
import com.zsmall.common.enums.productActivity.ProductActivityExceptionEnum;
import com.zsmall.common.enums.productActivity.ProductActivityReviewState;
import com.zsmall.common.enums.productActivity.ProductActivityStateEnum;
import com.zsmall.product.entity.domain.dto.stock.SkuStock;
import com.zsmall.product.entity.mapper.ProductSkuStockMapper;
import com.zsmall.system.entity.util.DownloadRecordV2Util;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.BufferedOutputStream;
import java.io.File;
import java.io.IOException;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Set;

/**
 * 商品活动相关
 *
 * <AUTHOR>
 * @date 2023/7/21
 */
@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping(value = "/business/productLockActivity")
public class ProductActivityController {
    @Resource
    private  ISupplierProductActivityService supplierProductActivityService;
    @Resource
    private  ISupplierProductActivityReviewRecordService reviewRecordService;
    @Resource
    private IDistributorProductActivityService distributorProductActivityService;
    @Resource
    private  ProductSkuStockMapper productSkuStockMapper;
    @Resource
    private  ProductActiveService productActiveService;

    /**
     * 分页查询活动列表
     */
    @GetMapping("/getList")
    public Object getProductList(ProductActivitySearchDTO bo, PageQuery pageQuery) {
        String tenantId = LoginHelper.getTenantId();
        if (StrUtil.isEmpty(tenantId)) {
            return R.fail("请登陆后使用");
        }
        bo.setTenantType(LoginHelper.getTenantType());
        //超管和供应商查询的是供应商活动,分销商查询的是分销商活动
        if (TenantType.Supplier.name().equals(LoginHelper.getTenantType()) || TenantType.Manager.name().equals(LoginHelper.getTenantType())) {
            if (TenantType.Supplier.name().equals(LoginHelper.getTenantType())){
                bo.setSupplierTenantId(LoginHelper.getTenantId());
            }
            IPage<SupplierProductActivity> page = productActiveService.selectSupplierListResponseDTO(pageQuery.build(), bo);
            CopyOptions copyOptions = CopyOptions.create()
                                                 .setFieldMapping(new HashMap<String, String>() {{
                                                     // 在这里添加源字段名到目标字段名的映射，例如:
                                                     // EsOrdersDTO.currency -> OrderExportListDTO.currencyCode
                                                     put("supplierActivityCode", "activityCode");
                                                 }});
            List<ProductActivitySearchListResponseDTO> s = BeanUtil.copyToList(page.getRecords(), ProductActivitySearchListResponseDTO.class,copyOptions);
            s.forEach(q -> {
                if (ObjectUtil.isNotNull(q.getActiveStartTime()) && ObjectUtil.isNotNull(q.getActiveEndTime())) {
                    q.setRemainingDay(DateUtil.betweenDay(q.getActiveStartTime(), q.getActiveEndTime(), false));
                }
                q.setLockedRemaining(q.getDropShippingQuantityLocked()+q.getPickupQuantityLocked()-q.getDropShippingLockedUsed()-q.getPickupLockedUsed());
            });
            return TableDataInfo.build(s, page.getTotal());
        }else {
            bo.setDistributorTenantId(LoginHelper.getTenantId());
            IPage<DistributorProductActivity> page = productActiveService.selectDistributorListResponseDTO(pageQuery.build(), bo);
            CopyOptions copyOptions = CopyOptions.create()
                                                 .setFieldMapping(new HashMap<String, String>() {{
                                                     // 在这里添加源字段名到目标字段名的映射，例如:
                                                     // EsOrdersDTO.currency -> OrderExportListDTO.currencyCode
                                                     put("distributorActivityCode", "activityCode");
                                                 }});
            List<ProductActivitySearchListResponseDTO> s = BeanUtil.copyToList(page.getRecords(), ProductActivitySearchListResponseDTO.class,copyOptions);
            s.forEach(q -> {
                if (ObjectUtil.isNotNull(q.getActiveStartTime()) && ObjectUtil.isNotNull(q.getActiveEndTime())) {
                    q.setRemainingDay(DateUtil.betweenDay(q.getActiveStartTime(), q.getActiveEndTime(), false));
                    q.setLockedRemaining(q.getDropShippingQuantityLocked()+q.getPickupQuantityLocked()-q.getDropShippingLockedUsed()-q.getPickupLockedUsed());
                }
            });
            return TableDataInfo.build(s, page.getTotal());
        }
    }

    /**
     * 查询活动详情
     * @param activeCode 活动编码
     * @param isSupplier 释放是国内供应商活动
     * @return
     */
    @GetMapping("/getProductActiveDetails")
    public R<ProductActivityDetailsDTO> getProductActiveDetails(String activeCode, Boolean isSupplier) {
        if (StrUtil.isEmpty(activeCode)){
            throw new RuntimeException("请传入活动编码");
        }
        if (ObjectUtil.isNull(isSupplier)){
            throw new RuntimeException("请传入是否是供应商");
        }
        if (StrUtil.isEmpty(LoginHelper.getTenantId())){
            throw new RuntimeException("请登陆后使用");
        }
        List<ProductActivityDetailsDTO> details = productActiveService.selectProductActivityDetailsDTO(isSupplier, Set.of(activeCode));
        if (CollUtil.isEmpty(details)){
            return R.ok();
        }
        ProductActivityDetailsDTO dto = details.get(0);
        if (ObjectUtil.equals(LoginHelper.getTenantType(),TenantType.Supplier.name()) && ObjectUtil.isNull(dto.getDistributorActivityId())){
            List<DistributorProductActivityDetails> actives = distributorProductActivityService.getBaseMapper().getDistributorAvailableActivesBySupplierActiveCode(dto.getSupplierActivityCode());
            dto.setDistributorProductActivityDetails(actives);
            if (CollUtil.isNotEmpty(actives)){
                boolean hasNonInProgressActivity = actives.stream().anyMatch(da -> ProductActivityStateEnum.InProgress.name().equals(da.getActivityState()));
                dto.setHasInProgressDisActivity(hasNonInProgressActivity);
            }else {
                dto.setHasInProgressDisActivity(null);
            }
        }else {
            dto.setHasInProgressDisActivity(null);
        }
        return R.ok(dto);
    }

    /**
     * 供应商活动导入
     * @param file 文件
     * @return
     */
    @Log(title = "供应商活动导入")
    @PostMapping("supplierProductActiveImport")
    public R supplierProductActiveImport(@RequestParam("file") MultipartFile file) throws IOException {
        if (ObjectUtil.notEqual(LoginHelper.getTenantType(), TenantType.Supplier.name())){
            return R.fail("当前操作只支持供应商操作");
        }
        if (file.isEmpty()) {
            throw new ServiceException("上传文件为空");
        }
        productActiveService.supplierProductActiveImport(file);
        return R.ok();
    }

    /**
     * 供应商锁货活动导出
     */
    @PostMapping("/supplierProductActivityListExport")
    public R supplierProductActivityListExport(@RequestBody ProductActivitySearchDTO bo, HttpServletResponse response) {
        String tenantId = LoginHelper.getTenantId();
        if (StrUtil.isEmpty(tenantId)) {
           throw new RuntimeException("请登陆后使用");
        }
        if (!TenantType.Supplier.name().equals(LoginHelper.getTenantType())){
            throw new RuntimeException("供应商锁货活动导出只支持供应商操作");
        }
        bo.setTenantId(tenantId);
        bo.setTenantType(LoginHelper.getTenantType());
        String fileName = StrUtil.format(FileNameConstants.SUPPLIER_PRODUCT_ACTIVITY_LIST_EXPORT, DateUtil.format(new Date(), "yyMMdd-HHmmssSS"));
        DownloadRecordV2Util.generate(fileName, DownloadTypePlusEnum.SupplierProductActivityListExport, tempFileSavePath -> {
            File tempFile = new File(tempFileSavePath);
            BufferedOutputStream outputStream = FileUtil.getOutputStream(tempFile);
//            try {
//                ExcelUtil.resetResponse("供应商锁货活动导出", response, false);
//            } catch (UnsupportedEncodingException e) {
//                throw new RuntimeException(e);
//            }
            ExcelWriter excelWriter = EasyExcel.write(outputStream).build();
            List<AdminProductActivityListExportDTO> page = productActiveService.selectAdminProductActivityListExport(bo);
            List<SupplierProductActivityListExportDTO> dtos = BeanUtil.copyToList(page, SupplierProductActivityListExportDTO.class);
            dtos.forEach(s->{
                s.setQuantityLocked(s.getPickupQuantityLocked()+s.getDropShippingQuantityLocked());
                s.setQuantityLockedRemaining(s.getQuantityLocked()-s.getPickupLockedUsed()-s.getDropShippingLockedUsed());
                s.setSite(s.getSite()+"/"+s.getCurrencySymbol());
            });
            WriteSheet writeSheet = EasyExcel.writerSheet(0, "供应商锁货活动导出")
                                             .head(SupplierProductActivityListExportDTO.class)
                                             .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                                             .registerConverter(new ExcelBigNumberConvert())
                                             .build();
            excelWriter.write(dtos, writeSheet);
            excelWriter.finish();
            IoUtil.close(outputStream);
            return tempFile;
        });
        return R.ok();
    }
    /**
     * Admin锁货活动导出
     */
    @PostMapping("/adminProductActivityListExport")
    public R adminProductActivityListExport(@RequestBody ProductActivitySearchDTO bo, HttpServletResponse response) {
        String tenantId = LoginHelper.getTenantId();
        if (StrUtil.isEmpty(tenantId)) {
            throw new RuntimeException("请登陆后使用");
        }
        if (!TenantType.Manager.name().equals(LoginHelper.getTenantType())){
            throw new RuntimeException("管理员锁货活动导出只支持管理员操作");
        }
        bo.setTenantId(tenantId);
        bo.setTenantType(LoginHelper.getTenantType());
        String fileName = StrUtil.format(FileNameConstants.ADMIN_PRODUCT_ACTIVITY_LIST_EXPORT, DateUtil.format(new Date(), "yyMMdd-HHmmssSS"));
        DownloadRecordV2Util.generate(fileName, DownloadTypePlusEnum.AdminProductActivityListExport, tempFileSavePath -> {
            File tempFile = new File(tempFileSavePath);
            BufferedOutputStream outputStream = FileUtil.getOutputStream(tempFile);
            ExcelWriter excelWriter = EasyExcel.write(outputStream).build();
            List<AdminProductActivityListExportDTO> page = productActiveService.selectAdminProductActivityListExport(bo);
            page.forEach(s->{
                s.setSite(s.getSite()+"/"+s.getCurrencySymbol());
                s.setQuantityLockedRemaining(s.getPickupQuantityLocked()+s.getDropShippingQuantityLocked()-s.getPickupLockedUsed()-s.getDropShippingLockedUsed());
            });
            WriteSheet writeSheet = EasyExcel.writerSheet(0, "管理员锁货活动导出")
                                             .head(AdminProductActivityListExportDTO.class)
                                             .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                                             .registerConverter(new ExcelBigNumberConvert())
                                             .build();
            excelWriter.write(page, writeSheet);
            excelWriter.finish();
            IoUtil.close(outputStream);
            return tempFile;
        });
        return R.ok();
    }

    /**
     * 供应商锁货仓库导出
     */
    @PostMapping("/supplierProductActivityWarehouseExport")
    public R supplierProductActivityWarehouseExport(@RequestBody ProductActivitySearchDTO bo, HttpServletResponse response) {
        String tenantId = LoginHelper.getTenantId();
        if (StrUtil.isEmpty(tenantId)) {
            throw new RuntimeException("请登陆后使用");
        }
        if (!TenantType.Supplier.name().equals(LoginHelper.getTenantType())){
            throw new RuntimeException("供应商锁货活动导出只支持供应商操作");
        }
        bo.setTenantId(tenantId);
        bo.setTenantType(LoginHelper.getTenantType());
        String fileName = StrUtil.format(FileNameConstants.SUPPLIER_PRODUCT_ACTIVITY_WAREHOUSE_EXPORT, DateUtil.format(new Date(), "yyMMdd-HHmmssSS"));
        DownloadRecordV2Util.generate(fileName, DownloadTypePlusEnum.SupplierProductActivityWarehouseExport, tempFileSavePath -> {
            File tempFile = new File(tempFileSavePath);
            BufferedOutputStream outputStream = FileUtil.getOutputStream(tempFile);
            ExcelWriter excelWriter = EasyExcel.write(outputStream).build();
            List<AdminProductActivityWarehouseExportDTO> ad = productActiveService.selectProductActivityWarehouseExport(bo);
            List<SupplierProductActivityWarehouseExportDTO> dtos = BeanUtil.copyToList(ad, SupplierProductActivityWarehouseExportDTO.class);
            WriteSheet writeSheet = EasyExcel.writerSheet(0, "供应商锁货仓库导出")
                                             .head(SupplierProductActivityWarehouseExportDTO.class)
                                             .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                                             .registerConverter(new ExcelBigNumberConvert())
                                             .build();
            excelWriter.write(dtos, writeSheet);
            excelWriter.finish();
            IoUtil.close(outputStream);
            return tempFile;
        });
        return R.ok();
    }

    /**
     * admin锁货仓库导出
     */
    @PostMapping("/adminProductActivityWarehouseExport")
    public R adminProductActivityWarehouseExport(@RequestBody ProductActivitySearchDTO bo, HttpServletResponse response) {
        String tenantId = LoginHelper.getTenantId();
        if (StrUtil.isEmpty(tenantId)) {
            throw new RuntimeException("请登陆后使用");
        }
        if (!TenantType.Manager.name().equals(LoginHelper.getTenantType())){
            throw new RuntimeException("超管锁货仓库导出只支持管理员操作");
        }
        bo.setTenantId(tenantId);
        bo.setTenantType(LoginHelper.getTenantType());
        String fileName = StrUtil.format(FileNameConstants.ADMIN_PRODUCT_ACTIVITY_WAREHOUSE_EXPORT, DateUtil.format(new Date(), "yyMMdd-HHmmssSS"));
        DownloadRecordV2Util.generate(fileName, DownloadTypePlusEnum.AdminProductActivityWarehouseExport, tempFileSavePath -> {
            File tempFile = new File(tempFileSavePath);
            BufferedOutputStream outputStream = FileUtil.getOutputStream(tempFile);
            ExcelWriter excelWriter = EasyExcel.write(outputStream).build();
            List<AdminProductActivityWarehouseExportDTO> ad = productActiveService.selectProductActivityWarehouseExport(bo);
            WriteSheet writeSheet = EasyExcel.writerSheet(0, "超管锁货仓库导出")
                                             .head(AdminProductActivityWarehouseExportDTO.class)
                                             .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                                             .registerConverter(new ExcelBigNumberConvert())
                                             .build();
            excelWriter.write(ad, writeSheet);
            excelWriter.finish();
            IoUtil.close(outputStream);
            return tempFile;
        });
        return R.ok();
    }


    /**
     * 供应商锁货详情导出
     */
    @PostMapping("/supplierProductActivityDetailsExport")
    public R supplierProductActivityDetailsExport(@RequestBody ProductActivitySearchDTO bo, HttpServletResponse response) {
        String tenantId = LoginHelper.getTenantId();
        if (StrUtil.isEmpty(tenantId)) {
            throw new RuntimeException("请登陆后使用");
        }
        if (!TenantType.Supplier.name().equals(LoginHelper.getTenantType())){
            throw new RuntimeException("供应商锁货活动导出只支持供应商操作");
        }
        bo.setTenantId(tenantId);
        bo.setTenantType(LoginHelper.getTenantType());
        String fileName = StrUtil.format(FileNameConstants.SUPPLIER_PRODUCT_ACTIVITY_DETAILS_EXPORT, DateUtil.format(new Date(), "yyMMdd-HHmmssSS"));
        DownloadRecordV2Util.generate(fileName, DownloadTypePlusEnum.SupplierProductActivityDetailsExport, tempFileSavePath -> {
            File tempFile = new File(tempFileSavePath);
            BufferedOutputStream outputStream = FileUtil.getOutputStream(tempFile);
            ExcelWriter excelWriter = EasyExcel.write(outputStream).build();
            List<AdminProductActivityDetailsExportDTO> page = productActiveService.selectAdminProductActivityDetailsExport(bo);
            List<SupplierProductActivityDetailsExportDTO> dtos = BeanUtil.copyToList(page, SupplierProductActivityDetailsExportDTO.class);

            //处理出单数量
            dtos.forEach(s->{
                s.setQuantityLocked(s.getSupplierPickupQuantityLocked()+s.getSupplierDropShippingQuantityLocked());
                s.setActivityOrderNum(s.getActivityOrderNum());
                s.setSite(s.getSite()+"/"+s.getCurrencySymbol());
                //判断分销活动ID是否存在
                if (StrUtil.isNotEmpty(s.getDistributorActivityCode())){
                    s.setDistributorQuantityLocked(s.getDistributorPickupQuantityLocked()+s.getDistributorDropShippingQuantityLocked());
                }
            });
           WriteSheet writeSheet = EasyExcel.writerSheet(0, "供应商锁货详情导出")
                                 .head(SupplierProductActivityDetailsExportDTO.class)
                                 .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                                 .registerConverter(new ExcelBigNumberConvert())
                                 .build();
            excelWriter.write(dtos, writeSheet);
            excelWriter.finish();
            IoUtil.close(outputStream);
            return tempFile;
        });
        return R.ok();
    }
    /**
     * Admin锁货详情导出
     */
    @PostMapping("/adminProductActivityDetailsExport")
    public R adminProductActivityDetailsExport(@RequestBody ProductActivitySearchDTO bo, HttpServletResponse response) {
        String tenantId = LoginHelper.getTenantId();
        if (StrUtil.isEmpty(tenantId)) {
            throw new RuntimeException("请登陆后使用");
        }
        if (!TenantType.Manager.name().equals(LoginHelper.getTenantType())){
            throw new RuntimeException("超管锁货活动详情导出只支持超管操作");
        }
        bo.setTenantId(tenantId);
        bo.setTenantType(LoginHelper.getTenantType());
        String fileName = StrUtil.format(FileNameConstants.ADMIN_PRODUCT_ACTIVITY_DETAILS_EXPORT, DateUtil.format(new Date(), "yyMMdd-HHmmssSS"));
        DownloadRecordV2Util.generate(fileName, DownloadTypePlusEnum.AdminProductActivityDetailsExport, tempFileSavePath -> {
            File tempFile = new File(tempFileSavePath);
            BufferedOutputStream outputStream = FileUtil.getOutputStream(tempFile);
            ExcelWriter excelWriter = EasyExcel.write(outputStream).build();
            List<AdminProductActivityDetailsExportDTO> page = productActiveService.selectAdminProductActivityDetailsExport(bo);

            //处理出单数量
            page.forEach(s->{
                //供应商出单只能用供应商ID查询
               // s.setQuantityLocked(s.getSupplierPickupQuantityLocked()+s.getSupplierDropShippingQuantityLocked());
                s.setSite(s.getSite()+"/"+s.getCurrencySymbol());
            });
            WriteSheet writeSheet = EasyExcel.writerSheet(0, "超管锁货详情导出")
                                             .head(AdminProductActivityDetailsExportDTO.class)
                                             .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                                             .registerConverter(new ExcelBigNumberConvert())
                                             .build();
            excelWriter.write(page, writeSheet);
            excelWriter.finish();
            IoUtil.close(outputStream);
            return tempFile;
        });
        return R.ok();
    }


    /**
     * 分销商锁货详情导出
     */
    @PostMapping("/distributorProductActivityDetailsExport")
    public R distributorProductActivityDetailsExport(@RequestBody ProductActivitySearchDTO bo, HttpServletResponse response) {
        String tenantId = LoginHelper.getTenantId();
        if (StrUtil.isEmpty(tenantId)) {
            throw new RuntimeException("请登陆后使用");
        }
        if (!TenantType.Distributor.name().equals(LoginHelper.getTenantType())){
            throw new RuntimeException("分销商锁货活动详情导出只支持分销商操作");
        }
        bo.setTenantId(tenantId);
        bo.setTenantType(LoginHelper.getTenantType());
        String fileName = StrUtil.format(FileNameConstants.DISTRIBUTOR_PRODUCT_ACTIVITY_DETAILS_EXPORT, DateUtil.format(new Date(), "yyMMdd-HHmmssSS"));
        DownloadRecordV2Util.generate(fileName, DownloadTypePlusEnum.DistributorProductActivityDetailsExport, tempFileSavePath -> {
            File tempFile = new File(tempFileSavePath);
            BufferedOutputStream outputStream = FileUtil.getOutputStream(tempFile);
            ExcelWriter excelWriter = EasyExcel.write(outputStream).build();
            List<DistributorProductActivityDetailsExportDTO> page = productActiveService.selectDistributorProductActivityDetailsExport(bo);

            WriteSheet writeSheet = EasyExcel.writerSheet(0, "分销商锁货详情导出")
                                             .head(DistributorProductActivityDetailsExportDTO.class)
                                             .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                                             .registerConverter(new ExcelBigNumberConvert())
                                             .build();
            excelWriter.write(page, writeSheet);
            excelWriter.finish();
            IoUtil.close(outputStream);
            return tempFile;
        });
        return R.ok();
    }
    /**
     * 分销商锁货活动导出
     */
    @PostMapping("/distributorProductActivityListExport")
    public R distributorProductActivityListExport(@RequestBody ProductActivitySearchDTO bo, HttpServletResponse response) {
        String tenantId = LoginHelper.getTenantId();
        if (StrUtil.isEmpty(tenantId)) {
            throw new RuntimeException("请登陆后使用");
        }
        if (!TenantType.Distributor.name().equals(LoginHelper.getTenantType())){
            throw new RuntimeException("分销商锁货活动详情导出只支持分销商操作");
        }
        bo.setTenantId(tenantId);
        bo.setTenantType(LoginHelper.getTenantType());
        String fileName = StrUtil.format(FileNameConstants.DISTRIBUTOR_PRODUCT_ACTIVITY_LIST_EXPORT, DateUtil.format(new Date(), "yyMMdd-HHmmssSS"));
        DownloadRecordV2Util.generate(fileName, DownloadTypePlusEnum.DistributorProductActivityListExport, tempFileSavePath -> {
            File tempFile = new File(tempFileSavePath);
            BufferedOutputStream outputStream = FileUtil.getOutputStream(tempFile);
            ExcelWriter excelWriter = EasyExcel.write(outputStream).build();
            List<DistributorProductActivityListExportDTO> page = productActiveService.selectDistributorProductActivityListExport(bo);
            page.forEach(s->{
                s.setSite(s.getSite()+"/"+s.getCurrencySymbol());
                s.setQuantityLocked(s.getPickupQuantityLocked()+s.getDropShippingQuantityLocked());
                s.setQuantityRemaining(s.getQuantityLocked()-s.getPickupLockedUsed()-s.getDropShippingLockedUsed());
                s.setRemainingDay(DateUtil.betweenDay(s.getActiveStartTime(), s.getActiveEndTime(), false));
            });
            WriteSheet writeSheet = EasyExcel.writerSheet(0, "分销商锁货活动列表导出")
                                             .head(DistributorProductActivityListExportDTO.class)
                                             .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                                             .registerConverter(new ExcelBigNumberConvert())
                                             .build();
            excelWriter.write(page, writeSheet);
            excelWriter.finish();
            IoUtil.close(outputStream);
            return tempFile;
        });
        return R.ok();
    }


    /**
     * admin锁货审核
     */
    @Transactional
    @PostMapping("/productActivityReview")
    public R productActivityReview( String activityCode, String reviewState, String reviewOpinion) {
        String tenantId = LoginHelper.getTenantId();
        if (StrUtil.isEmpty(tenantId)) {
            throw new RuntimeException("请登陆后使用");
        }
        if (!TenantType.Manager.name().equals(LoginHelper.getTenantType())){
            throw new RuntimeException("活动审核只支持超管操作");
        }
        SupplierProductActivity active = supplierProductActivityService.getByActivityCode(activityCode);
        if (ObjectUtil.isEmpty(active)){
            throw new RuntimeException("活动不存在:"+ activityCode);
        }
        if (!ProductActivityStateEnum.PendingReview.name().equals(active.getActivityState())){
            throw new RuntimeException("活动状态不是待审核状态");
        }
        //驳回
        if (ObjectUtil.equals(ProductActivityReviewState.Rejected.name(),reviewState)){
            if (StrUtil.isEmpty(reviewOpinion)){
                throw new RuntimeException("拒绝原因不能为空");
            }
            if (reviewOpinion.length()>50){
                throw new RuntimeException("拒绝原因不能超过50个字符");
            }
            //更新活动状态
            active.setActivityState(ProductActivityStateEnum.NotApproved.name());
            active.setReviewState(ProductActivityReviewState.Rejected.name());
            active.setReviewOpinion(reviewOpinion);
            supplierProductActivityService.updateById(active);
            //插入审核记录 拒绝
            SupplierProductActivityReviewRecord reviewRecord = new SupplierProductActivityReviewRecord();
            reviewRecord.setSupplierActivityId(active.getId());
            reviewRecord.setActivityOriginState(active.getActivityState());
            reviewRecord.setReviewManager(tenantId);
            reviewRecord.setReviewTime(new Date());
            reviewRecord.setReviewOpinion(reviewOpinion);
            reviewRecord.setReviewState(reviewState);
            reviewRecordService.save(reviewRecord);
        }else if (ObjectUtil.equals(ProductActivityReviewState.Accepted.name(),reviewState)){
            //先将活动状态更新为审核中
            active.setActivityState(ProductActivityStateEnum.UnderReview.name());
            supplierProductActivityService.updateById(active);
            //异步处理
            ThreadUtil.execAsync(()->{
                //先调用erp锁库存接口
                Boolean aBoolean = productActiveService.erpProductLockInventoryReserved(activityCode);
                //如果成功
                if (aBoolean){
                    //更新活动状态
                    active.setActivityState(ProductActivityStateEnum.Published.name());
                    active.setReviewState(ProductActivityReviewState.Accepted.name());
                    active.setExceptionCode(ProductActivityExceptionEnum.NOT_EXCEPTION.getCode());
                    supplierProductActivityService.updateById(active);
                    //插入审核记录 通过
                    SupplierProductActivityReviewRecord reviewRecord = new SupplierProductActivityReviewRecord();
                    reviewRecord.setSupplierActivityId(active.getId());
                    reviewRecord.setActivityOriginState(active.getActivityState());
                    reviewRecord.setReviewManager(tenantId);
                    reviewRecord.setReviewTime(new Date());
                    reviewRecord.setReviewState(reviewState);
                    reviewRecordService.save(reviewRecord);
                }else {
                    //未通过审核
                    active.setActivityState(ProductActivityStateEnum.NotApproved.name());
                    active.setReviewState(ProductActivityReviewState.Rejected.name());
                    active.setReviewOpinion(ProductReviewOpinion.reason5.getZhValue());
                    active.setExceptionCode(ProductActivityExceptionEnum.ERP_LOCK_EXCEPTION.getCode());
                    supplierProductActivityService.updateById(active);
                    //插入日志
                    SupplierProductActivityReviewRecord reviewRecord = new SupplierProductActivityReviewRecord();
                    reviewRecord.setSupplierActivityId(active.getId());
                    reviewRecord.setActivityOriginState(active.getActivityState());
                    reviewRecord.setReviewManager(tenantId);
                    reviewRecord.setReviewTime(new Date());
                    reviewRecord.setReviewOpinion("ERP锁库存接口调用失败");
                    reviewRecord.setReviewState(reviewState);
                    reviewRecordService.save(reviewRecord);
                }
            });
        }else {
            return R.fail("错误的审核类型:" +reviewState);
        }
        return R.ok();
    }

    /**
     * 供应商添加活动
     */
    @PostMapping("/addSupplierProductActive")
    @Transactional
    public R<Void> addSupplierProductActive(@Valid @RequestBody SupplierProductActivityAddDTO dto) {
        String tenantId = LoginHelper.getTenantId();
        if (StrUtil.isEmpty(tenantId)) {
            throw new RuntimeException("请登陆后使用");
        }
        if (!TenantType.Supplier.name().equals(LoginHelper.getTenantType())){
            throw new RuntimeException("活动审核只支持供应商操作");
        }
        productActiveService.addSupplierProductActive(dto);
        return R.ok();
    }

    /**
     * 供应商编辑活动
     * @param dto 活动编辑DTO
     * @return R
     */
    @PostMapping("/updateSupplierProductActive")
    @Transactional
    public R<Void> updateSupplierProductActive(@Valid @RequestBody SupplierProductActivityUpdateDTO dto) {
        String tenantId = LoginHelper.getTenantId();
        if (StrUtil.isEmpty(tenantId)) {
            throw new RuntimeException("请登陆后使用");
        }
        if (!TenantType.Supplier.name().equals(LoginHelper.getTenantType())){
            throw new RuntimeException("活动审核只支持供应商操作");
        }
        productActiveService.updateSupplierProductActive(dto);
        return R.ok();
    }



    /**
     * 添加分销商活动(押金支付成功后调用)
     * @param dto 供应商活动编码
     * @return R
     */
    @PostMapping("/addDistributorProductActive")
    @Transactional
    public R<Void> addDistributorProductActive(@Valid @RequestBody DistributorProductActivityAddDTO  dto) {
        String tenantId = LoginHelper.getTenantId();
        if (StrUtil.isEmpty(tenantId)) {
            throw new RuntimeException("请登陆后使用");
        }
        if (!TenantType.Distributor.name().equals(LoginHelper.getTenantType())){
            throw new RuntimeException("分销添加活动只支持分销商操作");
        }
        productActiveService.addDistributorProductActive(dto);
        return R.ok();
    }

    /**
     * 分销商活动押金支付
     * @param dto 供应商活动编码
     * @return R
     */
    @PostMapping("/distributorActiveDepositPay")
    public R distributorActiveDepositPay(@Valid @RequestBody DistributorProductActivityAddDTO  dto) {
        try {
            String tenantId = LoginHelper.getTenantId();
            if (StrUtil.isEmpty(tenantId)) {
                throw new RuntimeException("请登陆后使用");
            }
            if (!TenantType.Distributor.name().equals(LoginHelper.getTenantType())){
                throw new RuntimeException("分销添加活动只支持分销商操作");
            }
            productActiveService.distributorActiveDepositPay(dto);
            return R.ok();
        }catch (Exception e){
            return R.fail(e.getMessage());
        }
    }
    /**
     * 分销商活动取消
     * @param distributorProductActiveCode 分销商活动编码
     * @return R
     */
    @PostMapping("/cancelDistributorProductActive")
    @Transactional
    public R<Void> cancelDistributorProductActive(@RequestParam String  distributorProductActiveCode) {
        String tenantId = LoginHelper.getTenantId();
        if (StrUtil.isEmpty(tenantId)) {
            throw new RuntimeException("请登陆后使用");
        }
        if (!TenantType.Distributor.name().equals(LoginHelper.getTenantType())){
            throw new RuntimeException("分销商活动取消只支持分销商操作");
        }
        productActiveService.cancelDistributorProductActive(distributorProductActiveCode);
        return R.ok();
    }

    /**
     * 供应商活动取消
     * @param supplierProductActiveCode 供应商活动编码
     * @return R
     */
    @PostMapping("/cancelSupplierProductActive")
    @Transactional
    public R<Void> cancelSupplierProductActive(@RequestParam String  supplierProductActiveCode) {
        String tenantId = LoginHelper.getTenantId();
        if (StrUtil.isEmpty(tenantId)) {
            throw new RuntimeException("请登陆后使用");
        }
        if (!TenantType.Supplier.name().equals(LoginHelper.getTenantType())){
            throw new RuntimeException("活动审核只支持供应商操作");
        }
        productActiveService.cancelSupplierProductActive(supplierProductActiveCode);
        return R.ok();
    }
    /**
     * 分销商活动到期自动取消(测试专用)
     * @param distributorProductActiveCode 分销商活动编码
     * @param time 结束时间
     * @return R
     */
    @PostMapping("/sendDistributorActivityExpire")
    public R<Void> sendDistributorActivityExpire(String  distributorProductActiveCode, String time) {
        String tenantId = LoginHelper.getTenantId();
        if (StrUtil.isEmpty(tenantId)) {
            throw new RuntimeException("请登陆后使用");
        }
        if (!TenantType.Distributor.name().equals(LoginHelper.getTenantType())){
            throw new RuntimeException("分销添加活动只支持分销商操作");
        }
        productActiveService.sendDistributorActivityExpire(distributorProductActiveCode,time);
        return R.ok(DateUtil.formatDateTime(new Date()));
    }

    /**
     *  供应商添加活动时获取商品仓库列表
     * @param productSkuCode 商品编码
     * @param site 商品编码
     * @return R
     */
    @GetMapping("/getSupplierSkuStockByProductSkuCode")
    public R<List<SkuStock>> getSupplierSkuStockByProductSkuCode(String productSkuCode,String  site) {
        String tenantId = LoginHelper.getTenantId();
        if (StrUtil.isEmpty(tenantId)) {
            throw new RuntimeException("请登陆后使用");
        }
        if (!TenantType.Supplier.name().equals(LoginHelper.getTenantType())){
            throw new RuntimeException("只支持分销商操作");
        }
        List<SkuStock> stock = productActiveService.getSupplierSkuStockByProductSkuCode(productSkuCode, site);
        return R.ok(stock);
    }

    /**
     *  供应商活动提交审核
     * @param suppLierActiveCode 供应商活动编码
     * @return R
     */
    @PostMapping("/submitForReview")
    @Transactional
    public R submitForReview(@RequestParam String  suppLierActiveCode) {
        String tenantId = LoginHelper.getTenantId();
        if (StrUtil.isEmpty(tenantId)) {
            throw new RuntimeException("请登陆后使用");
        }
        if (!TenantType.Supplier.name().equals(LoginHelper.getTenantType())){
            throw new RuntimeException("只支持供应商操作");
        }
        productActiveService.submitForReview(suppLierActiveCode);
        return R.ok();
    }

    /**
     *  拉库存测试验证
     * @param productSkuCode 商品编码
     * @param json 库存json
     * @return R
     */
    @SaIgnore
    @PostMapping("/pullProductSkuStockTest")
    @Transactional
    public R pullProductSkuStock( String  productSkuCode,String json) {
       productActiveService.pullProductSkuStockTest(productSkuCode,json);
        return R.ok();
    }

    /**
     *  供应商添加活动商品列表
     * @return R
     */
    @SaIgnore
    @GetMapping("/getActivityProductSkuList")
    public TableDataInfo<ActivityProductSkuDTO> getActivityProductSkuList(String productSkuCode,
                                                                          String productSku,
                                                                          String productName,
                                                                          String site,
                                                                          Integer pageSize,
                                                                          Integer pageNum) {
        if (StrUtil.isEmpty(site)){
            throw new RuntimeException("请传入站点");
        }
        if (ObjectUtil.isNull(pageSize)){
            throw new RuntimeException("请传入分页大小");
        }
        if (ObjectUtil.isNull(pageNum)){
            throw new RuntimeException("请传入页面数");
        }
        String tenantId = LoginHelper.getTenantId();
        if (StrUtil.isEmpty(tenantId)) {
            throw new RuntimeException("请登陆后使用");
        }
        if (!TenantType.Supplier.name().equals(LoginHelper.getTenantType())){
            throw new RuntimeException("只支持供应商操作");
        }
        // MyBatis-Plus分页从1开始，确保pageNum至少为1
        Page<ActivityProductSkuDTO> page=new Page<>(pageNum,pageSize);
        return productActiveService.getActivityProductSkuList(productSkuCode,productSku,productName,site,page);
    }


}
