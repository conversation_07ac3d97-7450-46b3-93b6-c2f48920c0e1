package com.zsmall.product.biz.support;

import com.zsmall.bma.open.member.service.IMemberDiscountV2Service;
import com.zsmall.bma.open.member.service.MemberRuleRelationV2Service;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;

/**
 * lty notes 停止维护 查找MemberV2Support
 *
 * <AUTHOR> Theo
 * @create 2024/8/8 16:49
 */
@Slf4j
@Component
@RequiredArgsConstructor
@Deprecated
public class MemberSupport {

    private final MemberRuleRelationV2Service iMemberRuleRelationService;
    private final IMemberDiscountV2Service memberDiscountService;
    public BigDecimal getDiscountFactor(Long dictCode) {
        // 1. 获取会员折扣
        return memberDiscountService.getMemberDiscountByDictCode(dictCode);
    }
}
