package com.zsmall.product.biz.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.ZipUtil;
import cn.hutool.http.HtmlUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hengjian.common.core.constant.GlobalConstants;
import com.hengjian.common.core.domain.R;
import com.hengjian.common.core.domain.model.LoginUser;
import com.hengjian.common.core.enums.TenantType;
import com.hengjian.common.core.utils.ServletUtils;
import com.hengjian.common.excel.utils.ExcelUtil;
import com.hengjian.common.mybatis.core.page.PageQuery;
import com.hengjian.common.mybatis.core.page.TableDataInfo;
import com.hengjian.common.redis.utils.RedisUtils;
import com.hengjian.common.satoken.utils.LoginHelper;
import com.hengjian.common.tenant.helper.TenantHelper;
import com.hengjian.extend.event.OSSObtainEvent;
import com.hengjian.system.service.ISysTenantService;
import com.zsmall.bma.open.member.service.RuleLevelProductPriceV2Service;
import com.zsmall.common.constant.FileNameConstants;
import com.zsmall.common.enums.common.AttachmentTypeEnum;
import com.zsmall.common.enums.downloadRecord.DownloadTypePlusEnum;
import com.zsmall.common.enums.product.AttributeTypeEnum;
import com.zsmall.common.enums.product.SkuShelfStateEnum;
import com.zsmall.common.enums.product.SupportedLogisticsEnum;
import com.zsmall.common.enums.productSku.DropShippingStockAvailableEnum;
import com.zsmall.common.properties.FileProperties;
import com.zsmall.common.util.DecimalUtil;
import com.zsmall.extend.utils.ZSMallSystemEventUtils;
import com.zsmall.product.biz.service.TenantFavoritesService;
import com.zsmall.product.entity.domain.*;
import com.zsmall.product.entity.domain.bo.tenantFavorites.TenantFavoritesDeleteBo;
import com.zsmall.product.entity.domain.bo.tenantFavorites.TenantFavoritesExportBo;
import com.zsmall.product.entity.domain.bo.tenantFavorites.TenantFavoritesListBo;
import com.zsmall.product.entity.domain.vo.ProductSkuAttachmentVo;
import com.zsmall.product.entity.domain.vo.ProductSkuStockVo;
import com.zsmall.product.entity.domain.vo.product.ProductSkuFavoritesSimpleVo;
import com.zsmall.product.entity.domain.vo.product.ProductSkuPackageVo;
import com.zsmall.product.entity.domain.vo.product.ProductSkuStockPackageVo;
import com.zsmall.product.entity.domain.vo.tenantFavorites.TenantFavoritesExport;
import com.zsmall.product.entity.domain.vo.tenantFavorites.TenantFavoritesListVo;
import com.zsmall.product.entity.iservice.*;
import com.zsmall.system.entity.iservice.IShippingReturnsService;
import com.zsmall.system.entity.util.DownloadRecordUtil;
import java.io.BufferedOutputStream;
import java.io.File;
import java.util.*;
import java.util.stream.Collectors;
import javax.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 租户收藏夹-业务实现
 *
 * <AUTHOR>
 * @date 2023/8/23
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TenantFavoritesServiceImpl implements TenantFavoritesService {

    private final ITenantFavoritesService iTenantFavoritesService;
    private final IProductService iProductService;
    private final IProductAttachmentService iProductAttachmentService;
    private final IProductSkuService iProductSkuService;
    private final IProductSkuDetailService iProductSkuDetailService;
    private final IProductSkuPriceService iProductSkuPriceService;
    private final IProductCategoryService iProductCategoryService;
    private final IProductAttributeService iProductAttributeService;
    private final IProductSkuAttachmentService iProductSkuAttachmentService;
    private final IProductSkuStockService iProductSkuStockService;
    private final IShippingReturnsService iShippingReturnsService;
    private final RuleLevelProductPriceV2Service ruleLevelProductPriceService;
    private final FileProperties fileProperties;
    private final ISysTenantService sysTenantService;

    /**
     * 分页查询租户收藏夹
     *
     * @param bo
     * @param pageQuery
     */
    @Override
    public TableDataInfo<TenantFavoritesListVo> queryPage(TenantFavoritesListBo bo, PageQuery pageQuery) {
        if (ObjectUtil.isNull(bo.getTenantId())){
            bo.setTenantId(LoginHelper.getTenantId());
        }
        Page<TenantFavorites> page = TenantHelper.ignore(() -> iTenantFavoritesService.getBaseMapper()
                                                                                        .selectTenantFavoritesListVoPage(pageQuery.build(), bo));
        List<TenantFavorites> tenantFavorites = page.getRecords();
        if (CollUtil.isEmpty(tenantFavorites)){
            return TableDataInfo.build();
        }
        Set<String> productSkuCodeSet = tenantFavorites.stream()
                                             .map(TenantFavorites::getProductSkuCode)
                                             .collect(Collectors.toSet());

        LambdaQueryWrapper<ProductSku> o = new LambdaQueryWrapper<>();
        o.in(ProductSku::getProductSkuCode,productSkuCodeSet);
        List<ProductSku> productSkus = TenantHelper.ignore(()->iProductSkuService.getBaseMapper().selectList(o));
        Map<String, ProductSku> productSkuMap = productSkus.stream()
                                                     .collect(Collectors.toMap(ProductSku::getProductSkuCode, productSku -> productSku));

        List<Long> productSkuIdList = productSkus.stream().map(ProductSku::getId).distinct().collect(Collectors.toList());
        LambdaQueryWrapper<ProductSkuDetail> productSkuDetailLQW = new LambdaQueryWrapper<>();
        productSkuDetailLQW.in(ProductSkuDetail::getProductSkuId,productSkuIdList);
        List<ProductSkuDetail> productSkuDetailList = TenantHelper.ignore(()->iProductSkuDetailService.getBaseMapper().selectList(productSkuDetailLQW));
        Map<Long, ProductSkuDetail> productSkuDetailMap = productSkuDetailList.stream()
                                                           .collect(Collectors.toMap(ProductSkuDetail::getProductSkuId, productSkuDetail -> productSkuDetail));


        Set<Long> productCodeSet = tenantFavorites.stream()
                                                       .map(TenantFavorites::getProductId)
                                                       .collect(Collectors.toSet());
        LambdaQueryWrapper<Product> oo = new LambdaQueryWrapper<>();
        oo.in(Product::getId,productCodeSet);
        List<Product> products =TenantHelper.ignore(()->iProductService.getBaseMapper().selectList(oo)) ;
        Map<Long, Product> productMap = products.stream().collect(Collectors.toMap(Product::getId, product -> product));

        //查询主图信息
        List<ProductSkuAttachmentVo> productSkuAttachmentVos =TenantHelper.ignore(()->iProductSkuAttachmentService.getBaseMapper().queryFirstImageByProductSkuCodes(new ArrayList<>(productSkuCodeSet)));
        Map<Long, ProductSkuAttachmentVo> productSkuAttachmentMap = productSkuAttachmentVos.stream()
                                                                                           .collect(Collectors.toMap(
                                                                                               ProductSkuAttachmentVo::getProductSkuId, // keyMapper
                                                                                               productSkuAttachmentVo -> productSkuAttachmentVo, // valueMapper
                                                                                               (existing, replacement) -> existing
                                                                                           ));
        //查询站点商品价格
        Map<String, ProductSkuPrice> productSkuSitePriceMap = iProductSkuPriceService.getProductSkuSitePriceMapById(productSkuCodeSet);
        Map<String, RuleLevelProductPrice> ruleLevelProductPriceSitePriceMap = iProductSkuPriceService.getRuleLevelProductPriceSitePriceMapById(productSkuCodeSet,bo.getTenantId());
        //构建返回信息
        List<TenantFavoritesListVo> objects = new ArrayList<>();
        for (TenantFavorites tenantFavorite : tenantFavorites) {
            TenantFavoritesListVo tenantFavoritesListVo=new TenantFavoritesListVo();
            tenantFavoritesListVo.setFavoritesId(tenantFavorite.getId());
            tenantFavoritesListVo.setSite(tenantFavorite.getSite());
            tenantFavoritesListVo.setCurrency(tenantFavorite.getCurrency());
            ProductSkuAttachmentVo productSkuAttachmentVo = productSkuAttachmentMap.get(tenantFavorite.getProductSkuId());
            if (ObjectUtil.isNotNull(productSkuAttachmentVo)){
                tenantFavoritesListVo.setProductSkuImage(productSkuAttachmentVo.getAttachmentShowUrl());
            }
            //查询商品信息
            Product product = productMap.get(tenantFavorite.getProductId());
            if (ObjectUtil.isNotNull(product)){
                tenantFavoritesListVo.setProductName(product.getName());
                tenantFavoritesListVo.setProductCode(product.getProductCode());
            }
            ProductSku productSku = productSkuMap.get(tenantFavorite.getProductSkuCode());
            tenantFavoritesListVo.setSku(productSku.getSku());
            tenantFavoritesListVo.setProductSkuCode(productSku.getProductSkuCode());
            tenantFavoritesListVo.setEnShelfState(productSku.getShelfState().name());
            if (ObjectUtil.equal(productSku.getShelfState().name(), SkuShelfStateEnum.OnShelf.name())){
                tenantFavoritesListVo.setCnShelfState("已上架");
            } else if (ObjectUtil.equal(productSku.getShelfState().name(), SkuShelfStateEnum.OffShelf.name())){
                tenantFavoritesListVo.setCnShelfState("已下架");
            } else if (ObjectUtil.equal(productSku.getShelfState().name(), SkuShelfStateEnum.ForcedOffShelf.name())){
                tenantFavoritesListVo.setCnShelfState("未审核");
            }
            // 产品尺寸信息
            ProductSkuDetail productSkuDetail = productSkuDetailMap.get(tenantFavorite.getProductSkuId());
            tenantFavoritesListVo.setProductSize(productSkuDetail.getLength()+"x"+productSkuDetail.getWidth()+"x"+productSkuDetail.getHeight()+" "+productSkuDetail.getLengthUnit().name());
            tenantFavoritesListVo.setProductWeight(productSkuDetail.getWeight()+" "+productSkuDetail.getWeightUnit().name());
            tenantFavoritesListVo.setPackageSize(productSkuDetail.getPackLength()+"x"+productSkuDetail.getPackWidth()+"x"+productSkuDetail.getPackHeight()+" "+productSkuDetail.getPackLengthUnit().name());
            tenantFavoritesListVo.setPackageWeight(productSkuDetail.getPackWeight()+" "+productSkuDetail.getPackWeightUnit().name());
            if (ObjectUtil.isNotNull(productSkuDetail.getDescription())){
                tenantFavoritesListVo.setDescription(HtmlUtil.cleanHtmlTag(productSkuDetail.getDescription()));
            }
            String productType = product.getProductType().name();
            String type = null ;
            if("NormalProduct".equals(productType)){
                type = "普通商品";
            }
            if("WholesaleProduct".equals(productType)){
                type = "批发商品";
            }
            tenantFavoritesListVo.setEnProductType(productType);
            tenantFavoritesListVo.setCnProductType(type);
            String supportedLogistics = product.getSupportedLogistics().name();
            String logistics = null;
            if("All".equals(supportedLogistics)){
                logistics = "一件代发 & 自提";
            }
            if("PickUpOnly".equals(supportedLogistics)){
                logistics = "自提";
            }
            if("DropShippingOnly".equals(supportedLogistics)){
                logistics = "一件代发";
            }
            tenantFavoritesListVo.setEnSupportedLogistics(supportedLogistics);
            tenantFavoritesListVo.setCnSupportedLogistics(logistics);
          //  log.info("tenantFavorite.getSite():{}",tenantFavorite.getSite());
            RuleLevelProductPrice memberPrice = ruleLevelProductPriceSitePriceMap.get(productSku.getId() + "-" + tenantFavorite.getSite());
            if (ObjectUtil.isNotNull(memberPrice)){
                tenantFavoritesListVo.setPickUpPrice(String.valueOf(memberPrice.getPlatformPickUpPrice()));
                tenantFavoritesListVo.setDropShippingPrice(String.valueOf(memberPrice.getPlatformDropShippingPrice()));
            }else {
                ProductSkuPrice productSkuPrice = productSkuSitePriceMap.get(productSku.getId()+"-"+tenantFavorite.getSite());
                if (ObjectUtil.isNotNull(productSkuPrice)){
                    tenantFavoritesListVo.setPickUpPrice(String.valueOf(productSkuPrice.getPlatformPickUpPrice()));
                    tenantFavoritesListVo.setDropShippingPrice(String.valueOf(productSkuPrice.getPlatformDropShippingPrice()));
                }
            }
            //查询分仓库存数据
            List<HashMap<String, Object>> stockWithWarehouseSystemCode = TenantHelper.ignore(() -> iProductSkuPriceService.getStockWithWarehouseSystemCodeByResultMap(productSku.getProductSkuCode(),productSku.getTenantId()));
            tenantFavoritesListVo.setSkuStockList(stockWithWarehouseSystemCode);
            int totalStockAvailable = stockWithWarehouseSystemCode.stream()
                                                                  .filter(stockMap -> stockMap.containsKey("stockAvailable"))
                                                                  .mapToInt(stockMap -> (Integer) stockMap.get("stockAvailable"))
                                                                  .sum();
            Integer totalProductStock = 0;
            for (HashMap<String, Object> stockMap : stockWithWarehouseSystemCode) {
                if (stockMap.containsKey("dropShippingStockAvailable") && null != stockMap.get("dropShippingStockAvailable")
                    && stockMap.get("dropShippingStockAvailable").equals(DropShippingStockAvailableEnum.NOT_SINGLE.getCode())){
                    totalProductStock += (Integer) stockMap.get("stockAvailable");
                    stockMap.put("proxyStock",(Integer) stockMap.get("stockAvailable"));
                }else {
                    stockMap.put("proxyStock",0);
                }
            }
            tenantFavoritesListVo.setStockTotal(totalStockAvailable);
            tenantFavoritesListVo.setProxyStockTotal(totalProductStock);
            objects.add(tenantFavoritesListVo);
        }
        //手动分页
        return TableDataInfo.build(objects,page.getTotal());
    }

    /**
     * 删除收藏夹项目
     *
     * @param bo
     */
    @Override
    public R<Void> deleteFavorites(TenantFavoritesDeleteBo bo) {
        LoginHelper.getLoginUser(TenantType.Distributor);
        List<String> favoritesIds = bo.getFavoritesIds();
        if (CollUtil.isNotEmpty(favoritesIds)) {
            for (String favoritesId : favoritesIds) {
                TenantFavorites byId = iTenantFavoritesService.getById(favoritesId);
                iTenantFavoritesService.deleteByIds(favoritesIds);
                RedisUtils.deleteKeys(GlobalConstants.FAVORITES_SPU+LoginHelper.getTenantId()+byId.getProductCode());
            }
        }
        return R.ok();
    }

    /**
     * 导出收藏夹商品数据
     *
     * @param bo
     */
    @Override
    public R<Void> exportFavorites(TenantFavoritesExportBo bo) {
        LoginHelper.getLoginUser(TenantType.Distributor);
        Locale headerLocale = ServletUtils.getHeaderLocale();

        String fileName = StrUtil.format(FileNameConstants.FAVORITES_PRODUCT, DateUtil.format(new Date(), "yyMMdd-HHmmssSS"));
        DownloadRecordUtil.generate(fileName, DownloadTypePlusEnum.FavoritesProduct, tempFileSavePath -> {

            List<String> favoritesIds = bo.getFavoritesIds();
            List<ProductSkuFavoritesSimpleVo> favoritesSimpleVoList = new ArrayList<>();
            if (CollUtil.isNotEmpty(favoritesIds)) {
                favoritesSimpleVoList.addAll(iProductSkuService.queryByFavoritesIdsForFavorites(favoritesIds));
            }

            File tempFile = new File(tempFileSavePath);
            BufferedOutputStream outputStream = FileUtil.getOutputStream(tempFile);
            ExcelUtil.exportExcelWithLocale(favoritesSimpleVoList, "FavoritesProduct", ProductSkuFavoritesSimpleVo.class, true, outputStream, headerLocale);
            IoUtil.close(outputStream);
            return tempFile;
        });
        return R.ok();
    }

    /**
     * 导出收藏夹商品资料压缩包
     *
     * @param bo
     */
    @Override
    public R<Void> exportFavoritesZip(TenantFavoritesExportBo bo) {
        LoginUser loginUser = LoginHelper.getLoginUser(TenantType.Distributor);
        Locale headerLocale = ServletUtils.getHeaderLocale();
        String dateFormat = DateUtil.format(new Date(), "yyMMdd-HHmmssSS");

        // 检查是否完善信息
        ZSMallSystemEventUtils.checkDisInfoPerfection(loginUser.getTenantId());

        String fileName = StrUtil.format(FileNameConstants.FAVORITES_PRODUCT_PACKAGE, dateFormat);
        DownloadRecordUtil.generate(fileName, DownloadTypePlusEnum.FavoritesProductPackage, tempFileSavePath -> {
            List<String> favoritesIds = bo.getFavoritesIds();
            List<Long> productIds = iTenantFavoritesService.queryProductIdsByFavoritesIds(favoritesIds);

            // SPU压缩包暂存目录
            // 示例 /www/zs-mall/temp/ProductZip-yyMMdd-HH:mm:ss.SSSS
            String zipSavePath = StrUtil.builder(fileProperties.getTempSavePath(), File.separator, "ProductZip-", dateFormat).toString();
            log.info("【导出收藏夹商品资料压缩包】SPU压缩包暂存目录 => {}", zipSavePath);

            List<Product> productList = iProductService.listByIds(productIds);
            for (Product product : productList) {
                String supplierId = product.getTenantId();
                Long productId = product.getId();
                String productName = product.getName();
                String productCode = product.getProductCode();
                String description = product.getDescription();
                SupportedLogisticsEnum supportedLogistics = product.getSupportedLogistics();

                // SPU文件夹暂存目录
                // 示例 /www/zs-mall/temp/ProductZip/ZP123456
                String spuFolderSavePath = StrUtil.builder(zipSavePath, File.separator, productCode).toString();
                log.info("【导出收藏夹商品资料压缩包】SPU文件夹暂存目录 => {}", spuFolderSavePath);

                // 分类
                String category = iProductCategoryService.queryCategoryNameChainByIdOrderByLevelASC(product.getBelongCategoryId());

                // 商品特色
                StringBuilder featureBuilder = new StringBuilder();
                // 通用规格
                StringBuilder genericSpecBuilder = new StringBuilder();
                // 可选规格
                StringBuilder optionalSpecBuilder = new StringBuilder();
                List<ProductAttribute> productAttributes = iProductAttributeService.queryByProductId(productId);
                for (ProductAttribute productAttribute : productAttributes) {
                    String attributeName = productAttribute.getAttributeName();
                    String attributeValue = productAttribute.getAttributeValue();

                    if (StrUtil.isBlank(attributeValue)) {
                        continue;
                    }

                    AttributeTypeEnum attributeType = productAttribute.getAttributeType();
                    switch (attributeType) {
                        case Feature:
                            featureBuilder.append("[").append(attributeName).append(": ").append(attributeValue).append("]");
                            break;
                        case GenericSpec:
                            genericSpecBuilder.append("[").append(attributeName).append(": ").append(attributeValue).append("]");
                            break;
                        case OptionalSpec:
                            optionalSpecBuilder.append("[").append(attributeName).append(": ").append(CollUtil.join(productAttribute.getAttributeValues(), "/")).append("]");
                            break;
                        default:
                            break;
                    }
                }

                // SPU其他附件
                List<ProductAttachment> otherFiles = iProductAttachmentService.queryByProductIdAndType(productId, AttachmentTypeEnum.File);
                // 查询SPU首图
                ProductSkuAttachmentVo firstImageVo = iProductSkuAttachmentService.queryFirstImageByProductId(productId);

                List<ProductSku> productSkuList = iProductSkuService.getAcceptedAndOnShelfProductSkuListByProductId(productId);
                List<ProductSkuPackageVo> skuPackageVoList = new ArrayList<>();
                List<ProductSkuStockPackageVo> stockPackageVoList = new ArrayList<>();
                if (CollUtil.isNotEmpty(productSkuList)) {
                    for (ProductSku productSku : productSkuList) {
                        Long productSkuId = productSku.getId();
                        String productSkuCode = productSku.getProductSkuCode();
                        String specComposeName = productSku.getSpecComposeName();

                        ProductSkuDetail productSkuDetail = iProductSkuDetailService.queryByProductSkuId(productSkuId);
                        // 商品收藏导出暂不更改,默认us站点价格 临时改动-后续看产品要不要改
                        ProductSkuPrice productSkuPrice = iProductSkuPriceService.queryByProductSkuId(productSkuId);
                        if(ObjectUtil.isEmpty(productSkuPrice)){
                            // 如果美国站点缺失,随机先拿一个站点
                            LambdaQueryWrapper<ProductSkuPrice> wrapper = new LambdaQueryWrapper<>();
                            wrapper.eq(ProductSkuPrice::getProductSkuId,productSkuId);
                            wrapper.last("limit 1");
                            productSkuPrice= TenantHelper.ignore(() -> iProductSkuPriceService.getOne(wrapper));
                        }
                        List<ProductSkuAttachment> skuAttachmentList = iProductSkuAttachmentService.queryBySkuIdAndAttachmentTypeOrderBySortAsc(productSkuId, AttachmentTypeEnum.Image);

                        String defaultShippingReturnsContents =
                            iShippingReturnsService.getDefaultShippingReturnsContentsBySupplierId(supplierId);

                        String lengthUnit = productSkuDetail.getLengthUnit().name();
                        String weightUnit = productSkuDetail.getWeightUnit().name();
                        String packLengthUnit = productSkuDetail.getPackLengthUnit().name();
                        String packWeightUnit = productSkuDetail.getPackWeightUnit().name();

                        ProductSkuPackageVo vo = new ProductSkuPackageVo();
                        vo.setProductName(productName);
                        vo.setProductCode(productCode);
                        vo.setImageShowUrl(firstImageVo.getAttachmentShowUrl());
                        vo.setProductCategory(category);
                        vo.setFeatures(featureBuilder.toString());
                        vo.setGenericSpec(genericSpecBuilder.toString());
                        vo.setDescription(description);
                        vo.setOptionalSpec(optionalSpecBuilder.toString());
                        vo.setSupportedLogistics(supportedLogistics.getByLocale(headerLocale.toString()));
                        vo.setShippingReturnDescription(HtmlUtil.cleanHtmlTag(defaultShippingReturnsContents));
                        vo.setProductLink(null);
                        vo.setProductSkuCode(productSkuCode);
                        vo.setSpecComposeName(specComposeName);
                        vo.setLength(productSkuDetail.getLength() + lengthUnit);
                        vo.setWidth(productSkuDetail.getWidth() + lengthUnit);
                        vo.setHeight(productSkuDetail.getHeight() + lengthUnit);
                        vo.setWeight(productSkuDetail.getWeight() + weightUnit);
                        vo.setPackLength(productSkuDetail.getPackLength() + packLengthUnit);
                        vo.setPackWidth(productSkuDetail.getPackWidth() + packLengthUnit);
                        vo.setPackHeight(productSkuDetail.getPackHeight() + packLengthUnit);
                        vo.setPackWeight(productSkuDetail.getPackWeight() + packWeightUnit);

                        if (SupportedLogisticsEnum.PickUpOnly.equals(supportedLogistics)) {
                            vo.setPlatformPickUpPrice(DecimalUtil.bigDecimalToString(productSkuPrice.getPlatformPickUpPrice()));
                            vo.setPlatformDropShippingPrice("-");
                        } else if (SupportedLogisticsEnum.DropShippingOnly.equals(supportedLogistics)) {
                            vo.setPlatformPickUpPrice("-");
                            vo.setPlatformDropShippingPrice(DecimalUtil.bigDecimalToString(productSkuPrice.getPlatformDropShippingPrice()));
                        } else {
                            vo.setPlatformPickUpPrice(DecimalUtil.bigDecimalToString(productSkuPrice.getPlatformPickUpPrice()));
                            vo.setPlatformDropShippingPrice(DecimalUtil.bigDecimalToString(productSkuPrice.getPlatformDropShippingPrice()));
                        }
                        vo.setMsrp(productSkuPrice.getMsrp());
                        skuPackageVoList.add(vo);

                        List<ProductSkuStockVo> stockVoList = iProductSkuStockService.queryValidByProductSkuCode(productSkuCode);
                        for (ProductSkuStockVo stockVo : stockVoList) {
                            stockPackageVoList.add(new ProductSkuStockPackageVo(stockVo.getProductSkuCode(), stockVo.getStockAvailable(), stockVo.getWarehouseSystemCode()));
                        }

                        // 保存图片
                        if (CollUtil.isNotEmpty(skuAttachmentList)) {
                            // SKU图片暂存路径
                            // 示例 /www/zs-mall/temp/ProductZip/ZP123456/ZPS334455_Image
                            String skuImageSavePath = StrUtil.builder(spuFolderSavePath, File.separator, productSkuCode, "_Image").toString();
                            log.info("【导出收藏夹商品资料压缩包】SKU图片暂存路径 => {}", skuImageSavePath);

                            for (int i = 0; i < skuAttachmentList.size(); i++) {
                                ProductSkuAttachment skuAttachment = skuAttachmentList.get(i);
                                Long ossId = skuAttachment.getOssId();
                                String attachmentSuffix = skuAttachment.getAttachmentSuffix();
                                if (StrUtil.isBlank(attachmentSuffix)) {
                                    attachmentSuffix = FileUtil.getSuffix(skuAttachment.getAttachmentSavePath());
                                }

                                if (ossId != null) {
                                    byte[] bytes = OSSObtainEvent.obtainFileBytes(ossId);
                                    // 示例 /www/zs-mall/temp/ProductZip/ZP123456/ZPS334455_Image/Image1
                                    String imageSavePath = StrUtil.builder(skuImageSavePath).append(File.separator).append("Image").append(i + 1).append(".").append(attachmentSuffix).toString();
                                    log.info("【导出收藏夹商品资料压缩包】图片保存路径 => {}", imageSavePath);

                                    FileUtil.writeBytes(bytes, imageSavePath);
                                }
                            }
                        }
                    }
                }

                if (CollUtil.isNotEmpty(otherFiles)) {
                    // SPU其他附件暂存路径
                    // 示例 /www/zs-mall/temp/ProductZip/ZP123456/Product_Attachment
                    String otherFileSavePath = StrUtil.builder(spuFolderSavePath, File.separator, "Product_Attachment").toString();
                    log.info("【导出收藏夹商品资料压缩包】SPU其他附件暂存路径 => {}", otherFileSavePath);

                    for (int i = 0; i < otherFiles.size(); i++) {
                        ProductAttachment otherFile = otherFiles.get(i);
                        String attachmentSuffix = otherFile.getAttachmentSuffix();
                        if (StrUtil.isBlank(attachmentSuffix)) {
                            attachmentSuffix = FileUtil.getSuffix(otherFile.getAttachmentSavePath());
                        }

                        Long ossId = otherFile.getOssId();
                        if (ossId != null) {
                            byte[] bytes = OSSObtainEvent.obtainFileBytes(ossId);
                            // 示例 /www/zs-mall/temp/ProductZip/ZP123456/Product_Attachment/Attachment1
                            FileUtil.writeBytes(bytes, StrUtil.builder(otherFileSavePath).append(File.separator).append("Attachment").append(i + 1).append(".").append(attachmentSuffix).toString());
                        }
                    }
                }

                // 商品表格暂存路径
                // 示例 /www/zs-mall/temp/ProductZip/ZP123456/ProductExcel.xlsx
                String productExcelSavePath = StrUtil.builder(spuFolderSavePath, File.separator, "ProductExcel.xlsx").toString();
                log.info("【导出收藏夹商品资料压缩包】商品表格暂存路径 => {}", productExcelSavePath);
                BufferedOutputStream productExcelOS = FileUtil.getOutputStream(productExcelSavePath);
                ExcelUtil.exportExcelWithLocale(skuPackageVoList, "FavoritesProduct", ProductSkuPackageVo.class, true, productExcelOS, headerLocale);
                IoUtil.close(productExcelOS);

                // 商品库存表格暂存路径
                // 示例 /www/zs-mall/temp/ProductZip/ZP123456/ProductStockExcel.xlsx
                String productStockExcelSavePath = StrUtil.builder(spuFolderSavePath, File.separator, "ProductStockExcel.xlsx").toString();
                log.info("【导出收藏夹商品资料压缩包】商品库存表格暂存路径 => {}", productStockExcelSavePath);
                BufferedOutputStream productStockExcelOS = FileUtil.getOutputStream(productStockExcelSavePath);
                ExcelUtil.exportExcelWithLocale(stockPackageVoList, "FavoritesProduct", ProductSkuStockPackageVo.class, true, productStockExcelOS, headerLocale);
                IoUtil.close(productStockExcelOS);

                Integer downloadCount = product.getDownloadCount();
                product.setDownloadCount(downloadCount != null ? downloadCount + 1 : 1);
            }

            File zip = ZipUtil.zip(zipSavePath, tempFileSavePath, true);
            FileUtil.del(zipSavePath);

            TenantHelper.ignore(() -> iProductService.updateBatchById(productList));
            return zip;
        });
        return R.ok();
    }

    @Override
    public R<Void> exportV2(TenantFavoritesListBo bo, HttpServletResponse response) {
        bo.setTenantId(LoginHelper.getTenantId());
        Locale headerLocale = ServletUtils.getHeaderLocale();
        String fileName = StrUtil.format(FileNameConstants.TENANT_FAVORITES_EXPORT, DateUtil.format(new Date(), "yyMMdd-HHmmssSS"));
        DownloadRecordUtil.generate(fileName, DownloadTypePlusEnum.TenantFavoritesExport, tempFileSavePath -> {
            PageQuery pageQuery = new PageQuery();
            pageQuery.setPageNum(0);
            pageQuery.setPageSize(Integer.MAX_VALUE);
            TableDataInfo<TenantFavoritesListVo> tenantFavoritesListVoTableDataInfo = queryPage(bo, pageQuery);
            List<TenantFavoritesListVo> tenantFavoritesListVos = tenantFavoritesListVoTableDataInfo.getRows();
            ArrayList<TenantFavoritesExport> list = new ArrayList<>();
            for (TenantFavoritesListVo s : tenantFavoritesListVos) {
                //处理价格
                if (s.getEnSupportedLogistics().equals("PickUpOnly")){
                    s.setDropShippingPrice("不支持");
                }
                if (s.getEnSupportedLogistics().equals("DropShippingOnly")){
                    s.setPickUpPrice("不支持");
                }
                s.getSkuStockList().forEach(ss -> {
                    TenantFavoritesExport tenantFavoritesExport = BeanUtil.copyProperties(s, TenantFavoritesExport.class);
                    tenantFavoritesExport.setSiteCurrency(s.getSite()+"/"+s.getCurrency());
                    tenantFavoritesExport.setWarehouseName(String.valueOf(ss.get("warehouseName")));
                    tenantFavoritesExport.setStockAvailable(null == ss.get("stockAvailable") ? "0": String.valueOf(ss.get("stockAvailable")));
                    if(null != ss.get("dropShippingStockAvailable") && ss.get("dropShippingStockAvailable").equals(DropShippingStockAvailableEnum.NOT_SINGLE.getCode())){
                        tenantFavoritesExport.setProxyStock(String.valueOf(ss.get("stockAvailable")));
                    }else {
                        tenantFavoritesExport.setProxyStock("0");
                    }
                    tenantFavoritesExport.setWarehouseAddress(String.valueOf(ss.get("warehouseAddress")));
                    tenantFavoritesExport.setDetailedAddress(String.valueOf(ss.get("detailedAddress")));
                    list.add(tenantFavoritesExport);
                });
            }
            File tempFile = new File(tempFileSavePath);
            BufferedOutputStream outputStream = FileUtil.getOutputStream(tempFile);
            ExcelUtil.exportExcelWithLocale(list, "TenantList", TenantFavoritesExport.class, false, outputStream, headerLocale);
            IoUtil.close(outputStream);
            return tempFile;
        });
        return R.ok();
    }

    /**
     * 查询收藏夹数量
     * @return
     */
    @Override
    public Integer getTenantFavoritesCount() {
        TenantFavorites t=new TenantFavorites();
        LambdaQueryWrapper<TenantFavorites> q = new LambdaQueryWrapper<>();
        q.groupBy(TenantFavorites::getProductSkuId);
        List<TenantFavorites> tenantFavorites = iTenantFavoritesService.getBaseMapper().selectList(q);
        return tenantFavorites.size();
    }
    public static void main(String[] args){
        HtmlUtil.cleanHtmlTag(null);
    }
}
