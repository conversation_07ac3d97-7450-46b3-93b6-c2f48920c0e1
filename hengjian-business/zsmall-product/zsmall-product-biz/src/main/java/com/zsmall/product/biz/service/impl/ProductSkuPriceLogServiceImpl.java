package com.zsmall.product.biz.service.impl;

import com.zsmall.product.biz.service.ProductSkuPriceLogService;
import com.zsmall.product.entity.iservice.IProductSkuPriceLogService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * 商品sku价格变动日志Service业务层处理
 *
 * <AUTHOR> Li
 * @date 2023-05-22
 */
@RequiredArgsConstructor
@Service
public class ProductSkuPriceLogServiceImpl implements ProductSkuPriceLogService {

    private final IProductSkuPriceLogService iProductSkuPriceLogService;


}
