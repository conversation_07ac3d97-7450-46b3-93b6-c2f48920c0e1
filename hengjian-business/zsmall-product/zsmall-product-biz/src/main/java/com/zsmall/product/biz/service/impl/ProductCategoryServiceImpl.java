package com.zsmall.product.biz.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.hengjian.common.core.domain.R;
import com.hengjian.common.core.exception.RStatusCodeException;
import com.hengjian.common.core.utils.MapstructUtils;
import com.hengjian.common.core.utils.StringUtils;
import com.zsmall.common.enums.common.GlobalStateEnum;
import com.zsmall.common.enums.statuscode.ZSMallStatusCodeEnum;
import com.zsmall.product.biz.service.IProductCategoryRelationService;
import com.zsmall.product.biz.service.ProductCategoryService;
import com.zsmall.product.entity.domain.ProductCategory;
import com.zsmall.product.entity.domain.bo.category.ProductCategoryBo;
import com.zsmall.product.entity.domain.bo.category.ProductCategoryParamBo;
import com.zsmall.product.entity.domain.vo.category.ProductCategorySearchVo;
import com.zsmall.product.entity.domain.vo.category.ProductCategorySelectVo;
import com.zsmall.product.entity.domain.vo.category.ProductCategoryVo;
import com.zsmall.product.entity.iservice.IProductCategoryService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 商品分类Service业务层处理
 *
 * <AUTHOR> Li
 * @date 2023-05-18
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class ProductCategoryServiceImpl implements ProductCategoryService {

    private final IProductCategoryService iProductCategoryService;
    private final IProductCategoryRelationService iProductCategoryRelationService;

    /**
     * 查询商品分类
     */
    @Override
    public ProductCategoryVo queryById(Long id){
        return iProductCategoryService.queryById(id);
    }


    /**
     * 查询商品分类列表
     */
    @Override
    public List<ProductCategoryVo> queryList(ProductCategoryParamBo bo) {
        return iProductCategoryService.queryList(bo);
    }

    /**
     * 查询商品分类列表（提供给下拉选使用）
     *
     * @param parentId
     * @return
     */
    @Override
    public List<ProductCategorySelectVo> queryListForSelect(Long parentId) {
//        Long parentId = bo.getParentId();
        LambdaQueryWrapper<ProductCategory> lqw = Wrappers.lambdaQuery();
        if (parentId != null) {
            lqw.eq(ProductCategory::getParentId, parentId);
        } else {
            lqw.eq(ProductCategory::getParentId, 0L);
        }
        lqw.eq(ProductCategory::getCategoryState, GlobalStateEnum.Valid);
        lqw.orderByAsc(ProductCategory::getCategorySort);

        List<ProductCategory> categoryList = iProductCategoryService.list(lqw);
        List<ProductCategorySelectVo> categorySelectVos = BeanUtil.copyToList(categoryList, ProductCategorySelectVo.class);
        for (ProductCategorySelectVo categorySelectVo : categorySelectVos) {
            categorySelectVo.setLeaf(!iProductCategoryService.existsByParentId(categorySelectVo.getId()));
        }
        return categorySelectVos;
    }

    @Override
    public List<ProductCategoryVo> queryListByIds(List<Long> ids) {
        return iProductCategoryService.queryListByIds(ids);
    }

    private LambdaQueryWrapper<ProductCategory> buildQueryWrapper(ProductCategoryBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<ProductCategory> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getParentId() != null, ProductCategory::getParentId, bo.getParentId());
        lqw.eq(bo.getCategoryLevel() != null, ProductCategory::getCategoryLevel, bo.getCategoryLevel());
        lqw.like(StringUtils.isNotBlank(bo.getCategoryName()), ProductCategory::getCategoryName, bo.getCategoryName());
        lqw.like(ObjectUtil.isNotNull(bo.getCategoryOtherName()), ProductCategory::getCategoryOtherName, bo.getCategoryOtherName());
        lqw.eq(bo.getCategorySort() != null, ProductCategory::getCategorySort, bo.getCategorySort());
        lqw.eq(StringUtils.isNotBlank(bo.getCategoryImageSavePath()), ProductCategory::getCategoryImageSavePath, bo.getCategoryImageSavePath());
        lqw.eq(StringUtils.isNotBlank(bo.getCategoryImageShowUrl()), ProductCategory::getCategoryImageShowUrl, bo.getCategoryImageShowUrl());
        lqw.eq(StringUtils.isNotBlank(bo.getCategoryIconSavePath()), ProductCategory::getCategoryIconSavePath, bo.getCategoryIconSavePath());
        lqw.eq(StringUtils.isNotBlank(bo.getCategoryIconShowUrl()), ProductCategory::getCategoryIconShowUrl, bo.getCategoryIconShowUrl());
        lqw.eq(bo.getCategoryState() != null, ProductCategory::getCategoryState, bo.getCategoryState());
        return lqw;
    }

    /**
     * 新增商品分类
     */
    @Override
    public Boolean insertByBo(ProductCategoryBo bo) {
        ProductCategory add = MapstructUtils.convert(bo, ProductCategory.class);
        log.info("ProductCategory = {}", JSONUtil.toJsonStr(add));
        validEntityBeforeSave(add);
        boolean flag = iProductCategoryService.save(add);
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改商品分类
     */
    @Override
    public Boolean updateByBo(ProductCategoryBo bo) {
        ProductCategory update = MapstructUtils.convert(bo, ProductCategory.class);
        validEntityBeforeSave(update);
        return iProductCategoryService.updateById(update);
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(ProductCategory entity){
        entity.setCategoryName(StrUtil.trim(entity.getCategoryName()));
        Boolean existsed = iProductCategoryService.existsCategoryName(entity.getParentId(), entity.getCategoryName(), entity.getId());
        if (existsed) {
            throw new RStatusCodeException(ZSMallStatusCodeEnum.PRODUCT_CATEGORY_REPEAT);
        }
        entity.setCategoryState(1);
        Long parentId = entity.getParentId();
        if (ObjectUtil.equals(parentId, 0L)) {
            entity.setCategoryLevel(1);
        } else {
            ProductCategoryVo productCategoryVo = this.queryById(parentId);
            Integer categoryLevel = productCategoryVo.getCategoryLevel();
            entity.setCategoryLevel(categoryLevel + 1);
        }
    }

    /**
     * 批量删除商品分类
     */
    @Override
    public R<Void> deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        List<Long> categoryIds = new ArrayList<>();
        if(isValid){
            /**
             * 1、判断是否已经关联商品，若关联则无法删除
             * 2、若是存在子类目，则删除子类目
             */
            for (Long id: ids) {
                List<ProductCategory> productCategories = iProductCategoryService.queryCategoryChainByIdDesc(id);
                List<Long> idList = productCategories.stream().map(ProductCategory::getId).collect(Collectors.toList());

                List<String> productCodeList = iProductCategoryRelationService.existProductCodeRelationByCategoryIds(idList);
                if (CollUtil.isNotEmpty(productCodeList)) {
                    return R.fail(ZSMallStatusCodeEnum.PRODUCT_CATEGORY_EXIST_PRODUCT.args(CollUtil.join(productCodeList, ", ")));
                }
                categoryIds.addAll(idList);
            }
        }
        if (!iProductCategoryService.removeBatchByIds(categoryIds)) {
            return R.fail();
        }
        return R.ok();
    }

    @Override
    public List<ProductCategorySearchVo> listForSearch(String categoryName) {
        List<ProductCategory> productCategoryList = iProductCategoryService.queryLikeByCategoryName(categoryName);
        List<ProductCategorySearchVo> productCategorySearchVoList = new ArrayList<>();
        Set<Long> parentProductCategoryIdList = new HashSet<>();
        if(CollUtil.isNotEmpty(productCategoryList)){
            for (ProductCategory productCategory: productCategoryList){
                if(productCategory.getParentId() == 0L){
                    parentProductCategoryIdList.add(productCategory.getId());
                }else{
                    // 根据子节点查询出父类
                    List<ProductCategory> productCategoryListChildren = iProductCategoryService.queryCategoryChainById(productCategory.getId());
                    if (CollUtil.isNotEmpty(productCategoryListChildren)){
                        List<ProductCategory> collect = productCategoryListChildren.stream()
                                                                                   .sorted(Comparator.comparing(ProductCategory::getParentId))
                                                                                   .collect(Collectors.toList());
                        if(collect.get(0).getParentId().equals(0L)){
                            parentProductCategoryIdList.add(collect.get(0).getId());
                        }
                    }
                }
            }
            // 根据顶级父节点查询出该节点下全部类目数据
            if(CollUtil.isNotEmpty(parentProductCategoryIdList)){
                for (Long id: parentProductCategoryIdList){
                    List<ProductCategory> productCategoryListParent = iProductCategoryService.queryCategoryChainByIdDesc(id);
                    // 转换为VO
                    List<ProductCategorySearchVo> productCategorySearchVoList1 = convertToSearchVoList(productCategoryListParent);
                    productCategorySearchVoList.addAll(productCategorySearchVoList1);
                }
            }
        }
        return productCategorySearchVoList;
    }

    @Override
    public List<ProductCategorySearchVo> listForSearchForMontage(String categoryName) {
        List<ProductCategory> productCategoryList = iProductCategoryService.queryLikeByCategoryName(categoryName);
        List<ProductCategorySearchVo> productCategorySearchVoList = new ArrayList<>();
        if(CollUtil.isNotEmpty(productCategoryList)) {
            for (ProductCategory productCategory : productCategoryList) {
                StringBuffer sb = new StringBuffer();
                List<Long> idList = new ArrayList<>();
                StringBuffer ids = new StringBuffer();
                if (productCategory.getId() == 0L) {
                    sb.append(productCategory.getCategoryName());
                    idList.add(productCategory.getId());
                    ids.append(productCategory.getId());
                    productCategorySearchVoList.add(new ProductCategorySearchVo(sb.toString(),idList,ids.toString()));
                } else {
                    // 根据子节点查询出父类
                    List<ProductCategory> productCategoryListChildren = iProductCategoryService.queryCategoryChainById(productCategory.getId());
                    System.out.println(productCategoryListChildren);
                    if (CollUtil.isNotEmpty(productCategoryListChildren)) {
                        List<ProductCategory> collect = productCategoryListChildren.stream().sorted(Comparator.comparing(ProductCategory::getParentId)).collect(Collectors.toList());
                        if (CollUtil.isNotEmpty(collect)) {
                            for(ProductCategory productCategory1: collect){
                                sb.append(productCategory1.getCategoryName()).append(">");
                                idList.add(productCategory1.getId());
                                ids.append(productCategory1.getId()).append(",");
                            }
                            if (sb.length() > 0) {
                                sb.setLength(sb.length() - 1);
                            }
                            if (ids.length() > 0) {
                                ids.setLength(ids.length() - 1);
                            }
                            productCategorySearchVoList.add(new ProductCategorySearchVo(sb.toString(),idList,ids.toString()));
                        }
                    }
                }
            }
        }
        return productCategorySearchVoList;
    }

    @Override
    public List<ProductCategorySearchVo> listForSearchForAllMontage(String categoryName) {
        List<ProductCategory> productCategoryList = iProductCategoryService.queryLikeByCategoryName(categoryName);
        Set<ProductCategorySearchVo> productCategorySearchVoList = new HashSet<>();
        if(CollUtil.isNotEmpty(productCategoryList)) {
            for (ProductCategory productCategory : productCategoryList) {
                StringBuffer sb = new StringBuffer();
                List<Long> idList = new ArrayList<>();
                StringBuffer ids = new StringBuffer();
                if (productCategory.getId() == 0L) {
                    sb.append(productCategory.getCategoryName());
                    idList.add(productCategory.getId());
                    ids.append(productCategory.getId());
                    productCategorySearchVoList.add(new ProductCategorySearchVo(sb.toString(),idList,ids.toString()));
                } else {
                    // 根据子节点查询出父类
                    List<ProductCategory> productCategoryListChildren = iProductCategoryService.queryAllCategoryChainById(productCategory.getId());
                    System.out.println(productCategoryListChildren);

                    buildCategoryPaths(productCategoryListChildren,productCategorySearchVoList);
                }
            }
        }
        return new ArrayList<>(productCategorySearchVoList);
    }

    /**
     * 将ProductCategory列表转换为ProductCategorySearchVo列表
     *
     * @param categories
     * @return
     */
    public static List<ProductCategorySearchVo> convertToSearchVoList(List<ProductCategory> categories) {
        // 创建映射以存储id和ProductCategorySearchVo对象的映射关系
        Map<Long, ProductCategorySearchVo> categoryMap = new HashMap<>();
        List<ProductCategorySearchVo> rootList = new ArrayList<>();

        for (ProductCategory category : categories) {
            // 创建ProductCategorySearchVo对象
            ProductCategorySearchVo searchVo = new ProductCategorySearchVo()
                .setId(category.getId())
                .setParentId(category.getParentId())
                .setCategoryName(category.getCategoryName())
                .setCategoryOtherName(category.getCategoryOtherName());
            // 将ProductCategorySearchVo对象存入映射
            categoryMap.put(category.getId(), searchVo);
        }
        for (ProductCategory category : categories) {
            ProductCategorySearchVo currentVo = categoryMap.get(category.getId());
            if (category.getParentId() == null || category.getParentId() == 0) {
                // 如果是根节点，添加到根列表
                rootList.add(currentVo);
            } else {
                // 如果有父节点，添加到父节点的childrenList中
                ProductCategorySearchVo parentVo = categoryMap.get(category.getParentId());
                if (parentVo != null && parentVo.getChildrenList() == null) {
                    parentVo.setChildrenList(new ArrayList<>());
                }
                parentVo.getChildrenList().add(currentVo);
            }
        }
        return rootList;
    }

    public void montageString(StringBuffer sb, List<Long> ids,ProductCategorySearchVo productCategorySearchVo){
        if(CollUtil.isNotEmpty(productCategorySearchVo.getChildrenList())){
            List<ProductCategorySearchVo> childrenList = productCategorySearchVo.getChildrenList();
            for (ProductCategorySearchVo productCategorySearchVo1 : childrenList) {
                sb.append(productCategorySearchVo1.getCategoryName()).append(">");
                ids.add(productCategorySearchVo1.getId());
                montageString(sb,ids,productCategorySearchVo1);
            }
        }
    }

    public static void buildCategoryPaths(List<ProductCategory> categories,Set<ProductCategorySearchVo> productCategorySearchVoList) {
        // 将 category 列表转换为 Map，以便根据 id 快速查找 parent
        Map<Long, ProductCategory> categoryMap = categories.stream()
                                                           .collect(Collectors.toMap(ProductCategory::getId, category -> category));
        // 遍历每个 category 构建完整链路
        for (ProductCategory category : categories) {
//            Map<String, String> pathInfo = new HashMap<>();
            List<String> nameChain = new ArrayList<>();
            List<String> idChain = new ArrayList<>();
            // 当前节点
            ProductCategory current = category;
            // 一直追溯到根节点
            while (current != null && current.getParentId() != 0) {
                nameChain.add(current.getCategoryName());
                idChain.add(current.getId().toString());
                current = categoryMap.get(current.getParentId());
            }
            // 如果有顶层节点（parentId = 0），也要加入链路
            if (current != null) {
                nameChain.add(current.getCategoryName());
                idChain.add(current.getId().toString());
            }
            // 将链路按顺序拼接
            Collections.reverse(nameChain);
            Collections.reverse(idChain);
//            pathInfo.put("title", String.join(">", nameChain));
//            pathInfo.put("id", String.join(",", idChain));
            productCategorySearchVoList.add(new ProductCategorySearchVo(String.join(">", nameChain),String.join(",", idChain)));
        }
    }
}
