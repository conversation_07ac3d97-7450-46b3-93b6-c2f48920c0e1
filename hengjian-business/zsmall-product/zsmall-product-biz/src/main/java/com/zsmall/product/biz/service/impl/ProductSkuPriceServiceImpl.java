package com.zsmall.product.biz.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hengjian.common.core.domain.R;
import com.hengjian.common.core.domain.model.LoginUser;
import com.hengjian.common.core.exception.RStatusCodeException;
import com.hengjian.common.core.utils.ServletUtils;
import com.hengjian.common.log.annotation.InMethodLog;
import com.hengjian.common.mybatis.core.page.PageQuery;
import com.hengjian.common.satoken.holder.LoginContextLocalHolder;
import com.hengjian.common.satoken.holder.runnable.HeaderLocaleRunnable;
import com.hengjian.common.satoken.utils.LoginHelper;
import com.zsmall.common.constant.FileNameConstants;
import com.zsmall.common.domain.LocaleMessage;
import com.zsmall.common.domain.dto.ChangeFieldDTO;
import com.zsmall.common.enums.ExcelMessageEnum;
import com.zsmall.common.enums.downloadRecord.DownloadTypePlusEnum;
import com.zsmall.common.enums.priceLog.PriceOperateLog;
import com.zsmall.common.enums.product.ProductReviewTypeEnum;
import com.zsmall.common.enums.product.ProductVerifyStateEnum;
import com.zsmall.common.enums.product.ShelfStateEnum;
import com.zsmall.common.enums.product.SupportedLogisticsEnum;
import com.zsmall.common.enums.statuscode.ZSMallStatusCodeEnum;
import com.zsmall.common.exception.ExcelMessageException;
import com.zsmall.common.util.ZExcelUtil;
import com.zsmall.product.biz.service.ProductReviewRecordService;
import com.zsmall.product.biz.service.ProductSkuPriceService;
import com.zsmall.product.entity.anno.IsDuplication;
import com.zsmall.product.entity.domain.Product;
import com.zsmall.product.entity.domain.ProductSku;
import com.zsmall.product.entity.domain.ProductSkuPrice;
import com.zsmall.product.entity.domain.bo.product.ProductPriceBo;
import com.zsmall.product.entity.domain.bo.siteBo.SiteBo;
import com.zsmall.product.entity.domain.bo.siteBo.SitePriceCleanBo;
import com.zsmall.product.entity.domain.dto.product.ProductReviewDTO;
import com.zsmall.product.entity.domain.dto.productSku.ProductSkuReviewDTO;
import com.zsmall.product.entity.domain.dto.productSkuPrice.ProductSkuPriceExcelDTO;
import com.zsmall.product.entity.domain.vo.product.ProductPriceExportDto;
import com.zsmall.product.entity.iservice.*;
import com.zsmall.system.entity.domain.SiteCountryCurrency;
import com.zsmall.system.entity.iservice.ISiteCountryCurrencyService;
import com.zsmall.system.entity.util.DownloadRecordV2Util;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Sheet;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.BufferedOutputStream;
import java.io.File;
import java.io.InputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 商品SKU定价Service业务层处理
 *
 * <AUTHOR> Li
 * @date 2023-05-22
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class ProductSkuPriceServiceImpl implements ProductSkuPriceService {
    private static final ExecutorService executor = Executors.newFixedThreadPool(2);
    private final IProductSkuService iProductSkuService;
    private final IProductService iProductService;
    private final IProductSkuPriceService iProductSkuPriceService;
    private final IProductSkuPriceLogService iProductSkuPriceLogService;
    private final IProductSkuPriceRuleService iProductSkuPriceRuleService;
    private final ProductReviewRecordService productReviewRecordService;
    private final IProductReviewRecordService iProductReviewRecordService;
    private final ISiteCountryCurrencyService iSiteCountryCurrencyService;


    @InMethodLog(value = "上传商品价格更新Excel")
    @Override
    @Transactional(rollbackFor = {ExcelMessageException.class, Exception.class})
    public R<JSONObject> uploadProductPriceUpdate(MultipartFile file) {
        if (file == null) {
            return R.fail(ZSMallStatusCodeEnum.UPLOAD_FILE_IS_EMPTY);
        }
        try {
            //业务处理
            InputStream inputStream = file.getInputStream();

            ExcelReader reader = ExcelUtil.getReader(inputStream);

            if (reader != null) {
                Sheet sheet = reader.getSheet();
                String sheetName = sheet.getSheetName();
                log.info("uploadOrderExcel - sheetName = {}", sheetName);
                int columnCount = reader.getColumnCount();
                log.info("uploadOrderExcel - columnCount = {}", columnCount);
                String title = "Item No,Country Code,Unit Price,Operation Fee,Final Delivery Fee";
                List<SiteCountryCurrency> siteCountryCurrencies = iSiteCountryCurrencyService.list();
                Map<String, SiteCountryCurrency> currencyMap = siteCountryCurrencies.stream()
                                                                                .collect(Collectors.toMap(SiteCountryCurrency::getCountryCode, Function.identity()));
                // 必填的title
                String requireTitle = "Item No,Unit Price";
                String[] titleArray = StringUtils.split(title, ",");
                if (columnCount != titleArray.length) {
                    throw new RStatusCodeException(ZSMallStatusCodeEnum.EXCEL_COLUMN_COUNT_NOT_MATCH);
                }

                // 有效行
                int physicalRows = sheet.getPhysicalNumberOfRows();
                log.info("uploadOrderExcel - physicalRows = {}", physicalRows);

                String excelType = "dispatchedImport";
                List<ProductSkuPriceExcelDTO> importDTOS = ZExcelUtil.parseFieldDTO(reader, ProductSkuPriceExcelDTO.class, 1);
                log.info("importDTOS = {}", JSONUtil.toJsonStr(importDTOS));


                if (CollUtil.isNotEmpty(importDTOS)) {
                    LocaleMessage localeMessage = new LocaleMessage();

                    List<String> itemNoList = new ArrayList<>();
                    //导入数据格式校验
                    int showRowIndex = 1;
                    for (ProductSkuPriceExcelDTO importDTO : importDTOS) {
                        showRowIndex += 1;
                        String itemNo = importDTO.getItemNo();
                        String repeatPrice = itemNo+ importDTO.getCountryCode();
                        if (itemNoList.contains(repeatPrice)) {
                            throw new ExcelMessageException(ExcelMessageEnum.ITEM_NO_REPEAT.buildLocalMessage(showRowIndex));
                        }
                        itemNoList.add(repeatPrice);
                        String unitPriceStr = StrUtil.trim(importDTO.getUnitPrice());
                        String operationFeeStr = StrUtil.trim(importDTO.getOperationFee());
                        String finalDeliveryFeeStr = StrUtil.trim(importDTO.getFinalDeliveryFee());
                        String msrpStr = StrUtil.trim(importDTO.getMsrp());
                        String countryCode = StrUtil.trim(importDTO.getCountryCode());

                        if (StrUtil.isBlank(itemNo)) {
                            throw new ExcelMessageException(ExcelMessageEnum.REQUIRE.buildLocalMessage(showRowIndex, "1"));
                        }
                        if(StrUtil.isBlank(countryCode)){
                            throw new ExcelMessageException(ExcelMessageEnum.REQUIRE.buildLocalMessage(showRowIndex,"2"));
                        }
                        if (!currencyMap.containsKey(countryCode)) {
                            throw new ExcelMessageException(ExcelMessageEnum.COUNTRY_CODE_NOT_EXIST.args(countryCode).buildLocalMessage(showRowIndex,"2"));
                        }
                        if (StrUtil.isBlank(unitPriceStr)) {
                            throw new ExcelMessageException(ExcelMessageEnum.REQUIRE.buildLocalMessage(showRowIndex, "3"));
                        }
                        if (StrUtil.isBlank(operationFeeStr)) {
                            throw new ExcelMessageException(ExcelMessageEnum.REQUIRE.buildLocalMessage(showRowIndex, "4"));
                        }
                        if (StrUtil.isBlank(finalDeliveryFeeStr)) {
                            throw new ExcelMessageException(ExcelMessageEnum.REQUIRE.buildLocalMessage(showRowIndex, "5"));
                        }
//                        if (StrUtil.isBlank(msrpStr)) {
//                            throw new ExcelMessageException(ExcelMessageEnum.REQUIRE.buildLocalMessage(showRowIndex, "5"));
//                        }

                        BigDecimal unitPrice = BigDecimal.ZERO;
                        BigDecimal operationFee = BigDecimal.ZERO;
                        BigDecimal finalDeliveryFee = BigDecimal.ZERO;
                        BigDecimal msrp = BigDecimal.ZERO;

                        try {
                            unitPrice = NumberUtil.toBigDecimal(unitPriceStr).setScale(2, RoundingMode.HALF_UP);
                            operationFee = NumberUtil.toBigDecimal(operationFeeStr).setScale(2, RoundingMode.HALF_UP);
                            finalDeliveryFee = NumberUtil.toBigDecimal(finalDeliveryFeeStr).setScale(2, RoundingMode.HALF_UP);
                            msrp = NumberUtil.toBigDecimal(msrpStr).setScale(2, RoundingMode.HALF_UP);
                        }catch (Exception e) {
                            throw new ExcelMessageException(ExcelMessageEnum.PRODUCT_PRICE_FORMAT_ERROR.buildLocalMessage(showRowIndex));
                        }
                        if (unitPrice.compareTo(BigDecimal.ZERO) <= 0
                            || operationFee.compareTo(BigDecimal.ZERO) <= 0
                            || finalDeliveryFee.compareTo(BigDecimal.ZERO) <= 0
                        ) {
                            throw new ExcelMessageException(ExcelMessageEnum.PRICE_GREATER_ZERO.buildLocalMessage(showRowIndex));
//                            localeMessage.appendSurround(ExcelMessageEnum.PRICE_GREATER_ZERO.buildLocalMessage(showRowIndex), "<p>", "</p></br>");
                        }
                        if (NumberUtil.toBigDecimal(msrp).compareTo(BigDecimal.ZERO) < 0) {
                            throw new ExcelMessageException(ExcelMessageEnum.PRICE_GREATER_ZERO.buildLocalMessage(showRowIndex));
//                            localeMessage.appendSurround(ExcelMessageEnum.PRICE_GREATER_ZERO.buildLocalMessage(showRowIndex), "<p>", "</p></br>");
                        }

                    }

                    if (localeMessage.hasData()) {
                        throw new ExcelMessageException(localeMessage);
                    }

                    //校验通过，关联数据库操作
                    List<ProductReviewDTO> productReviewDTOs = new ArrayList<>();
                    List<Product> validProductList = new ArrayList<>();
                    List<ProductSku> validSkuList = new ArrayList<>();
                    List<ProductSkuPrice> nowChangePrices = new ArrayList<>();
                    List<ProductSkuPrice> oldPrices = new ArrayList<>();

                    showRowIndex = 1;
                    // 同产品不同站点生成同一个审批记录但是生成多个详情
                    for (ProductSkuPriceExcelDTO importDTO : importDTOS) {

                        showRowIndex += 1;
                        String itemNo = importDTO.getItemNo();
                        String unitPriceStr = StrUtil.trim(importDTO.getUnitPrice());
                        String operationFeeStr = StrUtil.trim(importDTO.getOperationFee());
                        String finalDeliveryFeeStr = StrUtil.trim(importDTO.getFinalDeliveryFee());
                        String countryCode = StrUtil.trim(importDTO.getCountryCode());
                        BigDecimal unitPrice = NumberUtil.toBigDecimal(unitPriceStr).setScale(2, RoundingMode.HALF_UP);
                        BigDecimal operationFee = NumberUtil.toBigDecimal(operationFeeStr).setScale(2, RoundingMode.HALF_UP);
                        BigDecimal finalDeliveryFee = NumberUtil.toBigDecimal(finalDeliveryFeeStr).setScale(2, RoundingMode.HALF_UP);
                        SiteCountryCurrency currency = currencyMap.get(countryCode);
                        Long siteId = currency.getId();
                        String msrp = importDTO.getMsrp();
                        ProductSku productSku = iProductSkuService.queryByProductSkuCode(itemNo);
                        if (productSku == null) {
                            throw new ExcelMessageException(ExcelMessageEnum.PRODUCT_NOT_EXIST.buildLocalMessage(showRowIndex));
                        }

                        //判断当前SKU是否正在审核
                        Boolean exists = iProductReviewRecordService.existsByProductSkuCode(itemNo, ProductReviewTypeEnum.Price, ProductVerifyStateEnum.Pending);
                        if (exists) {
                            throw new ExcelMessageException(ExcelMessageEnum.NEW_PRODUCT_PRICE_VERIFY_PENDING.buildLocalMessage(showRowIndex));
                        }

                        String productSkuCode = productSku.getProductSkuCode();
                        Long productSkuId = productSku.getId();

                        Product product = iProductService.queryByProductCode(productSku.getProductCode());
                        String productCode = product.getProductCode();
                        SupportedLogisticsEnum supportedLogistics = product.getSupportedLogistics();
                        ProductVerifyStateEnum verifyState = product.getVerifyState();
                        // SPU数据变更
                        List<ChangeFieldDTO> SPUChangeFields = new ArrayList<>();
                        SPUChangeFields.add(new ChangeFieldDTO("supportedLogistics", supportedLogistics.name(),
                            supportedLogistics.name()));

                        log.info("productSkuCode = {}, productCode = {}, supportedLogistics = {}", productSkuCode, product.getProductCode(), supportedLogistics.name());
                        ProductSkuPrice oldPrice = iProductSkuPriceService.queryByProductSkuCodeAndSite(productSkuCode,siteId);

                        ProductSkuPrice newPrice = iProductSkuPriceRuleService.matchRule(productSkuCode, unitPrice, operationFee, finalDeliveryFee, false);
                        newPrice.setProductSkuId(productSkuId);
                        newPrice.setProductSkuCode(productSkuCode);
                        Boolean isNewSite = false;
                        if(ObjectUtil.isEmpty(oldPrice)){
                            isNewSite = true;
                        }else {
                            newPrice.setId(oldPrice.getId());
                        }

                        if (StrUtil.isNotBlank(msrp)) {
                            newPrice.setMsrp(NumberUtil.toBigDecimal(msrp).setScale(2, RoundingMode.HALF_UP));
                        } else {
                            if(isNewSite){
                                newPrice.setMsrp(BigDecimal.ZERO);
                            }else {
                                newPrice.setMsrp(oldPrice.getMsrp());

                            }

                        }
                        log.info("oldProductSkuPrice = {}", JSONUtil.toJsonStr(oldPrice));
                        log.info("newProductSkuPrice = {}", JSONUtil.toJsonStr(newPrice));

                        ShelfStateEnum skuShelfState = ShelfStateEnum.OffShelf;

                        // 商品SPU未过审或者被拒绝时，价格变更直接生效 或 商品SPU已过审，但Sku未过审或被拒绝时
                        if (ProductVerifyStateEnum.Draft.equals(verifyState) || ProductVerifyStateEnum.Rejected.equals(verifyState)) {
                            if (isNewSite){
                                iProductSkuPriceLogService.recordPriceChanges(null,PriceOperateLog.Add.name());
                            }else {
                                iProductSkuPriceLogService.recordPriceChanges(oldPrice,PriceOperateLog.Update.name());
                                oldPrices.add(oldPrice);
                            }
                            nowChangePrices.add(newPrice);


                            //记录价格日志
                            skuShelfState = ShelfStateEnum.ForcedOffShelf;
                            productSku.setVerifyState(ProductVerifyStateEnum.Draft);
                            if (!validProductList.contains(product)) {
                                product.setVerifyState(ProductVerifyStateEnum.Draft);
                                validProductList.add(product);
                            }
                        } else if (ProductVerifyStateEnum.Accepted.equals(verifyState)) {  // 商品SPU已过审
                            ProductVerifyStateEnum skuVerifyState = productSku.getVerifyState();
                            // 但Sku未过审或被拒绝时
                            if (ProductVerifyStateEnum.Draft.equals(skuVerifyState) || ProductVerifyStateEnum.Rejected.equals(skuVerifyState)) {
                                nowChangePrices.add(newPrice);

                                // 记录变更字段及值
                                ProductSkuReviewDTO productSkuReviewDTO = new ProductSkuReviewDTO();
                                productSkuReviewDTO.setProductSkuCode(productSkuCode);
                                productSkuReviewDTO.setSiteId(siteId);
                                productSkuReviewDTO.setProductSkuPriceId(oldPrice.getId());
                                if(isNewSite){
                                    productSkuReviewDTO.setReviewType(PriceOperateLog.Add.name());
                                    productSkuReviewDTO.addField("msrp", "0", "0");
                                    productSkuReviewDTO.addField("pickUpPrice", "0", NumberUtil.toStr(newPrice.getOriginalPickUpPrice()));
                                    productSkuReviewDTO.addField("dropShippingPrice", "0", NumberUtil.toStr(newPrice.getOriginalDropShippingPrice()));
                                    productSkuReviewDTO.addField("unitPrice", "0", NumberUtil.toStr(newPrice.getOriginalUnitPrice()));
                                    productSkuReviewDTO.addField("operationFee", "0", NumberUtil.toStr(newPrice.getOriginalOperationFee()));
                                    productSkuReviewDTO.addField("finalDeliveryFee", "0", NumberUtil.toStr(newPrice.getOriginalFinalDeliveryFee()));
                                }else {
                                    oldPrices.add(oldPrice);
                                    productSkuReviewDTO.setReviewType(PriceOperateLog.Update.name());
                                    productSkuReviewDTO.addField("msrp", NumberUtil.toStr(oldPrice.getMsrp()), "0");
                                    productSkuReviewDTO.addField("pickUpPrice", NumberUtil.toStr(oldPrice.getOriginalPickUpPrice()), NumberUtil.toStr(newPrice.getOriginalPickUpPrice()));
                                    productSkuReviewDTO.addField("dropShippingPrice", NumberUtil.toStr(oldPrice.getOriginalDropShippingPrice()), NumberUtil.toStr(newPrice.getOriginalDropShippingPrice()));
                                    productSkuReviewDTO.addField("unitPrice", NumberUtil.toStr(oldPrice.getOriginalUnitPrice()), NumberUtil.toStr(newPrice.getOriginalUnitPrice()));
                                    productSkuReviewDTO.addField("operationFee", NumberUtil.toStr(oldPrice.getOriginalOperationFee()), NumberUtil.toStr(newPrice.getOriginalOperationFee()));
                                    productSkuReviewDTO.addField("finalDeliveryFee", NumberUtil.toStr(oldPrice.getOriginalFinalDeliveryFee()), NumberUtil.toStr(newPrice.getOriginalFinalDeliveryFee()));
                                }

                                // 改版

                                List<ProductSkuReviewDTO> productSkuReviewDTOS = CollUtil.newArrayList(productSkuReviewDTO);

                                //添加审核数据
                                ProductReviewDTO productReviewDTO = new ProductReviewDTO();
                                productReviewDTO.setProductCode(productCode);
                                productReviewDTO.setSubmitTenantId(LoginHelper.getTenantId());
                                productReviewDTO.setChangeFields(SPUChangeFields);
                                productReviewDTO.setProductSkuReviewList(productSkuReviewDTOS);
                                productReviewDTO.setReviewType(ProductReviewTypeEnum.NewProductSku);
                                productReviewDTO.setReviewStatus(ProductVerifyStateEnum.Pending);
                                productReviewDTOs.add(productReviewDTO);

                                productSku.setVerifyState(ProductVerifyStateEnum.Pending);
                                skuShelfState = ShelfStateEnum.ForcedOffShelf;
                            } else if (ProductVerifyStateEnum.Pending.equals(skuVerifyState)) {  // 正在审核中时，不接受新修改的价格
                                //返回响应信息 商品正在审核中
//                                localeMessage.appendSurround(ExcelMessageEnum.NEW_PRODUCT_PRICE_VERIFY_PENDING.buildLocalMessage(showRowIndex), "<p>", "</p></br>");
                            } else if (ProductVerifyStateEnum.Accepted.equals(skuVerifyState)) {
                                if(isNewSite){
                                    ProductSkuReviewDTO productSkuReviewDTO = new ProductSkuReviewDTO();
                                    productSkuReviewDTO.setProductSkuCode(productSkuCode);
                                    productSkuReviewDTO.setSiteId(siteId);
//                                    productSkuReviewDTO.setProductSkuPriceId(oldPrice.getId());
                                    productSkuReviewDTO.setReviewType(PriceOperateLog.Add.name());
                                    productSkuReviewDTO.addField("msrp", "0", "0");
                                    productSkuReviewDTO.addField("pickUpPrice", "0", NumberUtil.toStr(newPrice.getOriginalPickUpPrice()));
                                    productSkuReviewDTO.addField("dropShippingPrice", "0", NumberUtil.toStr(newPrice.getOriginalDropShippingPrice()));
                                    productSkuReviewDTO.addField("unitPrice", "0", NumberUtil.toStr(newPrice.getOriginalUnitPrice()));
                                    productSkuReviewDTO.addField("operationFee", "0", NumberUtil.toStr(newPrice.getOriginalOperationFee()));
                                    productSkuReviewDTO.addField("finalDeliveryFee", "0", NumberUtil.toStr(newPrice.getOriginalFinalDeliveryFee()));
                                    List<ProductSkuReviewDTO> productSkuReviewDTOS = CollUtil.newArrayList(productSkuReviewDTO);
                                    //添加审核数据
                                    ProductReviewDTO productReviewDTO = new ProductReviewDTO();
                                    productReviewDTO.setProductCode(productCode);
                                    productReviewDTO.setSubmitTenantId(LoginHelper.getTenantId());
                                    productReviewDTO.setChangeFields(SPUChangeFields);
                                    productReviewDTO.setProductSkuReviewList(productSkuReviewDTOS);
                                    productReviewDTO.setReviewType(ProductReviewTypeEnum.Price);
                                    productReviewDTO.setReviewStatus(ProductVerifyStateEnum.Pending);
                                    productReviewDTOs.add(productReviewDTO);
                                }else {
                                    // 已通过审核时，需要提交价格变更审核
                                    if (!NumberUtil.equals(oldPrice.getMsrp(), newPrice.getMsrp())
                                        || !NumberUtil.equals(oldPrice.getOriginalDropShippingPrice(), newPrice.getOriginalDropShippingPrice())
                                        || !NumberUtil.equals(oldPrice.getOriginalPickUpPrice(), newPrice.getOriginalPickUpPrice())) {
                                        // 记录变更字段及值
                                        ProductSkuReviewDTO productSkuReviewDTO = new ProductSkuReviewDTO();
                                        productSkuReviewDTO.setProductSkuCode(productSkuCode);
                                        productSkuReviewDTO.setSiteId(siteId);
                                        productSkuReviewDTO.setProductSkuPriceId(oldPrice.getId());
                                        productSkuReviewDTO.setReviewType(PriceOperateLog.Update.name());
                                        productSkuReviewDTO.addField("msrp", NumberUtil.toStr(oldPrice.getMsrp()), "0");
                                        productSkuReviewDTO.addField("pickUpPrice", NumberUtil.toStr(oldPrice.getOriginalPickUpPrice()), NumberUtil.toStr(newPrice.getOriginalPickUpPrice()));
                                        productSkuReviewDTO.addField("dropShippingPrice", NumberUtil.toStr(oldPrice.getOriginalDropShippingPrice()), NumberUtil.toStr(newPrice.getOriginalDropShippingPrice()));
                                        productSkuReviewDTO.addField("unitPrice", NumberUtil.toStr(oldPrice.getOriginalUnitPrice()), NumberUtil.toStr(newPrice.getOriginalUnitPrice()));
                                        productSkuReviewDTO.addField("operationFee", NumberUtil.toStr(oldPrice.getOriginalOperationFee()), NumberUtil.toStr(newPrice.getOriginalOperationFee()));
                                        productSkuReviewDTO.addField("finalDeliveryFee", NumberUtil.toStr(oldPrice.getOriginalFinalDeliveryFee()), NumberUtil.toStr(newPrice.getOriginalFinalDeliveryFee()));

                                        List<ProductSkuReviewDTO> productSkuReviewDTOS = CollUtil.newArrayList(productSkuReviewDTO);
                                        //添加审核数据
                                        ProductReviewDTO productReviewDTO = new ProductReviewDTO();
                                        productReviewDTO.setProductCode(productCode);
                                        productReviewDTO.setSubmitTenantId(LoginHelper.getTenantId());
                                        productReviewDTO.setChangeFields(SPUChangeFields);
                                        productReviewDTO.setProductSkuReviewList(productSkuReviewDTOS);
                                        productReviewDTO.setReviewType(ProductReviewTypeEnum.Price);
                                        productReviewDTO.setReviewStatus(ProductVerifyStateEnum.Pending);
                                        productReviewDTOs.add(productReviewDTO);
//                                    productSku.setVerifyState(ProductVerifyStateEnum.Pending);
                                    } else { //价格未变动，无需修改价格信息， 返回响应相信：商品价格未变动
                                        throw new ExcelMessageException(ExcelMessageEnum.PRODUCT_PRICE_NOT_CHANGE.buildLocalMessage(showRowIndex));
//                                    localeMessage.appendSurround(ExcelMessageEnum.PRODUCT_PRICE_NOT_CHANGE.buildLocalMessage(showRowIndex), "<p>", "</p></br>");
                                    }
                                }
                                skuShelfState = productSku.getShelfState();
                            }
                        }
                        productSku.setShelfState(skuShelfState);
                        validSkuList.add(productSku);
                    }

                    if (localeMessage.hasData()) {
                        throw new ExcelMessageException(localeMessage);
                    }

                    //保存立即变更的商品价格
                    if (CollUtil.isNotEmpty(nowChangePrices)) {
                        iProductSkuPriceService.saveOrUpdateBatch(nowChangePrices);
                        for (ProductSkuPrice oldSkuPrice : oldPrices) {
                            iProductSkuPriceLogService.recordPriceChanges(oldSkuPrice, PriceOperateLog.Update.name());
                        }
                    }
                    //保存商品信息
                    if (CollUtil.isNotEmpty(validProductList)) {
                        iProductService.saveOrUpdateBatch(validProductList);
                    }
                    if (CollUtil.isNotEmpty(validSkuList)) {
                        iProductSkuService.saveOrUpdateBatch(validSkuList);
                    }
                    //保存审核信息  根据产品维度进行去重,生成了多个审批记录
                    // productReviewDTOs内的数据筛选,1.如果有相同的productCode,则合并productSkuReviewList
                    Map<String, ProductReviewDTO> productReviewDTOMap = productReviewDTOs.stream()
                                                                                         .collect(Collectors.toMap(
                                                                                             ProductReviewDTO::getProductCode,
                                                                                             dto -> dto,
                                                                                             (dto1, dto2) -> {
                                                                                                 dto1.getProductSkuReviewList().addAll(dto2.getProductSkuReviewList());
                                                                                                 return dto1;
                                                                                             }
                                                                                         ));
                    List<ProductReviewDTO> reviewDTOS = new ArrayList<>(productReviewDTOMap.values());
                    for (ProductReviewDTO reviewDTO : reviewDTOS) {
                        productReviewRecordService.generateReviewRecord(reviewDTO);
                    }
                }
            }
            return R.ok();
        }catch (ExcelMessageException e) {
            log.error("Exception error = {}", e.getMessage());
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return R.fail(e.getLocaleMessage().toMessage());
        }catch (RStatusCodeException e) {
            log.error("Exception error = {}", e.getStatusCode());
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return R.fail(e.getStatusCode());
        }catch (Exception e) {
            log.error("Exception error = {}", e.getMessage(), e);
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return R.fail();
        }
    }

    @Override
    public ProductSkuPrice getProductSkuPriceByCode(String productSkuCode, Long siteId) {
        return iProductSkuPriceService.getOne(new LambdaQueryWrapper<ProductSkuPrice>()
            .eq(ProductSkuPrice::getProductSkuCode,productSkuCode)
            .eq(ProductSkuPrice::getSiteId,siteId)
            .eq(ProductSkuPrice::getDelFlag,0));
    }
    @Override
    public ProductSkuPrice getProductSkuPriceByCodeAndCountryCode(String productSkuCode,String countryCode) {
        return iProductSkuPriceService.getOne(new LambdaQueryWrapper<ProductSkuPrice>().eq(ProductSkuPrice::getProductSkuCode,productSkuCode).eq(ProductSkuPrice::getCountryCode,countryCode).eq(ProductSkuPrice::getDelFlag,0));
    }

    @Override
    public List<SiteBo> getSitePrice() {
        LambdaQueryWrapper<SiteCountryCurrency> wrapper = new LambdaQueryWrapper<SiteCountryCurrency>().eq(SiteCountryCurrency::getDelFlag, 0);
        List<SiteCountryCurrency> siteCountryCurrencies = iSiteCountryCurrencyService.list(wrapper);
        List<SiteBo> productSitePriceBos = new ArrayList<>();
        for (SiteCountryCurrency siteCountryCurrency : siteCountryCurrencies) {
            SiteBo siteBo = new SiteBo();
            siteBo.setSiteId(siteCountryCurrency.getId());
            siteBo.setCurrencySymbol(siteCountryCurrency.getCurrencySymbol());
            siteBo.setCountryCode(siteCountryCurrency.getCountryCode());
            siteBo.setCountryName(siteCountryCurrency.getCountryName());
            siteBo.setCurrencyName(siteCountryCurrency.getCountryName());
            siteBo.setCurrencyCode(siteCountryCurrency.getCurrencyCode());

            productSitePriceBos.add(siteBo);
        }
        return productSitePriceBos;
    }

    /**
     * 功能描述：初始化价格和站点(非会员)
     * 1.将所有基础表单清洗，生成站点信息,价格信息,币种符号信息(product_sku_price,rule_level_price,...)
     * 2.将所有业务价格表单清洗,生成站点信息,价格信息,币种符号信息(orders,order_items,order_item_price,...)
     * 3.默认清洗
     * 4.站点对应价格清洗
     * <AUTHOR>
     * @date 2024/12/23
     */
    @Override
    @IsDuplication
    public Boolean initializePricesAndSites(List<SitePriceCleanBo> sitePriceCleanBos) {

        // 1.对应的基础表单先清洗 2.清洗对应产品的订单站点信息 3.对应产品的价格信息不需要先清洗,历史订单的价格信息不需要清洗 4.非指定使用默认站点(建议分开做)
//        List<SiteCountryCurrency> siteCountryCurrencies = iSiteCountryCurrencyService.list();
//        // siteCountryCurrencies 转换成map,key是siteId,value是SiteCountryCurrency
//        Map<Long, SiteCountryCurrency> siteCountryCurrencyMap = siteCountryCurrencies.stream()
//                                                                                     .collect(Collectors.toMap(SiteCountryCurrency::getId, Function.identity()));
//
//        List<ProductSkuPrice> productSkuPrices = new ArrayList<>();
//        for (SitePriceCleanBo sitePriceCleanBo : sitePriceCleanBos) {
//            List<String> itemNos = sitePriceCleanBo.getItemNos();
//            Long siteId = sitePriceCleanBo.getSiteId();
//            SiteCountryCurrency currency = siteCountryCurrencyMap.get(siteId);
//            for (String itemNo : itemNos) {
//                ProductSku productSku = iProductSkuService.queryByProductSkuCode(itemNo);
//                if (productSku == null) {
//                    log.error("商品SKU不存在, itemNo = {}", itemNo);
//                    continue;
//                }
//                // 产品价格清洗
//                ProductSkuPrice productSkuPrice = iProductSkuPriceService.queryByProductSkuCode(itemNo);
//                if (ObjectUtil.isNotEmpty(productSkuPrice)) {
//                    productSkuPrice.setCountryCode(currency.getCountryCode());
//                    productSkuPrice.setCurrency(currency.getCurrencyCode());
//                    productSkuPrice.setCurrencySymbol(currency.getCurrencySymbol());
//                    productSkuPrice.setSiteId(siteId);
//                    productSkuPrices.add(productSkuPrice);
//                }
//
//            }
//        }
//        // 指定:产品-站点价格清洗
//        iProductSkuPriceService.updateBatchById(productSkuPrices);
//        // 指定:产品关联订单站点-符号-币种-国家清洗 orders order_items order_item_price

        // 默认:产品-站点价格清洗
        // 默认:产品-站点价格生成
        return Boolean.TRUE;

    }

    @Override
    public void exportAsync(ProductPriceBo bo, PageQuery pageQuery, HttpServletResponse response) {
        String tenantId = LoginHelper.getTenantId();
        String queryType = bo.getQueryType();
        String queryValue = StrUtil.isBlank(bo.getQueryValue()) ? null : StrUtil.trim(bo.getQueryValue());
        List<String> itemNos = bo.getItemNos();
        Integer pageSize = 1;
        Integer pageNum = Integer.MAX_VALUE;

        if (StrUtil.isBlank(queryType) || StrUtil.isBlank(queryValue)) {
            queryType = null;
            queryValue = null;
        }
        Page<ProductSku> page = new Page<>(pageNum, pageSize);
        IPage<ProductSku> productSkuPage = iProductSkuService.getQueryCount(queryType, queryValue, page, bo.getSiteId(), itemNos,bo.getSkuAuditStatus());
        Integer rowCount = Math.toIntExact(productSkuPage.getTotal());
        if(rowCount==0){
            throw new RuntimeException("当前查询条件无商品数据,请重新输入");
        }
        Locale locale = ServletUtils.getHeaderLocale();
        LoginUser loginUser = LoginHelper.getLoginUser();
        new Thread(() -> {
            try {
                exportAsync(bo, response, locale,loginUser);
            } catch (Exception e) {
                // 处理异常，记录日志或执行其他恢复操作
                log.error("导出商品列表异常", e);
            }
        }).start();
    }
    @Override
    public void exportNotAsync(ProductPriceBo bo, PageQuery pageQuery, HttpServletResponse response) {
        String tenantId = LoginHelper.getTenantId();
        String queryType = bo.getQueryType();
        String queryValue = StrUtil.isBlank(bo.getQueryValue()) ? null : StrUtil.trim(bo.getQueryValue());
        List<String> itemNos = bo.getItemNos();
        Integer pageSize = 1;
        Integer pageNum = Integer.MAX_VALUE;


        if (StrUtil.isBlank(queryType) || StrUtil.isBlank(queryValue)) {
            queryType = null;
            queryValue = null;
        }
        Page<ProductSku> page = new Page<>(pageNum, pageSize);
        IPage<ProductSku> productSkuPage = iProductSkuService.getQueryCount(queryType, queryValue, page, bo.getSiteId(),itemNos, bo.getSkuAuditStatus());
        Integer rowCount = Math.toIntExact(productSkuPage.getTotal());
        if(rowCount==0){
            throw new RuntimeException("当前查询条件无商品数据,请重新输入");
        }
        exportNotAsync(bo, response);
    }
    private void exportAsync(ProductPriceBo bo, HttpServletResponse response, Locale locale, LoginUser loginU) {
        Runnable task = () -> {
            try {
                Locale headerLocale = LoginContextLocalHolder.getLocaleInfo();

                LoginUser loginUser = LoginContextLocalHolder.getLoginInfo();
                String tenantId = loginUser.getTenantId();
                String fileName = StrUtil.format(FileNameConstants.PRODUCT_PRICE_EXPORT, DateUtil.format(new Date(), "yyMMdd-HHmmssSS"));
                DownloadRecordV2Util.generate(fileName, DownloadTypePlusEnum.ProductPriceExport, tempFileSavePath -> {
                    ConcurrentLinkedQueue<ProductPriceExportDto> productExportDtoList = new ConcurrentLinkedQueue<>();
                    try {
                        // 线上目前最大导出数据量为 580896 条 , 后续为了防止内存溢出，可以分页导出,查询压力并不大
                        List<ProductPriceExportDto> pageData = getProductPriceExportDto(bo, tenantId);
                        productExportDtoList.addAll(pageData);
                    } catch (Exception e) {
                        log.error("数据处理异常",e);
                    }

                    List<ProductPriceExportDto> sortedData = new ArrayList<>(productExportDtoList);

                    File tempFile = new File(tempFileSavePath);
                    BufferedOutputStream outputStream = FileUtil.getOutputStream(tempFile);
                    com.hengjian.common.excel.utils.ExcelUtil.exportExcelWithLocale(sortedData, "Product Price list", ProductPriceExportDto.class, false, outputStream, headerLocale);
                    IoUtil.close(outputStream);
                    return tempFile;
                });
            } catch (Exception e) {
                // 处理异常，记录日志或执行其他恢复操作
                log.error("导出商品列表异常", e);
            }
        };
        HeaderLocaleRunnable runnable = new HeaderLocaleRunnable(loginU, task, locale);
        executor.submit(runnable);
        try {
            executor.awaitTermination(10, TimeUnit.MINUTES);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }
    private void exportNotAsync(ProductPriceBo bo, HttpServletResponse response) {
        try {
            Locale headerLocale = ServletUtils.getHeaderLocale();

//            LoginUser loginUser = LoginContextLocalHolder.getLoginInfo();
            String tenantId = LoginHelper.getTenantId();
            String fileName = StrUtil.format(FileNameConstants.PRODUCT_PRICE_EXPORT, DateUtil.format(new Date(), "yyMMdd-HHmmssSS"));
            DownloadRecordV2Util.generate(fileName, DownloadTypePlusEnum.ProductPriceExport, tempFileSavePath -> {
                ConcurrentLinkedQueue<ProductPriceExportDto> productExportDtoList = new ConcurrentLinkedQueue<>();
                try {
                    List<ProductPriceExportDto> pageData = getProductPriceExportDto(bo, tenantId);
                    productExportDtoList.addAll(pageData);
                } catch (Exception e) {
                    log.error("数据处理异常",e);
                }

                List<ProductPriceExportDto> sortedData = new ArrayList<>(productExportDtoList);

                File tempFile = new File(tempFileSavePath);
                BufferedOutputStream outputStream = FileUtil.getOutputStream(tempFile);
                com.hengjian.common.excel.utils.ExcelUtil.exportExcelWithLocale(sortedData, "Product Price list", ProductPriceExportDto.class, false, outputStream, headerLocale);
                IoUtil.close(outputStream);
                return tempFile;
            });
        } catch (Exception e) {
            // 处理异常，记录日志或执行其他恢复操作
            log.error("导出商品列表异常", e);
        }
    }
    private List<ProductPriceExportDto> getProductPriceExportDto(ProductPriceBo bo, String tenantId) {
        List<SiteCountryCurrency> siteCountryCurrencies = iSiteCountryCurrencyService.list();
        Map<Long, SiteCountryCurrency> currencyMap = siteCountryCurrencies.stream()
                                                                            .collect(Collectors.toMap(SiteCountryCurrency::getId, Function.identity()));
        List<ProductPriceExportDto> productPriceExportDtos = iProductSkuService.getProductPriceExportDto(bo, tenantId);
        // 过滤掉productPriceExportDtos内sku为null的数据
        productPriceExportDtos = productPriceExportDtos.stream()
                                                       .filter(dto -> ObjectUtil.isNotEmpty(dto.getSku()))
                                                       .collect(Collectors.toList());
        for (ProductPriceExportDto productPriceExportDto : productPriceExportDtos) {
            Long siteId = productPriceExportDto.getSiteId();
            SiteCountryCurrency currency = currencyMap.get(siteId);
            String s = currency.getCountryCode() + "/" + currency.getCurrencyCode();
            productPriceExportDto.setCountryCodeAndCurrencyCode(s);
        }
        return productPriceExportDtos;
    }
}
