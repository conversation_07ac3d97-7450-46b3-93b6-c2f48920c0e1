package com.zsmall.product.biz.service;

import com.hengjian.common.core.domain.R;
import com.hengjian.common.mybatis.core.page.PageQuery;
import com.hengjian.common.mybatis.core.page.TableDataInfo;
import com.zsmall.common.domain.delivery.DeliveryFeeStock;
import com.zsmall.common.domain.dto.OrderReceiveFromThirdDTO;
import com.zsmall.common.domain.dto.SaleOrderItemDTO;
import com.zsmall.common.domain.dto.stock.AdjustStockDTO;
import com.zsmall.common.enums.order.LogisticsTypeEnum;
import com.zsmall.common.exception.StockException;
import com.zsmall.product.entity.domain.ProductSku;
import com.zsmall.product.entity.domain.ProductSkuStock;
import com.zsmall.product.entity.domain.bo.productSkuStock.StockEditBo;
import com.zsmall.product.entity.domain.bo.productSkuStock.StockListBo;
import com.zsmall.product.entity.domain.bo.productSkuStock.StockStateSwitchBo;
import com.zsmall.product.entity.domain.vo.productSkuStock.SkuStockInfoVo;
import com.zsmall.product.entity.domain.vo.productSkuStock.StockValidWarehouseSelectVo;
import org.springframework.web.multipart.MultipartFile;

import java.util.HashMap;
import java.util.List;

/**
 * 商品SKU库存Service接口
 *
 * <AUTHOR> Li
 * @date 2023-05-26
 */
public interface ProductSkuStockService {

    /**
     * 批量调整库存
     */
    void adjustStock(List<AdjustStockDTO> dtoList) throws StockException;

    /**
     * 调整库存
     */
    String adjustStock(AdjustStockDTO dto, String orderNo) throws StockException;

    /**
     * 查询Sku库存列表
     */
    TableDataInfo<SkuStockInfoVo> queryProductSkuStockList(StockListBo bo, PageQuery pageQuery);

    /**
     * 编辑库存数量
     */
    R<Void> editStockQuantity(StockEditBo bo);

    /**
     * 切换库存状态
     */
    R<Void> switchStockState(StockStateSwitchBo bo);

    /**
     * 查询库存可用仓库
     */
    R<List<StockValidWarehouseSelectVo>> queryValidWarehouseList(String stockCode);

    /**
     * 上传库存Excel
     */
    R<Void> uploadStockExcel(MultipartFile file) throws Exception;

    /**
     * 功能描述：获取待交付库存
     *
     * @param regionCode       地区代码
     * @param productSkuCode   产品sku代码
     * @param logisticsType    物流类型
     * @param quantity         量
     * @param logisticsAccount 物流账户
     * @param postalCode       邮政编码
     * @param carrier
     * @param dTenantId     分销商ID
     * @param sTenantId     供应商ID
     * @return {@link ProductSkuStock }
     * <AUTHOR>
     * @date 2024/06/14
     */
    ProductSkuStock getStockForDeliver(String regionCode, String productSkuCode, LogisticsTypeEnum logisticsType, Integer quantity, Boolean logisticsAccount, String postalCode, String carrier,
                                       String dTenantId, String sTenantId);


    String getWarehouseSystemCode(ProductSku productSku, OrderReceiveFromThirdDTO orderReceiveFromThirdDTO, SaleOrderItemDTO orderItemDTO, Boolean aFalse);

    HashMap<String, List<String>> getStashList(List<DeliveryFeeStock>deliveryFeeStocks);

    String adjustStockOnlyErp(AdjustStockDTO dto, String orderNo) throws StockException;

    /**
     * 下载中心导出
     *
     * @param bo
     */
    void export(StockListBo bo);
}
