package com.zsmall.product.biz.service;

import cn.hutool.json.JSONObject;
import com.hengjian.common.core.domain.R;
import com.zsmall.product.entity.domain.bo.product.ReqSkuMappingPageBody;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * 商品映射相关-Service
 * <AUTHOR>
 * @create 2021/12/31 11:23
 */
public interface MappingService {

	/**
	 * 上传映射配置Excel
	 * @param request
	 * @return
	 */
	R<JSONObject> uploadMappingExcel(HttpServletRequest request) throws Exception;

	/**
	 * 下载映射配置Excel
	 * @param apiRequest
	 * @param response
	 */
	R<Void> downloadMappingExcel(ReqSkuMappingPageBody apiRequest, HttpServletResponse response);

}
