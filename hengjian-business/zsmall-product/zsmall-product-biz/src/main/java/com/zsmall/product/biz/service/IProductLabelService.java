package com.zsmall.product.biz.service;

import com.hengjian.common.core.domain.R;
import com.hengjian.common.mybatis.core.page.PageQuery;
import com.hengjian.common.mybatis.core.page.TableDataInfo;
import com.zsmall.product.entity.domain.ProductLabel;
import com.zsmall.product.entity.domain.bo.ProductLabelBo;
import com.zsmall.product.entity.domain.bo.label.ReqLabelBindingBatchBody;
import com.zsmall.product.entity.domain.bo.label.ReqLabelBindingBody;
import com.zsmall.product.entity.domain.bo.product.ReqProductBody;
import com.zsmall.product.entity.domain.vo.ProductLabelVo;
import com.zsmall.product.entity.domain.vo.product.ProductToLabelBody;

import java.util.Collection;
import java.util.List;

/**
 * 标签Service接口
 *
 * <AUTHOR> Li
 * @date 2023-05-31
 */
public interface IProductLabelService {

    /**
     * 查询标签
     */
    ProductLabelVo queryById(Long id);

    /**
     * 查询标签列表
     */
    TableDataInfo<ProductLabelVo> queryPageList(ProductLabelBo bo, PageQuery pageQuery);

    /**
     * 查询标签列表
     */
    List<ProductLabelVo> queryList(ProductLabelBo bo);

    /**
     * 查询所有标签，根据序号升序
     */
    List<ProductLabel> queryAllOrderBySort();

    /**
     * 根据标签名查询
     */
    ProductLabel queryByLabelName(String labelName);

    /**
     * 新增标签
     */
    Boolean insertByBo(ProductLabelBo bo) throws Exception;

    /**
     * 修改标签
     */
    Boolean updateByBo(ProductLabelBo bo) throws Exception;

    /**
     * 校验并批量删除标签信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 更新标签排序
     *
     * @param bo
     * @return
     */
    boolean updateSort(ProductLabelBo bo);

    /**
     * 商品绑定标签
     *
     * @param requestBody
     * @return
     * @throws Exception
     */
    R<Void> binding(ReqLabelBindingBody requestBody) throws Exception;

    /**
     * 商品批量绑定标签
     *
     * @param requestBody
     * @return
     */
    R<Void> bindingBatch(ReqLabelBindingBatchBody requestBody) throws Exception;

    /**
     * 商品批量解绑标签
     *
     * @param requestBody
     * @return
     */
    R<Void> unbindBatch(ReqLabelBindingBatchBody requestBody);

    /**
     * 翻页查询商品列表（标签绑定）
     *
     * @param bo
     * @param pageQuery
     * @return
     */
    TableDataInfo<ProductToLabelBody> getProductPageToLabel(ReqProductBody bo, PageQuery pageQuery);

}
