package com.zsmall.product.biz.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zsmall.common.annotaion.Manager;
import com.zsmall.product.biz.service.RuleLevelProductBizPriceService;
import com.zsmall.product.entity.domain.RuleLevelProductPrice;
import com.zsmall.product.entity.mapper.RuleLevelProductPriceMapper;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2024/5/21 18:45
 */

@Manager
@Deprecated
public class IRuleLevelProductPriceManager extends ServiceImpl<RuleLevelProductPriceMapper, RuleLevelProductPrice> implements RuleLevelProductBizPriceService {
}
