package com.zsmall.product.biz.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.zsmall.product.biz.service.IProductLabelRelationService;
import com.zsmall.product.entity.domain.ProductLabel;
import com.zsmall.product.entity.domain.ProductLabelRelation;
import com.zsmall.product.entity.mapper.ProductLabelMapper;
import com.zsmall.product.entity.mapper.ProductLabelRelationMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 商品标签关联Service业务层处理
 *
 * <AUTHOR> Li
 * @date 2023-05-31
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class ProductLabelRelationServiceImpl implements IProductLabelRelationService {

    private final ProductLabelRelationMapper baseMapper;
    private final ProductLabelMapper productLabelMapper;

    @Override
    public void removeByProductId(Long productId) {
        LambdaQueryWrapper<ProductLabelRelation> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProductLabelRelation::getProductId, productId);
        baseMapper.delete(queryWrapper);
    }

    @Override
    public void saveBatch(List<ProductLabelRelation> relationEntityList) {
        if (CollUtil.isNotEmpty(relationEntityList)) {
            for (ProductLabelRelation productLabelRelation : relationEntityList) {
                baseMapper.insert(productLabelRelation);
            }
        }
    }

    @Override
    public ProductLabelRelation getByProductIdAndLabelId(Long productId, Long labelId) {
        log.info("进入【查询商品是否绑定了标签】方法, productId = {}, labelId = {}", productId, labelId);
        LambdaQueryWrapper<ProductLabelRelation> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProductLabelRelation::getProductId, productId).eq(ProductLabelRelation::getLabelId, labelId);
        return baseMapper.selectOne(queryWrapper);
    }

    @Override
    public void removeByLabelIdAndProductId(Long labelId, Long productId) {
        log.info("进入【根据标签id商品id解除关联关系】方法, labelId = {}, productId = {}", labelId, productId);
        LambdaQueryWrapper<ProductLabelRelation> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProductLabelRelation::getLabelId, labelId).eq(ProductLabelRelation::getProductId, productId);
        baseMapper.delete(queryWrapper);
    }

    @Override
    public List<ProductLabel> getLabelByProductId(Long productId) {
        log.info("进入【查询商品标签】方法, productId = {}", productId);
        LambdaQueryWrapper<ProductLabelRelation> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProductLabelRelation::getProductId, productId).eq(ProductLabelRelation::getDelFlag, 0);
        List<ProductLabelRelation> relationList = baseMapper.selectList(queryWrapper);
        if (CollUtil.isEmpty(relationList)) {
            return new ArrayList<>();
        }
        List<Long> labelIdList = relationList.stream().map(ProductLabelRelation::getLabelId).collect(Collectors.toList());
        List<ProductLabel> labelList = productLabelMapper.selectBatchIds(labelIdList);
        log.info("labelList = {}", JSONUtil.toJsonStr(labelList));
        return labelList;
    }

    @Override
    public void removeByLabelIdList(List<Long> labelIdList) {
        log.info("进入【移除商品绑定的标签】方法, labelIdList = {}", JSONUtil.toJsonStr(labelIdList));
        LambdaQueryWrapper<ProductLabelRelation> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(ProductLabelRelation::getLabelId, labelIdList);
        baseMapper.delete(queryWrapper);
    }

    @Override
    public List<ProductLabelRelation> getByLabelIdList(List<Long> labelIdList) {
        log.info("进入【根据标签id集合查询绑定到商品】方法, labelIdList = {}", JSONUtil.toJsonStr(labelIdList));
        LambdaQueryWrapper<ProductLabelRelation> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(ProductLabelRelation::getLabelId, labelIdList);
        return baseMapper.selectList(queryWrapper);
    }

    @Override
    public List<Long> getProductIdListByLabelId(Long labelId) {
        log.info("进入【根据标签id查询商品id】方法, labelId = {}", labelId);
        LambdaQueryWrapper<ProductLabelRelation> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProductLabelRelation::getLabelId, labelId);
        List<ProductLabelRelation> list = baseMapper.selectList(queryWrapper);
        return list.stream().map(ProductLabelRelation::getProductId).collect(Collectors.toList());
    }


}
