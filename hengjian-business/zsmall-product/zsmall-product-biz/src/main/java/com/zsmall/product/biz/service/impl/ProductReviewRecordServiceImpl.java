package com.zsmall.product.biz.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hengjian.common.core.domain.R;
import com.hengjian.common.core.enums.TenantType;
import com.hengjian.common.core.exception.RStatusCodeException;
import com.hengjian.common.core.service.UserService;
import com.hengjian.common.mybatis.core.page.PageQuery;
import com.hengjian.common.mybatis.core.page.TableDataInfo;
import com.hengjian.common.satoken.holder.LoginContextHolder;
import com.hengjian.common.satoken.utils.LoginHelper;
import com.hengjian.common.tenant.helper.TenantHelper;
import com.zsmall.common.domain.dto.ChangeFieldDTO;
import com.zsmall.common.enums.common.ChannelTypeEnum;
import com.zsmall.common.enums.priceLog.PriceOperateLog;
import com.zsmall.common.enums.product.*;
import com.zsmall.common.enums.statuscode.ZSMallStatusCodeEnum;
import com.zsmall.extend.utils.ZSMallSystemEventUtils;
import com.zsmall.product.biz.service.ProductReviewChangeDetailService;
import com.zsmall.product.biz.service.ProductReviewRecordService;
import com.zsmall.product.biz.service.ProductSkuPriceRuleService;
import com.zsmall.product.biz.service.ProductSkuService;
import com.zsmall.product.biz.support.EsProductSupport;
import com.zsmall.product.biz.support.ProductSupport;
import com.zsmall.product.entity.domain.*;
import com.zsmall.product.entity.domain.bo.ProductReviewRecordBo;
import com.zsmall.product.entity.domain.bo.SubmitReviewRecordBo;
import com.zsmall.product.entity.domain.bo.productSku.ProductSkuReviewBo;
import com.zsmall.product.entity.domain.dto.product.ProductReviewDTO;
import com.zsmall.product.entity.domain.dto.product.ProductReviewPageDTO;
import com.zsmall.product.entity.domain.dto.productSku.ProductSkuBody;
import com.zsmall.product.entity.domain.dto.productSku.ProductSkuReviewDTO;
import com.zsmall.product.entity.domain.dto.productSkuPrice.ProductPriceChangeDto;
import com.zsmall.product.entity.domain.vo.ProductReviewChangeDetailVo;
import com.zsmall.product.entity.domain.vo.ProductReviewRecordVo;
import com.zsmall.product.entity.domain.vo.ProductSkuAttachmentVo;
import com.zsmall.product.entity.domain.vo.productReview.ProductReviewPageVo;
import com.zsmall.product.entity.domain.vo.productSkuPrice.ProductSkuPriceVo;
import com.zsmall.product.entity.iservice.*;
import com.zsmall.system.entity.domain.SiteCountryCurrency;
import com.zsmall.system.entity.iservice.ISiteCountryCurrencyService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 商品审核记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-05-31
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class ProductReviewRecordServiceImpl implements ProductReviewRecordService {

    private final IRuleLevelProductPriceService iRuleLevelProductPriceService;
    private final IProductReviewRecordService iProductReviewRecordService;
    private final IProductService iProductService;
    private final IProductAttributeService iProductAttributeService;
    private final ProductReviewChangeDetailService productReviewChangeDetailService;
    private final IProductReviewChangeDetailService iProductReviewChangeDetailService;
    private final ProductSkuService productSkuService;
    private final IProductSkuService iProductSkuService;
    private final IProductSkuStockService iProductSkuStockService;
    private final IProductWholesaleTieredPriceService iProductWholesaleTieredPriceService;
    private final IProductWholesaleTieredPriceLogService iProductWholesaleTieredPriceLogService;
    private final IProductSkuWholesalePriceService iProductSkuWholesalePriceService;
    private final IProductSkuWholesalePriceLogService iProductSkuWholesalePriceLogService;
    private final IProductSkuPriceService iProductSkuPriceService;
    private final ProductSkuPriceRuleService iProductSkuPriceRuleService;
    private final IProductSkuAttachmentService iProductSkuAttachmentService;
    private final UserService userService;
    private final ProductSupport productSupport;
    private final IProductSkuPriceLogService iProductSkuPriceLogService;
    private final IProductMappingService iProductMappingService;
    private final ITaskSkuPriceChangeService iTaskSkuPriceChangeService;
    private final IProductWholesaleDetailService iProductWholesaleDetailService;
    private final ISiteCountryCurrencyService iSiteCountryCurrencyService;
    private final EsProductSupport esProductSupport;

    /**
     * 查询商品审核记录列表
     */
    @Override
    public TableDataInfo<ProductReviewPageVo> queryPageList(ProductReviewRecordBo bo, PageQuery pageQuery) throws ExecutionException, InterruptedException {
        String reviewStatus = bo.getReviewStatus();
        String reviewType = bo.getReviewType();
        String queryType = bo.getQueryType();
        String queryValue = bo.getQueryValue();
        Long siteId = bo.getSiteId();
        ProductReviewPageDTO productReviewPageDTO = new ProductReviewPageDTO();
        productReviewPageDTO.setQueryValue(queryValue);
        productReviewPageDTO.setQueryType(queryType);
        productReviewPageDTO.setVerifyType(reviewType);
        productReviewPageDTO.setVerifyState(reviewStatus);
        productReviewPageDTO.setSiteId(siteId);
        Page<ProductReviewRecordVo> reviewRecordPage = iProductReviewRecordService.queryPage(pageQuery.build(), productReviewPageDTO);
        List<ProductReviewRecordVo> records = reviewRecordPage.getRecords();
        List<ProductReviewPageVo> results = new ArrayList<>();
        List<SiteCountryCurrency> siteCountryCurrencies = iSiteCountryCurrencyService.list();
        Map<Long, SiteCountryCurrency> siteCountryCurrencyMap = siteCountryCurrencies.stream()
                                                                      .collect(Collectors.toMap(SiteCountryCurrency::getId, Function.identity()));
        if (CollUtil.isNotEmpty(records)) {
            for (ProductReviewRecordVo record : records) {
                log.info("record = {}", JSONUtil.toJsonStr(record));
                Long recordEntityId = record.getId();
                String productCode = record.getProductCode();
                ProductReviewTypeEnum recordReviewType = record.getReviewType();
                Product p = iProductService.queryByProductCodeNotDelete(productCode);
                ProductVerifyStateEnum verifyState = p.getVerifyState();
                ProductTypeEnum productType = p.getProductType();
                ProductReviewPageVo reviewPageVo = new ProductReviewPageVo();

                reviewPageVo.setTenantID(record.getTenantID());
                reviewPageVo.setReviewRecordId(recordEntityId);
                reviewPageVo.setProductCode(productCode);
                reviewPageVo.setProductType(productType.name());
                reviewPageVo.setName(p.getName());
                reviewPageVo.setReviewStatus(record.getReviewState().name());
                reviewPageVo.setSubmitDateTime(DateUtil.format(record.getSubmitDateTime(), "yyyy-MM-dd HH:mm:ss"));
                reviewPageVo.setReviewDateTime(DateUtil.format(record.getReviewDateTime(), "yyyy-MM-dd HH:mm:ss"));
                reviewPageVo.setReviewType(recordReviewType.name());

                ProductSkuAttachmentVo attachmentVo = iProductSkuAttachmentService.queryFirstImageByProductId(p.getId());
                if (ObjectUtil.isNotEmpty(attachmentVo)) {
                    reviewPageVo.setProductImageShowUrl(attachmentVo.getAttachmentShowUrl());
                }

                List<ProductReviewChangeDetail> spuChangeDetails =
                    iProductReviewChangeDetailService.queryDetail(recordEntityId, productCode,
                        null, "supportedLogistics",siteId);
                if (CollUtil.isNotEmpty(spuChangeDetails)) {
                    for (ProductReviewChangeDetail spuChangeDetail : spuChangeDetails) {
                        String fieldValueBefore = spuChangeDetail.getFieldValueBefore();
                        String fieldValueAfter = spuChangeDetail.getFieldValueAfter();
                        reviewPageVo.setSupportedLogistics(fieldValueAfter);
                        reviewPageVo.setSupportedLogisticsBefore(fieldValueBefore);
                        reviewPageVo.setSupportedLogisticsAfter(fieldValueAfter);
                    }
                }

                List<ProductSku> productSkus = iProductSkuService.queryByProductIdNotDelete(p.getId());
                List<ProductSkuBody> skuList = new ArrayList<>();

                for (ProductSku sku : productSkus) {
                    String productSkuCode = sku.getProductSkuCode();
                    ArrayList<ProductSkuReviewBo> productSkuReviewBos = new ArrayList<>();
                    List<ProductReviewChangeDetailVo> changeDetails = iProductReviewChangeDetailService
                        .queryByReviewRecordIdAndCode(recordEntityId, productCode, productSkuCode,siteId);
                    if (CollUtil.isEmpty(changeDetails)) {
                        continue;
                    }

                    ProductSkuBody skuBody = new ProductSkuBody();
                    String skuCode = sku.getSku();
                    BigDecimal originalUnitPrice = BigDecimal.ZERO;
                    BigDecimal originalOperationFee = BigDecimal.ZERO;
                    BigDecimal originalDropShippingPrice = BigDecimal.ZERO;
                    //满足条件则为国外现货批发商品, 否则为正常商品 目前没有国内现货逻辑
                    if (ObjectUtil.isNotNull(productType) && ObjectUtil.equals(productType, ProductTypeEnum.WholesaleProduct)) {
                        List<ProductWholesaleTieredPrice> wts = iProductWholesaleTieredPriceService.queryListByProductId(p.getId());
                        if (CollUtil.size(wts) > 0) {
                            ProductSkuWholesalePrice wp = iProductSkuWholesalePriceService.getOneByProductSkuIdAndTieredPriceId(sku.getId(), wts.get(0).getId());
                            log.info("ProductSkuWholesalePriceVo = {}", JSONUtil.toJsonStr(wp));
                            if (ObjectUtil.isNotEmpty(wp)) {
                                BigDecimal originUnitPrice = wp.getOriginUnitPrice();
                                BigDecimal platformUnitPrice = wp.getPlatformUnitPrice();
                                originalUnitPrice = originUnitPrice;
                            }
                        }
                    } else {
                        // 此处是单记录的价格填充,要拿到对应的站点的修改记录

                        // changeDetails 转换成map,key是siteId,value是对应的List<ProductReviewChangeDetailVo>
                        Map<Long, List<ProductReviewChangeDetailVo>> detailsMap = changeDetails.stream()
                                                                                            .collect(Collectors.groupingBy(ProductReviewChangeDetailVo::getSiteId));


                        for (Map.Entry<Long, List<ProductReviewChangeDetailVo>> entry : detailsMap.entrySet()) {
                            List<ProductReviewChangeDetailVo> details = entry.getValue();
                            ProductReviewChangeDetailVo productReviewChangeDetailVo = details.get(0);
                            String skuReviewType = productReviewChangeDetailVo.getReviewType();

                            ProductSkuReviewBo productSkuReviewBo = new ProductSkuReviewBo();
                            productSkuReviewBo.setSiteId(productReviewChangeDetailVo.getSiteId());
                            // 下面三个从map里拿
                            SiteCountryCurrency currency = siteCountryCurrencyMap.get(productReviewChangeDetailVo.getSiteId());
                            productSkuReviewBo.setCurrencySymbol(currency.getCurrencySymbol());
                            productSkuReviewBo.setCountryCode(currency.getCountryCode());
                            productSkuReviewBo.setCurrency(currency.getCurrencyCode());
                            // todo 这里的reviewType的维度不一致 属性编辑不会产生审批,只有价格会 新增维度统一落到明细
                            productSkuReviewBo.setReviewType(productReviewChangeDetailVo.getReviewType());
                            productReviewChangeDetailService.setChangeToObject(productSkuReviewBo, details);
                            productSkuReviewBo.setReviewType(skuReviewType);
                            productSkuReviewBos.add(productSkuReviewBo);

                        }

                    }
                    // sku-site-details  sku:site[ one:more ] site:details[ one:more ]
                    skuBody.setSkuCode(skuCode);
                    skuBody.setItemNo(productSkuCode);
                    skuBody.setProductSkuReviewBos(productSkuReviewBos);
                    skuList.add(skuBody);
                }
                if(CollUtil.isEmpty(skuList)){
                    continue;
                }
                if (ObjectUtil.equals(verifyState, ProductVerifyStateEnum.Accepted) && (ObjectUtil.equals(recordReviewType, ProductReviewTypeEnum.Price))) {
                    TaskSkuPriceChange priceChange = iTaskSkuPriceChangeService.queryByReviewRecordId(recordEntityId);
                    if (priceChange != null) {
                        reviewPageVo.setEffectiveDate(priceChange.getExecuteDate());
                    }
                }

                String reviewOpinion = record.getReviewOpinion();
                JSONArray reviewOpinionOption = record.getReviewOpinionOption();
                reviewPageVo.setReviewOpinion(reviewOpinion);
                reviewPageVo.setSubmitUser(userService.selectUserNameByIdNoTenant(record.getSubmitUserId()));
                reviewPageVo.setReviewOpinionOption(reviewOpinionOption);
                reviewPageVo.setSkuList(skuList);
                results.add(reviewPageVo);
            }
        }
        return TableDataInfo.build(results, reviewRecordPage.getTotal());
    }

    /**
     * 审核通过
     */
    @Transactional(rollbackFor = {Exception.class, RStatusCodeException.class})
    @Override
    public R<Void> productReview(ProductReviewRecordBo bo) throws Exception {
//        LoginHelper.getLoginUser(TenantType.Manager);
        return this.batchReviewProduct(bo);
    }

    /**
     * 提交商品审核
     *
     * @param bo
     * @return
     */
    @Override
    @Transactional(rollbackFor = {Exception.class, RStatusCodeException.class})
    public R<Void> submitProductReview(SubmitReviewRecordBo bo) throws RStatusCodeException {
        LoginHelper.getLoginUser(TenantType.Supplier);
        String productCode = bo.getProductCode();
        if (StrUtil.isBlank(productCode)) {
            return R.fail(ZSMallStatusCodeEnum.REQUIRED_CANNOT_EMPTY);
        }
        extractedProductReview(productCode);
        return R.ok();
    }

    @Override
    public void extractedProductReview(String productCode) throws RStatusCodeException {
        Product product = iProductService.queryByProductCodeNotDelete(productCode);
        if (product != null) {
            Boolean existsed = iProductAttributeService.existRequiredAttribute(product.getId());
            if (existsed) {
                throw new RStatusCodeException(ZSMallStatusCodeEnum.PRODUCT_EXISTS_REQUIRED_SPEC);
            }

            List<ProductSku> saveProductSkuList = new ArrayList<>();
            ProductTypeEnum productType = product.getProductType();
            if (ObjectUtil.isNotNull(productType) && ObjectUtil.equals(productType, ProductTypeEnum.NormalProduct)) {
                this.submitNormalProductReview(product, saveProductSkuList);
            }
            if (ObjectUtil.isNotNull(productType) && ObjectUtil.equals(productType, ProductTypeEnum.WholesaleProduct)) {
                this.submitWholesaleProductReview(product, saveProductSkuList);
            }
            iProductService.saveOrUpdate(product);
            if (CollectionUtils.isNotEmpty(saveProductSkuList)) {
                iProductSkuService.saveOrUpdateBatch(saveProductSkuList);
            }
        } else {
            throw new RStatusCodeException(ZSMallStatusCodeEnum.PRODUCT_NOT_EXIST);
        }
    }

    /**
     * 提交审核（正常商品）
     *
     * @param product
     * @param saveProductSkuList
     * @return
     */
    public void submitNormalProductReview(Product product, List<ProductSku> saveProductSkuList) throws RStatusCodeException {
        ProductVerifyStateEnum verifyType = product.getVerifyState();
        if (ObjectUtil.equals(verifyType, ProductVerifyStateEnum.Accepted)) {
            throw new RStatusCodeException(ZSMallStatusCodeEnum.PRODUCT_ACCEPTED);
        }

        ProductReviewRecord productReviewRecord =
            iProductReviewRecordService.queryRecordByProductCodeAndReviewType(product.getProductCode(),
                ProductVerifyStateEnum.Draft);

        List<ProductSku> productSkus = iProductSkuService.queryByProductIdNotDelete(product.getId());

        if (ObjectUtil.isNull(productReviewRecord)) {
            List<ProductSkuReviewDTO> productSkuReviewList = new ArrayList<>();

            for (ProductSku sku : productSkus) {

                List<ProductSkuPriceVo> priceVos = iProductSkuPriceService.getSitePriceBySkuId(sku.getId());
                for (ProductSkuPriceVo priceVo : priceVos) {
                    BigDecimal originalUnitPrice = priceVo.getOriginalUnitPrice();
                    BigDecimal originalOperationFee = priceVo.getOriginalOperationFee();
                    BigDecimal originalFinalDeliveryFee = priceVo.getOriginalFinalDeliveryFee();
                    BigDecimal beforeMsrp = priceVo.getMsrp();
                    BigDecimal beforeDropShippingPrice = priceVo.getOriginalDropShippingPrice();
                    BigDecimal beforePickUpPrice = priceVo.getOriginalPickUpPrice();
                    // 记录变更字段及值
                    ProductSkuReviewDTO productSkuReviewDTO = new ProductSkuReviewDTO();
                    productSkuReviewDTO.setSiteId(priceVo.getSiteId());
                    productSkuReviewDTO.setProductSkuCode(sku.getProductSkuCode());
                    // 判断给update还是add
                    productSkuReviewDTO.setReviewType(PriceOperateLog.Add.name());
                    productSkuReviewDTO.addField("msrp", NumberUtil.toStr(beforeMsrp), NumberUtil.toStr(beforeMsrp, "0"));
                    productSkuReviewDTO.addField("dropShippingPrice", NumberUtil.toStr(beforeDropShippingPrice),
                        NumberUtil.toStr(beforeDropShippingPrice, "0"));
                    productSkuReviewDTO.addField("pickUpPrice", NumberUtil.toStr(beforePickUpPrice),
                        NumberUtil.toStr(beforePickUpPrice, "0"));
                    productSkuReviewDTO.addField("unitPrice", NumberUtil.toStr(originalUnitPrice),
                        NumberUtil.toStr(originalUnitPrice, "0"));
                    productSkuReviewDTO.addField("operationFee", NumberUtil.toStr(originalOperationFee),
                        NumberUtil.toStr(originalOperationFee, "0"));
                    productSkuReviewDTO.addField("finalDeliveryFee", NumberUtil.toStr(originalFinalDeliveryFee),
                        NumberUtil.toStr(originalFinalDeliveryFee, "0"));
                    productSkuReviewList.add(productSkuReviewDTO);
                }
            }

            ProductReviewDTO productReviewDTO = new ProductReviewDTO();
            productReviewDTO.setProductCode(product.getProductCode());
            productReviewDTO.setProductSkuReviewList(productSkuReviewList);
            productReviewDTO.setReviewStatus(verifyType);
            productReviewDTO.setReviewType(ProductReviewTypeEnum.NewProduct);

            // 记录SPU的变更supportedLogistics
            List<ChangeFieldDTO> SPUChangeFields = new ArrayList<>();
            SPUChangeFields.add(new ChangeFieldDTO("supportedLogistics", product.getSupportedLogistics().name(), product.getSupportedLogistics().name()));
            productReviewDTO.setChangeFields(SPUChangeFields);
            this.generateReviewRecord(productReviewDTO);
        }
        product.setVerifyState(ProductVerifyStateEnum.Pending);
        productSkus.forEach(productSku -> productSku.setVerifyState(ProductVerifyStateEnum.Pending));
        saveProductSkuList.addAll(productSkus);
        this.submitReview(product.getProductCode());
    }

    /**
     * 提交审核（批发）
     *
     * @param product
     * @param saveProductSkuList
     * @return
     * @throws RStatusCodeException
     */
    @Transactional(rollbackFor = Exception.class)
    public void submitWholesaleProductReview(Product product, List<ProductSku> saveProductSkuList) throws RStatusCodeException {

        ProductVerifyStateEnum verifyType = product.getVerifyState();
        if (ObjectUtil.equals(verifyType, ProductVerifyStateEnum.Accepted)) {
            throw new RStatusCodeException(ZSMallStatusCodeEnum.PRODUCT_ACCEPTED);
        }

        Long productId = product.getId();
        ProductWholesaleDetail wholesaleDetail = iProductWholesaleDetailService.queryByProductId(productId);

        ProductReviewRecord productReviewRecordEntity =
            iProductReviewRecordService.queryRecordByProductCodeAndReviewType(product.getProductCode(),
                ProductVerifyStateEnum.Draft);

        List<ProductSku> productSkus = iProductSkuService.queryByProductIdNotDelete(productId);

        if (ObjectUtil.isNull(productReviewRecordEntity)) {
            List<ProductSkuReviewDTO> productSkuReviewList = new ArrayList<>();
            for (ProductSku sku : productSkus) {
                // 记录变更字段及值
                ProductSkuReviewDTO productSkuReviewDTO = new ProductSkuReviewDTO();
                productSkuReviewDTO.setProductSkuCode(sku.getProductSkuCode());
                productSkuReviewDTO.addField("msrp", "0", "0");
                productSkuReviewList.add(productSkuReviewDTO);
            }

            ProductReviewDTO productReviewDTO = new ProductReviewDTO();
            productReviewDTO.setProductCode(product.getProductCode());
            productReviewDTO.setProductSkuReviewList(productSkuReviewList);
            productReviewDTO.setReviewStatus(verifyType);
            productReviewDTO.setReviewType(ProductReviewTypeEnum.NewProduct);

            List<ChangeFieldDTO> SPUChangeFields = new ArrayList<>();
            // 记录SPU的变更
            SPUChangeFields.add(new ChangeFieldDTO("supportedLogistics", JSONUtil.toJsonStr(wholesaleDetail.getDeliveryType()), JSONUtil.toJsonStr(wholesaleDetail.getDeliveryType())));
            productReviewDTO.setChangeFields(SPUChangeFields);
            this.generateReviewRecord(productReviewDTO);
        }

        product.setVerifyState(ProductVerifyStateEnum.Pending);
        productSkus.forEach(productSku -> productSku.setVerifyState(ProductVerifyStateEnum.Pending));
        saveProductSkuList.addAll(productSkus);
        this.submitReview(product.getProductCode());
    }

    /**
     * 查询指定商品的未提交记录，提交审核
     *
     * @param productCode
     */
    @Override
    public void submitReview(String productCode) {
        List<ProductReviewRecord> recordEntities = iProductReviewRecordService.selectList(productCode);
        // 若存在不明原因的多个Draft的审核记录，则只取最新的改为Pending，其余的改成Abandoned
        if (CollUtil.isNotEmpty(recordEntities)) {
            for (int i = 0; i < recordEntities.size(); i++) {
                ProductReviewRecord recordEntity = recordEntities.get(i);
                if (i == 0) {
                    recordEntity.setSubmitDateTime(new Date());
                    recordEntity.setSubmitUserId(LoginHelper.getUserId());
                    recordEntity.setReviewState(ProductVerifyStateEnum.Pending);
                } else {
                    recordEntity.setReviewState(ProductVerifyStateEnum.Abandoned);
                }
            }
            iProductReviewRecordService.saveOrUpdateBatch(recordEntities);
        }

    }


    public R<Void> batchReviewProduct(ProductReviewRecordBo bo) throws Exception {
        String reviewStatus = bo.getReviewStatus();
        List<Long> reviewRecordIds = bo.getReviewRecordIds();
        if (CollUtil.isEmpty(reviewRecordIds)) {
            return R.fail(ZSMallStatusCodeEnum.REQUIRED_CANNOT_EMPTY);
        }
        // 处理审核状态
        ProductVerifyStateEnum reviewStatusEnum = ProductVerifyStateEnum.valueOf(reviewStatus);
        String reviewOpinion = bo.getReviewOpinion();

        String effectiveType = bo.getEffectiveType();
        String effectiveDateString = bo.getEffectiveDate();
        Date effectiveDate = StrUtil.isNotBlank(effectiveDateString) ? DateUtil.parse(effectiveDateString) : null;

        JSONArray reviewOpinionOption = bo.getReviewOpinionOption();
        if (ObjectUtil.equals(reviewStatusEnum, ProductVerifyStateEnum.Rejected) &&
            CollUtil.isEmpty(reviewOpinionOption)) {
            return R.fail(ZSMallStatusCodeEnum.REJECTION_REASON_CANNOT_BE_EMPTY);
        }

        List<ProductReviewRecord> dataRecordList = iProductReviewRecordService.queryPendingListByIds(reviewRecordIds);

        List<Product> productList = new ArrayList<>();
        List<ProductSku> saveProductSkuList = new ArrayList<>();
        List<ProductReviewRecord> recordList = new ArrayList<>();
        List<ProductSkuPrice> priceEntityList = new ArrayList<>();
        List<Long> removePriceIds = new ArrayList<>();
        Map<Long, Long> ruleIdMap = new HashMap<>();
        // 价格变更List，搜集后统一发送通知
        List<ProductPriceChangeDto> productPriceChangeList = new ArrayList<>();

        // 需要生成价格变更定时器的审核记录
        List<ProductReviewRecord> priceRecord = new ArrayList<>();
        Map<Long, SiteCountryCurrency> siteIdMap = iSiteCountryCurrencyService.getSiteIdMap();
        for (ProductReviewRecord record : dataRecordList) {
            Long recordId = record.getId();
            String productCode = record.getProductCode();
            ProductReviewTypeEnum reviewType = record.getReviewType();

            Product product = iProductService.queryByProductCodeNotDelete(productCode);
            if (product == null) {
                return R.fail(ZSMallStatusCodeEnum.PRODUCT_NOT_EXIST);
            }

            record.setReviewState(reviewStatusEnum);
            record.setReviewUserId(LoginHelper.getUserId());
            record.setReviewDateTime(new Date());
            //审核拒绝,保存驳回理由
            if (ObjectUtil.equals(reviewStatusEnum, ProductVerifyStateEnum.Rejected)) {
                record.setReviewOpinion(reviewOpinion);
                record.setReviewOpinionOption(reviewOpinionOption);
            } else if (ObjectUtil.equals(reviewStatusEnum, ProductVerifyStateEnum.Accepted)) {
                List<ProductReviewChangeDetailVo> detailList =
                    iProductReviewChangeDetailService.queryByReviewRecordIdAndCode(recordId, productCode, null,null);
                if (CollUtil.isNotEmpty(detailList)) {
                    ProductReviewChangeDetailVo productReviewChangeDetailVo = detailList.get(0);
                    String fieldName = productReviewChangeDetailVo.getFieldName();
                    String fieldValueBefore = productReviewChangeDetailVo.getFieldValueBefore();
                    String fieldValueAfter = productReviewChangeDetailVo.getFieldValueAfter();
                    if (StrUtil.equals(fieldName, "supportedLogistics") && !StrUtil.equals(fieldValueAfter, fieldValueBefore)) {
                        product.setSupportedLogistics(SupportedLogisticsEnum.valueOf(fieldValueAfter));
                    }
                }
            }

            switch (reviewType) {
                case NewProduct:
                    reviewNewProduct(product, reviewStatusEnum, productList, saveProductSkuList);
                    break;
                case NewProductSku:
                    reviewNewProductSku(product, recordId, reviewStatusEnum, productList, saveProductSkuList);
                    break;
                case Price:
                    ProductTypeEnum productType = product.getProductType();
                    if (ObjectUtil.equals(productType, ProductTypeEnum.NormalProduct)) {
                        if (ObjectUtil.equals(reviewStatusEnum, ProductVerifyStateEnum.Accepted)) {
                            // 价格相关
                            reviewAcceptPrice(record, product, effectiveType, effectiveDate, productList, priceEntityList, ruleIdMap,
                                productPriceChangeList, priceRecord, removePriceIds,siteIdMap);

                        }
                    }
                    // todo 修改相应的sku的上下架状态
                    updateProductSkuStatus(product, saveProductSkuList);
                    if (ObjectUtil.equals(productType, ProductTypeEnum.WholesaleProduct)) {
                        wholesaleReviewPrice(reviewStatusEnum, record, product, effectiveType, priceRecord);
                    }
                    break;
                default:
                    break;
            }
            recordList.add(record);
        }


        if (CollUtil.isNotEmpty(recordList)) {
            iProductReviewRecordService.saveOrUpdateBatch(recordList);
        }
        if(CollUtil.isNotEmpty(removePriceIds)){
            TenantHelper.ignore(() -> iProductSkuPriceService.removeByIds(removePriceIds));

        }
        if (CollUtil.isNotEmpty(productList)) {
            TenantHelper.ignore(() -> iProductService.saveOrUpdateBatch(productList));
            TenantHelper.ignore(() -> productSkuService.setProductSkuStock(saveProductSkuList));
        }


        if (CollUtil.isNotEmpty(saveProductSkuList)) {
            TenantHelper.ignore(() -> iProductSkuService.saveOrUpdateBatch(saveProductSkuList));
        }

        TenantHelper.ignore(() -> {
            //审核立即生效，则查询该SKU有没有存在指定日期生效的价格变更定时任务，存在则直接删除
            //同步ES TODO
            if (CollUtil.isNotEmpty(priceEntityList)) {
                // 所有还未执行的价格更新任务
                List<ProductReviewChangeDetail> allTaskTodo = new ArrayList<>();
                List<ProductMapping> productMappingList = new ArrayList<>();
                priceEntityList.removeIf(price -> removePriceIds.contains(price.getId()));
                iProductSkuPriceService.saveOrUpdateBatch(priceEntityList);
                if (CollUtil.isNotEmpty(ruleIdMap)) {
                    priceEntityList.forEach(productSkuPriceEntity -> {

                        Long id = productSkuPriceEntity.getProductSkuId();
                        String productSkuCode = productSkuPriceEntity.getProductSkuCode();

                        Long priceId = productSkuPriceEntity.getId();
                        Long ruleId = ruleIdMap.get(id);
                        iProductSkuPriceRuleService.bindRule(id, productSkuCode, priceId, ruleId);

                        //因为当前价格变更为立即生效，所以需要查询该SKU是否存在还未执行的价格变更任务，有则全部置为不允许更新
                        if (StrUtil.isNotBlank(productSkuCode)) {
                            List<ProductReviewChangeDetail> queryByTaskTodo =
                                iProductReviewChangeDetailService.queryByTaskTodo(productSkuCode);
                            queryByTaskTodo.forEach(changeDetail -> changeDetail.setAllowUpdate(0L));
                            allTaskTodo.addAll(queryByTaskTodo);
                        }

                        //1、获取商品映射数据 2、修改映射数据的最总价格 todo 需要测试看看会不会受影响
                        List<ProductMapping> productMappings = iProductMappingService.queryListByProductSkuCode(productSkuCode);
                        for (ProductMapping productMapping : productMappings) {
                            if (StrUtil.isBlank(productMapping.getActivityCode()) && ChannelTypeEnum.Shopify.equals(productMapping.getChannelType())) {
                                productMapping.setPriceChanges(true);
                                productMappingList.add(productMapping);
                            }
                        }
                    });
                }
                if (CollUtil.isNotEmpty(allTaskTodo)) {
                    iProductReviewChangeDetailService.saveOrUpdateBatch(allTaskTodo);
                }
                if (CollUtil.isNotEmpty(productMappingList)) {
                    iProductMappingService.batchSaveOrUpdateNoTenant(productMappingList);
                }
            }
        });


        // 价格变更List，搜集后统一发送通知
        productSupport.toPushProductPriceChangeEvent(productPriceChangeList);

        // 指定生效日期的价格变更，需要生成定时任务 ,同步es
        productSupport.generateTaskSkuPriceChange(effectiveType, effectiveDate, priceRecord);

        // 同步ES
        esProductSupport.productUpload(productList);

        return R.ok();
    }

    private void updateProductSkuStatus(Product product, List<ProductSku> saveProductSkuList) {
        LambdaQueryWrapper<ProductSku> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProductSku::getProductId,product.getId());
        List<ProductSku> list = TenantHelper.ignore(() -> iProductSkuService.list(wrapper));
        list.forEach(productSku -> {
            productSku.setShelfState(ShelfStateEnum.OnShelf);
            productSku.setVerifyState(ProductVerifyStateEnum.Accepted);
            saveProductSkuList.add(productSku);
        });
    }

    /**
     * 批发商品价格变更审核
     */
    private void wholesaleReviewPrice(ProductVerifyStateEnum reviewStatusEnum, ProductReviewRecord record, Product product,
                                      String effectiveType, List<ProductReviewRecord> priceRecord) {
        Long productId = product.getId();
        Long recordId = record.getId();
        String productCode = record.getProductCode();
        // 查询审核记录关联的Item No
        List<String> priceProductSkuCodeList = iProductReviewRecordService.queryProductSkuCodeByRecord(recordId);

        if (ObjectUtil.equals(reviewStatusEnum, ProductVerifyStateEnum.Accepted)) {
            ProductWholesaleDetail productWholesaleDetail = null;
            List<ProductWholesaleTieredPriceLog> pwtpsLog = new ArrayList<>();
            List<ProductWholesaleTieredPrice> newPwtps = new ArrayList<>();
            List<ProductSkuWholesalePriceLog> pswpsLog = new ArrayList<>();
            List<ProductSkuWholesalePrice> newPswps = new ArrayList<>();
            List<ProductWholesaleTieredPrice> delTieredPrices = new ArrayList<>();
            List<ProductReviewChangeDetailVo> detailList =
                iProductReviewChangeDetailService.queryByReviewRecordIdAndCode(recordId, productCode, null,null);
            log.info("batchReview - detailList = {}", JSONUtil.toJsonStr(detailList));
            // 如果是指定日期生效的，才需要生成价格变更定时任务
            if (StrUtil.equals(effectiveType, "DATE")) {
                priceRecord.add(record);
            }else {
                for (ProductReviewChangeDetailVo ls : detailList) {
                    String fieldName = ls.getFieldName();
                    String fieldValueAfter = ls.getFieldValueAfter();
                    String fieldValueBefore = ls.getFieldValueBefore();
                    if (StrUtil.equals("productSkuWholesalePrice", fieldName)) {
                        List<ProductSkuWholesalePriceLog> listBefore = JSONUtil.parseArray(fieldValueBefore).toList(ProductSkuWholesalePriceLog.class);
                        listBefore.forEach(lb -> {
                            lb.setProductSkuWholesalePriceId(lb.getId());
                            lb.setId(null);
                        });
                        pswpsLog.addAll(listBefore);
                    }
                    if (StrUtil.equals("productWholesaleTieredPrice", fieldName)) {
                        List<ProductWholesaleTieredPrice> listAfter = JSONUtil.parseArray(fieldValueAfter).toList(ProductWholesaleTieredPrice.class);
                        List<ProductWholesaleTieredPriceLog> listBefore = JSONUtil.parseArray(fieldValueBefore).toList(ProductWholesaleTieredPriceLog.class);
                        listBefore.forEach(lb -> {
                            lb.setProductWholesaleTieredPriceId(lb.getId());
                            lb.setId(null);
                        });
                        pwtpsLog.addAll(listBefore);
                        newPwtps.addAll(listAfter);
                    }
                    if (StrUtil.equals("productWholesaleDetail", fieldName)) {
                        productWholesaleDetail = JSONUtil.toBean(fieldValueAfter, ProductWholesaleDetail.class);
                    }
                    if (StrUtil.equals("delWholesalePrice", fieldName)) {
                        delTieredPrices = JSONUtil.parseArray(fieldValueBefore).toList(ProductWholesaleTieredPrice.class);
                    }
                }
                if (productWholesaleDetail != null) {
                    iProductWholesaleDetailService.saveOrUpdate(productWholesaleDetail);
                }
                if (CollUtil.isNotEmpty(pwtpsLog)) {
                    iProductWholesaleTieredPriceLogService.saveOrUpdateBatch(pwtpsLog);
                }
                if (CollUtil.isNotEmpty(newPwtps)) {
                    iProductWholesaleTieredPriceService.saveOrUpdateBatch(newPwtps);
                    newPwtps.stream().forEach(np -> {
                        Long id = np.getId();
                        List<ProductSkuWholesalePrice> wholesalePriceEntities = np.getWholesalePrices();
                        wholesalePriceEntities.stream().forEach(wp -> {
                            wp.setTieredPriceId(id);
                        });
                        newPswps.addAll(wholesalePriceEntities);
                    });
                    iProductSkuWholesalePriceService.saveOrUpdateBatch(newPswps);
                }
                if (CollUtil.isNotEmpty(pswpsLog)) {
                    iProductSkuWholesalePriceLogService.saveBatch(pswpsLog);
                }
                if (CollUtil.isNotEmpty(delTieredPrices)) {
                    iProductWholesaleTieredPriceService.removeByIds(delTieredPrices);
                }
            }
            List<ProductSku> saveProductSkuList = new ArrayList<>();
            for (String productSkuCode : priceProductSkuCodeList) {
                ProductSku productSku = iProductSkuService.queryByProductSkuCode(productSkuCode);

                boolean update = false;
                if (ObjectUtil.equals(productSku.getVerifyState(), ProductVerifyStateEnum.Pending)) {
                    productSku.setVerifyState(ProductVerifyStateEnum.Accepted);
                    update = true;
                }

                if (ObjectUtil.equals(productSku.getShelfState(), ShelfStateEnum.ForcedOffShelf)) {
                    productSku.setShelfState(ShelfStateEnum.OnShelf);
                    update = true;
                }

                if (update) {
                    saveProductSkuList.add(productSku);
                }
            }
            // 开始调整每个SKU的库存
            productSkuService.setProductSkuStockWholesale(saveProductSkuList);
            if (CollUtil.isNotEmpty(saveProductSkuList)) {
                TenantHelper.ignore(() -> iProductSkuService.saveOrUpdateBatch(saveProductSkuList));
            }
        } else {
            List<ProductSku> saveProductSkuList = new ArrayList<>();
            for (String productSkuCode : priceProductSkuCodeList) {
                ProductSku productSku = iProductSkuService.queryByProductSkuCode(productSkuCode);
                if (productSku.getShelfState().equals(ShelfStateEnum.ForcedOffShelf) && ObjectUtil.equals(productSku.getVerifyState(), ProductVerifyStateEnum.Pending)) {
                    productSku.setVerifyState(ProductVerifyStateEnum.Rejected);
                    saveProductSkuList.add(productSku);
                }
            }
            TenantHelper.ignore(() -> iProductSkuService.updateBatchById(saveProductSkuList));
            List<ProductSku> skuList = iProductSkuService.queryByProductIdNotDelete(productId);
            if (CollUtil.isEmpty(skuList)) {
                product.setVerifyState(ProductVerifyStateEnum.Rejected);
                product.setShelfState(ShelfStateEnum.OffShelf);
                TenantHelper.ignore(() -> iProductService.updateById(product));
            }
        }

    }


    /**
     *正常商品价格审核
     */
    private void reviewAcceptPrice(ProductReviewRecord record, Product product, String effectiveType, Date effectiveDate,
                                   List<Product> productList, List<ProductSkuPrice> priceEntityList, Map<Long, Long> ruleIdMap,
                                   List<ProductPriceChangeDto> productPriceChangeList, List<ProductReviewRecord> priceRecord,
                                   List<Long> removePriceIds, Map<Long, SiteCountryCurrency> siteIdMap) {

        Long recordId = record.getId();
        String supTenantId = product.getTenantId();
        String productCode = record.getProductCode();
        // 当前日期
        Date nowDate = new Date();
        String currentEffectiveDateString = DateUtil.format(nowDate, "yyyy-MM-dd");
        if (StrUtil.equals(effectiveType, "DATE")) {
            // 前端传来的指定日期
//            DateTime specify = DateUtil.parse(effectiveDate, "yyyy-MM-dd");
            // 如果指定日期小于等于当期日期，则返回错误提示，指定日期不能小于等于今天
            if (DateUtil.compare(effectiveDate, nowDate) <= 0) {
                throw new RStatusCodeException(ZSMallStatusCodeEnum.SPECIFY_DATE_NOT_EQUAL_NOW_OR_LESS);
            }
            // 指定日期生效的价格变更，生效时间就是前端传来的日期
            currentEffectiveDateString = DateUtil.format(effectiveDate, "yyyy-MM-dd");
        }

        // 查询审核记录关联的Item No
        List<String> priceProductSkuCodeList = iProductReviewRecordService.queryProductSkuCodeByRecord(recordId);
        Map<String,Long> priceProductSkuCodeAndIdMap = iProductSkuService.getMapByCodes(priceProductSkuCodeList);
//        log.info("batchReview - priceProductSkuCodeList = {}", JSONUtil.toJsonStr(priceProductSkuCodeList));
        if (CollUtil.isNotEmpty(priceProductSkuCodeList)) {
            for (String productSkuCode : priceProductSkuCodeList) {
                List<ProductReviewChangeDetailVo> detailList = iProductReviewChangeDetailService.queryByReviewRecordIdAndCode(recordId, productCode, productSkuCode,null);
                log.info("batchReview - detailList = {}", JSONUtil.toJsonStr(detailList));
                // detailList中reviewType是Update/Add/Delete 的取出来分别处理到新的list内
                List<ProductReviewChangeDetailVo> updateList = detailList.stream()
                                                                         .filter(detail -> PriceOperateLog.Update.name().equals(detail.getReviewType()))
                                                                         .collect(Collectors.toList());

                List<ProductReviewChangeDetailVo> addList = detailList.stream()
                                                                      .filter(detail -> PriceOperateLog.Add.name().equals(detail.getReviewType()))
                                                                      .collect(Collectors.toList());

                List<ProductReviewChangeDetailVo> deleteList = detailList.stream()
                                                                         .filter(detail -> PriceOperateLog.Delete.name().equals(detail.getReviewType()))
                                                                         .collect(Collectors.toList());

                // updateList 复用之前逻辑
                if(CollUtil.isNotEmpty(updateList)){
                    // updateList转换为map,key是siteId,value是对应的List<ProductReviewChangeDetailVo>
                    Map<Long, List<ProductReviewChangeDetailVo>> detailsMap = updateList.stream()
                                                                                        .collect(Collectors.groupingBy(ProductReviewChangeDetailVo::getSiteId));
                    // 遍历detailsMap
                    for (Map.Entry<Long, List<ProductReviewChangeDetailVo>> entry : detailsMap.entrySet()) {
                        Long siteId = entry.getKey();
                        SiteCountryCurrency currency = siteIdMap.get(siteId);
                        List<ProductReviewChangeDetailVo> details = entry.getValue();
                        priceLogMethod(effectiveType, priceEntityList, ruleIdMap, productPriceChangeList, productSkuCode, details, currentEffectiveDateString,PriceOperateLog.Update.name(),null,siteId,currency);

                    }

                }
                // addList
                if(CollUtil.isNotEmpty(addList)){
                    Map<Long, List<ProductReviewChangeDetailVo>> detailsMap = addList.stream()
                                                                                        .collect(Collectors.groupingBy(ProductReviewChangeDetailVo::getSiteId));
                    // 遍历detailsMap
                    for (Map.Entry<Long, List<ProductReviewChangeDetailVo>> entry : detailsMap.entrySet()) {
                        Long siteId = entry.getKey();
                        SiteCountryCurrency currency = siteIdMap.get(siteId);
                        List<ProductReviewChangeDetailVo> details = entry.getValue();
                        priceLogMethod(effectiveType, priceEntityList, ruleIdMap, productPriceChangeList, productSkuCode, details, currentEffectiveDateString,PriceOperateLog.Add.name(),null, siteId, currency);

                    }

                }

                // deleteList
                if(CollUtil.isNotEmpty(deleteList)){
                    ProductReviewChangeDetailVo productReviewChangeDetailVo = deleteList.get(0);
                    Long siteId = productReviewChangeDetailVo.getSiteId();
                    Long productSkuPriceId = productReviewChangeDetailVo.getProductSkuPriceId();
                    // 遍历detailsMap
                    deletePrice(productSkuPriceId,priceProductSkuCodeAndIdMap.get(productSkuCode),siteId,supTenantId);

                }
            }
            // 如果是指定日期生效的，才需要生成价格变更定时任务
            if (StrUtil.equals(effectiveType, "DATE")) {
                priceRecord.add(record);
            } else {
                productList.add(product);
            }
        }
    }

    /**
     * 功能描述：删除价格,会员价和商品价
     *
     * @param productSkuPriceId 产品sku价格id
     * @param productSkuId      产品id
     * @param siteId            站点ID
     * <AUTHOR>
     * @date 2025/02/26
     */
    private void deletePrice(Long productSkuPriceId, Long productSkuId, Long siteId,String tenantId) {
        // todo 供应商删除站点价格会把相应的商品的会员价格内的站点价格同时删除,所以要删除数据可以根据siteId+productSkuId+租户标识
        LambdaUpdateWrapper<RuleLevelProductPrice> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(RuleLevelProductPrice::getProductSkuId, productSkuId);
        wrapper.eq(RuleLevelProductPrice::getSiteId, siteId);
        wrapper.eq(RuleLevelProductPrice::getRuleCustomizerTenantId,tenantId);
        wrapper.set(RuleLevelProductPrice::getDelFlag, 2);
        TenantHelper.ignore(() -> iRuleLevelProductPriceService.update(wrapper));
        TenantHelper.ignore(() -> iProductSkuPriceService.removeById(productSkuPriceId));
    }

    private void priceLogMethod(String effectiveType, List<ProductSkuPrice> priceEntityList, Map<Long, Long> ruleIdMap,
                                List<ProductPriceChangeDto> productPriceChangeList, String productSkuCode,
                                List<ProductReviewChangeDetailVo> detailList, String currentEffectiveDateString,
                                String name, List<Long> removePriceIds, Long siteId, SiteCountryCurrency currency) {
        if(PriceOperateLog.Update.name().equals(name)){
            updatePriceLog(effectiveType, priceEntityList, ruleIdMap, productPriceChangeList, productSkuCode, detailList, currentEffectiveDateString, name, removePriceIds, siteId);
        }
        if(PriceOperateLog.Add.name().equals(name)){
            addPriceLog(effectiveType, priceEntityList, ruleIdMap, productPriceChangeList, productSkuCode, detailList, currentEffectiveDateString, name, removePriceIds, siteId,currency);
        }
    }

    /**
     * 功能描述：添加价格日志
     *
     * @param effectiveType              有效类型
     * @param priceEntityList            价格实体列表
     * @param ruleIdMap                  规则id映射
     * @param productPriceChangeList     产品价格变动表
     * @param productSkuCode             产品sku代码
     * @param detailList                 明细表
     * @param currentEffectiveDateString 当前生效日期字符串
     * @param name                       名称
     * @param removePriceIds             删除价格ID
     * @param siteId                     站点id
     * @param currency
     * <AUTHOR>
     * @date 2025/02/10
     */
    private void addPriceLog(String effectiveType, List<ProductSkuPrice> priceEntityList, Map<Long, Long> ruleIdMap,
                             List<ProductPriceChangeDto> productPriceChangeList, String productSkuCode,
                             List<ProductReviewChangeDetailVo> detailList, String currentEffectiveDateString, String name,
                             List<Long> removePriceIds, Long siteId, SiteCountryCurrency currency) {

        ProductSkuPrice productSkuPrice = new ProductSkuPrice();

        if(PriceOperateLog.Delete.name().equals(name)){
            detailList.forEach(delete -> {
                Long productSkuPriceId = delete.getProductSkuPriceId();
                if (ObjectUtil.isNotNull(productSkuPriceId)) {
                    removePriceIds.add(productSkuPriceId);
                }
            });
        }
        ProductPriceChangeDto productPriceChangeDto = new ProductPriceChangeDto();
        productReviewChangeDetailService.setChangeToObject(productPriceChangeDto, detailList);
        productPriceChangeDto.setProductSkuCode(productSkuCode);

        ProductSku productSku = iProductSkuService.queryByProductSkuCode(productSkuCode);
        String tenantId = productSku.getTenantId();
        productPriceChangeDto.setSupplierTenantId(tenantId);
        Long skuId = productSku.getId();


        productPriceChangeDto.setBefore_pickUpPrice(BigDecimal.ZERO);
        productPriceChangeDto.setBefore_dropShippingPrice(BigDecimal.ZERO);
        //变更后的供应商价格（原价、操作费、远程派送费）
        BigDecimal originalUnitPrice = productPriceChangeDto.getAfter_unitPrice();
        BigDecimal originalOperationFee = productPriceChangeDto.getAfter_operationFee();
        BigDecimal originalFinalDeliveryFee = productPriceChangeDto.getAfter_finalDeliveryFee();
        BigDecimal msrp = productPriceChangeDto.getAfter_msrp();

        // 此方法会消除id和siteId

        productSkuPrice = iProductSkuPriceRuleService.matchRule(productSkuCode, originalUnitPrice, originalOperationFee, originalFinalDeliveryFee, false);
        productPriceChangeDto.setAfter_pickUpPrice(productSkuPrice.getPlatformPickUpPrice());
        productPriceChangeDto.setAfter_dropShippingPrice(productSkuPrice.getPlatformDropShippingPrice());
        // 即时生效的价格变更，需要现在就修改
        if (StrUtil.equals(effectiveType, "NOW")) {
            productSkuPrice.setSiteId(siteId);
            productSkuPrice.setProductSkuId(skuId);
            productSkuPrice.setCurrency(currency.getCurrencyCode());
            productSkuPrice.setCountryCode(currency.getCountryCode());
            productSkuPrice.setCurrencySymbol(currency.getCurrencySymbol());
            productSkuPrice.setProductId(productSku.getProductId());
            productSkuPrice.setProductSkuCode(productSkuCode);
            //记录价格变化日志
            iProductSkuPriceLogService.recordPriceChanges(productSkuPrice, name);
            if (ObjectUtil.isNull(productSkuPrice)) {
                productSkuPrice = new ProductSkuPrice();
            }

            Long ruleId = productSkuPrice.getProductSkuPriceRuleId();
            //绑定公式
//          iProductSkuPriceRuleService.bindRule(productSkuPrice, ruleId);
            productSkuPrice.setMsrp(msrp);
            priceEntityList.add(productSkuPrice);

            if (!ObjectUtil.equals(ruleId, 0)) {
                ruleIdMap.put(skuId, ruleId);
            }
        }
        productPriceChangeDto.setEffectiveTime(currentEffectiveDateString);
        // 搜集数据，统一发送价格变更通知
        productPriceChangeList.add(productPriceChangeDto);

    }

    /**
     * 功能描述：更新价格日志
     *
     * @param effectiveType              有效类型
     * @param priceEntityList            价格实体列表
     * @param ruleIdMap                  规则id映射
     * @param productPriceChangeList     产品价格变动表
     * @param productSkuCode             产品sku代码
     * @param detailList                 明细表
     * @param currentEffectiveDateString 当前生效日期字符串
     * @param name                       名称
     * @param removePriceIds             删除价格ID
     * @param siteId                     站点id
     * <AUTHOR>
     * @date 2025/02/10
     */
    private void updatePriceLog(String effectiveType, List<ProductSkuPrice> priceEntityList, Map<Long, Long> ruleIdMap,
                           List<ProductPriceChangeDto> productPriceChangeList, String productSkuCode,
                           List<ProductReviewChangeDetailVo> detailList, String currentEffectiveDateString, String name,
                           List<Long> removePriceIds, Long siteId) {
        ProductSkuPrice productSkuPrice = TenantHelper.ignore(() -> iProductSkuPriceService.queryByProductSkuCodeAndSite(productSkuCode, siteId));

        Long skuPriceId = productSkuPrice.getId();
        if(PriceOperateLog.Delete.name().equals(name)){
            detailList.forEach(delete -> {
                Long productSkuPriceId = delete.getProductSkuPriceId();
                if (ObjectUtil.isNotNull(productSkuPriceId)) {
                    removePriceIds.add(productSkuPriceId);
                }
            });
        }
        ProductPriceChangeDto productPriceChangeDto = new ProductPriceChangeDto();
        productReviewChangeDetailService.setChangeToObject(productPriceChangeDto, detailList);
        productPriceChangeDto.setProductSkuCode(productSkuCode);

        ProductSku productSku = iProductSkuService.queryByProductSkuCode(productSkuCode);
        String tenantId = productSku.getTenantId();
        productPriceChangeDto.setSupplierTenantId(tenantId);
        Long skuId = productSku.getId();


        productPriceChangeDto.setBefore_pickUpPrice(productSkuPrice.getPlatformPickUpPrice());
        productPriceChangeDto.setBefore_dropShippingPrice(productSkuPrice.getPlatformDropShippingPrice());
        //变更后的供应商价格（原价、操作费、远程派送费）
        BigDecimal originalUnitPrice = productPriceChangeDto.getAfter_unitPrice();
        BigDecimal originalOperationFee = productPriceChangeDto.getAfter_operationFee();
        BigDecimal originalFinalDeliveryFee = productPriceChangeDto.getAfter_finalDeliveryFee();
        BigDecimal msrp = productPriceChangeDto.getAfter_msrp();
        Long priceId = skuPriceId;
        // 此方法会消除id和siteId
        productSkuPrice = iProductSkuPriceRuleService.matchRule(productSkuCode, originalUnitPrice, originalOperationFee, originalFinalDeliveryFee, false);
        productPriceChangeDto.setAfter_pickUpPrice(productSkuPrice.getPlatformPickUpPrice());
        productPriceChangeDto.setAfter_dropShippingPrice(productSkuPrice.getPlatformDropShippingPrice());
        // 即时生效的价格变更，需要现在就修改
        if (StrUtil.equals(effectiveType, "NOW")) {
            productSkuPrice.setSiteId(siteId);
            productSkuPrice.setProductSkuId(skuId);
            productSkuPrice.setId(skuPriceId);
            productSkuPrice.setProductSkuCode(productSkuCode);
            //记录价格变化日志
            if (ObjectUtil.isNotNull(skuPriceId)) {
                iProductSkuPriceLogService.recordPriceChanges(productSkuPrice, name);
            }
            if (ObjectUtil.isNull(productSkuPrice)) {
                productSkuPrice = new ProductSkuPrice();
            } else {
                productSkuPrice.setId(priceId);
            }

            Long ruleId = productSkuPrice.getProductSkuPriceRuleId();
            //绑定公式
//                                    iProductSkuPriceRuleService.bindRule(productSkuPrice, ruleId);
            productSkuPrice.setMsrp(msrp);
            priceEntityList.add(productSkuPrice);

            if (!ObjectUtil.equals(ruleId, 0)) {
                ruleIdMap.put(skuId, ruleId);
            }
        }
        productPriceChangeDto.setEffectiveTime(currentEffectiveDateString);
        // 搜集数据，统一发送价格变更通知
        productPriceChangeList.add(productPriceChangeDto);
    }

    /**
     * 新商品SKU审核
     * @param product
     * @param recordId
     * @param reviewStatusEnum
     * @param productList
     * @param saveProductSkuList
     */
    private void reviewNewProductSku(Product product, Long recordId, ProductVerifyStateEnum reviewStatusEnum,
                                     List<Product> productList, List<ProductSku> saveProductSkuList) {
        List<String> productSkuCodeList = iProductReviewRecordService.queryProductSkuCodeByRecord(recordId);
        List<ProductSku> newProductSkuList = new ArrayList<>();
        for (String productSkuCode : productSkuCodeList) {
            ProductSku productSku = iProductSkuService.queryByProductSkuCode(productSkuCode);
            productSku.setVerifyState(reviewStatusEnum);
            if (ObjectUtil.equals(reviewStatusEnum, ProductVerifyStateEnum.Accepted)) {
                productSku.setShelfState(ShelfStateEnum.OnShelf);

                Long productSkuId = productSku.getId();
                //欧洲站临时改造方案,目前公式逻辑没有应用,暂不深入了解
                List<ProductSkuPrice> productSkuPrices = iProductSkuPriceService.queryListByProductSkuId(productSkuId);
                for (ProductSkuPrice productSkuPrice : productSkuPrices) {
                    //匹配公式
                    Long productSkuPriceRuleId = iProductSkuPriceRuleService.matchRule(productSkuPrice, false);
                    if (ObjectUtil.isNull(productSkuPriceRuleId)) {
                        throw new RStatusCodeException(ZSMallStatusCodeEnum.SYSTEM_ERROR_E10028);
                    }
                    //绑定公式
                    iProductSkuPriceRuleService.bindRule(productSkuPrice, productSkuPriceRuleId);
                }

            } else {
                productSku.setShelfState(ShelfStateEnum.ForcedOffShelf);
            }
            newProductSkuList.add(productSku);
        }
        productList.add(product);
        saveProductSkuList.addAll(newProductSkuList);
    }

    /**
     * 新商品审核
     * @param product
     * @param reviewStatusEnum
     * @param productList
     * @param saveProductSkuList
     */
    private void reviewNewProduct(Product product, ProductVerifyStateEnum reviewStatusEnum, List<Product> productList,
                                  List<ProductSku> saveProductSkuList) {
        ProductTypeEnum productType = product.getProductType();
        Long productId = product.getId();

        product.setVerifyState(reviewStatusEnum);
        product.setVerifyTime(new Date());

        List<ProductSku> productSkuList = iProductSkuService.queryByProductIdNotDelete(productId);

        // 审核通过，商品SPU保持下架，供货商可以操作上下架
        if (ObjectUtil.equals(reviewStatusEnum, ProductVerifyStateEnum.Accepted)) {
            product.setShelfState(ShelfStateEnum.OffShelf);

            Boolean automaticallyListed = ZSMallSystemEventUtils.checkAutoOnShelfEvent(product.getTenantId());
            if (automaticallyListed) {
                product.setShelfState(ShelfStateEnum.OnShelf);
                if (product.getFirstOnShelfTime() == null) {
                    product.setFirstOnShelfTime(new Date());
                }
                product.setLastOnShelfTime(new Date());
            }

            //正常商品审核处理
            if (ObjectUtil.equals(productType, ProductTypeEnum.NormalProduct)) {
                for (ProductSku productSku : productSkuList) {
                    Long productSkuId = productSku.getId();
                    // 欧洲站点临时处理,里面的逻辑没有细看
                    List<ProductSkuPrice> productSkuPrices = iProductSkuPriceService.queryListByProductSkuId(productSkuId);
                    for (ProductSkuPrice productSkuPrice : productSkuPrices) {
                        //匹配公式
                        Long productSkuPriceRuleId = iProductSkuPriceRuleService.matchRule(productSkuPrice, false);
                        if (ObjectUtil.isNull(productSkuPriceRuleId)) {
                            throw new RStatusCodeException(ZSMallStatusCodeEnum.SYSTEM_ERROR_E10028);
                        }
                        //绑定公式
                        iProductSkuPriceRuleService.bindRule(productSkuPrice, productSkuPriceRuleId);
                    }

                }
            }
        } else {
            product.setShelfState(ShelfStateEnum.ForcedOffShelf);
        }

        for (ProductSku productSku : productSkuList) {
            if (ObjectUtil.equals(productSku.getVerifyState(), ProductVerifyStateEnum.Pending)) {
                productSku.setVerifyState(product.getVerifyState());
                // 审核通过，SKU直接上架，审核不通过，保持强制下架状态
                productSku.setShelfState(ProductVerifyStateEnum.Accepted.equals(reviewStatusEnum) ? ShelfStateEnum.OnShelf : ShelfStateEnum.ForcedOffShelf);
            }
            saveProductSkuList.add(productSku);
        }
        productList.add(product);
    }

    /**
     * 生成审核记录
     */
    @Override
    public void generateReviewRecord(ProductReviewDTO productReviewDTO) {
        log.info("生成审核记录 productReviewDTO {}", JSONUtil.toJsonStr(productReviewDTO));
        ProductReviewRecord productReviewRecordEntity = new ProductReviewRecord();
        String productCode = productReviewDTO.getProductCode();
        ProductVerifyStateEnum reviewStatus = productReviewDTO.getReviewStatus();
        List<ChangeFieldDTO> SPUChangeFields = productReviewDTO.getChangeFields();
        ProductReviewTypeEnum reviewType = productReviewDTO.getReviewType();

        productReviewRecordEntity.setProductCode(productCode);
        productReviewRecordEntity.setReviewType(reviewType);
        productReviewRecordEntity.setReviewState(reviewStatus);
        if (ProductVerifyStateEnum.Pending.equals(reviewStatus)) {
            productReviewRecordEntity.setSubmitDateTime(new Date());
            // 可能会存在 开了线程 拿不到的情况
            Long userId = LoginHelper.getUserId();
            if(ObjectUtil.isEmpty(userId)){
                userId = LoginContextHolder.getLoginInfo().getUserId();
            }
            productReviewRecordEntity.setSubmitUserId(userId);
        }

        List<ProductReviewChangeDetail> productReviewChangeDetailEntities = new ArrayList<>();

        // 记录SPU的变动
        if (CollUtil.isNotEmpty(SPUChangeFields)) {
            for (ChangeFieldDTO spuChangeField : SPUChangeFields) {
                ProductReviewChangeDetail detailEntity = new ProductReviewChangeDetail();
                detailEntity.setProductCode(productCode);
                detailEntity.setFieldName(spuChangeField.getFieldName());
                detailEntity.setFieldValueBefore(spuChangeField.getFieldValueBefore());
                detailEntity.setFieldValueAfter(spuChangeField.getFieldValueAfter());
                productReviewChangeDetailEntities.add(detailEntity);
            }
        }

        List<ProductSkuReviewDTO> productSkuReviewList = productReviewDTO.getProductSkuReviewList();
        // 记录SKU的变动
        for (ProductSkuReviewDTO productSkuReviewDTO : productSkuReviewList) {
            String productSkuCode = productSkuReviewDTO.getProductSkuCode();

            List<ChangeFieldDTO> changeFields = productSkuReviewDTO.getChangeFields();
            for (ChangeFieldDTO changeField : changeFields) {
                ProductReviewChangeDetail detailEntity = new ProductReviewChangeDetail();
                detailEntity.setSiteId(productSkuReviewDTO.getSiteId());
                detailEntity.setReviewType(productSkuReviewDTO.getReviewType());
                detailEntity.setProductCode(productCode);
                detailEntity.setProductSkuPriceId(productSkuReviewDTO.getProductSkuPriceId());
                detailEntity.setProductSkuCode(productSkuCode);
                detailEntity.setFieldName(changeField.getFieldName());
                detailEntity.setFieldValueBefore(changeField.getFieldValueBefore());
                detailEntity.setFieldValueAfter(changeField.getFieldValueAfter());
                productReviewChangeDetailEntities.add(detailEntity);
            }
        }

        iProductReviewRecordService.saveOrUpdate(productReviewRecordEntity);
        Long recordEntityId = productReviewRecordEntity.getId();
        if (ProductReviewTypeEnum.NewProduct.equals(reviewType)) {
            iProductReviewRecordService.setAllPendingToAbandoned(productCode, recordEntityId);
        }

        productReviewChangeDetailEntities.forEach(item -> item.setReviewRecordId(recordEntityId));
        iProductReviewChangeDetailService.saveOrUpdateBatch(productReviewChangeDetailEntities);
    }

    @Override
    public R<Void> submitProductsReview(List<String> bos) {
        for (String productCode : bos) {
            if (StrUtil.isBlank(productCode)) {
                return R.fail(ZSMallStatusCodeEnum.REQUIRED_CANNOT_EMPTY);
            }
            extractedProductReview(productCode);
        }
        return R.ok();
    }

}
