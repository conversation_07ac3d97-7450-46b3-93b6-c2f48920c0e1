package com.zsmall.product.biz.service;

import com.hengjian.common.core.domain.R;
import com.hengjian.common.mybatis.core.page.PageQuery;
import com.hengjian.common.mybatis.core.page.TableDataInfo;
import com.zsmall.product.entity.domain.bo.tenantFavorites.TenantFavoritesDeleteBo;
import com.zsmall.product.entity.domain.bo.tenantFavorites.TenantFavoritesExportBo;
import com.zsmall.product.entity.domain.bo.tenantFavorites.TenantFavoritesListBo;
import com.zsmall.product.entity.domain.vo.tenantFavorites.TenantFavoritesListVo;

import javax.servlet.http.HttpServletResponse;
import java.util.List;


/**
 * 租户收藏夹-接口
 *
 * <AUTHOR>
 * @date 2023/8/23
 */
public interface TenantFavoritesService {

    /**
     * 分页查询租户收藏夹
     */
    TableDataInfo<TenantFavoritesListVo> queryPage(TenantFavoritesListBo bo, PageQuery pageQuery);

    /**
     * 删除收藏夹项目
     */
    R<Void> deleteFavorites(TenantFavoritesDeleteBo bo);

    /**
     * 导出收藏夹商品数据
     */
    R<Void> exportFavorites(TenantFavoritesExportBo bo);

    /**
     * 导出收藏夹商品资料压缩包
     */
    R<Void> exportFavoritesZip(TenantFavoritesExportBo bo);

    /**
     * 功能描述：导出v2
     *
     * @param bo        bo
     * @param pageQuery 页面查询
     * @param response
     * @return {@link R }<{@link Void }>
     * <AUTHOR>
     * @date 2024/04/26
     */
    R<Void> exportV2(TenantFavoritesListBo bo,  HttpServletResponse response);

    /**
     * 查询收藏夹数量
     * @return
     */
    Integer getTenantFavoritesCount();
}
