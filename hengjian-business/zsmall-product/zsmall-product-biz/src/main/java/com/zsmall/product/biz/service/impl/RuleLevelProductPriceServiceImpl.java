package com.zsmall.product.biz.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.StopWatch;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.lang.UUID;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hengjian.common.core.domain.R;
import com.hengjian.common.core.domain.model.LoginUser;
import com.hengjian.common.core.enums.TenantType;
import com.hengjian.common.mybatis.core.page.PageQuery;
import com.hengjian.common.mybatis.core.page.TableDataInfo;
import com.hengjian.common.satoken.holder.runnable.UserRunnable;
import com.hengjian.common.satoken.utils.LoginHelper;
import com.hengjian.common.tenant.helper.TenantHelper;
import com.hengjian.extend.utils.SystemEventUtils;
import com.hengjian.system.domain.vo.SysOssVo;
import com.zsmall.bma.open.member.iservice.IMemberLevelV2ServiceImpl;
import com.zsmall.bma.open.member.iservice.IMemberRuleRelationV2ServiceImpl;
import com.zsmall.bma.open.member.service.RuleLevelProductPriceV2Service;
import com.zsmall.bma.open.member.service.impl.RuleLevelProductPriceV2ServiceImpl;
import com.zsmall.common.domain.LocaleMessage;
import com.zsmall.common.enums.BusinessCodeEnum;
import com.zsmall.common.enums.ExcelMessageEnum;
import com.zsmall.common.enums.common.AttachmentTypeEnum;
import com.zsmall.common.enums.common.ChannelTypeEnum;
import com.zsmall.common.enums.common.GlobalStateEnum;
import com.zsmall.common.enums.member.MemberlLevelEnum;
import com.zsmall.common.enums.order.LogisticsTypeEnum;
import com.zsmall.common.enums.orderImportRecord.ImportStateEnum;
import com.zsmall.common.enums.product.*;
import com.zsmall.common.enums.statuscode.ZSMallStatusCodeEnum;
import com.zsmall.common.enums.warehouse.WarehouseTypeEnum;
import com.zsmall.common.exception.ExcelMessageException;
import com.zsmall.common.properties.FileProperties;
import com.zsmall.common.util.ExcelMsgBuilder;
import com.zsmall.common.util.FileDownUtil;
import com.zsmall.common.util.ZExcelUtil;
import com.zsmall.order.entity.iservice.IWholesaleIntentionOrderItemService;
import com.zsmall.product.biz.service.*;
import com.zsmall.product.biz.support.EsProductSupport;
import com.zsmall.product.biz.support.ProductSupport;
import com.zsmall.product.entity.anno.ProductPriceOperateLimit;
import com.zsmall.product.entity.domain.*;
import com.zsmall.product.entity.domain.bo.UniversalBo.UniversalQueryBo;
import com.zsmall.product.entity.domain.bo.member.RulePriceQueryBo;
import com.zsmall.product.entity.domain.bo.product.ProductImportBo;
import com.zsmall.product.entity.domain.bo.productSku.ProductPriceImportBo;
import com.zsmall.product.entity.domain.bo.productSku.ProductSkuImportBo;
import com.zsmall.product.entity.domain.dto.productImport.LevelPriceParseDTO;
import com.zsmall.product.entity.domain.dto.productImport.LevelPriceV2DTO;
import com.zsmall.product.entity.domain.dto.productImport.MemberPriceProductImportDTO;
import com.zsmall.product.entity.domain.dto.productImport.ProductImportDTO;
import com.zsmall.product.entity.domain.member.MemberLevel;
import com.zsmall.product.entity.domain.member.MemberRuleRelation;
import com.zsmall.product.entity.domain.vo.ProductSkuAttachmentVo;
import com.zsmall.product.entity.domain.vo.member.*;
import com.zsmall.product.entity.domain.vo.product.LevelPriceVo;
import com.zsmall.product.entity.domain.vo.product.ProductExportVo;
import com.zsmall.product.entity.domain.vo.product.ProductListVo;
import com.zsmall.product.entity.domain.vo.productGlobalAttribute.ProductGlobalAttributeSimpleVo;
import com.zsmall.product.entity.domain.vo.productSku.ProductSkuListVo;
import com.zsmall.product.entity.iservice.*;
import com.zsmall.bma.open.member.mapper.MemberLevelMapper;
import com.zsmall.product.entity.mapper.RuleLevelProductPriceMapper;
import com.zsmall.product.entity.util.ProductCodeGenerator;
import com.zsmall.system.entity.iservice.IDownloadRecordService;
import com.zsmall.warehouse.entity.domain.LogisticsTemplate;
import com.zsmall.warehouse.entity.domain.Warehouse;
import com.zsmall.warehouse.entity.iservice.ILogisticsTemplateService;
import com.zsmall.warehouse.entity.iservice.IWarehouseService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.BeanUtils;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【rule_level_product_price】的数据库操作Service实现
 * @createDate 2024-05-15 11:51:47
 */
@Service
@Slf4j
@RequiredArgsConstructor
@Deprecated
public class RuleLevelProductPriceServiceImpl extends ServiceImpl<RuleLevelProductPriceMapper, RuleLevelProductPrice>
    implements RuleLevelProductPriceService {

    private final ProductSkuService productSkuService;

    // 数据库Service
    private final IProductService iProductService;
    private final IProductMappingService iProductMappingService;
    private final IProductCategoryService iProductCategoryService;
    private final ProductReviewRecordService productReviewRecordService;
    private final IProductReviewRecordService iProductReviewRecordService;
    private final IProductReviewChangeDetailService iProductReviewChangeDetailService;
    private final IProductCategoryRelationService iProductCategoryRelationService;
    private final IProductAttributeService iProductAttributeService;
    private final IProductGlobalAttributeService iProductGlobalAttributeService;
    private final IProductAttachmentService iProductAttachmentService;
    private final IProductSkuService iProductSkuService;
//    private final IProductActivityService iProductActivityService;
//    private final IProductActivityItemService iProductActivityItemService;
    private final IProductSkuAttributeService iProductSkuAttributeService;
    private final IProductSkuAttachmentService iProductSkuAttachmentService;
    private final IProductSkuDetailService iProductSkuDetailService;
    private final IProductSkuPriceService iProductSkuPriceService;
    private final IProductSkuPriceLogService iProductSkuPriceLogService;
    private final IProductSkuStockService iProductSkuStockService;
    private final ProductSkuPriceRuleService iProductSkuPriceRuleService;
    private final IProductSkuPriceRuleRelationService iProductSkuPriceRuleRelationService;
    private final ILogisticsTemplateService iLogisticsTemplateService;
    private final IProductImportRecordService iProductImportRecordService;
    private final IWarehouseService iWarehouseService;
    private final IDownloadRecordService iDownloadRecordService;
    private final ProductSupport productSupport;
    private final EsProductSupport esProductSupport;
    private final IWholesaleIntentionOrderItemService iWholesaleIntentionOrderItemService;

    private final ProductCodeGenerator productCodeGenerator;
    private final FileProperties fileProperties;
    private final RuleLevelProductPriceV2Service iRuleLevelProductPriceService;

    private final IMemberLevelV2ServiceImpl memberLevelService;


    private final IMemberRuleRelationV2ServiceImpl memberRuleRelationService;


    private final MemberLevelMapper memberLevelMapper;

    private final RuleLevelProductPriceMapper ruleLevelProductPriceMapper;


    @Override
    public RuleLevelProductPriceTableInfoVo queryPageList(RulePriceQueryBo bo, PageQuery pageQuery) {

        // 等级 id 等级名称
//        LoginHelper.getLoginUser(TenantType.Supplier);
        TenantType tenantTypeEnum = LoginHelper.getTenantTypeEnum();
        bo.setProductType(ProductTypeEnum.NormalProduct.name());
        Long siteId = bo.getSiteId();
        // 这里开异步任务1
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        IPage<Product> productIPage = iProductService.queryPageListForMemberLevel(pageQuery.build(), bo);
        stopWatch.stop();
        log.info("查询方法结束,耗时信息:{}", stopWatch.prettyPrint());
        StopWatch stopWatch2 = new StopWatch();
        stopWatch2.start();
        // 这里开异步任务2
        // 查处所有匹配项
        String tenantId = LoginHelper.getTenantId();
        List<Product> records = productIPage.getRecords();

        List<MemberLevel> memberLevels = memberLevelService.list(new LambdaQueryWrapper<MemberLevel>().eq(MemberLevel::getDelFlag, 0)
                                                                                                      .eq(MemberLevel::getTenantId, tenantId)
                                                                                                      .eq(MemberLevel::getStatus, 0));
        //  可以不分配 直接分价
        //  List<MemberRuleRelation> ruleRelations = memberRuleRelationService.list(new LambdaQueryWrapper<MemberRuleRelation>().eq(MemberRuleRelation::getDelFlag, 0));
        List<Long> productIds = records.stream().map(Product::getId).collect(Collectors.toList());
        UniversalQueryBo universalQueryBo = new UniversalQueryBo();
        universalQueryBo.setQueryType(bo.getQueryType());
        universalQueryBo.setQueryValue(bo.getQueryValue());
        universalQueryBo.setShelfState(bo.getShelfState());
        universalQueryBo.setProductType(bo.getProductType());
        universalQueryBo.setSiteId(bo.getSiteId());
//        universalQueryBo.setAuditStatus(bo.getA);
        universalQueryBo.setSkuAuditStatus(bo.getSkuAuditStatus());
        universalQueryBo.setSkuShelfState(bo.getSkuShelfState());


        // 批量查,查完了通过id对比放入HashSet
        List<ProductSkuListVo> productSkuLists = iProductSkuService.queryProductSkuListVoByProductIds(productIds, siteId, universalQueryBo);
        List<Long> productSkuIds = productSkuLists.stream().map(ProductSkuListVo::getProductSkuId)
                                                  .collect(Collectors.toList());

        // 这里开异步,需要考虑tenantId是不是能正确传入
        if (CollUtil.isNotEmpty(productIds) && CollUtil.isEmpty(productSkuIds)) {
            throw new RuntimeException("数据库存在脏数据,请联系管理员维护");
        }
        List<Long> levelIds = memberLevels.stream().map(MemberLevel::getId).collect(Collectors.toList());
        List<RuleLevelProductPrice> productPrices = null;
        if (CollUtil.isNotEmpty(productSkuIds) && CollUtil.isNotEmpty(levelIds)) {
            productPrices = baseMapper.getBatchPrice(levelIds, productSkuIds, tenantId,null);
        }
        if (CollUtil.isEmpty(productSkuIds) || CollUtil.isEmpty(levelIds)) {
            return null;
        }
        Map<String, RuleLevelProductPrice> priceMap = productPrices.stream()
                                                                   .collect(Collectors.toMap(i -> i.getLevelId() + "-" + i.getProductSkuId(), value -> value));

        // 此处memberLevel为固定三个,性能影响不大 不止3个了 需求变化了
        List<ProductSkuListVo> skuListVos = new ArrayList<>();
        for (ProductSkuListVo productSkuList : productSkuLists) {
            ProductSkuListVo skuListVo = new ProductSkuListVo();
            BeanUtils.copyProperties(productSkuList, skuListVo);
            List<RuleLevelSkuPriceVo> priceVos = new ArrayList<>();
            for (MemberLevel memberLevel : memberLevels) {
                String key = memberLevel.getId() + "-" + productSkuList.getProductSkuId();
                if (priceMap.containsKey(key)) {

                    RuleLevelProductPrice ruleLevelProductPrice = priceMap.get(memberLevel.getId() + "-" + productSkuList.getProductSkuId());
                    Long dictCode = memberLevel.getDictCode();
                    String levelName = memberLevelService.getLevelName(dictCode);


//                    if (ObjectUtil.isNotEmpty(ruleLevelProductPrice.getOriginalPickUpPrice()) || ObjectUtil.isNotEmpty(ruleLevelProductPrice.getOriginalDropShippingPrice())) {
//                    }
                    RuleLevelSkuPriceVo ruleLevelSkuPriceVo = new RuleLevelSkuPriceVo();
                    ruleLevelSkuPriceVo.setPickUpPrice(ruleLevelProductPrice.getOriginalPickUpPrice());
                    ruleLevelSkuPriceVo.setDropPrice(ruleLevelProductPrice.getOriginalDropShippingPrice());
                    ruleLevelSkuPriceVo.setDictCode(dictCode);
                    ruleLevelSkuPriceVo.setLevelName(levelName);
                    ruleLevelSkuPriceVo.setRuleLevelPriceId(ruleLevelProductPrice.getId());
                    ruleLevelSkuPriceVo.setLevelId(ruleLevelProductPrice.getLevelId());
                    priceVos.add(ruleLevelSkuPriceVo);
//                    skuListVo.setRulePriceId(ruleLevelProductPrice.getId());
                }
            }
            if (ObjectUtil.isNotEmpty(priceVos)) {
                skuListVo.setSkuPriceVoList(priceVos);
            }
            if (ObjectUtil.isNotEmpty(skuListVo)) {
                skuListVos.add(skuListVo);
            }
        }
        Map<Long, List<ProductSkuListVo>> listVoMap = skuListVos.stream()
                                                                .collect(Collectors.groupingBy(ProductSkuListVo::getProductId));

        // 到此结束任务2 开始同步
        List<ProductListVo> results = new ArrayList<>();
        for (Product product : records) {
            String name = product.getName();
            String productCode = product.getProductCode();
            ShelfStateEnum shelfState = product.getShelfState();
            ProductVerifyStateEnum verifyState = product.getVerifyState();
            SupportedLogisticsEnum supportedLogistics = product.getSupportedLogistics();

            Long productId = product.getId();

            ProductSkuAttachmentVo productSkuAttachmentVo = iProductSkuAttachmentService.queryFirstImageByProductId(productId);
            ProductListVo productListVo = new ProductListVo();
            productListVo.setProductName(name);
            productListVo.setProductCode(productCode);
            productListVo.setProductType(product.getProductType().name());
            productListVo.setVerifyState(verifyState.name());
            productListVo.setShelfState(shelfState.name());
            productListVo.setInventoryPushTime(product.getInventoryPushTime());
            productListVo.setSupportedLogistics(ObjectUtil.isNotNull(supportedLogistics) ? supportedLogistics.name() : null);

            if (ProductVerifyStateEnum.Rejected.equals(verifyState)) {
                ProductReviewRecord reviewRecord = iProductReviewRecordService.queryRecordByProductCodeAndReviewType(productCode, ProductVerifyStateEnum.Rejected);
                if (reviewRecord != null) {
                    productListVo.setReviewOpinion(reviewRecord.getReviewOpinion());
                    productListVo.setReviewOpinionOption(reviewRecord.getReviewOpinionOption());
                }
            }

            if (productSkuAttachmentVo != null) {
                String attachmentShowUrl = productSkuAttachmentVo.getAttachmentShowUrl();
                productListVo.setImageShowUrl(attachmentShowUrl);
            }
            List<ProductSkuListVo> skuListVo = listVoMap.get(product.getId());
            productListVo.setSkuList(skuListVo);

            results.add(productListVo);
        }


        RuleLevelProductPriceTableInfoVo vo = BeanUtil.toBean(TableDataInfo.build(results, productIPage.getTotal()), RuleLevelProductPriceTableInfoVo.class);
//        if (TenantType.Supplier.equals(tenantTypeEnum)) {
//            Boolean autoOnShelf = ZSMallSystemEventUtils.checkAutoOnShelfEvent(LoginHelper.getTenantId());
//            vo.setAutoOnShelf(autoOnShelf);
//        }
        stopWatch2.stop();
        log.info("数据解析流程结束:{}", stopWatch2.prettyPrint());
        return vo;

    }


    /**
     * 功能描述：添加
     *
     * @param vo vo
     * @return {@link R }<{@link Void }>
     * <AUTHOR>
     * @date 2024/06/05
     */
    @Override
    public R<Void> add(List<MemberSkuPriceVo> vo) {
        String tenantId = LoginHelper.getTenantId();
        vo.forEach(item -> item.setRuleCustomizerTenantId(tenantId));
        RuleLevelProductPrice ruleLevelProductPrice = new RuleLevelProductPrice();
        BeanUtils.copyProperties(vo, ruleLevelProductPrice);
        if (ObjectUtil.isNotEmpty(ruleLevelProductPrice)) {

            if (ObjectUtil.isNotEmpty(ruleLevelProductPrice.getOriginalUnitPrice())
                && ObjectUtil.isNotEmpty(ruleLevelProductPrice.getOriginalOperationFee())
                && ObjectUtil.isNotEmpty(ruleLevelProductPrice.getOriginalFinalDeliveryFee())
            ) {

                BigDecimal drop = ruleLevelProductPrice.getOriginalUnitPrice()
                                                       .add(ruleLevelProductPrice.getOriginalOperationFee())
                                                       .add(ruleLevelProductPrice.getPlatformFinalDeliveryFee());
                ruleLevelProductPrice.setPlatformDropShippingPrice(drop);
                BigDecimal pick = ruleLevelProductPrice.getOriginalUnitPrice()
                                                       .add(ruleLevelProductPrice.getOriginalOperationFee());
                ruleLevelProductPrice.setOriginalPickUpPrice(pick);
            }
            if (ObjectUtil.isNotEmpty(ruleLevelProductPrice.getOriginalUnitPrice())
                && ObjectUtil.isNotEmpty(ruleLevelProductPrice.getOriginalOperationFee())
            ) {
                BigDecimal pick = ruleLevelProductPrice.getOriginalUnitPrice()
                                                       .add(ruleLevelProductPrice.getOriginalOperationFee());
                ruleLevelProductPrice.setOriginalPickUpPrice(pick);
            }
        }
        save(ruleLevelProductPrice);
        return R.ok();
    }

    /**
     * 功能描述：编辑 要加密等性校验
     *
     * @param vo vo
     * @return {@link R }<{@link Void }>
     * <AUTHOR>
     * @date 2024/06/05
     */
    @Override
    @ProductPriceOperateLimit
    public R<Void> edit(MemberSkuPriceVo vo) {
        String tenantId = LoginHelper.getTenantId();
        Long userId = LoginHelper.getUserId();
        Long productId = vo.getProductId();
        Long productSkuId = vo.getProductSkuId();
        List<MemberLevelPriceVo> priceListVo = vo.getMemberLevelPriceListVo();
        List<RuleLevelProductPrice> ruleLevelProductPrices = new ArrayList<>();
        List<RuleLevelProductPrice> ruleLevelForSave = new ArrayList<>();
        // 这里原本设计用集合便利的方式 结果前端说不会用 做不了 卡组件 非让用大对象 后续维护的人不要惊讶为什么后段代码这么写
        for (MemberLevelPriceVo priceVo : priceListVo) {
            //priceVo.setOriginalFinalDeliveryFee(BigDecimal.ZERO);
            // 默认售价逻辑
            if (priceVo.getIsDefault()) {
                BigDecimal originalUnitPrice = priceVo.getOriginalUnitPrice();
                BigDecimal originalFinalDeliveryFee = priceVo.getOriginalFinalDeliveryFee();
                BigDecimal originalOperationFee = priceVo.getOriginalOperationFee();
                if (ObjectUtil.isNotEmpty(originalUnitPrice) && ObjectUtil.isNotEmpty(originalFinalDeliveryFee) && ObjectUtil.isNotEmpty(originalOperationFee)) {
                    changeProductPrice(productSkuId, priceVo.getOriginalUnitPrice(), priceVo.getOriginalOperationFee(), priceVo.getOriginalFinalDeliveryFee(), LoginHelper.getTenantId());
                    continue;
                }
            }

            RuleLevelProductPrice ruleLevelProductPrice = new RuleLevelProductPrice();
            ruleLevelProductPrice.setRuleCustomizerTenantId(tenantId);
            ruleLevelProductPrice.setProductSkuId(productSkuId);
            ruleLevelProductPrice.setProductId(productId);
            ruleLevelProductPrice.setDelFlag(0);
            ruleLevelProductPrice.setCreateBy(userId);
            ruleLevelProductPrice.setUpdateBy(userId);
            BigDecimal pickPrice = null;
            BigDecimal dropPrice = null;


            ruleLevelProductPrice.setId(priceVo.getRulePriceId());
            ruleLevelProductPrice.setLevelId(priceVo.getLevelId());
            ruleLevelProductPrice.setOriginalUnitPrice(priceVo.getOriginalUnitPrice());
            ruleLevelProductPrice.setOriginalOperationFee(priceVo.getOriginalOperationFee());
            ruleLevelProductPrice.setOriginalFinalDeliveryFee(priceVo.getOriginalFinalDeliveryFee());

            ruleLevelProductPrice.setPlatformUnitPrice(priceVo.getOriginalUnitPrice());
            ruleLevelProductPrice.setPlatformOperationFee(priceVo.getOriginalOperationFee());
            ruleLevelProductPrice.setPlatformFinalDeliveryFee(priceVo.getOriginalFinalDeliveryFee());

            // 两个价格需要计算
            pickPrice = computationPrice(priceVo.getOriginalUnitPrice(), priceVo.getOriginalFinalDeliveryFee(), priceVo.getOriginalOperationFee(), LogisticsTypeEnum.PickUp);
            dropPrice = computationPrice(priceVo.getOriginalUnitPrice(), priceVo.getOriginalFinalDeliveryFee(), priceVo.getOriginalOperationFee(), LogisticsTypeEnum.DropShipping);

            ruleLevelProductPrice.setOriginalPickUpPrice(pickPrice);
            ruleLevelProductPrice.setOriginalDropShippingPrice(dropPrice);
            ruleLevelProductPrice.setPlatformDropShippingPrice(dropPrice);
            ruleLevelProductPrice.setPlatformPickUpPrice(pickPrice);
            // 实际为新增业务 产品糅合在一块了
            if (ObjectUtil.isEmpty(priceVo.getRulePriceId()) && (ObjectUtil.isNotEmpty(priceVo.getOriginalUnitPrice())
                || ObjectUtil.isNotEmpty(priceVo.getOriginalOperationFee())
                || ObjectUtil.isNotEmpty(priceVo.getOriginalFinalDeliveryFee()))) {
                if (ObjectUtil.isEmpty(ruleLevelProductPrice.getId())) {
                    ruleLevelForSave.add(ruleLevelProductPrice);
                    continue;
                }
            }
            ruleLevelProductPrices.add(ruleLevelProductPrice);
        }

        for (RuleLevelProductPrice ruleLevelProductPrice : ruleLevelProductPrices) {
            ruleLevelProductPriceMapper.updateById(ruleLevelProductPrice);
        }
        // 最大3
        for (RuleLevelProductPrice price : ruleLevelForSave) {
            ruleLevelProductPriceMapper.insert(price);
        }

//        for (int i = 0; i <= 3; i++) {
//            RuleLevelProductPrice ruleLevelProductPrice = new RuleLevelProductPrice();
//
//            ruleLevelProductPrice.setRuleCustomizerTenantId(tenantId);
//            ruleLevelProductPrice.setProductSkuId(productSkuId);
//            ruleLevelProductPrice.setProductId(productId);
//            ruleLevelProductPrice.setDelFlag(0);
//            ruleLevelProductPrice.setCreateBy(userId);
//            ruleLevelProductPrice.setUpdateBy(userId);
//            BigDecimal pickPrice = null;
//            BigDecimal dropPrice = null;
//            // 黄金
//            if (i == 0) {
//                // 编辑逻辑里面还有新增逻辑
//                MemberLevel one = memberLevelService.getOne(new LambdaQueryWrapper<MemberLevel>().eq(MemberLevel::getDictCode, MemberlLevelEnum.GOLD.getDictCode()));
//                // 如果没有id 但是有金额说明要走新增逻辑
//                if (ObjectUtil.isNull(one)) {
//                    continue;
//                }
//
//                ruleLevelProductPrice.setId(vo.getRulePriceId1());
//                ruleLevelProductPrice.setLevelId(one.getId());
//                ruleLevelProductPrice.setOriginalUnitPrice(originalUnitPrice);
//                ruleLevelProductPrice.setOriginalOperationFee(originalOperationFee);
//                ruleLevelProductPrice.setOriginalFinalDeliveryFee(originalFinalDeliveryFee);
//
//                ruleLevelProductPrice.setPlatformUnitPrice(originalUnitPrice);
//                ruleLevelProductPrice.setPlatformOperationFee(originalOperationFee);
//                ruleLevelProductPrice.setPlatformFinalDeliveryFee(vo.getOriginalFinalDeliveryFee2());
//
//                // 两个价格需要计算
//                pickPrice = computationPrice(originalUnitPrice, originalFinalDeliveryFee, originalOperationFee, LogisticsTypeEnum.PickUp);
//                dropPrice = computationPrice(originalUnitPrice, originalFinalDeliveryFee, originalOperationFee, LogisticsTypeEnum.DropShipping);
//
//            }
//            // 白银
//            if (i == 1) {
//                MemberLevel one = memberLevelService.getOne(new LambdaQueryWrapper<MemberLevel>().eq(MemberLevel::getDictCode, MemberlLevelEnum.SILVER.getDictCode()));
//                // 白银等级 商品 如果存在记录就更新
//                if (ObjectUtil.isNull(one)) {
//                    continue;
//                }
//
//                ruleLevelProductPrice.setLevelId(one.getId());
//                ruleLevelProductPrice.setId(vo.getRulePriceId2());
//                ruleLevelProductPrice.setOriginalUnitPrice(vo.getOriginalUnitPrice2());
//                ruleLevelProductPrice.setOriginalOperationFee(vo.getOriginalOperationFee2());
//                ruleLevelProductPrice.setOriginalFinalDeliveryFee(vo.getOriginalFinalDeliveryFee2());
//
//                ruleLevelProductPrice.setPlatformUnitPrice(vo.getOriginalUnitPrice2());
//                ruleLevelProductPrice.setPlatformOperationFee(vo.getOriginalOperationFee2());
//                ruleLevelProductPrice.setPlatformFinalDeliveryFee(vo.getOriginalFinalDeliveryFee2());
//
//                pickPrice = computationPrice(vo.getOriginalUnitPrice2(), vo.getOriginalFinalDeliveryFee2(), vo.getOriginalOperationFee2(), LogisticsTypeEnum.PickUp);
//                dropPrice = computationPrice(vo.getOriginalUnitPrice2(), vo.getOriginalFinalDeliveryFee2(), vo.getOriginalOperationFee2(), LogisticsTypeEnum.DropShipping);
//
//            }
//            // 青铜
//            if (i == 2) {
//                ruleLevelProductPrice.setId(vo.getRulePriceId3());
//                MemberLevel one = memberLevelService.getOne(new LambdaQueryWrapper<MemberLevel>().eq(MemberLevel::getDictCode, MemberlLevelEnum.BRONZE.getDictCode()));
//                if (ObjectUtil.isNull(one)) {
//                    continue;
//                }
//                ruleLevelProductPrice.setLevelId(one.getId());
//
//                ruleLevelProductPrice.setOriginalUnitPrice(vo.getOriginalUnitPrice3());
//                ruleLevelProductPrice.setOriginalOperationFee(vo.getOriginalOperationFee3());
//                ruleLevelProductPrice.setOriginalFinalDeliveryFee(vo.getOriginalFinalDeliveryFee3());
//
//                ruleLevelProductPrice.setPlatformUnitPrice(vo.getOriginalUnitPrice3());
//                ruleLevelProductPrice.setPlatformOperationFee(vo.getOriginalOperationFee3());
//                ruleLevelProductPrice.setPlatformFinalDeliveryFee(vo.getOriginalFinalDeliveryFee3());
//
//                pickPrice = computationPrice(vo.getOriginalUnitPrice3(), vo.getOriginalFinalDeliveryFee3(), vo.getOriginalOperationFee3(), LogisticsTypeEnum.PickUp);
//                dropPrice = computationPrice(vo.getOriginalUnitPrice3(), vo.getOriginalFinalDeliveryFee3(), vo.getOriginalOperationFee3(), LogisticsTypeEnum.DropShipping);
//
//            }
//            // 改默认售价逻辑
//            if (i == 3) {
//                BigDecimal originalUnitPrice = vo.getOriginalUnitPrice();
//                BigDecimal originalFinalDeliveryFee = vo.getOriginalFinalDeliveryFee();
//                BigDecimal originalOperationFee = vo.getOriginalOperationFee();
//                if (ObjectUtil.isNotEmpty(originalUnitPrice) && ObjectUtil.isNotEmpty(originalFinalDeliveryFee) && ObjectUtil.isNotEmpty(originalOperationFee)) {
//
//                    changeProductPrice(ruleLevelProductPrice.getProductSkuId(), vo.getOriginalUnitPrice(), vo.getOriginalOperationFee(), vo.getOriginalFinalDeliveryFee(), LoginHelper.getTenantId());
//                    continue;
//                }
//
//            }
//
//            ruleLevelProductPrice.setOriginalPickUpPrice(pickPrice);
//            ruleLevelProductPrice.setOriginalDropShippingPrice(dropPrice);
//            ruleLevelProductPrice.setPlatformDropShippingPrice(dropPrice);
//            ruleLevelProductPrice.setPlatformPickUpPrice(pickPrice);
//            // 实际为新增业务 产品糅合在一块了
//            if (ObjectUtil.isEmpty(vo.getRulePriceId1()) && (ObjectUtil.isNotEmpty(originalUnitPrice)
//                || ObjectUtil.isNotEmpty(originalOperationFee)
//                || ObjectUtil.isNotEmpty(originalFinalDeliveryFee))) {
//                if (ObjectUtil.isEmpty(ruleLevelProductPrice.getId())) {
//                    ruleLevelForSave.add(ruleLevelProductPrice);
//                    continue;
//                }
//            }
//
//            if (ObjectUtil.isEmpty(vo.getRulePriceId2()) && (ObjectUtil.isNotEmpty(vo.getOriginalUnitPrice2())
//                || ObjectUtil.isNotEmpty(vo.getOriginalOperationFee2())
//                || ObjectUtil.isNotEmpty(vo.getOriginalFinalDeliveryFee2()))) {
//                if (ObjectUtil.isEmpty(ruleLevelProductPrice.getId())) {
//                    ruleLevelForSave.add(ruleLevelProductPrice);
//                    continue;
//
//                }
//            }
//            if (ObjectUtil.isEmpty(vo.getRulePriceId3()) && (ObjectUtil.isNotEmpty(vo.getOriginalUnitPrice3())
//                || ObjectUtil.isNotEmpty(vo.getOriginalOperationFee3())
//                || ObjectUtil.isNotEmpty(vo.getOriginalFinalDeliveryFee3()))) {
//                if (ObjectUtil.isEmpty(ruleLevelProductPrice.getId())) {
//                    ruleLevelForSave.add(ruleLevelProductPrice);
//                    continue;
//                }
//            }
//
//            ruleLevelProductPrices.add(ruleLevelProductPrice);
//        }
//        // 最大3
//        for (RuleLevelProductPrice ruleLevelProductPrice : ruleLevelProductPrices) {
//            ruleLevelProductPriceMapper.updateById(ruleLevelProductPrice);
//        }
//        // 最大3
//        for (RuleLevelProductPrice price : ruleLevelForSave) {
//            ruleLevelProductPriceMapper.insert(price);
//        }
        return R.ok();
    }

    /**
     * 功能描述：更改产品价格
     *
     * @param productSkuId 产品sku id
     * @param
     * <AUTHOR>
     * @date 2024/05/22
     */
    private Integer changeProductPrice(Long productSkuId, BigDecimal unitPrice, BigDecimal operationFee,
                                       BigDecimal finalDeliveryFee, String tenantId) {
//        // 最终有效的SKU集合
//        List<ChangeFieldDTO> SPUChangeFields = new ArrayList<>();
//
//        List<ProductSku> validSkuList = new ArrayList<>();
//        List<ProductSkuReviewDTO> newProductSkuReviewList = new ArrayList<>();
//
//        // 已存在的SKU价格变更审核DTO
//        List<ProductSkuReviewDTO> productSkuReviewList = new ArrayList<>();
//        ProductSku productSku = iProductSkuService.queryById(productSkuId);
//        // 是否已存在改价
//        Boolean exists = iProductReviewRecordService.existsByProductSkuCode(productSku.getProductSkuCode(), ProductReviewTypeEnum.Price,
//            ProductVerifyStateEnum.Pending);
//        if (exists) {
//            return 1;
//        }
//
//
//        Product product = iProductService.queryByProductSkuCode(productSku.getProductSkuCode());
//        SupportedLogisticsEnum beforeSupportedLogistics = product.getSupportedLogistics();
//        SPUChangeFields.add(new ChangeFieldDTO("supportedLogistics", beforeSupportedLogistics.name(),
//            product.getSupportedLogistics().name()));
//        ProductVerifyStateEnum verifyState = product.getVerifyState();
//        ShelfStateEnum skuShelfState = ShelfStateEnum.OffShelf;
//        //
//        ProductSkuPrice productSkuPrice = iProductSkuPriceService.queryByProductSkuId(productSkuId);
//        // 金额是否变动
//        BigDecimal originalUnitPrice = productSkuPrice.getOriginalUnitPrice();
//        BigDecimal originalOperationFee = productSkuPrice.getOriginalOperationFee();
//        BigDecimal originalFinalDeliveryFee = productSkuPrice.getOriginalFinalDeliveryFee();
//        if (Objects.equals(originalUnitPrice, unitPrice) && Objects.equals(originalOperationFee, operationFee) && Objects.equals(finalDeliveryFee, originalFinalDeliveryFee)) {
//            return 2;
//        }
//
//        if (ObjectUtil.isNull(productSkuPrice)) {
//            productSkuPrice = new ProductSkuPrice();
//            productSkuPrice.setProductSkuId(productSkuId);
//            productSkuPrice.setProductSkuCode(productSku.getProductSkuCode());
//            productSkuPrice.setMsrp(BigDecimal.ZERO);
//            productSkuPrice.setOriginalUnitPrice(BigDecimal.ZERO);
//            productSkuPrice.setOriginalOperationFee(BigDecimal.ZERO);
//            productSkuPrice.setOriginalFinalDeliveryFee(BigDecimal.ZERO);
//            productSkuPrice.setOriginalPickUpPrice(BigDecimal.ZERO);
//            productSkuPrice.setOriginalDropShippingPrice(BigDecimal.ZERO);
//            productSkuPrice.setPlatformUnitPrice(BigDecimal.ZERO);
//            productSkuPrice.setPlatformOperationFee(BigDecimal.ZERO);
//            productSkuPrice.setPlatformFinalDeliveryFee(BigDecimal.ZERO);
//            productSkuPrice.setPlatformPickUpPrice(BigDecimal.ZERO);
//            productSkuPrice.setPlatformDropShippingPrice(BigDecimal.ZERO);
//        }
//        ProductSkuPrice matchRule = iProductSkuPriceRuleService.matchRule(productSku.getProductSkuCode(), unitPrice, operationFee, finalDeliveryFee, false);
//        Long newPriceRuleId = matchRule.getProductSkuPriceRuleId();
//
//        BigDecimal beforeMsrp = productSkuPrice.getMsrp();
//        BigDecimal beforeUnitPrice = productSkuPrice.getOriginalUnitPrice();
//        BigDecimal beforeOperationFee = productSkuPrice.getOriginalOperationFee();
//        BigDecimal beforeFinalDeliveryFee = productSkuPrice.getOriginalFinalDeliveryFee();
//        BigDecimal beforePickUpPrice = productSkuPrice.getOriginalPickUpPrice();
//        BigDecimal beforeDropShippingPrice = productSkuPrice.getOriginalDropShippingPrice();
//
//        BigDecimal unitPriceMd = matchRule.getPlatformUnitPrice();
//        BigDecimal operationFeeMd = matchRule.getPlatformOperationFee();
//        BigDecimal finalDeliveryFeeMd = matchRule.getPlatformFinalDeliveryFee();
//        BigDecimal pickUpPriceMd = matchRule.getPlatformPickUpPrice();
//        BigDecimal dropShippingPriceMd = matchRule.getPlatformDropShippingPrice();
//        BigDecimal msrp = productSkuPrice.getMsrp();
//        BigDecimal pickUpPrice = NumberUtil.add(unitPrice, operationFee);
//        BigDecimal dropShippingPrice = NumberUtil.add(pickUpPrice, finalDeliveryFee);
//        // 商品SPU未过审或者被拒绝时，价格变更直接生效
//        if (ProductVerifyStateEnum.Draft.equals(verifyState) || ProductVerifyStateEnum.Rejected.equals(verifyState)) {
//
//            productSkuPrice.setMsrp(msrp);
//            productSkuPrice.setOriginalUnitPrice(unitPrice);
//            productSkuPrice.setOriginalOperationFee(operationFee);
//            productSkuPrice.setOriginalFinalDeliveryFee(finalDeliveryFee);
//            productSkuPrice.setOriginalPickUpPrice(pickUpPrice);
//            productSkuPrice.setOriginalDropShippingPrice(dropShippingPrice);
//            productSkuPrice.setPlatformUnitPrice(unitPriceMd);
//            productSkuPrice.setPlatformOperationFee(operationFeeMd);
//            productSkuPrice.setPlatformFinalDeliveryFee(finalDeliveryFeeMd);
//            productSkuPrice.setPlatformPickUpPrice(pickUpPriceMd);
//            productSkuPrice.setPlatformDropShippingPrice(dropShippingPriceMd);
//
//            skuShelfState = ShelfStateEnum.ForcedOffShelf;
//            productSku.setVerifyState(ProductVerifyStateEnum.Draft);
//        } else if (ProductVerifyStateEnum.Accepted.equals(verifyState)) {  // 商品SPU已过审
//            ProductVerifyStateEnum skuVerifyState = productSku.getVerifyState();
//            // 但Sku未过审或被拒绝时
//            if (ProductVerifyStateEnum.Draft.equals(skuVerifyState) || ProductVerifyStateEnum.Rejected.equals(skuVerifyState)) {
//                productSkuPrice.setMsrp(msrp);
//                productSkuPrice.setOriginalUnitPrice(unitPrice);
//                productSkuPrice.setOriginalOperationFee(operationFee);
//                productSkuPrice.setOriginalFinalDeliveryFee(finalDeliveryFee);
//                productSkuPrice.setOriginalPickUpPrice(pickUpPrice);
//                productSkuPrice.setOriginalDropShippingPrice(dropShippingPrice);
//                productSkuPrice.setPlatformUnitPrice(unitPriceMd);
//                productSkuPrice.setPlatformOperationFee(operationFeeMd);
//                productSkuPrice.setPlatformFinalDeliveryFee(finalDeliveryFeeMd);
//                productSkuPrice.setPlatformPickUpPrice(pickUpPriceMd);
//                productSkuPrice.setPlatformDropShippingPrice(dropShippingPriceMd);
//
//                //如果价格信息已存在，则原价存入价格日志表，新价格存入价格表
//                if (ObjectUtil.isNotNull(productSkuPrice.getId())) {
//                    //记录价格日志
//                    iProductSkuPriceLogService.recordPriceChanges(productSkuPrice, PriceOperateLog.MemberPriceUpdate.name());
//                }
//
//                // 记录变更字段及值
//                ProductSkuReviewDTO productSkuReviewDTO = new ProductSkuReviewDTO();
//                productSkuReviewDTO.setProductSkuCode(productSku.getProductSkuCode());
//                productSkuReviewDTO.addField("msrp", NumberUtil.toStr(beforeMsrp), NumberUtil.toStr(msrp, "0"));
//                productSkuReviewDTO.addField("pickUpPrice", NumberUtil.toStr(beforePickUpPrice), NumberUtil.toStr(pickUpPrice, "0"));
//                productSkuReviewDTO.addField("dropShippingPrice", NumberUtil.toStr(beforeDropShippingPrice), NumberUtil.toStr(dropShippingPrice, "0"));
//                productSkuReviewDTO.addField("unitPrice", NumberUtil.toStr(beforeUnitPrice), NumberUtil.toStr(unitPrice, "0"));
//                productSkuReviewDTO.addField("operationFee", NumberUtil.toStr(beforeOperationFee), NumberUtil.toStr(operationFee, "0"));
//                productSkuReviewDTO.addField("finalDeliveryFee", NumberUtil.toStr(beforeFinalDeliveryFee), NumberUtil.toStr(finalDeliveryFee, "0"));
//                newProductSkuReviewList.add(productSkuReviewDTO);
//                productSku.setVerifyState(ProductVerifyStateEnum.Pending);
//                skuShelfState = ShelfStateEnum.ForcedOffShelf;
//            } else if (ProductVerifyStateEnum.Pending.equals(skuVerifyState)) {  // 正在审核中时，不接受新修改的价格
//                skuShelfState = ShelfStateEnum.ForcedOffShelf;
//            } else if (ProductVerifyStateEnum.Accepted.equals(skuVerifyState)) {  // 已通过审核时，需要提交价格变更审核
//                skuShelfState=ShelfStateEnum.OnShelf;
//                if (!NumberUtil.equals(beforeMsrp, msrp)
//                    || !NumberUtil.equals(beforeDropShippingPrice, dropShippingPrice)
//                    || !NumberUtil.equals(beforePickUpPrice, pickUpPrice)) {
//                    // 记录变更字段及值
//                    ProductSkuReviewDTO productSkuReviewDTO = new ProductSkuReviewDTO();
//                    productSkuReviewDTO.setProductSkuCode(productSku.getProductSkuCode());
//                    productSkuReviewDTO.addField("msrp", NumberUtil.toStr(beforeMsrp), NumberUtil.toStr(msrp, "0"));
//                    productSkuReviewDTO.addField("dropShippingPrice", NumberUtil.toStr(beforeDropShippingPrice), NumberUtil.toStr(dropShippingPrice, "0"));
//                    productSkuReviewDTO.addField("pickUpPrice", NumberUtil.toStr(beforePickUpPrice), NumberUtil.toStr(pickUpPrice, "0"));
//                    productSkuReviewDTO.addField("unitPrice", NumberUtil.toStr(beforeUnitPrice), NumberUtil.toStr(unitPrice, "0"));
//                    productSkuReviewDTO.addField("operationFee", NumberUtil.toStr(beforeOperationFee), NumberUtil.toStr(operationFee, "0"));
//                    productSkuReviewDTO.addField("finalDeliveryFee", NumberUtil.toStr(beforeFinalDeliveryFee), NumberUtil.toStr(finalDeliveryFee, "0"));
//                    productSkuReviewDTO.addField("ruleId", StrUtil.toString(newPriceRuleId), StrUtil.toString(newPriceRuleId));
//                    productSkuReviewList.add(productSkuReviewDTO);
//                }
//            }
//
//
//        }
//        iProductSkuPriceService.updateById(productSkuPrice);
//        productSku.setShelfState(skuShelfState);
//        validSkuList.add(productSku);
//
//        iProductSkuService.updateBatchById(validSkuList);
//        if (ProductVerifyStateEnum.Rejected.equals(verifyState)) {
//            product.setVerifyState(ProductVerifyStateEnum.Draft);
//        }
//
//        if (ProductVerifyStateEnum.Accepted.equals(verifyState)) {
//            if (CollUtil.isNotEmpty(productSkuReviewList)) {
//                ProductReviewDTO productReviewDTO = new ProductReviewDTO();
//                productReviewDTO.setProductCode(productSku.getProductCode());
//                productReviewDTO.setSubmitTenantId(LoginHelper.getTenantId());
//                productReviewDTO.setChangeFields(SPUChangeFields);
//                productReviewDTO.setProductSkuReviewList(productSkuReviewList);
//                productReviewDTO.setReviewType(ProductReviewTypeEnum.Price);
//                productReviewDTO.setReviewStatus(ProductVerifyStateEnum.Pending);
//                productReviewRecordService.generateReviewRecord(productReviewDTO);
//                product.setHasPriceChange(true);
//                // 存在价格变更审核，支持的物流类型要还原会修改前，等待价格审核通过后，再一起生效
//                product.setSupportedLogistics(beforeSupportedLogistics);
//            }
//
//            if (CollUtil.isNotEmpty(newProductSkuReviewList)) {
//                ProductReviewDTO productReviewDTO = new ProductReviewDTO();
//                productReviewDTO.setProductCode(productSku.getProductCode());
//                // 外部传参数,还有userId
//                productReviewDTO.setSubmitTenantId(tenantId);
//                productReviewDTO.setChangeFields(SPUChangeFields);
//                productReviewDTO.setProductSkuReviewList(newProductSkuReviewList);
//                productReviewDTO.setReviewType(ProductReviewTypeEnum.NewProductSku);
//                productReviewDTO.setReviewStatus(ProductVerifyStateEnum.Pending);
//                productReviewRecordService.generateReviewRecord(productReviewDTO);
//            }
//        }
//        ProductSku productSku1 = new ProductSku();
//        productSku1.setProductId(productSkuId);
//        productSku1.setProductSkuCode(productSku.getProductSkuCode());
////        productSkuService.setProductSkuStock(Arrays.asList(productSku1));
//        iProductService.updateById(product);
//        esProductSupport.productUpload(product);
        // 如果走的修改业务 打标记,后续新增的时候排除
        return 3;
    }


    @Override
    @ProductPriceOperateLimit
    public R<Void> add(MemberSkuPriceVo vo) {
        String tenantId = LoginHelper.getTenantId();
        Long userId = LoginHelper.getUserId();
        Long productId = vo.getProductId();
        Long productSkuId = vo.getProductSkuId();
        List<MemberLevelPriceVo> priceListVo = vo.getMemberLevelPriceListVo();
        List<RuleLevelProductPrice> ruleLevelProductPrices = new ArrayList<>();
        for (MemberLevelPriceVo memberLevelPriceVo : priceListVo) {
            memberLevelPriceVo.setOriginalFinalDeliveryFee(BigDecimal.ZERO);
            if (memberLevelPriceVo.getIsDefault()) {
                BigDecimal originalUnitPrice = memberLevelPriceVo.getOriginalUnitPrice();
                BigDecimal originalFinalDeliveryFee = memberLevelPriceVo.getOriginalFinalDeliveryFee();
                BigDecimal originalOperationFee = memberLevelPriceVo.getOriginalOperationFee();
                if (ObjectUtil.isNotEmpty(originalUnitPrice) && ObjectUtil.isNotEmpty(originalFinalDeliveryFee) && ObjectUtil.isNotEmpty(originalOperationFee)) {
                    changeProductPrice(productSkuId, memberLevelPriceVo.getOriginalUnitPrice(), memberLevelPriceVo.getOriginalOperationFee(), memberLevelPriceVo.getOriginalFinalDeliveryFee(), LoginHelper.getTenantId());
                    continue;
                }
            }
            RuleLevelProductPrice ruleLevelProductPrice = new RuleLevelProductPrice();
            ruleLevelProductPrices.add(ruleLevelProductPrice);
            ruleLevelProductPrice.setRuleCustomizerTenantId(tenantId);
            ruleLevelProductPrice.setProductSkuId(productSkuId);
            ruleLevelProductPrice.setProductId(productId);
            ruleLevelProductPrice.setDelFlag(0);
            ruleLevelProductPrice.setCreateBy(userId);
            ruleLevelProductPrice.setUpdateBy(userId);
            BigDecimal originalUnitPrice = memberLevelPriceVo.getOriginalUnitPrice();
            BigDecimal originalOperationFee = memberLevelPriceVo.getOriginalOperationFee();
            BigDecimal originalFinalDeliveryFee = memberLevelPriceVo.getOriginalFinalDeliveryFee();
            BigDecimal pickPrice = null;
            BigDecimal dropPrice = null;

//            MemberLevel one = memberLevelService.getOne(new LambdaQueryWrapper<MemberLevel>().eq(MemberLevel::getDictCode, MemberlLevelEnum.GOLD.getDictCode()));
//            if (ObjectUtil.isNull(one)) {
//                continue;
//            }
            if (ObjectUtil.isEmpty(originalUnitPrice) && ObjectUtil.isEmpty(originalOperationFee) && ObjectUtil.isEmpty(originalFinalDeliveryFee)) {
                continue;
            }
            ruleLevelProductPrice.setLevelId(memberLevelPriceVo.getLevelId());
            ruleLevelProductPrice.setOriginalUnitPrice(originalUnitPrice);
            ruleLevelProductPrice.setOriginalOperationFee(originalOperationFee);
            ruleLevelProductPrice.setOriginalFinalDeliveryFee(originalFinalDeliveryFee);

            ruleLevelProductPrice.setPlatformUnitPrice(originalUnitPrice);
            ruleLevelProductPrice.setPlatformOperationFee(originalOperationFee);
            ruleLevelProductPrice.setPlatformFinalDeliveryFee(originalFinalDeliveryFee);

            // 两个价格需要计算
            pickPrice = computationPrice(originalUnitPrice, originalFinalDeliveryFee, originalOperationFee, LogisticsTypeEnum.PickUp);
            dropPrice = computationPrice(originalUnitPrice, originalFinalDeliveryFee, originalOperationFee, LogisticsTypeEnum.DropShipping);

            ruleLevelProductPrice.setOriginalPickUpPrice(pickPrice);
            ruleLevelProductPrice.setOriginalDropShippingPrice(dropPrice);

            ruleLevelProductPrice.setPlatformPickUpPrice(pickPrice);
            ruleLevelProductPrice.setPlatformDropShippingPrice(dropPrice);
        }
        ((RuleLevelProductPriceV2ServiceImpl) AopContext.currentProxy()).saveBatch(ruleLevelProductPrices);
//        for (int i = 0; i < 3; i++) {
//            RuleLevelProductPrice ruleLevelProductPrice = new RuleLevelProductPrice();
//            ruleLevelProductPrice.setRuleCustomizerTenantId(tenantId);
//            ruleLevelProductPrice.setProductSkuId(productSkuId);
//            ruleLevelProductPrice.setProductId(productId);
//            ruleLevelProductPrice.setDelFlag(0);
//            ruleLevelProductPrice.setCreateBy(userId);
//            ruleLevelProductPrice.setUpdateBy(userId);
//            BigDecimal pickPrice = null;
//            BigDecimal dropPrice = null;
//            // 黄金
//            if (i == 0) {
//
//                MemberLevel one = memberLevelService.getOne(new LambdaQueryWrapper<MemberLevel>().eq(MemberLevel::getDictCode, MemberlLevelEnum.GOLD.getDictCode()));
//                if (ObjectUtil.isNull(one)) {
//                    continue;
//                }
//                if (ObjectUtil.isEmpty(originalUnitPrice1) && ObjectUtil.isEmpty(originalOperationFee1) && ObjectUtil.isEmpty(originalFinalDeliveryFee1)) {
//                    continue;
//                }
//                ruleLevelProductPrice.setLevelId(one.getId());
//                ruleLevelProductPrice.setOriginalUnitPrice(originalUnitPrice);
//                ruleLevelProductPrice.setOriginalOperationFee(originalOperationFee);
//                ruleLevelProductPrice.setOriginalFinalDeliveryFee(originalFinalDeliveryFee);
//
//                ruleLevelProductPrice.setPlatformUnitPrice(originalUnitPrice);
//                ruleLevelProductPrice.setPlatformOperationFee(originalOperationFee);
//                ruleLevelProductPrice.setPlatformFinalDeliveryFee(originalFinalDeliveryFee);
//
//                // 两个价格需要计算
//                pickPrice = computationPrice(originalUnitPrice, originalFinalDeliveryFee, originalOperationFee, LogisticsTypeEnum.PickUp);
//                dropPrice = computationPrice(originalUnitPrice, originalFinalDeliveryFee, originalOperationFee, LogisticsTypeEnum.DropShipping);
//
//            }
//            // 白银
//            if (i == 1) {
//                MemberLevel one = memberLevelService.getOne(new LambdaQueryWrapper<MemberLevel>().eq(MemberLevel::getDictCode, MemberlLevelEnum.SILVER.getDictCode()));
//                if (ObjectUtil.isNull(one)) {
////                    throw new RuntimeException("会员等级发生变化,请重新提交价格!");
//                    continue;
//                }
//                if (ObjectUtil.isEmpty(originalUnitPrice2) && ObjectUtil.isEmpty(originalOperationFee2) && ObjectUtil.isEmpty(originalFinalDeliveryFee2)) {
//                    continue;
//                }
//                ruleLevelProductPrice.setLevelId(one.getId());
//
//                ruleLevelProductPrice.setOriginalUnitPrice(vo.getOriginalUnitPrice2());
//                ruleLevelProductPrice.setOriginalOperationFee(vo.getOriginalOperationFee2());
//                ruleLevelProductPrice.setOriginalFinalDeliveryFee(vo.getOriginalFinalDeliveryFee2());
//
//                ruleLevelProductPrice.setPlatformUnitPrice(vo.getOriginalUnitPrice2());
//                ruleLevelProductPrice.setPlatformOperationFee(vo.getOriginalOperationFee2());
//                ruleLevelProductPrice.setPlatformFinalDeliveryFee(vo.getOriginalFinalDeliveryFee2());
//
//                pickPrice = computationPrice(vo.getOriginalUnitPrice2(), vo.getOriginalFinalDeliveryFee2(), vo.getOriginalOperationFee2(), LogisticsTypeEnum.PickUp);
//                dropPrice = computationPrice(vo.getOriginalUnitPrice2(), vo.getOriginalFinalDeliveryFee2(), vo.getOriginalOperationFee2(), LogisticsTypeEnum.DropShipping);
//
//            }
//            // 青铜
//            if (i == 2) {
//                MemberLevel one = memberLevelService.getOne(new LambdaQueryWrapper<MemberLevel>().eq(MemberLevel::getDictCode, MemberlLevelEnum.BRONZE.getDictCode()));
//                if (ObjectUtil.isNull(one)) {
//                    continue;
//                }
//                if (ObjectUtil.isEmpty(originalUnitPrice3) && ObjectUtil.isEmpty(originalOperationFee3) && ObjectUtil.isEmpty(originalFinalDeliveryFee3)) {
//                    continue;
//                }
//                // 如果没给价格 就跳过
//
//                ruleLevelProductPrice.setLevelId(one.getId());
//
//                ruleLevelProductPrice.setOriginalUnitPrice(vo.getOriginalUnitPrice3());
//                ruleLevelProductPrice.setOriginalOperationFee(vo.getOriginalOperationFee3());
//                ruleLevelProductPrice.setOriginalFinalDeliveryFee(vo.getOriginalFinalDeliveryFee3());
//
//                ruleLevelProductPrice.setPlatformUnitPrice(vo.getOriginalUnitPrice3());
//                ruleLevelProductPrice.setPlatformOperationFee(vo.getOriginalOperationFee3());
//                ruleLevelProductPrice.setPlatformFinalDeliveryFee(vo.getOriginalFinalDeliveryFee3());
//
//                pickPrice = computationPrice(vo.getOriginalUnitPrice3(), vo.getOriginalFinalDeliveryFee3(), vo.getOriginalOperationFee3(), LogisticsTypeEnum.PickUp);
//                dropPrice = computationPrice(vo.getOriginalUnitPrice3(), vo.getOriginalFinalDeliveryFee3(), vo.getOriginalOperationFee3(), LogisticsTypeEnum.DropShipping);
//
//            }
//            ruleLevelProductPrice.setOriginalPickUpPrice(pickPrice);
//            ruleLevelProductPrice.setOriginalDropShippingPrice(dropPrice);
//
//            ruleLevelProductPrice.setPlatformPickUpPrice(pickPrice);
//            ruleLevelProductPrice.setPlatformDropShippingPrice(dropPrice);
//
//            ruleLevelProductPrices.add(ruleLevelProductPrice);
//        }


        return R.ok();
    }

    @Override
    public MemberSkuPriceVo getSkuPrice(List<String> ruleLevelPriceIds) {
        // 查到的sku纬度是同一件商品
        List<RuleLevelProductPrice> ruleLevelProductPrices = ruleLevelProductPriceMapper.selectBatchIds(ruleLevelPriceIds);
        // ruleLevelProductPrices转换成levelId的list,并且去重
        List<Long> levelIds = ruleLevelProductPrices.stream()
                                                    .map(RuleLevelProductPrice::getLevelId)
                                                    .distinct()
                                                    .collect(Collectors.toList());
        MemberSkuPriceVo memberSkuPriceVo = new MemberSkuPriceVo();
        ProductSkuPrice productSkuPrice = iProductSkuPriceService.getOne(new LambdaQueryWrapper<ProductSkuPrice>().eq(ProductSkuPrice::getProductSkuId, ruleLevelProductPrices.get(0)
                                                                                                                                                                              .getProductSkuId()));
        LambdaQueryWrapper<MemberLevel> wrapper = new LambdaQueryWrapper<MemberLevel>().in(MemberLevel::getId, levelIds);


        List<MemberLevel> memberLevels = memberLevelService.list(wrapper);
        List<Long> codeList = memberLevels.stream().map(MemberLevel::getDictCode).collect(Collectors.toList());
        List<LevelPriceDTO> levelNames = memberLevelMapper.getLevelNames(codeList);
        Map<String, LevelPriceDTO> codeMap = levelNames.stream()
                                                       .collect(Collectors.toMap(LevelPriceDTO::getDictCode, Function.identity()));

        Map<Long, MemberLevel> memberLevelMap = memberLevels.stream()
                                                            .collect(Collectors.toMap(MemberLevel::getId, Function.identity()));


//        拿到id
        List<MemberLevelPriceVo> memberLevelPriceListVo = new ArrayList<>();
        // 开启的等级
//        memberLevelPriceListVo
        MemberLevelPriceVo first = new MemberLevelPriceVo();

        first.setOriginalUnitPrice(productSkuPrice.getOriginalUnitPrice());
        first.setOriginalOperationFee(productSkuPrice.getOriginalOperationFee());
        first.setOriginalFinalDeliveryFee(productSkuPrice.getOriginalFinalDeliveryFee());


        for (RuleLevelProductPrice ruleLevelProductPrice : ruleLevelProductPrices) {
            Long levelId = ruleLevelProductPrice.getLevelId();
            MemberLevel memberLevel = memberLevelMap.get(levelId);
            MemberLevelPriceVo memberLevelPriceVo = new MemberLevelPriceVo();
            LevelPriceDTO levelPriceDTO = codeMap.get(String.valueOf(memberLevel.getDictCode()));
            if(ObjectUtil.isNotEmpty(levelPriceDTO)){
                memberLevelPriceVo.setLevelName(levelPriceDTO.getDictLabel());
            }else{
                throw new RuntimeException("会员等级字典异常,请联系管理员");
            }
            memberLevelPriceVo.setLevelId(levelId);
            memberLevelPriceVo.setOriginalUnitPrice(ruleLevelProductPrice.getOriginalUnitPrice());
            memberLevelPriceVo.setOriginalOperationFee(ruleLevelProductPrice.getOriginalOperationFee());
            memberLevelPriceVo.setOriginalFinalDeliveryFee(ruleLevelProductPrice.getOriginalFinalDeliveryFee());
            memberLevelPriceVo.setRulePriceId(ruleLevelProductPrice.getId());
            memberLevelPriceVo.setIsDefault(Boolean.FALSE);
            memberLevelPriceListVo.add(memberLevelPriceVo);
            MemberLevel one = memberLevelMap.get(levelId);
            memberLevelPriceVo.setCreateTime(one.getCreateTime());
            Long dictCode = null;
            if (ObjectUtil.isNotNull(one)) {
                dictCode = one.getDictCode();
            }
            memberLevelPriceVo.setDictCode(dictCode);
        }
        // 根据元素的创建时间排序
        memberLevelPriceListVo.sort(Comparator.comparing(MemberLevelPriceVo::getCreateTime));
        memberSkuPriceVo.pushSaleOrderItemsList(memberLevelPriceListVo);
        return memberSkuPriceVo;
    }

    @Override
    public R<Void> uploadProductExcel(MultipartFile file) throws IOException {
//        LoginUser loginUser = LoginHelper.getLoginUser(TenantType.Supplier);
        String tenantId = LoginHelper.getTenantId();
//        String tenantId = "SISY1QG";
        if (file == null) {
            return R.fail(ZSMallStatusCodeEnum.UPLOAD_FILE_IS_EMPTY);
        }

        Boolean existsed = iProductImportRecordService.existImportingRecord();
        if (existsed) {
            return R.fail(ZSMallStatusCodeEnum.PRODUCT_IMPORT_FILE_EXIST_IMPORTING);
        }

        InputStream inputStream = file.getInputStream();
        ExcelReader reader = ExcelUtil.getReader(inputStream);

        if (reader != null) {
            Sheet sheet = reader.getSheet();
            String sheetName = sheet.getSheetName();
            log.info("上传商品Excel - sheetName = {}", sheetName);
            int columnCount = reader.getColumnCount();
            log.info("上传商品Excel - columnCount = {}", columnCount);
            if (columnCount < 56) {
                return R.fail(ZSMallStatusCodeEnum.EXCEL_COLUMN_COUNT_NOT_MATCH);
            }
        }

        // 初始化信息构建者
        ExcelMsgBuilder<MemberPriceProductImportDTO> builder = ZExcelUtil.msgBuilder(reader, 1, MemberPriceProductImportDTO.class);
        builder.setMsgPrefix("<p>").setMsgSuffix("</p></br>");

        ProductImportRecord record = new ProductImportRecord();
        record.setImportRecordNo(productCodeGenerator.codeGenerate(BusinessCodeEnum.ProductImportRecordNo));
        record.setImportFileName(file.getOriginalFilename());
        record.setImportState(ImportStateEnum.Importing);
        iProductImportRecordService.save(record);
        List<LevelPriceDTO> dicts = memberLevelMapper.getDictCodesByType("market_levelname");
        Map<String, String> map = dicts.stream()
                                       .collect(Collectors.toMap(LevelPriceDTO::getDictLabel, LevelPriceDTO::getDictCode));
        var ref = new Object() {
            String errorMsg = null;
        };
        // 这里开始剪裁

        LoginUser loginUser = LoginHelper.getLoginUser();

        Runnable task = () -> {
            String importRecordNo = record.getImportRecordNo();
            Long recordId = record.getId();

            List<MemberPriceProductImportDTO> dtoList;
            try {
                dtoList = ZExcelUtil.parseFieldDTOV2(reader, MemberPriceProductImportDTO.class, 1, 10);
                if (CollUtil.isEmpty(dtoList)) {
                    throw new ExcelMessageException(builder.build(ExcelMessageEnum.NOT_VALID_ROW));
                }
            } catch (ExcelMessageException e) {
                record.setImportMessage(e.getLocaleMessage().toJSON());
                record.setImportState(ImportStateEnum.Failed);
                iProductImportRecordService.updateById(record);
                return ;
            }

            LocaleMessage globalMessage = new LocaleMessage();
            // 全局Sku查重
            List<String> globalSku = new ArrayList<>();
            // 全局Upc查重
            List<String> globalUpc = new ArrayList<>();

            Map<String, ProductImportBo> productGroupMap = new HashMap<>();
            // 导入记录完成
            // 保存产品相关信息
            for (MemberPriceProductImportDTO dto : dtoList) {
                String sku = dto.getSku();
                // 每次循环开始设置当前的行号，以保证构建信息时展示的行号是正确的
                int showRowIndex = dto.getShowRowIndex();
                builder.setNowShowRow(showRowIndex);

                try {
                    String group = dto.getGroup();
                    ProductImportBo product = productGroupMap.get(group);
                    List<ProductAttribute> productAttributeList;

                    List<ProductSkuImportBo> productSkuList;
                    List<ProductGlobalAttribute> globalAttributeList;
                    List<ProductAttribute> optionalSpecList;
                    List<MemberRuleRelation> memberRuleRelationList;
                    List<LevelPriceParseDTO> priceDTOS = new ArrayList<>();
                    List<JSONObject> priceJSON = dto.getPriceJSON();
                    priceJSON.forEach(jsonObject -> {

                        LevelPriceParseDTO levelPrice = new LevelPriceParseDTO();

                        String unitPrice = String.valueOf(jsonObject.get("unitPrice"));
                        String operationFee = String.valueOf(jsonObject.get("operationFee"));
                        String finalDeliveryFee = String.valueOf(jsonObject.get("finalDeliveryFee"));
                        String levelName = String.valueOf(jsonObject.get("levelName"));
                        if (ObjectUtil.isNotEmpty(unitPrice)&&!"null".equals(unitPrice)) {
                            levelPrice.setUnitPrice(new BigDecimal(unitPrice));
                        }
                        if (ObjectUtil.isNotEmpty(operationFee)&&!"null".equals(operationFee)) {
                            levelPrice.setOperationFee(new BigDecimal(operationFee));
                        }
                        if (ObjectUtil.isNotEmpty(finalDeliveryFee)&&!"null".equals(finalDeliveryFee)) {
                            levelPrice.setFinalDeliveryFee(new BigDecimal(finalDeliveryFee));
                        }
                        // 截取会员价格之前的字符串去匹配id levelName
                        // 对字符串进行截取 会员售价前面的所有字符串
                        levelPrice.setLevelName(levelName);
                        int index = levelName.indexOf("会员售价");
                        if (index != -1) {
                            levelName = levelName.substring(0, index);
                        }
                        String dictCode = map.get(levelName);
                        levelPrice.setDictCode(dictCode);

                        ProductSku one = iProductSkuService.getOne(new LambdaQueryWrapper<ProductSku>().eq(ProductSku::getSku, sku)
                                                                                                       .last("limit 1"));
                        // 新增场景
                        if (ObjectUtil.isNotEmpty(one)) {
                            levelPrice.setProductId(one.getProductId());
                            levelPrice.setProductSkuId(one.getId());
                        }

                        priceDTOS.add(levelPrice);
                    });


                    if (product == null) {
                        product = new ProductImportBo();

                        String category = dto.getCategory();
                        String productName = dto.getProductName();
                        String supportedLogistics = dto.getSupportedLogistics();
                        String forbiddenChannel = dto.getForbiddenChannel();
                        String description = dto.getDescription();

                        if (StrUtil.isBlank(category)) {
                            throw new ExcelMessageException(builder.build(MemberPriceProductImportDTO::getCategory, ExcelMessageEnum.REQUIRE));
                        }
                        if (StrUtil.isBlank(productName)) {
                            throw new ExcelMessageException(builder.build(MemberPriceProductImportDTO::getProductName, ExcelMessageEnum.REQUIRE));
                        }
                        if (StrUtil.isBlank(supportedLogistics)) {
                            throw new ExcelMessageException(builder.build(MemberPriceProductImportDTO::getSupportedLogistics, ExcelMessageEnum.REQUIRE));
                        }

                        if (productName.length() > 150) {
                            throw new ExcelMessageException(builder.build(MemberPriceProductImportDTO::getProductName, ExcelMessageEnum.PRODUCT_NAME_LIMIT));
                        }

                        SupportedLogisticsEnum supportedLogisticsEnum;
                        // 支持的物流类型
                        if (StrUtil.equals(supportedLogistics, "Pick Up & Dropshipping")) {
                            supportedLogisticsEnum = SupportedLogisticsEnum.All;
                        } else if (StrUtil.equals(supportedLogistics, "Pick Up Only")) {
                            supportedLogisticsEnum = SupportedLogisticsEnum.PickUpOnly;
                        } else if (StrUtil.equals(supportedLogistics, "Dropshipping Only")) {
                            supportedLogisticsEnum = SupportedLogisticsEnum.DropShippingOnly;
                        } else {
                            throw new ExcelMessageException(builder.build(MemberPriceProductImportDTO::getSupportedLogistics, ExcelMessageEnum.ILLEGAL_ARGUMENT));
                        }

                        // 禁售渠道
                        JSONArray forbiddenChannelArray = new JSONArray();
                        if (StrUtil.isNotBlank(forbiddenChannel)) {
                            forbiddenChannel = StrUtil.replace(forbiddenChannel, "；", ";");
                            List<String> channelList = StrUtil.split(forbiddenChannel, ";");
                            for (String channel : channelList) {
                                try {
                                    ChannelTypeEnum channelType = ChannelTypeEnum.valueOf(channel);
                                    forbiddenChannelArray.add(channelType.name());
                                } catch (Exception e) {
                                }
                            }
                        }

//                        if (!forbiddenChannelArray.contains(ChannelTypeEnum.Wayfair.name())) {
//                            forbiddenChannelArray.add(ChannelTypeEnum.Wayfair.name());
//                        }

                        // 分类处理
                        String finalCateoryName;
                        if (StrUtil.contains(category, "/")) {
                            List<String> categoryList = StrUtil.split(category, "/");
                            finalCateoryName = CollUtil.getLast(categoryList);
                        } else {
                            finalCateoryName = category;
                        }

                        List<ProductCategory> productCategoryList = iProductCategoryService.queryByCategoryName(category);
                        if (CollUtil.isEmpty(productCategoryList)) {
                            throw new ExcelMessageException(builder.build(MemberPriceProductImportDTO::getCategory, ExcelMessageEnum.PRODUCT_CATEGORY_NOT_FOUND));
                        }
                        ProductCategory productCategory = productCategoryList.get(0);
                        Long belongCategoryId = productCategory.getId();

                        // 处理分类关系
                        List<ProductCategory> productCategoryTree = iProductCategoryService.queryCategoryChainById(belongCategoryId);
                        List<ProductCategoryRelation> categoryRelationList = new ArrayList<>();
                        if (CollUtil.isNotEmpty(productCategoryList)) {
                            for (ProductCategory one_productCategoryTree : productCategoryTree) {
                                ProductCategoryRelation pcr = new ProductCategoryRelation();
                                pcr.setProductCategoryId(one_productCategoryTree.getId());
                                categoryRelationList.add(pcr);
                            }
                        }

                        String productCode = productCodeGenerator.codeGenerate(BusinessCodeEnum.ProductCode);
                        product.setName(productName);
                        product.setBelongCategoryId(belongCategoryId);
                        product.setSupportedLogistics(supportedLogisticsEnum);
                        product.setDescription(StrUtil.emptyToNull(description));
                        product.setProductCode(productCode);
                        product.setProductType(ProductTypeEnum.NormalProduct);
                        product.setForbiddenChannel(forbiddenChannelArray);
                        product.setShelfState(ShelfStateEnum.ForcedOffShelf);
                        product.setVerifyState(ProductVerifyStateEnum.Draft);
                        product.setDownloadCount(0);
                        product.setCategoryRelationList(categoryRelationList);

                        productAttributeList = new ArrayList<>();
                        // 处理商品特色 和 通用规格
                        // 查询是否员工设置了该分类的必填的通用规格，有的话也要加入到待保存数组中
                        List<ProductGlobalAttributeSimpleVo> requiredGlobalAttributeList = iProductGlobalAttributeService.queryByProductCategoryIdAndRequired(product.getBelongCategoryId(), AttributeBelongEnum.Platform.getValue(),
                            AttributeScopeEnum.GenericSpec.getValue(), true);
                        if (CollUtil.isNotEmpty(requiredGlobalAttributeList)) {
                            for (int i = 0; i < requiredGlobalAttributeList.size(); i++) {
                                ProductGlobalAttributeSimpleVo attributeSimpleVo = requiredGlobalAttributeList.get(i);
                                ProductAttribute productAttribute = productSupport.setProductAttribute(null, null, attributeSimpleVo.getAttributeName(), null, AttributeTypeEnum.GenericSpec, attributeSimpleVo.getId(), i);
                                productAttributeList.add(productAttribute);
                            }
                        }

                        int genericSpecSort = CollUtil.size(productAttributeList);
                        for (int i = 1; i <= 5; i++) {
                            Object featureName = ReflectUtil.getFieldValue(dto, "featureName" + i);
                            Object featureValue = ReflectUtil.getFieldValue(dto, "featureValue" + i);
                            if (ObjectUtil.isAllNotEmpty(featureName, featureValue)) {
                                ProductAttribute productAttribute = productSupport.setProductAttribute(null, null, featureName.toString(), featureValue.toString(), AttributeTypeEnum.Feature, null, i - 1);
                                productAttributeList.add(productAttribute);
                            }

                            Object genericSpecName = ReflectUtil.getFieldValue(dto, "genericSpecName" + i);
                            Object genericSpecValue = ReflectUtil.getFieldValue(dto, "genericSpecValue" + i);
                            if (ObjectUtil.isAllNotEmpty(genericSpecName, genericSpecValue)) {
                                ProductAttribute productAttribute = productSupport.setProductAttribute(null, null, genericSpecName.toString(), genericSpecValue.toString(), AttributeTypeEnum.GenericSpec, null, genericSpecSort + i);
                                productAttributeList.add(productAttribute);
                            }
                        }

                        productSkuList = new ArrayList<>();
                        globalAttributeList = new ArrayList<>();
                        optionalSpecList = new ArrayList<>();

                        // 处理可选规格
                        String optionalSpecName1 = StrUtil.trim(dto.getOptionalSpecName1());
                        if (StrUtil.isBlank(optionalSpecName1)) {
                            throw new ExcelMessageException(builder.build(MemberPriceProductImportDTO::getOptionalSpecName1, ExcelMessageEnum.REQUIRE));
                        }

                        String optionalSpecName2 = StrUtil.trim(dto.getOptionalSpecName2());

                        ProductGlobalAttribute productGlobalAttribute1 = iProductGlobalAttributeService.queryByAttributeNameAndScopeAndCategory(tenantId, optionalSpecName1,
                            AttributeScopeEnum.OptionalSpec, product.getBelongCategoryId());
                        if (productGlobalAttribute1 == null) {
                            throw new ExcelMessageException(builder.build(MemberPriceProductImportDTO::getOptionalSpecName1, ExcelMessageEnum.PRODUCT_VARIANT_DIMENSION_NOT_FOUND));
                        }
                        ProductAttribute productAttribute1 = productSupport.setProductAttribute(null, null, optionalSpecName1, null, AttributeTypeEnum.OptionalSpec, productGlobalAttribute1.getId(), 0);
                        globalAttributeList.add(productGlobalAttribute1);
                        optionalSpecList.add(productAttribute1);

                        if (StrUtil.isNotBlank(optionalSpecName2)) {
                            // 重复的变体维度
                            if (StrUtil.equals(optionalSpecName1, optionalSpecName2)) {
                                throw new ExcelMessageException(builder.build(MemberPriceProductImportDTO::getOptionalSpecName2, ExcelMessageEnum.DUPLICATE_VARIANT));
                            }

                            ProductGlobalAttribute productGlobalAttribute2 = iProductGlobalAttributeService.queryByAttributeNameAndScopeAndCategory(tenantId, optionalSpecName2,
                                AttributeScopeEnum.OptionalSpec, product.getBelongCategoryId());
                            if (productGlobalAttribute2 == null) {
                                throw new ExcelMessageException(builder.build(MemberPriceProductImportDTO::getOptionalSpecName2, ExcelMessageEnum.PRODUCT_VARIANT_DIMENSION_NOT_FOUND));
                            }
                            ProductAttribute productAttribute2 = productSupport.setProductAttribute(null, null, optionalSpecName2, null, AttributeTypeEnum.OptionalSpec, productGlobalAttribute2.getId(), 0);
                            globalAttributeList.add(productGlobalAttribute2);
                            optionalSpecList.add(productAttribute2);
                        }
                    } else {
                        productAttributeList = product.getProductAttributeList();
                        optionalSpecList = product.getOptionalSpecList();
                        globalAttributeList = product.getGlobalAttributeList();
                        productSkuList = product.getProductSkuVoList();
                    }
                    String productName = product.getName();
                    String productCode = product.getProductCode();
                    String description = product.getDescription();
                    Long belongCategoryId = product.getBelongCategoryId();
                    product.setProductAttributeList(productAttributeList);


                    String erpSku = dto.getErpSku();
                    String upc = dto.getUpc();
                    String optionalSpecValue1 = StrUtil.trim(dto.getOptionalSpecValue1());
                    String optionalSpecValue2 = StrUtil.trim(dto.getOptionalSpecValue2());


                    List<String> specComposeNameList = new ArrayList<>();
                    List<String> specValNameList = new ArrayList<>();
                    specValNameList.add(optionalSpecValue1);
                    specValNameList.add(optionalSpecValue2);
                    String specValName = CollUtil.join(CollUtil.removeEmpty(specValNameList), "/");
                    Boolean containsSpecValName = product.containsSpecValName(specValName);
                    if (containsSpecValName) {
                        throw new ExcelMessageException(builder.buildOnlyRow(ExcelMessageEnum.DUPLICATE_VARIANT_VALUE_COMBINATION));
                    }

                    List<ProductSkuAttribute> skuAttributeList = new ArrayList<>();
                    ProductGlobalAttribute productGlobalAttribute1 = CollUtil.get(globalAttributeList, 0);
                    if (productGlobalAttribute1 != null) {
                        Boolean isSupportCustom = productGlobalAttribute1.getIsSupportCustom();
                        // 不支持自定义属性值
                        if (!isSupportCustom) {
                            JSONArray attributeValues = productGlobalAttribute1.getAttributeValues();
                            // 且当前填写的属性值又不存在规定的值数组内，报错提示
                            if (!attributeValues.contains(optionalSpecValue1)) {
                                throw new ExcelMessageException(builder.build(MemberPriceProductImportDTO::getOptionalSpecName1, ExcelMessageEnum.PRODUCT_VARIANT_NOT_SUPPORT_CUSTOM));
                            }
                        }
                    }

                    // 第一规格
                    Long sourceId1 = productGlobalAttribute1.getId();
                    ProductAttribute productAttribute1 = CollUtil.get(optionalSpecList, 0);
                    String attributeName1 = productAttribute1.getAttributeName();
                    JSONArray attributeValues1 = productAttribute1.getAttributeValues();
                    if (!CollUtil.contains(attributeValues1, optionalSpecValue1)) {
                        // throw new ExcelMessageException(builder.build(MemberPriceProductImportDTO::getOptionalSpecValue1, ExcelMessageEnum.PRODUCT_VARIANT_VALUE_REPEAT));
                        productAttribute1.addAttributeValue(optionalSpecValue1);
                    }
                    specComposeNameList.add(attributeName1 + "-" + optionalSpecValue1);

                    ProductSkuAttribute skuAttribute1 = new ProductSkuAttribute();
                    skuAttribute1.setAttributeType(AttributeTypeEnum.OptionalSpec);
                    skuAttribute1.setAttributeSort(0);
                    skuAttribute1.setAttributeName(attributeName1);
                    skuAttribute1.setAttributeValue(optionalSpecValue1);
                    skuAttribute1.setAttributeSourceId(sourceId1);
                    skuAttributeList.add(skuAttribute1);

                    // 第二规格
                    ProductGlobalAttribute productGlobalAttribute2 = CollUtil.get(globalAttributeList, 1);
                    if (productGlobalAttribute2 != null) {
                        if (StrUtil.isBlank(optionalSpecValue2)) {
                            throw new ExcelMessageException(builder.build(MemberPriceProductImportDTO::getOptionalSpecValue2, ExcelMessageEnum.REQUIRE));
                        }

                        Long sourceId2 = productGlobalAttribute2.getId();
                        Boolean isSupportCustom = productGlobalAttribute2.getIsSupportCustom();
                        if (!isSupportCustom) {
                            JSONArray attributeValues = productGlobalAttribute2.getAttributeValues();
                            // 且当前填写的属性值又不存在规定的值数组内，报错提示
                            if (!attributeValues.contains(optionalSpecValue2)) {
                                throw new ExcelMessageException(builder.build(MemberPriceProductImportDTO::getOptionalSpecName2, ExcelMessageEnum.PRODUCT_VARIANT_NOT_SUPPORT_CUSTOM));
                            }
                        }

                        ProductAttribute productAttribute2 = CollUtil.get(optionalSpecList, 1);
                        String attributeName2 = productAttribute2.getAttributeName();
                        JSONArray attributeValues2 = productAttribute2.getAttributeValues();
                        if (!CollUtil.contains(attributeValues2, optionalSpecValue2)) {
                            // throw new ExcelMessageException(builder.build(MemberPriceProductImportDTO::getOptionalSpecValue2, ExcelMessageEnum.PRODUCT_VARIANT_VALUE_REPEAT));
                            productAttribute2.addAttributeValue(optionalSpecValue2);
                        }
                        specComposeNameList.add(attributeName2 + "-" + optionalSpecValue2);

                        ProductSkuAttribute skuAttribute2 = new ProductSkuAttribute();
                        skuAttribute2.setAttributeType(AttributeTypeEnum.OptionalSpec);
                        skuAttribute2.setAttributeSort(1);
                        skuAttribute2.setAttributeName(attributeName2);
                        skuAttribute2.setAttributeValue(optionalSpecValue2);
                        skuAttribute2.setAttributeSourceId(sourceId2);
                        skuAttributeList.add(skuAttribute2);
                    }

                    String warehouseSystemCode = dto.getWarehouseSystemCode();
                    String logisticsTemplateName = dto.getLogisticsTemplateName();
                    Integer quantity = dto.getQuantity();
                    BigDecimal packLength = dto.getPackLength();
                    BigDecimal packWidth = dto.getPackWidth();
                    BigDecimal packHeight = dto.getPackHeight();
                    BigDecimal packWeight = dto.getPackWeight();
                    BigDecimal length = dto.getLength();
                    BigDecimal width = dto.getWidth();
                    BigDecimal height = dto.getHeight();
                    BigDecimal weight = dto.getWeight();
                    Boolean samePacking = StrUtil.equals(dto.getSamePacking(), "Yes");

                    BigDecimal originalUnitPrice = dto.getOriginalUnitPrice();
                    BigDecimal originalOperationFee = dto.getOriginalOperationFee();
                    BigDecimal originalFinalDeliveryFee = dto.getOriginalFinalDeliveryFee();
                    BigDecimal msrp = dto.getMsrp();


                    List<String> imageList = new ArrayList<>();
                    imageList.add(dto.getImage1());
                    imageList.add(dto.getImage2());
                    imageList.add(dto.getImage3());
                    imageList.add(dto.getImage4());
                    imageList.add(dto.getImage5());
                    imageList.add(dto.getImage6());
                    imageList.addAll(StrUtil.split(dto.getOtherImage(), ";"));
                    CollUtil.removeBlank(imageList);

                    String productSkuCode = productCodeGenerator.codeGenerate(BusinessCodeEnum.ProductSkuCode);

                    ProductSkuImportBo productSku = new ProductSkuImportBo();
                    productSku.setProductCode(productCode);
                    productSku.setProductSkuCode(productSkuCode);
                    productSku.setName(productName);
                    productSku.setSku(sku);
                    productSku.setErpSku(erpSku);
                    productSku.setUpc(upc);
                    productSku.setShelfState(ShelfStateEnum.ForcedOffShelf);
                    productSku.setVerifyState(ProductVerifyStateEnum.Draft);
                    productSku.setSpecComposeName(StrUtil.addSuffixIfNot(CollUtil.join(specComposeNameList, ";"), ";"));
                    productSku.setSpecValName(specValName);
                    productSku.setSort(CollUtil.size(productSkuList));
                    productSku.setStockTotal(0);


                    ProductSkuDetail productSkuDetail = new ProductSkuDetail();
                    productSkuDetail.setLength(length);
                    productSkuDetail.setWidth(width);
                    productSkuDetail.setHeight(height);
                    productSkuDetail.setLengthUnit(LengthUnitEnum.inch);
                    productSkuDetail.setWeight(weight);
                    productSkuDetail.setWeightUnit(WeightUnitEnum.lb);

                    if (samePacking) {
                        packLength = length;
                        packWidth = width;
                        packHeight = height;
                        packWeight = weight;
                    } else {
                        if (packLength == null) {
                            throw new ExcelMessageException(builder.build(MemberPriceProductImportDTO::getPackLength, ExcelMessageEnum.REQUIRE));
                        }
                        if (packWidth == null) {
                            throw new ExcelMessageException(builder.build(MemberPriceProductImportDTO::getPackWidth, ExcelMessageEnum.REQUIRE));
                        }
                        if (packHeight == null) {
                            throw new ExcelMessageException(builder.build(MemberPriceProductImportDTO::getPackHeight, ExcelMessageEnum.REQUIRE));
                        }
                        if (packWeight == null) {
                            throw new ExcelMessageException(builder.build(MemberPriceProductImportDTO::getPackWeight, ExcelMessageEnum.REQUIRE));
                        }
                    }

                    productSkuDetail.setPackLength(packLength);
                    productSkuDetail.setPackWidth(packWidth);
                    productSkuDetail.setPackHeight(packHeight);
                    productSkuDetail.setPackLengthUnit(LengthUnitEnum.inch);
                    productSkuDetail.setPackWeight(packWeight);
                    productSkuDetail.setPackWeightUnit(WeightUnitEnum.lb);
                    productSkuDetail.setSamePacking(samePacking);
                    productSkuDetail.setDescription(description);

                    // 处理图片
                    List<ProductSkuAttachment> skuAttachmentList = downloadImage(imageList);
                    if (CollUtil.isEmpty(skuAttachmentList)) {
                        throw new ExcelMessageException(builder.build(MemberPriceProductImportDTO::getWarehouseSystemCode, ExcelMessageEnum.INVALID_IMAGE));
                    }

                    // 处理仓库与库存
                    List<ProductSkuStock> skuStockList = new ArrayList<>();
                    StockManagerEnum stockManagerEnum = null;
                    List<String> warehouseSystemCodeList = StrUtil.split(warehouseSystemCode, ";");
                    for (String code : warehouseSystemCodeList) {
                        ProductSkuStock productSkuStock = new ProductSkuStock();
                        String stockCode = productCodeGenerator.codeGenerate(BusinessCodeEnum.StockCode);

                        Warehouse warehouse = iWarehouseService.queryByWarehouseSystemCodeHasTenant(tenantId, code);
                        if (warehouse == null) {
                            throw new ExcelMessageException(builder.build(MemberPriceProductImportDTO::getWarehouseSystemCode, ExcelMessageEnum.WAREHOUSE_NOT_FOUND));
                        }

                        WarehouseTypeEnum warehouseType = warehouse.getWarehouseType();
                        StockManagerEnum nowStockManager = warehouseType.toStockManager();
                        if (stockManagerEnum == null) {
                            stockManagerEnum = nowStockManager;
                        } else if (ObjectUtil.notEqual(stockManagerEnum, nowStockManager)) {
                            throw new ExcelMessageException(builder.build(MemberPriceProductImportDTO::getWarehouseSystemCode, ExcelMessageEnum.WAREHOUSE_TYPE_MUST_BE_SAME));
                        }

                        String logisticsTemplateNo = null;
                        if (StrUtil.isNotBlank(logisticsTemplateName)) {
                            LogisticsTemplate logisticsTemplate = iLogisticsTemplateService.queryByWarehouse(code, logisticsTemplateName);
                            if (logisticsTemplate == null) {
                                throw new ExcelMessageException(builder.build(MemberPriceProductImportDTO::getLogisticsTemplateName, ExcelMessageEnum.LOGISTICS_TEMPLATE_NOT_FOUND));
                            }
                            logisticsTemplateNo = logisticsTemplate.getLogisticsTemplateNo();
                        }

                        productSkuStock.setStockCode(stockCode);
                        productSkuStock.setStockTotal(quantity);
                        productSkuStock.setStockReserved(0);
                        productSkuStock.setStockAvailable(quantity);
                        productSkuStock.setStockState(GlobalStateEnum.Valid);
                        productSkuStock.setErpSku(erpSku);
                        productSkuStock.setProductCode(productCode);
                        productSkuStock.setProductSkuCode(productSkuCode);
                        productSkuStock.setWarehouseSystemCode(warehouseSystemCode);
                        productSkuStock.setLogisticsTemplateNo(logisticsTemplateNo);
                        skuStockList.add(productSkuStock);
                    }

                    productSku.setStockManager(stockManagerEnum);

                    // 价格数据
                    ProductSkuPrice productSkuPrice = new ProductSkuPrice();
                    productSkuPrice.setOriginalUnitPrice(originalUnitPrice);
                    productSkuPrice.setOriginalOperationFee(originalOperationFee);
                    productSkuPrice.setOriginalFinalDeliveryFee(originalFinalDeliveryFee);
                    productSkuPrice.setOriginalPickUpPrice(NumberUtil.add(originalUnitPrice, originalOperationFee));
                    productSkuPrice.setOriginalDropShippingPrice(NumberUtil.add(originalUnitPrice, originalOperationFee, originalFinalDeliveryFee));

                    productSkuPrice = iProductSkuPriceRuleService.matchRule(productSkuCode, originalUnitPrice, originalOperationFee, originalFinalDeliveryFee, false);
                    productSkuPrice.setProductSkuCode(productSkuCode);
                    productSkuPrice.setMsrp(msrp);

                    productSku.setSkuDetail(productSkuDetail);
                    productSku.setSkuPrice(productSkuPrice);
                    productSku.setSkuStockList(skuStockList);
                    productSku.setSkuAttributeList(skuAttributeList);
                    productSku.setSkuAttachmentList(skuAttachmentList);


                    boolean existSku = iProductSkuService.existSku(sku, tenantId, null);
                    product.setIsExists(existSku);
                    List<RuleLevelProductPrice> ruleLevelProductPrices = new ArrayList<>();
                    Boolean excelPrice = Boolean.TRUE;
                    // excel内所有填充了的开启的价格列  productSku.setRuleLevelProductPrices(ruleLevelProductPrices);
                    excelPrice = importSkuForPriceSaveV1(priceDTOS, excelPrice, builder, tenantId, ruleLevelProductPrices,productSku);


                    // 新增的价格业务
                    if ((existSku || globalSku.contains(sku)) && excelPrice) {
                        throw new ExcelMessageException(builder.build(MemberPriceProductImportDTO::getSku, ExcelMessageEnum.SKU_REPEAT));
                    } else if (existSku && !excelPrice) {// 产品在,有会员价 所以更新产品价格
                        ProductSku one;
                        one = iProductSkuService.getOne(new LambdaQueryWrapper<ProductSku>().eq(ProductSku::getSku, dto.getSku()));
                        // sku 存在 1. 修改产品原有金额
                        Integer isUpdate = changeProductPrice(one.getId(), dto.getOriginalUnitPrice(), dto.getOriginalOperationFee(), dto.getOriginalFinalDeliveryFee(), tenantId);
                        // 2. 修改该商品+供应商 的等级金额

                        List<RuleLevelProductPrice> list = list(new LambdaQueryWrapper<RuleLevelProductPrice>().eq(RuleLevelProductPrice::getProductSkuId, one.getId())
                                                                                                               .eq(RuleLevelProductPrice::getRuleCustomizerTenantId, tenantId));
                        // 3.  | 如果excel没值 但是分价有 就不管 |如果excel有 分价没有 则分价新增 | 如果excel有 分价有 则分价更新
                        productSku = importSkuForPriceUpdateV1(priceDTOS, list, productSku);
                        if (isUpdate == 3) {
                            productSku.setUpdated(true);
                        }

                    } else {
                        globalSku.add(sku);
                    }

                    if (StrUtil.isNotBlank(upc)) {
                        boolean existUpc = iProductSkuService.existUpc(upc, null);
                        if (existUpc || globalUpc.contains(upc)) {
                            throw new ExcelMessageException(builder.build(MemberPriceProductImportDTO::getUpc, ExcelMessageEnum.UPC_REPEAT));
                        } else {
                            globalUpc.add(upc);
                        }
                    }
                    product.addProductSku(productSku);
                    product.addSpecValName(specValName);
                    product.setProductAttributeList(productAttributeList);
                    product.setOptionalSpecList(optionalSpecList);
                    product.setGlobalAttributeList(globalAttributeList);
                    productGroupMap.put(group, product);
                } catch (ExcelMessageException e) {
                    globalMessage.append(e.getLocaleMessage());
                } catch (Exception e) {
                    globalMessage.append(builder.buildOnlyRow(ExcelMessageEnum.UNKNOWN_PARSING_ERROR));
                    log.error("商品上传，第{}行出现未知的解析错误，原因 {}", showRowIndex, e.getMessage(), e);
                }
            }


            if (globalMessage.hasData()) {
                record.setImportMessage(globalMessage.toJSON());
                record.setImportState(ImportStateEnum.Failed);
                iProductImportRecordService.updateById(record);
                ref.errorMsg = globalMessage.toJSON().toString();
            } else {
                Collection<ProductImportBo> productImportBoList = productGroupMap.values();

                try {
                    productSupport.saveProductImportForPrice(tenantId, productImportBoList);
                    record.setImportState(ImportStateEnum.Success);
                    record.setImportProducts(CollUtil.size(productImportBoList));
                } catch (DataIntegrityViolationException e) {
                    log.error("保存导入商品出现数据库异常，原因 {}", e.getMessage(), e);
                    LocaleMessage localeMessage = LocaleMessage.byStatusCode(ZSMallStatusCodeEnum.SAVE_IMPORT_DATA_ERROR);
                    Throwable cause = e.getCause();
                    if (cause != null) {
                        String message = cause.getMessage();
                        if (StrUtil.contains(message, "Data too long for column")) {
                            localeMessage = LocaleMessage.byStatusCode(ZSMallStatusCodeEnum.DATABASE_DATA_TOO_LONG);
                        }
                    }
                    record.setImportMessage(localeMessage.toJSON());
                    ref.errorMsg = globalMessage.toJSON().toString();
                    record.setImportState(ImportStateEnum.Failed);
                } catch (Exception e) {
                    log.error("保存导入商品出现未知异常，原因 {}", e.getMessage(), e);
                    record.setImportMessage(LocaleMessage.byStatusCodeToJSON(ZSMallStatusCodeEnum.SAVE_IMPORT_DATA_ERROR));
                    record.setImportState(ImportStateEnum.Failed);
                    ref.errorMsg = globalMessage.toJSON().toString();
                }
                iProductImportRecordService.updateById(record);
            }

        };
        UserRunnable userRunnable = new UserRunnable(loginUser, task);
//         开启线程导入
        ThreadUtil.execute(userRunnable);
        if (ObjectUtil.isNotNull(ref.errorMsg)) {
            return R.fail(ref.errorMsg);
        }
        return R.ok();
    }

    /**
     * 功能描述：上传产品excel v2
     *
     * @param file 文件
     * @return {@link R }<{@link Void }>
     * <AUTHOR>
     * @date 2024/07/16
     */
    @Override
    public R<Void> uploadProductExcelV2(MultipartFile file) throws IOException {
        List<LevelPriceV2DTO> dtoList = null;
        ExcelMsgBuilder<LevelPriceV2DTO> builder;
        Map<String, RuleLevelProductPrice> rulePriceMap ;
        Map<Long, Long> levelIdMap;
        Map<String, String> map;
        List<RuleLevelProductPrice> rulePrices;
        String tenantId;
        ProductImportRecord record = new ProductImportRecord();
        try{
            tenantId = LoginHelper.getTenantId();
            if (file == null) {
                return R.fail(ZSMallStatusCodeEnum.UPLOAD_FILE_IS_EMPTY);
            }

            Boolean existsed = iProductImportRecordService.existImportingRecord();
            if (existsed) {
                return R.fail(ZSMallStatusCodeEnum.PRODUCT_IMPORT_FILE_EXIST_IMPORTING);
            }

            InputStream inputStream = file.getInputStream();
            ExcelReader reader = ExcelUtil.getReader(inputStream);

            if (reader != null) {
                Sheet sheet = reader.getSheet();
                String sheetName = sheet.getSheetName();
                log.info("上传商品Excel - sheetName = {}", sheetName);
                int columnCount = reader.getColumnCount();
                log.info("上传商品Excel - columnCount = {}", columnCount);
                if (columnCount < 2) {
                    return R.fail(ZSMallStatusCodeEnum.EXCEL_COLUMN_COUNT_NOT_MATCH);
                }
            }

            // 初始化信息构建者
            builder = ZExcelUtil.msgBuilder(reader, 1, LevelPriceV2DTO.class);
            builder.setMsgPrefix("<p>").setMsgSuffix("</p></br>");


            record.setImportRecordNo(productCodeGenerator.codeGenerate(BusinessCodeEnum.ProductImportRecordNo));
            record.setImportFileName(file.getOriginalFilename());
            record.setImportState(ImportStateEnum.Importing);
            record.setTenantId(tenantId);
            iProductImportRecordService.save(record);

            try {
                dtoList = ZExcelUtil.parseFieldDTOV3(reader, LevelPriceV2DTO.class, 0, 2,2,3);
                if (CollUtil.isEmpty(dtoList)) {
                    throw new ExcelMessageException(builder.build(ExcelMessageEnum.NOT_VALID_ROW));
                }
            } catch (ExcelMessageException e) {
                record.setImportMessage(e.getLocaleMessage().toJSON());
                record.setImportState(ImportStateEnum.Failed);
                iProductImportRecordService.updateById(record);
                String string = e.getLocaleMessage().toJSON().get("zh_CN").toString();
                String replace = string.replace("<p>", "").replace("</p></br>", "");
                return R.fail(replace);
            }
            List<String> itemNos = dtoList.stream().map(LevelPriceV2DTO::getItemNo).collect(Collectors.toList());
            Map<String, Long> itemNoCounts = itemNos.stream()
                                                    .collect(Collectors.groupingBy(Function.identity(), Collectors.counting()));
            List<String> duplicates = itemNoCounts.entrySet().stream()
                                                  .filter(entry -> entry.getValue() > 1)
                                                  .map(Map.Entry::getKey)
                                                  .collect(Collectors.toList());
            if (CollUtil.isNotEmpty(duplicates)){
                throw new RuntimeException("上传会员定价，商品数据重复"+duplicates);
            }
            List<ProductSku> productSkus = iProductSkuService.list(new LambdaQueryWrapper<ProductSku>().in(ProductSku::getProductSkuCode, itemNos).eq(ProductSku::getDelFlag, 0));
            List<Long> productIds = productSkus.stream().map(ProductSku::getProductId).collect(Collectors.toList());
            List<Long> productSkuIds = productSkus.stream().map(ProductSku::getId).collect(Collectors.toList());
            List<LevelPriceDTO> dicts = memberLevelMapper.getDictCodesByType("market_levelname");
            List<String> codes = dicts.stream().map(LevelPriceDTO::getDictCode).collect(Collectors.toList());
            LambdaQueryWrapper<MemberLevel> eq = new LambdaQueryWrapper<MemberLevel>().eq(MemberLevel::getTenantId, tenantId)
                                                                                      .in(MemberLevel::getDictCode, codes)
                                                                                      .eq(MemberLevel::getDelFlag, 0)
                                                                                      .eq(MemberLevel::getStatus, 0);

            // 需要加一个拿产品的流程
            List<MemberLevel> levels = memberLevelService.list(eq);
            List<Long> ids = levels.stream().map(MemberLevel::getId).collect(Collectors.toList());
            LambdaQueryWrapper<RuleLevelProductPrice> wrapper = new LambdaQueryWrapper<RuleLevelProductPrice>()
                .eq(RuleLevelProductPrice::getRuleCustomizerTenantId, tenantId)
                .in(RuleLevelProductPrice::getLevelId, ids)
                .in(RuleLevelProductPrice::getProductSkuId, productSkuIds)
                .in(RuleLevelProductPrice::getProductId, productIds)
                .eq(RuleLevelProductPrice::getDelFlag, 0);
            //
            rulePrices = iRuleLevelProductPriceService.list(wrapper);;

            // rulePrices转换成 map key是 levelId:productSkuId:productId value是元素
            rulePriceMap = rulePrices.stream().collect(Collectors.toMap(rulePrice -> rulePrice.getLevelId() + ":" + rulePrice.getProductSkuId() + ":" + rulePrice.getProductId(),
                                                                            Function.identity()
                                                                        ));
            levelIdMap = levels.stream()
                                               .collect(Collectors.toMap(MemberLevel::getDictCode, MemberLevel::getId));
            // 通过dict+tenantId 拿到对应的levelId;
            map = dicts.stream()
                                           .collect(Collectors.toMap(LevelPriceDTO::getDictLabel, LevelPriceDTO::getDictCode));
        }catch (Exception e){
            log.error("上传商品Excel失败，原因 {}", e.getMessage(), e);
            record.setImportMessage(LocaleMessage.byStatusCodeToJSON(ZSMallStatusCodeEnum.QUERY_ITEM_NO_ERROR));
            record.setImportState(ImportStateEnum.Failed);
            iProductImportRecordService.updateById(record);
            return R.fail("上传失败,请核对itemNo或联系管理员");
        }

        var ref = new Object() {
            String errorMsg = null;
        };
        // 这里开始剪裁

        LocaleMessage globalMessage = new LocaleMessage();

        Map<String, ProductPriceImportBo> productGroupMap = new HashMap<>();

        Map<String, RuleLevelProductPrice> finalRulePriceMap = rulePriceMap;
        List<LevelPriceV2DTO> finalDtoList = dtoList;
//        finalDtoList 移除 itemNo重复的元素保留第一个
        Map<String, LevelPriceV2DTO> uniqueItems = finalDtoList.stream()
                                                          .collect(Collectors.toMap(
                                                              LevelPriceV2DTO::getItemNo, // keyMapper
                                                              Function.identity(),        // valueMapper
                                                              (existing, replacement) -> existing // mergeFunction，这里选择保留existing，即第一个
                                                          ));
        finalDtoList= new ArrayList<>(uniqueItems.values());
        ArrayList<ProductPriceImportBo> objects = new ArrayList<>();
        List<LevelPriceV2DTO> finalDtoList1 = finalDtoList;
        Runnable task = () -> {

            for (LevelPriceV2DTO dto : finalDtoList1) {
                String itemNo = dto.getItemNo();
                // 每次循环开始设置当前的行号，以保证构建信息时展示的行号是正确的
                int showRowIndex = dto.getShowRowIndex();
                builder.setNowShowRow(showRowIndex);

                try {
                    ProductPriceImportBo productPriceImportBo = new ProductPriceImportBo();
                    List<MemberRuleRelation> memberRuleRelationList;
                    List<LevelPriceParseDTO> priceDTOS = new ArrayList<>();
                    List<JSONObject> priceJSON = dto.getPriceJSON();
                    ProductSku one = iProductSkuService.getOne(new LambdaQueryWrapper<ProductSku>().eq(ProductSku::getProductSkuCode, itemNo)
                                                                                                   .eq(ProductSku::getDelFlag,0)
                                                                                                   .last("limit 1"));
                    if(ObjectUtil.isEmpty(one)){
                        throw new ExcelMessageException(builder.build(LevelPriceV2DTO::getItemNo, ExcelMessageEnum.ITEM_NO_DOES_NOT_EXIST));
                    }


                    priceJSON.forEach(jsonObject -> {

                        LevelPriceParseDTO levelPrice = new LevelPriceParseDTO();

                        String unitPrice = String.valueOf(jsonObject.get("unitPrice"));
                        String operationFee = String.valueOf(jsonObject.get("operationFee"));

                        String levelName = String.valueOf(jsonObject.get("levelName"));
                        if (ObjectUtil.isNotEmpty(unitPrice)&&!"null".equals(unitPrice)) {
                            levelPrice.setUnitPrice(new BigDecimal(unitPrice));
                        }
                        if (ObjectUtil.isNotEmpty(operationFee)&&!"null".equals(operationFee)) {
                            levelPrice.setOperationFee(new BigDecimal(operationFee));
                        }
                        String finalDeliveryFee = String.valueOf(jsonObject.get("finalDeliveryFee"));
                        if (ObjectUtil.isNotEmpty(finalDeliveryFee)&&!"null".equals(finalDeliveryFee)) {
                            levelPrice.setFinalDeliveryFee(new BigDecimal(finalDeliveryFee));
                        }
       //                 levelPrice.setFinalDeliveryFee(BigDecimal.ZERO);
                        // 截取会员价格之前的字符串去匹配id levelName
                        // 对字符串进行截取 会员售价前面的所有字符串
                        levelPrice.setLevelName(levelName);
                        int index = levelName.indexOf("会员售价");
                        if (index != -1) {
                            levelName = levelName.substring(0, index);
                        }
                        String dictCode = map.get(levelName);
                        levelPrice.setDictCode(dictCode);
                        Long levelId = levelIdMap.get(Long.valueOf(dictCode));
                        levelPrice.setLevelId(levelId);
                        // 新增场景
                        if (ObjectUtil.isNotEmpty(one)) {
                            levelPrice.setProductId(one.getProductId());
                            levelPrice.setProductSkuId(one.getId());
                        }
                        priceDTOS.add(levelPrice);
                    });



                    Boolean excelPrice = Boolean.TRUE;
                    // excel内所有填充了的开启的价格列  productSku.setRuleLevelProductPrices(ruleLevelProductPrices);
                    importSkuForPriceSaveV2(priceDTOS, excelPrice, builder, tenantId,productPriceImportBo,one, finalRulePriceMap);
                    // 会员价的更新/ 新增 新增-更新
                    // 新增的价格业务
                    if (ObjectUtil.isNotEmpty(one)) {// 产品在,有会员价 所以更新产品价格

                        // 1. 修改该商品+供应商 的等级金额
                        // 拿到供应商对此商品的所有关于等级的定价
                        List<RuleLevelProductPrice> list = list(new LambdaQueryWrapper<RuleLevelProductPrice>().eq(RuleLevelProductPrice::getProductSkuId, one.getId())
                                                                                                               .eq(RuleLevelProductPrice::getRuleCustomizerTenantId, tenantId));
                        // 3.  如果excel有 分价没有 则分价新增 | 如果excel有 分价有 则分价更新
                        importSkuForPriceUpdateV2(priceDTOS, list, productPriceImportBo);
                        // 更新操作
                    }
                    objects.add(productPriceImportBo);
                    productGroupMap.put(String.valueOf(showRowIndex),productPriceImportBo);
                } catch (ExcelMessageException e) {
                    globalMessage.append(e.getLocaleMessage());
                } catch (Exception e) {
                    globalMessage.append(builder.buildOnlyRow(ExcelMessageEnum.UNKNOWN_PARSING_ERROR));
                    log.error("商品上传，第{}行出现未知的解析错误，原因 {}", showRowIndex, e.getMessage(), e);
                }
            }

            if (globalMessage.hasData()) {
                record.setImportMessage(globalMessage.toJSON());
                record.setImportState(ImportStateEnum.Failed);
                iProductImportRecordService.updateById(record);
                ref.errorMsg = globalMessage.toJSON().get("zh_CN").toString();
                ref.errorMsg = ref.errorMsg.replace("<p>", "").replace("</p></br>", "");
            } else {

             //   Collection<ProductPriceImportBo> productImportBoList = productGroupMap.values();
                Collection<ProductPriceImportBo> productImportBoList = objects;
                try {
                    productSupport.saveProductImportForPriceV2(tenantId, productImportBoList);
                    record.setImportState(ImportStateEnum.Success);
                    record.setImportProducts(CollUtil.size(productImportBoList));
                } catch (DataIntegrityViolationException e) {
                    log.error("保存导入商品出现数据库异常，原因 {}", e.getMessage(), e);
                    LocaleMessage localeMessage = LocaleMessage.byStatusCode(ZSMallStatusCodeEnum.SAVE_IMPORT_DATA_ERROR);
                    Throwable cause = e.getCause();
                    if (cause != null) {
                        String message = cause.getMessage();
                        if (StrUtil.contains(message, "Data too long for column")) {
                            localeMessage = LocaleMessage.byStatusCode(ZSMallStatusCodeEnum.DATABASE_DATA_TOO_LONG);
                        }
                    }
                    record.setImportMessage(localeMessage.toJSON());
                    ref.errorMsg = globalMessage.toJSON().get("zh_CN").toString();
                    ref.errorMsg = ref.errorMsg.replace("<p>", "").replace("</p></br>", "");
                    record.setImportState(ImportStateEnum.Failed);
                } catch (Exception e) {
                    log.error("保存导入商品出现未知异常，原因 {}", e.getMessage(), e);
                    record.setImportMessage(LocaleMessage.byStatusCodeToJSON(ZSMallStatusCodeEnum.SAVE_IMPORT_DATA_ERROR));
                    record.setImportState(ImportStateEnum.Failed);
                    ref.errorMsg = globalMessage.toJSON().get("zh_CN").toString();
                    ref.errorMsg = ref.errorMsg.replace("<p>", "").replace("</p></br>", "");
                }
                iProductImportRecordService.updateById(record);
            }
        };
        LoginUser loginUser = LoginHelper.getLoginUser();
        UserRunnable userRunnable = new UserRunnable(loginUser, task);
//         开启线程导入
        ThreadUtil.execute(userRunnable);
        if (ObjectUtil.isNotNull(ref.errorMsg)) {
            return R.fail(ref.errorMsg);
        }
        return R.ok();
    }

    private boolean importSkuForPriceSaveV1(List<LevelPriceParseDTO> priceDTOS, Boolean excelPrice,
                                                       ExcelMsgBuilder<MemberPriceProductImportDTO> builder, String tenantId,
                                                       List<RuleLevelProductPrice> ruleLevelProductPrices,
                                                       ProductSkuImportBo productSku) throws ExcelMessageException {
//      拿当前的开启的会员等级进行过滤
        LambdaQueryWrapper<MemberLevel> eq = new LambdaQueryWrapper<MemberLevel>().eq(MemberLevel::getStatus, 0)
                                                                                  .eq(MemberLevel::getDelFlag, "0")
                                                                                  .eq(MemberLevel::getTenantId, tenantId);
        List<MemberLevel> list = memberLevelService.list(eq);
        List<Long> dictCodes = list.stream().map(MemberLevel::getDictCode).collect(Collectors.toList());
        for (LevelPriceParseDTO priceDTO : priceDTOS) {
            if(!dictCodes.contains(Long.valueOf(priceDTO.getDictCode()))){
                continue;
            }

            BigDecimal excelFinalDeliveryFee= null;
            BigDecimal excelOperationFee= null;
            BigDecimal excelUnitPrice = null;
            if (ObjectUtil.isNotEmpty(priceDTO.getUnitPrice())) {
                excelUnitPrice = priceDTO.getUnitPrice();
            }
            if (ObjectUtil.isNotEmpty(priceDTO.getOperationFee())) {
                excelOperationFee = priceDTO.getOperationFee();
            }
            if (ObjectUtil.isNotEmpty(priceDTO.getFinalDeliveryFee())) {
                excelFinalDeliveryFee = priceDTO.getFinalDeliveryFee();
            }

            if (ObjectUtil.isNotEmpty(excelUnitPrice) || ObjectUtil.isNotEmpty(excelOperationFee) || ObjectUtil.isNotEmpty(excelFinalDeliveryFee)) {
                excelPrice = Boolean.FALSE;
            }

            if (ObjectUtil.isNotEmpty(excelUnitPrice) && NumberUtil.isLess(excelUnitPrice, BigDecimal.ZERO)) {
                throw new ExcelMessageException(builder.build(MemberPriceProductImportDTO::getOriginalUnitPrice, ExcelMessageEnum.PRICE_CANNOT_BE_LESS_THAN_ZERO));
            }
            if (ObjectUtil.isNotEmpty(excelOperationFee) && NumberUtil.isLess(excelOperationFee, BigDecimal.ZERO)) {
                throw new ExcelMessageException(builder.build(MemberPriceProductImportDTO::getOriginalOperationFee, ExcelMessageEnum.PRICE_CANNOT_BE_LESS_THAN_ZERO));
            }
            if (ObjectUtil.isNotEmpty(excelFinalDeliveryFee) && NumberUtil.isLess(excelFinalDeliveryFee, BigDecimal.ZERO)) {
                throw new ExcelMessageException(builder.build(MemberPriceProductImportDTO::getOriginalFinalDeliveryFee, ExcelMessageEnum.PRICE_CANNOT_BE_LESS_THAN_ZERO));
            }
            RuleLevelProductPrice excelRuleLevelProductPrice = createRuleLevelProduct(excelUnitPrice, excelFinalDeliveryFee, excelOperationFee, tenantId, Long.valueOf(priceDTO.getDictCode()), priceDTO);

            if (ObjectUtil.isNotEmpty(excelRuleLevelProductPrice)) {
                ruleLevelProductPrices.add(excelRuleLevelProductPrice);
            }
        }
        productSku.setRuleLevelProductPrices(ruleLevelProductPrices);
        return excelPrice;
    }

    /**
     * 功能描述：导入sku以节省价格v2
     *
     * @param priceDTOS            价格dtos
     * @param excelPrice           excel价格
     * @param builder              建设者
     * @param tenantId             租户id
     * @param productPriceImportBo 产品价格进口bo
     * @param one                  一
     * @param rulePriceMap
     * @return boolean
     * <AUTHOR>
     * @date 2024/07/16
     */
    private boolean importSkuForPriceSaveV2(List<LevelPriceParseDTO> priceDTOS, Boolean excelPrice,
                                            ExcelMsgBuilder<LevelPriceV2DTO> builder, String tenantId,
                                            ProductPriceImportBo productPriceImportBo, ProductSku one,
                                            Map<String, RuleLevelProductPrice> rulePriceMap) throws ExcelMessageException {
//      拿当前的开启的会员等级进行过滤
        LambdaQueryWrapper<MemberLevel> eq = new LambdaQueryWrapper<MemberLevel>().eq(MemberLevel::getStatus, 0)
                                                                                  .eq(MemberLevel::getDelFlag, "0")
                                                                                  .eq(MemberLevel::getTenantId, tenantId);
        List<MemberLevel> list = memberLevelService.list(eq);
        List<Long> dictCodes = list.stream().map(MemberLevel::getDictCode).collect(Collectors.toList());
        for (LevelPriceParseDTO priceDTO : priceDTOS) {
            if(!dictCodes.contains(Long.valueOf(priceDTO.getDictCode()))){
                continue;
            }
            //
            if(ObjectUtil.isNotEmpty(priceDTO.getLevelId())){
//                 levelId:productSkuId:productId
                RuleLevelProductPrice ruleLevelProductPrice = rulePriceMap.get(priceDTO.getLevelId()+":"+priceDTO.getProductSkuId()+":"+priceDTO.getProductId());
                if(ObjectUtil.isNotEmpty(ruleLevelProductPrice)){
                    continue;
                }

            }
            // 已经有的价格需要跳过 产品+租户+等级
            BigDecimal excelFinalDeliveryFee= null;
            BigDecimal excelOperationFee= null;
            BigDecimal excelUnitPrice = null;
            if (ObjectUtil.isNotEmpty(priceDTO.getUnitPrice())) {
                excelUnitPrice = priceDTO.getUnitPrice();
            }
            if (ObjectUtil.isNotEmpty(priceDTO.getOperationFee())) {
                excelOperationFee = priceDTO.getOperationFee();
            }
            if (ObjectUtil.isNotEmpty(priceDTO.getFinalDeliveryFee())) {
                excelFinalDeliveryFee = priceDTO.getFinalDeliveryFee();
            }

            if (ObjectUtil.isNotEmpty(excelUnitPrice) || ObjectUtil.isNotEmpty(excelOperationFee) || ObjectUtil.isNotEmpty(excelFinalDeliveryFee)) {
                excelPrice = Boolean.FALSE;
            }

            if (ObjectUtil.isNotEmpty(excelUnitPrice) && NumberUtil.isLess(excelUnitPrice, BigDecimal.ZERO)) {
                throw new ExcelMessageException(builder.build(LevelPriceV2DTO::getUnitPrice, ExcelMessageEnum.PRICE_CANNOT_BE_LESS_THAN_ZERO));
            }
            if (ObjectUtil.isNotEmpty(excelOperationFee) && NumberUtil.isLess(excelOperationFee, BigDecimal.ZERO)) {
                throw new ExcelMessageException(builder.build(LevelPriceV2DTO::getOriginalOperationFee, ExcelMessageEnum.PRICE_CANNOT_BE_LESS_THAN_ZERO));
            }
            if (ObjectUtil.isNotEmpty(excelFinalDeliveryFee) && NumberUtil.isLess(excelFinalDeliveryFee, BigDecimal.ZERO)) {
                throw new ExcelMessageException(builder.build(LevelPriceV2DTO::getOriginalFinalDeliveryFee, ExcelMessageEnum.PRICE_CANNOT_BE_LESS_THAN_ZERO));
            }
            // 如果不存在
            RuleLevelProductPrice excelRuleLevelProductPrice = createRuleLevelProduct(excelUnitPrice, excelFinalDeliveryFee, excelOperationFee, tenantId, Long.valueOf(priceDTO.getDictCode()), priceDTO);
            if(ObjectUtil.isNotEmpty(excelRuleLevelProductPrice)){
                excelRuleLevelProductPrice.setProductSkuId(one.getId());
                excelRuleLevelProductPrice.setProductId(one.getProductId());
            }

            if (ObjectUtil.isNotEmpty(excelRuleLevelProductPrice)) {
//                ruleLevelProductPrices.add(excelRuleLevelProductPrice);
                productPriceImportBo.addRulePrice(excelRuleLevelProductPrice);
            }

        }

        return excelPrice;
    }
    /**
     * 功能描述：导入用于价格更新sku
     *
     * @param dto  到
     * @param list
     * <AUTHOR>
     * @date 2024/05/22
     */
    private void importSkuForPriceUpdate(ProductImportDTO dto, List<RuleLevelProductPrice> list) {
        // 等级产品单价
        BigDecimal levelOriginalUnitPrice1 = dto.getLevelOriginalUnitPrice1();
        BigDecimal levelOriginalUnitPrice2 = dto.getLevelOriginalUnitPrice2();
        BigDecimal levelOriginalUnitPrice3 = dto.getLevelOriginalUnitPrice3();

        // 等级远程派送费
        BigDecimal levelOriginalFinalDeliveryFee1 = dto.getLevelOriginalFinalDeliveryFee1();
        BigDecimal levelOriginalFinalDeliveryFee2 = dto.getLevelOriginalFinalDeliveryFee2();
        BigDecimal levelOriginalFinalDeliveryFee3 = dto.getLevelOriginalFinalDeliveryFee3();
        // 等级操作费
        BigDecimal levelOriginalOperationFee1 = dto.getLevelOriginalOperationFee1();
        BigDecimal levelOriginalOperationFee2 = dto.getLevelOriginalOperationFee2();
        BigDecimal levelOriginalOperationFee3 = dto.getLevelOriginalOperationFee3();


        List<RuleLevelProductPrice> ruleLevelProductPrices = new ArrayList<>();
        if (ObjectUtil.isEmpty(levelOriginalUnitPrice1) && ObjectUtil.isEmpty(levelOriginalUnitPrice2) && ObjectUtil.isEmpty(levelOriginalUnitPrice3) &&
            ObjectUtil.isEmpty(levelOriginalFinalDeliveryFee1) && ObjectUtil.isEmpty(levelOriginalFinalDeliveryFee2) && ObjectUtil.isEmpty(levelOriginalFinalDeliveryFee3) &&
            ObjectUtil.isEmpty(levelOriginalOperationFee1) && ObjectUtil.isEmpty(levelOriginalOperationFee2) && ObjectUtil.isEmpty(levelOriginalOperationFee3)) {
            return;
        }

        for (RuleLevelProductPrice ruleLevelProductPrice : list) {
            // 该供应商在该产品已经有了等级,
            Long levelId = ruleLevelProductPrice.getLevelId();
            MemberLevel memberLevel = memberLevelService.getById(levelId);
            Integer status = memberLevel.getStatus();
            String delFlag = memberLevel.getDelFlag();
            if (status == 1 || "0".equals(delFlag)) {
                continue;
            }
            RuleLevelProductPrice levelProductPrice = new RuleLevelProductPrice();


//            | 如果excel没值 但是分价有 就不管 |如果excel有 分价没有 则分价新增 | 如果excel有 分价有 则分价更新
            // 等级存在 并且是黄金
            BigDecimal originalUnitPrice = null;
            BigDecimal originalFinalDeliveryFee = null;
            BigDecimal originalOperationFee = null;
            if (MemberlLevelEnum.GOLD.getDictCode().equals(memberLevel.getDictCode())) {
                // 值也存在
                if (ObjectUtil.isNotEmpty(levelOriginalUnitPrice1) || ObjectUtil.isNotEmpty(levelOriginalFinalDeliveryFee1) || ObjectUtil.isNotEmpty(levelOriginalOperationFee1)) {
                    levelProductPrice.setId(ruleLevelProductPrice.getId());
                    levelProductPrice.setRuleCustomizerTenantId(LoginHelper.getTenantId());
                    levelProductPrice.setLevelId(levelId);

                    if (ObjectUtil.isNotEmpty(levelOriginalUnitPrice1)) {
                        originalUnitPrice = levelOriginalUnitPrice1;
                    }
                    if (ObjectUtil.isNotEmpty(levelOriginalFinalDeliveryFee1)) {
                        originalFinalDeliveryFee = levelOriginalFinalDeliveryFee1;
                    }
                    if (ObjectUtil.isNotEmpty(levelOriginalOperationFee1)) {
                        originalOperationFee = levelOriginalOperationFee1;
                    }

                }
            }
            if (MemberlLevelEnum.SILVER.getDictCode().equals(memberLevel.getDictCode())) {
                if (ObjectUtil.isNotEmpty(levelOriginalUnitPrice2) || ObjectUtil.isNotEmpty(levelOriginalFinalDeliveryFee2) || ObjectUtil.isNotEmpty(levelOriginalOperationFee2)) {
                    levelProductPrice.setId(ruleLevelProductPrice.getId());
                    levelProductPrice.setRuleCustomizerTenantId(LoginHelper.getTenantId());
                    levelProductPrice.setLevelId(levelId);

                    if (ObjectUtil.isNotEmpty(levelOriginalUnitPrice2)) {
                        originalUnitPrice = levelOriginalUnitPrice2;
                    }
                    if (ObjectUtil.isNotEmpty(levelOriginalFinalDeliveryFee2)) {
                        originalFinalDeliveryFee = levelOriginalFinalDeliveryFee2;
                    }
                    if (ObjectUtil.isNotEmpty(levelOriginalOperationFee2)) {
                        originalOperationFee = levelOriginalOperationFee2;
                    }

                }
            }
            if (MemberlLevelEnum.BRONZE.getDictCode().equals(memberLevel.getDictCode())) {
                if (ObjectUtil.isNotEmpty(levelOriginalUnitPrice3) || ObjectUtil.isNotEmpty(levelOriginalFinalDeliveryFee3) || ObjectUtil.isNotEmpty(levelOriginalOperationFee3)) {
                    levelProductPrice.setId(ruleLevelProductPrice.getId());
                    levelProductPrice.setRuleCustomizerTenantId(LoginHelper.getTenantId());
                    levelProductPrice.setLevelId(levelId);

                    if (ObjectUtil.isNotEmpty(levelOriginalUnitPrice3)) {
                        originalUnitPrice = levelOriginalUnitPrice3;
                    }
                    if (ObjectUtil.isNotEmpty(levelOriginalFinalDeliveryFee3)) {
                        originalFinalDeliveryFee = levelOriginalFinalDeliveryFee3;
                    }
                    if (ObjectUtil.isNotEmpty(levelOriginalOperationFee3)) {
                        originalOperationFee = levelOriginalOperationFee3;
                    }

                }
            }
            levelProductPrice.setOriginalUnitPrice(originalUnitPrice);
            levelProductPrice.setOriginalOperationFee(originalOperationFee);
            levelProductPrice.setOriginalFinalDeliveryFee(originalFinalDeliveryFee);

            levelProductPrice.setPlatformUnitPrice(originalUnitPrice);
            levelProductPrice.setPlatformOperationFee(originalOperationFee);
            levelProductPrice.setPlatformFinalDeliveryFee(originalFinalDeliveryFee);

            BigDecimal pikUpPrice = computationPrice(originalUnitPrice, originalFinalDeliveryFee, originalOperationFee, LogisticsTypeEnum.PickUp);
            BigDecimal dropPrice = computationPrice(originalUnitPrice, originalFinalDeliveryFee, originalOperationFee, LogisticsTypeEnum.DropShipping);

            levelProductPrice.setOriginalPickUpPrice(pikUpPrice);
            levelProductPrice.setOriginalDropShippingPrice(dropPrice);

            levelProductPrice.setPlatformPickUpPrice(pikUpPrice);
            levelProductPrice.setPlatformDropShippingPrice(dropPrice);

            levelProductPrice.setProductSkuId(ruleLevelProductPrice.getProductSkuId());
            levelProductPrice.setProductId(ruleLevelProductPrice.getProductId());

            levelProductPrice.setDelFlag(0);
            levelProductPrice.setUpdateBy(LoginHelper.getUserId());
            ruleLevelProductPrices.add(levelProductPrice);
        }

        iRuleLevelProductPriceService.updateBatchById(ruleLevelProductPrices);

    }

    /**
     * 功能描述：导入sku以更新价格v2
     *
     * @param levelPriceParseDTOs 水平价格解析数据传输
     * @param list                列表
     * @param productSku          产品sku
     * @return {@link ProductSkuImportBo }
     * <AUTHOR>
     * @date 2024/07/16
     */
    private ProductPriceImportBo importSkuForPriceUpdateV2(List<LevelPriceParseDTO> levelPriceParseDTOs,
                                                         List<RuleLevelProductPrice> list,
                                                         ProductPriceImportBo productSku) {

        // 转化成map,key是dictCode,然后遍历list, 如果dictCode匹配,那么更新这个等级的价格
        Map<String, LevelPriceParseDTO> parseMap = levelPriceParseDTOs.stream()
                                                                      .collect(Collectors.toMap(LevelPriceParseDTO::getDictCode, Function.identity()));

        // 如果是新增会员等级,将levelPriceParseDTOs 构建成levelProductPrice
        if (CollUtil.isNotEmpty(list)) {
            for (RuleLevelProductPrice ruleLevelProductPrice : list) {
                // 该供应商在该产品已经有了等级,
                Long levelId = ruleLevelProductPrice.getLevelId();
                MemberLevel memberLevel = memberLevelService.getById(levelId);
                if(ObjectUtil.isNotEmpty(memberLevel)){
                    Integer status = memberLevel.getStatus();
                    String delFlag = memberLevel.getDelFlag();
                    if (status == 1 || !"0".equals(delFlag)) {
                        continue;
                    }
                    RuleLevelProductPrice levelProductPrice = new RuleLevelProductPrice();

//              如果excel没值 但是分价有 就不管 |如果excel有 分价没有 则分价新增 | 如果excel有 分价有 则分价更新
                    BigDecimal originalUnitPrice = null;
                    BigDecimal originalFinalDeliveryFee = null;
                    BigDecimal originalOperationFee = null;

                    Long dictCode = memberLevel.getDictCode();
                    LevelPriceParseDTO levelPriceParseDTO = parseMap.get(String.valueOf(dictCode));


                    // 此处拿等级去匹配
                    BigDecimal unitPrice = levelPriceParseDTO.getUnitPrice();
                    if (ObjectUtil.isNull(unitPrice) || "null".equals(unitPrice)) {
                        unitPrice=ruleLevelProductPrice.getPlatformUnitPrice();
                    }
                    BigDecimal operationFee = levelPriceParseDTO.getOperationFee();
                    BigDecimal finalDeliveryFee = levelPriceParseDTO.getFinalDeliveryFee();

                    // 值也存在
                    if (ObjectUtil.isNotEmpty(unitPrice) || ObjectUtil.isNotEmpty(operationFee) || ObjectUtil.isNotEmpty(finalDeliveryFee)) {
                        levelProductPrice.setId(ruleLevelProductPrice.getId());
                        levelProductPrice.setRuleCustomizerTenantId(LoginHelper.getTenantId());
                        levelProductPrice.setLevelId(levelId);

                        if (ObjectUtil.isNotEmpty(unitPrice)) {
                            originalUnitPrice = unitPrice;
                        }
                        if (ObjectUtil.isNotEmpty(finalDeliveryFee)) {
                            originalFinalDeliveryFee = finalDeliveryFee;
                        }
                        if (ObjectUtil.isNotEmpty(operationFee)) {
                            originalOperationFee = operationFee;
                        }

                    }
                    levelProductPrice.setOriginalUnitPrice(originalUnitPrice);
                    levelProductPrice.setOriginalOperationFee(originalOperationFee);
                    levelProductPrice.setOriginalFinalDeliveryFee(originalFinalDeliveryFee);

                    levelProductPrice.setPlatformUnitPrice(originalUnitPrice);
                    levelProductPrice.setPlatformOperationFee(originalOperationFee);
                    levelProductPrice.setPlatformFinalDeliveryFee(originalFinalDeliveryFee);

                    BigDecimal pikUpPrice = computationPrice(originalUnitPrice, originalFinalDeliveryFee, originalOperationFee, LogisticsTypeEnum.PickUp);
                    BigDecimal dropPrice = computationPrice(originalUnitPrice, originalFinalDeliveryFee, originalOperationFee, LogisticsTypeEnum.DropShipping);

                    levelProductPrice.setOriginalPickUpPrice(pikUpPrice);
                    levelProductPrice.setOriginalDropShippingPrice(dropPrice);

                    levelProductPrice.setPlatformPickUpPrice(pikUpPrice);
                    levelProductPrice.setPlatformDropShippingPrice(dropPrice);

                    levelProductPrice.setProductSkuId(ruleLevelProductPrice.getProductSkuId());
                    levelProductPrice.setProductId(ruleLevelProductPrice.getProductId());

                    levelProductPrice.setDelFlag(0);
                    levelProductPrice.setUpdateBy(LoginHelper.getUserId());
                    productSku.addRulePriceForUpate(levelProductPrice);
                }

            }
        }

        // 如果还没做过等级list=0;所以应该以levelPriceParseDTOs 为循环,如果list为0,
//        productSku.setUpdate(Boolean.TRUE);
        return productSku;

    }
    /**
     * 功能描述：导入的会员价格相关 更新v1  这个方法会导致重复添加新增状态
     *
     * @param
     * @param list       列表
     * @param productSku 产品sku
     * @return {@link ProductSkuImportBo }
     * <AUTHOR>
     * @date 2024/07/11
     */
    private ProductSkuImportBo importSkuForPriceUpdateV1(List<LevelPriceParseDTO> levelPriceParseDTOs,
                                                         List<RuleLevelProductPrice> list,
                                                         ProductSkuImportBo productSku) {
//        list为数据库内已经存在的产品的等级价格,
//        Boolean continueFlag = Boolean.FALSE;
//        for (LevelPriceParseDTO levelPriceParseDTO : levelPriceParseDTOs) {
//            BigDecimal unitPrice = null;
//            if(ObjectUtil.isNotEmpty(levelPriceParseDTO.getUnitPrice())){
//                unitPrice = levelPriceParseDTO.getUnitPrice();
//            }
//
//            BigDecimal operationFee = levelPriceParseDTO.getOperationFee();
//            BigDecimal finalDeliveryFee = levelPriceParseDTO.getFinalDeliveryFee();
////            if (ObjectUtil.isEmpty(unitPrice) || ObjectUtil.isEmpty(operationFee) || ObjectUtil.isEmpty(finalDeliveryFee)) {
////                continueFlag = Boolean.TRUE;
////                break;
////            }
//        }
//        if (continueFlag) {
//            return null;
//        }
        // 转化成map,key是dictCode,然后遍历list, 如果dictCode匹配,那么更新这个等级的价格
        Map<String, LevelPriceParseDTO> parseMap = levelPriceParseDTOs.stream()
                                                                      .collect(Collectors.toMap(LevelPriceParseDTO::getDictCode, Function.identity()));
//        if (CollUtil.isEmpty(list)) {
//            for (LevelPriceParseDTO levelPriceParseDTO : levelPriceParseDTOs) {
//                String dictCode = levelPriceParseDTO.getDictCode();
//                LambdaQueryWrapper<MemberLevel> eq = new LambdaQueryWrapper<MemberLevel>().eq(MemberLevel::getTenantId, LoginHelper.getTenantId())
//                                                                                          .eq(MemberLevel::getDictCode, dictCode)
//                                                                                          .eq(MemberLevel::getDelFlag, 0);
//
//                MemberLevel memberLevel = memberLevelService.getOne(eq);
//                Long levelId = memberLevel.getId();
//                Integer status = memberLevel.getStatus();
//                String delFlag = memberLevel.getDelFlag();
//                if (status == 1 || !"0".equals(delFlag)) {
//                    continue;
//                }
//                RuleLevelProductPrice levelProductPrice = new RuleLevelProductPrice();
//
////            | 如果excel没值 但是分价有 就不管 |如果excel有 分价没有 则分价新增 | 如果excel有 分价有 则分价更新
//                // 等级存在 并且是黄金
//                BigDecimal originalUnitPrice = null;
//                BigDecimal originalFinalDeliveryFee = null;
//                BigDecimal originalOperationFee = null;
//
////                Long dictCode = memberLevel.getDictCode();
////                LevelPriceParseDTO levelPriceParseDTO = parseMap.get(String.valueOf(dictCode));
//
//
//                // 此处拿等级去匹配
//                BigDecimal unitPrice = levelPriceParseDTO.getUnitPrice();
//                BigDecimal operationFee = levelPriceParseDTO.getOperationFee();
//                BigDecimal finalDeliveryFee = levelPriceParseDTO.getFinalDeliveryFee();
//
//                // 值也存在
//                if (ObjectUtil.isNotEmpty(unitPrice) || ObjectUtil.isNotEmpty(operationFee) || ObjectUtil.isNotEmpty(finalDeliveryFee)) {
//                    levelProductPrice.setRuleCustomizerTenantId(LoginHelper.getTenantId());
//                    levelProductPrice.setLevelId(levelId);
//
//                    if (ObjectUtil.isNotEmpty(unitPrice)) {
//                        originalUnitPrice = unitPrice;
//                    }
//                    if (ObjectUtil.isNotEmpty(finalDeliveryFee)) {
//                        originalFinalDeliveryFee = finalDeliveryFee;
//                    }
//                    if (ObjectUtil.isNotEmpty(operationFee)) {
//                        originalOperationFee = operationFee;
//                    }
//
//                }
//                levelProductPrice.setOriginalUnitPrice(originalUnitPrice);
//                levelProductPrice.setOriginalOperationFee(originalOperationFee);
//                levelProductPrice.setOriginalFinalDeliveryFee(originalFinalDeliveryFee);
//
//                levelProductPrice.setPlatformUnitPrice(originalUnitPrice);
//                levelProductPrice.setPlatformOperationFee(originalOperationFee);
//                levelProductPrice.setPlatformFinalDeliveryFee(originalFinalDeliveryFee);
//
//                BigDecimal pikUpPrice = computationPrice(originalUnitPrice, originalFinalDeliveryFee, originalOperationFee, LogisticsTypeEnum.PickUp);
//                BigDecimal dropPrice = computationPrice(originalUnitPrice, originalFinalDeliveryFee, originalOperationFee, LogisticsTypeEnum.DropShipping);
//
//                levelProductPrice.setOriginalPickUpPrice(pikUpPrice);
//                levelProductPrice.setOriginalDropShippingPrice(dropPrice);
//
//                levelProductPrice.setPlatformPickUpPrice(pikUpPrice);
//                levelProductPrice.setPlatformDropShippingPrice(dropPrice);
//                // 新增业务
//                levelProductPrice.setProductSkuId(levelPriceParseDTO.getProductSkuId());
//                levelProductPrice.setProductId(levelPriceParseDTO.getProductId());
//
//                levelProductPrice.setDelFlag(0);
//                levelProductPrice.setUpdateBy(LoginHelper.getUserId());
//                productSku.addRulePrice(levelProductPrice);
//            }
//        }
        // 如果是新增会员等级,将levelPriceParseDTOs 构建成levelProductPrice
        if (CollUtil.isNotEmpty(list)) {
            for (RuleLevelProductPrice ruleLevelProductPrice : list) {
                // 该供应商在该产品已经有了等级,
                Long levelId = ruleLevelProductPrice.getLevelId();
                MemberLevel memberLevel = memberLevelService.getById(levelId);
                Integer status = memberLevel.getStatus();
                String delFlag = memberLevel.getDelFlag();
                if (status == 1 || !"0".equals(delFlag)) {
                    continue;
                }
                RuleLevelProductPrice levelProductPrice = new RuleLevelProductPrice();

//            | 如果excel没值 但是分价有 就不管 |如果excel有 分价没有 则分价新增 | 如果excel有 分价有 则分价更新
                // 等级存在 并且是黄金
                BigDecimal originalUnitPrice = null;
                BigDecimal originalFinalDeliveryFee = null;
                BigDecimal originalOperationFee = null;

                Long dictCode = memberLevel.getDictCode();
                LevelPriceParseDTO levelPriceParseDTO = parseMap.get(String.valueOf(dictCode));


                // 此处拿等级去匹配
                BigDecimal unitPrice = levelPriceParseDTO.getUnitPrice();
                BigDecimal operationFee = levelPriceParseDTO.getOperationFee();
                BigDecimal finalDeliveryFee = levelPriceParseDTO.getFinalDeliveryFee();

                // 值也存在
                if (ObjectUtil.isNotEmpty(unitPrice) || ObjectUtil.isNotEmpty(operationFee) || ObjectUtil.isNotEmpty(finalDeliveryFee)) {
                    levelProductPrice.setId(ruleLevelProductPrice.getId());
                    levelProductPrice.setRuleCustomizerTenantId(LoginHelper.getTenantId());
                    levelProductPrice.setLevelId(levelId);

                    if (ObjectUtil.isNotEmpty(unitPrice)) {
                        originalUnitPrice = unitPrice;
                    }
                    if (ObjectUtil.isNotEmpty(finalDeliveryFee)) {
                        originalFinalDeliveryFee = finalDeliveryFee;
                    }
                    if (ObjectUtil.isNotEmpty(operationFee)) {
                        originalOperationFee = operationFee;
                    }

                }
                levelProductPrice.setOriginalUnitPrice(originalUnitPrice);
                levelProductPrice.setOriginalOperationFee(originalOperationFee);
                levelProductPrice.setOriginalFinalDeliveryFee(originalFinalDeliveryFee);

                levelProductPrice.setPlatformUnitPrice(originalUnitPrice);
                levelProductPrice.setPlatformOperationFee(originalOperationFee);
                levelProductPrice.setPlatformFinalDeliveryFee(originalFinalDeliveryFee);

                BigDecimal pikUpPrice = computationPrice(originalUnitPrice, originalFinalDeliveryFee, originalOperationFee, LogisticsTypeEnum.PickUp);
                BigDecimal dropPrice = computationPrice(originalUnitPrice, originalFinalDeliveryFee, originalOperationFee, LogisticsTypeEnum.DropShipping);

                levelProductPrice.setOriginalPickUpPrice(pikUpPrice);
                levelProductPrice.setOriginalDropShippingPrice(dropPrice);

                levelProductPrice.setPlatformPickUpPrice(pikUpPrice);
                levelProductPrice.setPlatformDropShippingPrice(dropPrice);

                levelProductPrice.setProductSkuId(ruleLevelProductPrice.getProductSkuId());
                levelProductPrice.setProductId(ruleLevelProductPrice.getProductId());

                levelProductPrice.setDelFlag(0);
                levelProductPrice.setUpdateBy(LoginHelper.getUserId());
                productSku.addRulePriceForUpate(levelProductPrice);
            }
        }

        // 如果还没做过等级list=0;所以应该以levelPriceParseDTOs 为循环,如果list为0,

        return productSku;

    }

//    private ProductSkuImportBo importSkuForPriceUpdateV1(ProductImportDTO dto, List<RuleLevelProductPrice> list,
//                                                         ProductSkuImportBo productSku) {
//        // 等级产品单价
//        BigDecimal levelOriginalUnitPrice1 = dto.getLevelOriginalUnitPrice1();
//        BigDecimal levelOriginalUnitPrice2 = dto.getLevelOriginalUnitPrice2();
//        BigDecimal levelOriginalUnitPrice3 = dto.getLevelOriginalUnitPrice3();
//
//        // 等级远程派送费
//        BigDecimal levelOriginalFinalDeliveryFee1 = dto.getLevelOriginalFinalDeliveryFee1();
//        BigDecimal levelOriginalFinalDeliveryFee2 = dto.getLevelOriginalFinalDeliveryFee2();
//        BigDecimal levelOriginalFinalDeliveryFee3 = dto.getLevelOriginalFinalDeliveryFee3();
//        // 等级操作费
//        BigDecimal levelOriginalOperationFee1 = dto.getLevelOriginalOperationFee1();
//        BigDecimal levelOriginalOperationFee2 = dto.getLevelOriginalOperationFee2();
//        BigDecimal levelOriginalOperationFee3 = dto.getLevelOriginalOperationFee3();
//
//
//        List<RuleLevelProductPrice> ruleLevelProductPrices = new ArrayList<>();
//        if (ObjectUtil.isEmpty(levelOriginalUnitPrice1) && ObjectUtil.isEmpty(levelOriginalUnitPrice2) && ObjectUtil.isEmpty(levelOriginalUnitPrice3) &&
//            ObjectUtil.isEmpty(levelOriginalFinalDeliveryFee1) && ObjectUtil.isEmpty(levelOriginalFinalDeliveryFee2) && ObjectUtil.isEmpty(levelOriginalFinalDeliveryFee3) &&
//            ObjectUtil.isEmpty(levelOriginalOperationFee1) && ObjectUtil.isEmpty(levelOriginalOperationFee2) && ObjectUtil.isEmpty(levelOriginalOperationFee3)) {
//            return null;
//        }
//
//        for (RuleLevelProductPrice ruleLevelProductPrice : list) {
//            // 该供应商在该产品已经有了等级,
//            Long levelId = ruleLevelProductPrice.getLevelId();
//            MemberLevel memberLevel = memberLevelService.getById(levelId);
//            Integer status = memberLevel.getStatus();
//            String delFlag = memberLevel.getDelFlag();
//            if (status == 1 || !"0".equals(delFlag)) {
//                continue;
//            }
//            RuleLevelProductPrice levelProductPrice = new RuleLevelProductPrice();
//
//
////            | 如果excel没值 但是分价有 就不管 |如果excel有 分价没有 则分价新增 | 如果excel有 分价有 则分价更新
//            // 等级存在 并且是黄金
//            BigDecimal originalUnitPrice = null;
//            BigDecimal originalFinalDeliveryFee = null;
//            BigDecimal originalOperationFee = null;
//            if (MemberlLevelEnum.GOLD.getDictCode().equals(memberLevel.getDictCode())) {
//                // 值也存在
//                if (ObjectUtil.isNotEmpty(levelOriginalUnitPrice1) || ObjectUtil.isNotEmpty(levelOriginalFinalDeliveryFee1) || ObjectUtil.isNotEmpty(levelOriginalOperationFee1)) {
//                    levelProductPrice.setId(ruleLevelProductPrice.getId());
//                    levelProductPrice.setRuleCustomizerTenantId(LoginHelper.getTenantId());
//                    levelProductPrice.setLevelId(levelId);
//
//                    if (ObjectUtil.isNotEmpty(levelOriginalUnitPrice1)) {
//                        originalUnitPrice = levelOriginalUnitPrice1;
//                    }
//                    if (ObjectUtil.isNotEmpty(levelOriginalFinalDeliveryFee1)) {
//                        originalFinalDeliveryFee = levelOriginalFinalDeliveryFee1;
//                    }
//                    if (ObjectUtil.isNotEmpty(levelOriginalOperationFee1)) {
//                        originalOperationFee = levelOriginalOperationFee1;
//                    }
//
//                }
//            }
//            if (MemberlLevelEnum.SILVER.getDictCode().equals(memberLevel.getDictCode())) {
//                if (ObjectUtil.isNotEmpty(levelOriginalUnitPrice2) || ObjectUtil.isNotEmpty(levelOriginalFinalDeliveryFee2) || ObjectUtil.isNotEmpty(levelOriginalOperationFee2)) {
//                    levelProductPrice.setId(ruleLevelProductPrice.getId());
//                    levelProductPrice.setRuleCustomizerTenantId(LoginHelper.getTenantId());
//                    levelProductPrice.setLevelId(levelId);
//
//                    if (ObjectUtil.isNotEmpty(levelOriginalUnitPrice2)) {
//                        originalUnitPrice = levelOriginalUnitPrice2;
//                    }
//                    if (ObjectUtil.isNotEmpty(levelOriginalFinalDeliveryFee2)) {
//                        originalFinalDeliveryFee = levelOriginalFinalDeliveryFee2;
//                    }
//                    if (ObjectUtil.isNotEmpty(levelOriginalOperationFee2)) {
//                        originalOperationFee = levelOriginalOperationFee2;
//                    }
//
//                }
//            }
//            if (MemberlLevelEnum.BRONZE.getDictCode().equals(memberLevel.getDictCode())) {
//                if (ObjectUtil.isNotEmpty(levelOriginalUnitPrice3) || ObjectUtil.isNotEmpty(levelOriginalFinalDeliveryFee3) || ObjectUtil.isNotEmpty(levelOriginalOperationFee3)) {
//                    levelProductPrice.setId(ruleLevelProductPrice.getId());
//                    levelProductPrice.setRuleCustomizerTenantId(LoginHelper.getTenantId());
//                    levelProductPrice.setLevelId(levelId);
//
//                    if (ObjectUtil.isNotEmpty(levelOriginalUnitPrice3)) {
//                        originalUnitPrice = levelOriginalUnitPrice3;
//                    }
//                    if (ObjectUtil.isNotEmpty(levelOriginalFinalDeliveryFee3)) {
//                        originalFinalDeliveryFee = levelOriginalFinalDeliveryFee3;
//                    }
//                    if (ObjectUtil.isNotEmpty(levelOriginalOperationFee3)) {
//                        originalOperationFee = levelOriginalOperationFee3;
//                    }
//
//                }
//            }
//            levelProductPrice.setOriginalUnitPrice(originalUnitPrice);
//            levelProductPrice.setOriginalOperationFee(originalOperationFee);
//            levelProductPrice.setOriginalFinalDeliveryFee(originalFinalDeliveryFee);
//
//            levelProductPrice.setPlatformUnitPrice(originalUnitPrice);
//            levelProductPrice.setPlatformOperationFee(originalOperationFee);
//            levelProductPrice.setPlatformFinalDeliveryFee(originalFinalDeliveryFee);
//
//            BigDecimal pikUpPrice = computationPrice(originalUnitPrice, originalFinalDeliveryFee, originalOperationFee, LogisticsTypeEnum.PickUp);
//            BigDecimal dropPrice = computationPrice(originalUnitPrice, originalFinalDeliveryFee, originalOperationFee, LogisticsTypeEnum.DropShipping);
//
//            levelProductPrice.setOriginalPickUpPrice(pikUpPrice);
//            levelProductPrice.setOriginalDropShippingPrice(dropPrice);
//
//            levelProductPrice.setPlatformPickUpPrice(pikUpPrice);
//            levelProductPrice.setPlatformDropShippingPrice(dropPrice);
//
//            levelProductPrice.setProductSkuId(ruleLevelProductPrice.getProductSkuId());
//            levelProductPrice.setProductId(ruleLevelProductPrice.getProductId());
//
//            levelProductPrice.setDelFlag(0);
//            levelProductPrice.setUpdateBy(LoginHelper.getUserId());
//            productSku.addRulePriceForUpate(levelProductPrice);
//        }
//        return productSku;
////        iRuleLevelProductPriceService.updateBatchById(ruleLevelProductPrices);
//
//    }

    @Override
    public void export(RulePriceQueryBo bo, PageQuery pageQuery, HttpServletResponse response) throws IOException {
        pageQuery.setPageSize(Integer.MAX_VALUE);
        RuleLevelProductPriceTableInfoVo ruleTableInfo = queryPageList(bo, pageQuery);
        List<RuleLevelProductPriceListVo> rows = ruleTableInfo.getRows();
        ArrayList<ProductExportVo> exportVos = new ArrayList<>();

        for (RuleLevelProductPriceListVo row : rows) {

            List<ProductSkuListVo> skuLists = row.getSkuList();
            if (ObjectUtil.isEmpty(skuLists)) {
                continue;
            }
            // 先处理数据 再处理表头
            for (ProductSkuListVo skuList : skuLists) {
                ProductExportVo productExportVo = new ProductExportVo();
                BeanUtil.copyProperties(skuList, productExportVo);
                productExportVo.setProductName(row.getProductName());
                productExportVo.setProductCode(row.getProductCode());

                switch (skuList.getSkuShelfState()) {
                    case "OnShelf":
                        productExportVo.setSkuShelfState("上架");
                        break;
                    case "OffShelf":
                        productExportVo.setSkuShelfState("下架");
                        break;
                    case "ForcedOffShelf":
                        productExportVo.setSkuShelfState("未审核");
                        break;
                    default:
                        productExportVo.setSkuShelfState("未知");
                }
                switch (row.getVerifyState()) {
                    case "Draft":
                        productExportVo.setVerifyState("草稿");
                        break;
                    case "Pending":
                        productExportVo.setVerifyState("审核中");
                        break;
                    case "Accepted":
                        productExportVo.setVerifyState("已通过");
                        break;
                    case "Rejected":
                        productExportVo.setVerifyState("已驳回");
                        break;
                    case "Abandoned":
                        productExportVo.setVerifyState("废弃");
                        break;
                    default:
                        productExportVo.setVerifyState("未知");
                }
                List<RuleLevelSkuPriceVo> skuPriceVoList = skuList.getSkuPriceVoList();
                List<LevelPriceVo> levelPriceVos = new ArrayList<>();
                if (CollUtil.isNotEmpty(skuPriceVoList)) {
                    for (RuleLevelSkuPriceVo ruleLevelSkuPriceVo : skuPriceVoList) {
                        LevelPriceVo levelPriceVo = new LevelPriceVo();

                        String levelName = ruleLevelSkuPriceVo.getLevelName();
                        BigDecimal dropPrice = ruleLevelSkuPriceVo.getDropPrice();
                        BigDecimal pickUpPrice = ruleLevelSkuPriceVo.getPickUpPrice();
                        levelPriceVo.setDictCode(String.valueOf(ruleLevelSkuPriceVo.getDictCode()));
                        levelPriceVo.setLevelName(levelName);
                        levelPriceVo.setPickUpPrice(pickUpPrice);
                        levelPriceVo.setDropPrice(dropPrice);
                        levelPriceVos.add(levelPriceVo);
                    }
                }
                productExportVo.pushLevelPriceList(levelPriceVos);
                exportVos.add(productExportVo);
            }
        }
//      手写动态表头实现方式
        LambdaQueryWrapper<MemberLevel> eq = new LambdaQueryWrapper<MemberLevel>().eq(MemberLevel::getStatus, 0)
                                                                                  .eq(MemberLevel::getDelFlag, 0);
        List<MemberLevel> levels = memberLevelService.list(eq);
        List<Long> code = levels.stream().map(MemberLevel::getDictCode).collect(Collectors.toList());
        List<LevelPriceDTO> levelNames = memberLevelService.getLevelNames(code);

        exportProducts(exportVos, "Products.xlsx", levelNames, response);
    }


    public void exportProducts(List<ProductExportVo> products, String fileName, List<LevelPriceDTO> levels,
                               HttpServletResponse response) throws IOException {

        List<List<String>> headers = new ArrayList<>();
        headers.add(Collections.singletonList("商品名"));
        headers.add(Collections.singletonList("商品编号"));
        headers.add(Collections.singletonList("Item.No"));
        headers.add(Collections.singletonList("参考SKU"));
        headers.add(Collections.singletonList("sku销售状况"));
        headers.add(Collections.singletonList("规格"));
        headers.add(Collections.singletonList("库存数量"));
        headers.add(Collections.singletonList("自提价"));
        headers.add(Collections.singletonList("代发价格"));
        headers.add(Collections.singletonList("已售"));
        headers.add(Collections.singletonList("审核状态"));

        for (LevelPriceDTO level : levels) {
            headers.add(Collections.singletonList("自提价(" + level.getDictLabel() + "会员)"));
            headers.add(Collections.singletonList("代发价(" + level.getDictLabel() + "会员)"));
        }

        //  准备数据
        List<List<String>> data = new ArrayList<>();
        for (ProductExportVo product : products) {

            String productName = product.getProductName();
            String productCode = product.getProductCode();
            String productSkuCode = product.getProductSkuCode();
            String sku = product.getSku();
            String skuShelfState = product.getSkuShelfState();
            String specValName = product.getSpecValName();
            Integer stockTotal = product.getStockTotal();
            String pickUpPrice = product.getPickUpPrice();
            String dropShippingPrice = product.getDropShippingPrice();
            Integer stockSold = product.getStockSold();
            String verifyState = product.getVerifyState();

            List<String> row = new ArrayList<>();
            row.add(productName);
            row.add(productCode);
            row.add(productSkuCode);
            row.add(sku);
            row.add(skuShelfState);
            row.add(specValName);
            row.add(String.valueOf(stockTotal));
            row.add(pickUpPrice);
            row.add(dropShippingPrice);
            row.add(String.valueOf(stockSold));
            row.add(verifyState);

            List<LevelPriceVo> levelPriceList = product.getLevelPriceList();
            // levelPriceList 转换成以dictCode为key,value为LevelPriceVo的map
            Map<String, LevelPriceVo> levelPriceMap = levelPriceList.stream()
                                                                    .collect(Collectors.toMap(LevelPriceVo::getDictCode, Function.identity()));
            // 填充价格等级和价格
            for (LevelPriceDTO level : levels) {
                String dictCode = level.getDictCode();
                LevelPriceVo priceVo = levelPriceMap.getOrDefault(dictCode, null);
                if (priceVo != null && priceVo.getPickUpPrice() != null) {
                    row.add(String.valueOf(priceVo.getPickUpPrice()));
                } else {
                    row.add("");
                }
                if (priceVo != null && priceVo.getDropPrice() != null) {
                    row.add(String.valueOf(priceVo.getDropPrice()));
                } else {
                    row.add("");
                }
            }
            data.add(row);
        }
        ServletOutputStream out = response.getOutputStream();
        // 使用EasyExcel导出
        EasyExcel.write(out).head(headers).sheet("商品").doWrite(data);
    }

    @Override
    public RuleLevelProductPrice getMemberPrice(String supplierTenantId, String distributorTenantId,
                                                Long productSkuId) {

        Long levelId;
        if (StringUtils.isNotEmpty(distributorTenantId)) {
            LambdaQueryWrapper<MemberRuleRelation> lqw = new LambdaQueryWrapper<>();
            lqw.eq(MemberRuleRelation::getRuleFollowerTenantId, distributorTenantId)
               .eq(MemberRuleRelation::getRuleCustomizerTenantId, supplierTenantId)
               .eq(MemberRuleRelation::getDelFlag, 0);
            MemberRuleRelation memberRuleRelation = memberRuleRelationService.getOne(lqw);
            if (null != memberRuleRelation) {
                levelId = memberRuleRelation.getLevelId();
            } else {
                levelId = null;
                return null;
            }
        } else {
            levelId = null;
        }
        // levelId 对应的状态要开启
        MemberLevel level = TenantHelper.ignore(() -> memberLevelService.getById(levelId));
        if (null != levelId && ObjectUtil.isNotEmpty(level) && level.getStatus() == 0) {
            LambdaQueryWrapper<RuleLevelProductPrice> levelProductPriceLambdaQueryWrapper = new LambdaQueryWrapper<>();
            levelProductPriceLambdaQueryWrapper.eq(RuleLevelProductPrice::getRuleCustomizerTenantId, supplierTenantId)
                                               .eq(RuleLevelProductPrice::getLevelId, levelId)
                                               .eq(RuleLevelProductPrice::getProductSkuId, productSkuId)
                                               .eq(RuleLevelProductPrice::getDelFlag, 0);
            RuleLevelProductPrice ruleLevelProductPrice = TenantHelper.ignore(() -> getOne(levelProductPriceLambdaQueryWrapper));
            return ruleLevelProductPrice;
        }
        return null;


    }

    /**
     * 功能描述：导出 导入的模板
     *
     * @return {@link ResponseEntity }<{@link byte[] }>
     * <AUTHOR>
     * @date 2024/07/09
     */
    @Override
    public ResponseEntity<byte[]> exportTemplate() {
        String url = ruleLevelProductPriceMapper.getSysDictData("ProductImport_price_zh_CN");
//        通过url下载文件 MultipartFile
        Workbook fileAsStream;
        try {
            fileAsStream = FileDownUtil.downloadAndCreateWorkbook(url);
        } catch (Exception e) {
            log.error("模版下载异常", e);
            throw new RuntimeException("模版下载异常");
        }
        String tenantId = LoginHelper.getTenantId();
//        String tenantId = "SJN1857";
        // 获取当前所有的会员
        LambdaQueryWrapper<MemberLevel> eq = new LambdaQueryWrapper<MemberLevel>().eq(MemberLevel::getStatus, 0)
                                                                                  .eq(MemberLevel::getTenantId, tenantId)
                                                                                  .eq(MemberLevel::getDelFlag, 0);
        List<MemberLevel> memberLevels = memberLevelService.list(eq);

        return analyzeExcelV2(fileAsStream, 1, 3, 1, 1, memberLevels, 3, 12,0);
    }

    @Override
    public List<MemberLevelVO> getMemberRule() {
        String tenantId = LoginHelper.getTenantId();
        if (ObjectUtil.isEmpty(tenantId)){
            throw new RuntimeException("用户登陆信息失效,请重新登陆");
        }
        LambdaQueryWrapper<MemberLevel> eq = new LambdaQueryWrapper<MemberLevel>().eq(MemberLevel::getStatus, 0)
                                                                                  .eq(MemberLevel::getDelFlag, 0)
                                                                                  .eq(MemberLevel::getTenantId, tenantId);
        List<MemberLevel> memberLevels = memberLevelMapper.selectList(eq);
        List<Long> codes = memberLevels.stream().map(MemberLevel::getDictCode).collect(Collectors.toList());
        List<LevelPriceDTO> levelNames = memberLevelService.getLevelNames(codes);
        Map<String, String> collect = levelNames.stream()
                                                .collect(Collectors.toMap(LevelPriceDTO::getDictCode, LevelPriceDTO::getDictLabel));
        ArrayList<MemberLevelVO> list = new ArrayList<>();
        for (MemberLevel memberLevel : memberLevels) {
            MemberLevelVO memberLevelVO = new MemberLevelVO();
            String levelName = collect.get(String.valueOf(memberLevel.getDictCode()));

            BeanUtil.copyProperties(memberLevel, memberLevelVO);
            memberLevelVO.setLevelName(levelName);
            list.add(memberLevelVO);
        }
        return list;
    }

    /**
     * 功能描述：
     * 功能描述：
     * 功能描述：
     * 功能描述：
     * 功能描述：
     * 功能描述：分析excel
     *
     * @param firstCol     第一列 需要合并/复制的目标开始列  59
     * @param lastCol      最后一列 需要合并/复制的目标结束列 61
     * @param colNum       col num  克隆的源列 开始列 59
     * @param colNumPlus   Col Num Plus 克隆的源列  59
     * @param memberLevels 业务动态列名称集合
     * @param interval     合并间隔 3
     * @param width        列宽度
     *                     =     * @return {@link ResponseEntity }<{@link byte[] }>
     * <AUTHOR>
     * @date 2024/07/09
     */
    @Override
    public ResponseEntity<byte[]> analyzeExcel(Workbook workbook, Integer firstCol, Integer lastCol, Integer colNum,
                                               Integer colNumPlus, List<MemberLevel> memberLevels, Integer interval,
                                               Integer width) {
        try {
            // 读取上传的Excel文件
            Integer initColNum = colNum;
            Sheet sheet = workbook.getSheetAt(0);
            Boolean isChange = Boolean.TRUE;
            boolean isFirst = true;
            MemberLevel memberLevel1 = memberLevels.get(0);
            memberLevels.remove(0);
            // 第 56-58 56/58 需要更改名称
            for (int rowNum = 0; rowNum < 10; rowNum++) {
                Row row = sheet.getRow(rowNum);
                if (row == null) {
                    // 如果行不存在，则创建它（但在这个例子中，我们假设所有行都存在）
                    // row = sheet.createRow(rowNum);
                    continue; // 或者跳过空行
                }
                // 第一次循环更改
                int coefficient2 = 1;
                int coefficient3 = 1;
                int firstCol2 = firstCol;
                int rowMsgNum = 1;

                if (isChange) {
                    String levelName = memberLevelService.getLevelName(memberLevel1.getDictCode());
                    Row rowToStyle = sheet.getRow(0);
                    if (rowToStyle != null) {
                        Cell mergedCell = rowToStyle.getCell(firstCol);
                        if (mergedCell == null) {
                            mergedCell = rowToStyle.createCell(firstCol);
                        }
                        // dict_label+会员等级
                        mergedCell.setCellValue(levelName + "会员售价");
                    }
                    Row rowToStyle2 = sheet.getRow(8); // 第二行
                    if (rowToStyle != null) {
                        Cell mergedCell = rowToStyle2.getCell(firstCol);
                        if (mergedCell == null) {
                            mergedCell = rowToStyle2.createCell(firstCol);
                        }
                        // 如果有需要，也可以在这里设置合并单元格的值
                        // dict_label+会员等级
                        mergedCell.setCellValue(levelName + "会员售价");
                    }
                    isChange = false;
                }


                // 拿出第一个会员等级 用于修改,同时把这个会员等级从list内移除,这样后续就只会更新两次了
                for (MemberLevel memberLevel : memberLevels) {

                    int colNum2 = colNum + interval;

                    // start 影响了目标列填充位置
                    if (rowMsgNum == 3 && (rowNum != 0 && rowNum != 8)) {
                        continue;
                    }
                    for (int start = colNum2 - 3; start < colNum2; start++) {

                        Cell sourceCell = null;
                        // 除了1 和 9 是 56拿固定 其余
                        if (rowNum == 0 || rowNum == 8) {
                            sourceCell = row.getCell(firstCol);
                        } else {
                            sourceCell = row.getCell(firstCol2);
                            firstCol2++;
                            colNum = firstCol;
                        }

                        if (sourceCell != null) {
                            int targetColNum = 0;

                            if (rowNum == 0 || rowNum == 8) {
                                targetColNum = start + interval;
                            } else {
                                targetColNum = firstCol2 - 1 + interval;

                            }
                            // 目标列（BH-BJ）
                            Cell targetCell = row.getCell(targetColNum);
                            if (targetCell == null) {
                                targetCell = row.createCell(targetColNum);
                            }

                            // 复制单元格的内容
                            targetCell.setCellType(sourceCell.getCellType());
                            switch (sourceCell.getCellType()) {
                                case STRING:
                                    targetCell.setCellValue(sourceCell.getStringCellValue());
                                    break;
                                case NUMERIC:
                                    targetCell.setCellValue(sourceCell.getNumericCellValue());
                                    break;
                                case BOOLEAN:
                                    targetCell.setCellValue(sourceCell.getBooleanCellValue());
                                    break;
                                case FORMULA:
                                    targetCell.setCellFormula(sourceCell.getCellFormula());
                                    break;
                                case BLANK:
                                    // 空白单元格不需要额外处理，因为已经创建了
                                    break;
                                // 其他类型如ERROR, FORMULA_ERROR等可以根据需要处理
                                default:
                                    throw new IllegalStateException("Unexpected value type: " + sourceCell.getCellType());
                            }

                            // 复制样式 此处的样式会在后续更改列的名称时被覆盖所以在下方需要额外修改
                            CellStyle sourceStyle = sourceCell.getCellStyle();
                            CellStyle targetStyle = workbook.createCellStyle();
                            targetStyle.cloneStyleFrom(sourceStyle);
                            targetCell.setCellStyle(targetStyle);
                            sheet.setColumnWidth(targetColNum, width * 256);
                        }
                        rowMsgNum++;
                    }
                    colNum = colNum + interval;
                    String levelName = memberLevelService.getLevelName(memberLevel.getDictCode());
                    if (rowNum == 0) {
                        int iStart = firstCol + interval * coefficient2;
                        int iEnd = lastCol + interval * coefficient2;
                        // 字体样式
                        Row rowToStyle = sheet.getRow(0);
                        if (rowToStyle != null) {
                            Cell mergedCell = rowToStyle.getCell(colNum);
                            if (mergedCell == null) {
                                mergedCell = rowToStyle.createCell(firstCol + 3);
                            }

                            CellStyle style = workbook.createCellStyle();
                            style.setAlignment(HorizontalAlignment.CENTER); // 设置水平居中
                            // 如果需要垂直居中
                            style.setVerticalAlignment(VerticalAlignment.CENTER);
                            mergedCell.setCellStyle(style);
                            // dict_label+会员等级
                            Cell sourceCell = row.getCell(firstCol);
                            CellStyle sourceStyle = sourceCell.getCellStyle();
                            style.cloneStyleFrom(sourceStyle);
                            mergedCell.setCellValue(levelName + "会员售价");
                        }
                        CellRangeAddress cellRangeAddress = new CellRangeAddress(
                            rowNum, rowNum, iStart, iEnd
                        );
                        sheet.addMergedRegion(cellRangeAddress);

                        coefficient2++;
                    }
                    // 下标从0开始
                    if (rowNum == 8) {
                        if (isFirst) {
//                            firstCol = colNum;
                            lastCol = colNum + 2;
                        }
                        int iStart = firstCol + interval * coefficient3;
                        int iEnd = firstCol + interval * (coefficient3 + 1) - 1;

                        CellRangeAddress cellRangeAddress = new CellRangeAddress(
                            rowNum, rowNum, iStart, iEnd
                        );
                        sheet.addMergedRegion(cellRangeAddress);
                        // 下标从1开始
                        Row rowToStyle = sheet.getRow(8); // 第二行
                        if (rowToStyle != null) {
                            Cell mergedCell = rowToStyle.getCell(colNum);
                            if (mergedCell == null) {
                                mergedCell = rowToStyle.createCell(colNum);
                            }

                            CellStyle style = workbook.createCellStyle();
                            style.setAlignment(HorizontalAlignment.CENTER); // 设置水平居中
                            // 垂直居中，
                            style.setVerticalAlignment(VerticalAlignment.CENTER);
                            // 复制样式
                            Cell sourceCell = row.getCell(firstCol);
                            CellStyle sourceStyle = sourceCell.getCellStyle();
                            style.cloneStyleFrom(sourceStyle);

                            mergedCell.setCellStyle(style);
                            mergedCell.setCellValue(levelName + "会员售价");
                        }
                        coefficient3++;
                        isFirst = false;
                    }


                }
                colNum = initColNum;
            }

            // 将修改后的Workbook写入到ByteArrayOutputStream中
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            workbook.write(outputStream);
            workbook.close(); // 关闭Workbook以释放资源

            // 设置HTTP响应头
            HttpHeaders headers = new HttpHeaders();
            headers.add(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=modified-file.xlsx");
            headers.add(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_OCTET_STREAM_VALUE);

            // 返回修改后的Excel文件字节数组
            return ResponseEntity.ok()
                                 .headers(headers)
                                 .body(outputStream.toByteArray());

        } catch (IOException e) {
            // 处理异常，例如返回错误响应
            return ResponseEntity.badRequest().body(null); // 这里可以根据需要返回更详细的错误信息
        }
    }

    /**
     * 功能描述：
     * 功能描述：分析excel v2 单层结构
     *
     * @param workbook     工作簿
     * @param firstCol     第一科尔
     * @param lastCol      最后一个col
     * @param colNum       序号
     * @param colNumPlus   Col Num Plus
     * @param memberLevels 成员级别
     * @param interval     间隔
     * @param width        宽度
     * @param analyzeRow   分析行 一般是表头 从0 开始
     * @return {@link ResponseEntity }<{@link byte[] }>
     * <AUTHOR>
     * @date 2024/07/16
     */
    public ResponseEntity<byte[]> analyzeExcelV2(Workbook workbook, Integer firstCol, Integer lastCol, Integer colNum,
                                               Integer colNumPlus, List<MemberLevel> memberLevels, Integer interval,
                                               Integer width, Integer analyzeRow) {
        try {
            // 读取上传的Excel文件
            Integer initColNum = colNum;
            Sheet sheet = workbook.getSheetAt(0);
            Boolean isChange = Boolean.TRUE;
//            boolean isFirst = true;
            MemberLevel memberLevel1 = memberLevels.get(0);
            memberLevels.remove(0);
            // 第 56-58 56/58 需要更改名称
            for (int rowNum = 0; rowNum < 3; rowNum++) {
                Row row = sheet.getRow(rowNum);
                if (row == null) {
                    // 如果行不存在，则创建它（但在这个例子中，我们假设所有行都存在）
                    // row = sheet.createRow(rowNum);
                    continue; // 或者跳过空行
                }
                // 第一次循环更改
                int coefficient2 = 1;
//                int coefficient3 = 1;
                int firstCol2 = firstCol;
                int rowMsgNum = 1;

                if (isChange) {
                    String levelName = memberLevelService.getLevelName(memberLevel1.getDictCode());
                    Row rowToStyle = sheet.getRow(0);
                    if (rowToStyle != null) {
                        Cell mergedCell = rowToStyle.getCell(firstCol);
                        if (mergedCell == null) {
                            mergedCell = rowToStyle.createCell(firstCol);
                        }
                        // dict_label+会员等级
                        mergedCell.setCellValue(levelName + "会员售价");
                    }
//                    额外操作行
//                    Row rowToStyle2 = sheet.getRow(8); // 第二行
//                    if (rowToStyle != null) {
//                        Cell mergedCell = rowToStyle2.getCell(firstCol);
//                        if (mergedCell == null) {
//                            mergedCell = rowToStyle2.createCell(firstCol);
//                        }
//                        // 如果有需要，也可以在这里设置合并单元格的值
//                        // dict_label+会员等级
//                        mergedCell.setCellValue(levelName + "会员售价");
//                    }
                    isChange = false;
                }


                // 拿出第一个会员等级 用于修改,同时把这个会员等级从list内移除,这样后续就只会更新两次了
                for (MemberLevel memberLevel : memberLevels) {

                    int colNum2 = colNum + interval;

                    // start 影响了目标列填充位置
                    if (rowMsgNum == 3 && (rowNum != analyzeRow)) {
                        continue;
                    }
                    for (int start = colNum2 - 3; start < colNum2; start++) {

                        Cell sourceCell = null;
                        // 除了1 和 9 是 56拿固定 其余
                        if (rowNum == analyzeRow) {
                            sourceCell = row.getCell(firstCol);
                        } else {
                            sourceCell = row.getCell(firstCol2);
                            firstCol2++;
                            colNum = firstCol;
                        }

                        if (sourceCell != null) {
                            int targetColNum = 0;

                            if (rowNum == analyzeRow) {
                                targetColNum = start + interval;
                            } else {
                                targetColNum = firstCol2 - 1 + interval;

                            }
                            // 目标列（BH-BJ）
                            Cell targetCell = row.getCell(targetColNum);
                            if (targetCell == null) {
                                targetCell = row.createCell(targetColNum);
                            }

                            // 复制单元格的内容
                            targetCell.setCellType(sourceCell.getCellType());
                            switch (sourceCell.getCellType()) {
                                case STRING:
                                    targetCell.setCellValue(sourceCell.getStringCellValue());
                                    break;
                                case NUMERIC:
                                    targetCell.setCellValue(sourceCell.getNumericCellValue());
                                    break;
                                case BOOLEAN:
                                    targetCell.setCellValue(sourceCell.getBooleanCellValue());
                                    break;
                                case FORMULA:
                                    targetCell.setCellFormula(sourceCell.getCellFormula());
                                    break;
                                case BLANK:
                                    // 空白单元格不需要额外处理，因为已经创建了
                                    break;
                                // 其他类型如ERROR, FORMULA_ERROR等可以根据需要处理
                                default:
                                    throw new IllegalStateException("Unexpected value type: " + sourceCell.getCellType());
                            }

                            // 复制样式 此处的样式会在后续更改列的名称时被覆盖所以在下方需要额外修改
                            CellStyle sourceStyle = sourceCell.getCellStyle();
                            CellStyle targetStyle = workbook.createCellStyle();
                            targetStyle.cloneStyleFrom(sourceStyle);
                            targetCell.setCellStyle(targetStyle);
                            sheet.setColumnWidth(targetColNum, width * 256);
                        }
                        rowMsgNum++;
                    }
                    colNum = colNum + interval;
                    String levelName = memberLevelService.getLevelName(memberLevel.getDictCode());
                    if (rowNum == analyzeRow) {
                        int iStart = firstCol + interval * coefficient2;
                        int iEnd = lastCol + interval * coefficient2;
                        // 字体样式
                        Row rowToStyle = sheet.getRow(analyzeRow);
                        if (rowToStyle != null) {
                            Cell mergedCell = rowToStyle.getCell(colNum);
                            if (mergedCell == null) {
                                mergedCell = rowToStyle.createCell(firstCol + 3);
                            }

                            CellStyle style = workbook.createCellStyle();
                            style.setAlignment(HorizontalAlignment.CENTER); // 设置水平居中
                            // 如果需要垂直居中
                            style.setVerticalAlignment(VerticalAlignment.CENTER);
                            mergedCell.setCellStyle(style);
                            // dict_label+会员等级
                            Cell sourceCell = row.getCell(firstCol);
                            CellStyle sourceStyle = sourceCell.getCellStyle();
                            style.cloneStyleFrom(sourceStyle);
                            mergedCell.setCellValue(levelName + "会员售价");
                        }
                        CellRangeAddress cellRangeAddress = new CellRangeAddress(
                            rowNum, rowNum, iStart, iEnd
                        );
                        sheet.addMergedRegion(cellRangeAddress);

                        coefficient2++;
                    }
//                    // 下标从0开始 单层表头
//                    if (rowNum == 8) {
//                        if (isFirst) {
////                            firstCol = colNum;
//                            lastCol = colNum + 2;
//                        }
//                        int iStart = firstCol + interval * coefficient3;
//                        int iEnd = firstCol + interval * (coefficient3 + 1) - 1;
//
//                        CellRangeAddress cellRangeAddress = new CellRangeAddress(
//                            rowNum, rowNum, iStart, iEnd
//                        );
//                        sheet.addMergedRegion(cellRangeAddress);
//                        // 下标从1开始
//                        Row rowToStyle = sheet.getRow(8); // 第二行
//                        if (rowToStyle != null) {
//                            Cell mergedCell = rowToStyle.getCell(colNum);
//                            if (mergedCell == null) {
//                                mergedCell = rowToStyle.createCell(colNum);
//                            }
//
//                            CellStyle style = workbook.createCellStyle();
//                            style.setAlignment(HorizontalAlignment.CENTER); // 设置水平居中
//                            // 垂直居中，
//                            style.setVerticalAlignment(VerticalAlignment.CENTER);
//                            // 复制样式
//                            Cell sourceCell = row.getCell(firstCol);
//                            CellStyle sourceStyle = sourceCell.getCellStyle();
//                            style.cloneStyleFrom(sourceStyle);
//
//                            mergedCell.setCellStyle(style);
//                            mergedCell.setCellValue(levelName + "会员售价");
//                        }
//                        coefficient3++;
//                        isFirst = false;
//                    }


                }
                colNum = initColNum;
            }

            // 将修改后的Workbook写入到ByteArrayOutputStream中
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            workbook.write(outputStream);
            workbook.close(); // 关闭Workbook以释放资源

            // 设置HTTP响应头
            HttpHeaders headers = new HttpHeaders();
            headers.add(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=modified-file.xlsx");
            headers.add(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_OCTET_STREAM_VALUE);

            // 返回修改后的Excel文件字节数组
            return ResponseEntity.ok()
                                 .headers(headers)
                                 .body(outputStream.toByteArray());

        } catch (IOException e) {
            // 处理异常，例如返回错误响应
            return ResponseEntity.badRequest().body(null); // 这里可以根据需要返回更详细的错误信息
        }
    }

    /**
     * 功能描述：创建规则级产品
     *
     * @param levelOriginalUnitPrice        水平原始单价1
     * @param levelOriginalFinalDeliveryFee 水平原始最终交付费1
     * @param levelOriginalOperationFee     水平原始操作费1
     * @param tenantId                      租户id
     * @param priceDTO
     * @return {@link RuleLevelProductPrice }
     * <AUTHOR>
     * @date 2024/05/20
     */
    private RuleLevelProductPrice createRuleLevelProduct(BigDecimal levelOriginalUnitPrice,
                                                         BigDecimal levelOriginalFinalDeliveryFee,
                                                         BigDecimal levelOriginalOperationFee, String tenantId,
                                                         Long dictCode, LevelPriceParseDTO priceDTO) {
        // 通过code拿levelId

        MemberLevel one = memberLevelService.getOne(new LambdaQueryWrapper<MemberLevel>().eq(MemberLevel::getDictCode, dictCode)
                                                                                         .eq(MemberLevel::getDelFlag, 0)
                                                                                         .eq(MemberLevel::getTenantId, tenantId));
        if (ObjectUtil.isNull(one)) {
            return null;
        }
        if (ObjectUtil.isNotEmpty(levelOriginalUnitPrice) || ObjectUtil.isNotEmpty(levelOriginalFinalDeliveryFee) || ObjectUtil.isNotEmpty(levelOriginalOperationFee)) {
            RuleLevelProductPrice ruleLevelProductPrice = new RuleLevelProductPrice();
            ruleLevelProductPrice.setRuleCustomizerTenantId(tenantId);
            BigDecimal pikUpPrice = computationPrice(levelOriginalUnitPrice, levelOriginalFinalDeliveryFee, levelOriginalOperationFee, LogisticsTypeEnum.PickUp);
            BigDecimal dropPrice = computationPrice(levelOriginalUnitPrice, levelOriginalFinalDeliveryFee, levelOriginalOperationFee, LogisticsTypeEnum.DropShipping);
            ruleLevelProductPrice.setLevelId(one.getId());
            ruleLevelProductPrice.setOriginalUnitPrice(levelOriginalUnitPrice);
            ruleLevelProductPrice.setOriginalOperationFee(levelOriginalOperationFee);
            ruleLevelProductPrice.setOriginalFinalDeliveryFee(levelOriginalFinalDeliveryFee);
            ruleLevelProductPrice.setCreateBy(LoginHelper.getUserId());
            ruleLevelProductPrice.setOriginalPickUpPrice(pikUpPrice);
            ruleLevelProductPrice.setOriginalDropShippingPrice(dropPrice);

            ruleLevelProductPrice.setPlatformDropShippingPrice(dropPrice);
            ruleLevelProductPrice.setPlatformPickUpPrice(pikUpPrice);
            ruleLevelProductPrice.setPlatformOperationFee(levelOriginalOperationFee);
            ruleLevelProductPrice.setPlatformFinalDeliveryFee(levelOriginalFinalDeliveryFee);
            ruleLevelProductPrice.setPlatformUnitPrice(levelOriginalUnitPrice);


            if(ObjectUtil.isNotEmpty(priceDTO.getProductId())){
                ruleLevelProductPrice.setProductSkuId(priceDTO.getProductId());
            }
            if(ObjectUtil.isNotEmpty(priceDTO.getProductSkuId())){
                ruleLevelProductPrice.setProductSkuId(priceDTO.getProductSkuId());
            }
            return ruleLevelProductPrice;
        }
        return null;
    }

    /**
     * 功能描述：计算价格
     *
     * @param levelOriginalUnitPrice        水平原始单价
     * @param levelOriginalFinalDeliveryFee 水平原始最终交付费
     * @param levelOriginalOperationFee     水平原始运营费
     * @param logisticsTypeEnum             物流类型枚举
     * @return {@link BigDecimal }
     * <AUTHOR>
     * @date 2024/05/20
     */
    private BigDecimal computationPrice(BigDecimal levelOriginalUnitPrice, BigDecimal levelOriginalFinalDeliveryFee,
                                        BigDecimal levelOriginalOperationFee, LogisticsTypeEnum logisticsTypeEnum) {
        if (ObjectUtil.isNotEmpty(levelOriginalUnitPrice)
            && ObjectUtil.isNotEmpty(levelOriginalFinalDeliveryFee)
            && ObjectUtil.isNotEmpty(levelOriginalOperationFee)
        ) {
            if (LogisticsTypeEnum.PickUp.equals(logisticsTypeEnum)) {
                return levelOriginalUnitPrice.add(levelOriginalOperationFee);
            }
            if (LogisticsTypeEnum.DropShipping.equals(logisticsTypeEnum)) {
                return levelOriginalUnitPrice.add(levelOriginalOperationFee).add(levelOriginalFinalDeliveryFee);
            }
        }
        if (ObjectUtil.isNotEmpty(levelOriginalUnitPrice)
            && ObjectUtil.isNotEmpty(levelOriginalOperationFee)
        ) {
            if (LogisticsTypeEnum.PickUp.equals(logisticsTypeEnum)) {
                return levelOriginalUnitPrice.add(levelOriginalOperationFee);
            }

        }
        return null;

    }


    private List<ProductSkuAttachment> downloadImage(List<String> images) {
        // 处理图片
        List<ProductSkuAttachment> skuAttachmentList = new ArrayList<>();
        for (int i = 0; i < images.size(); i++) {
            String image = images.get(i);
            File file = null;
            try {
                String suffix = FileUtil.getSuffix(image);
                // 检查是否是非法的后缀
                if (StrUtil.length(suffix) > 5) {
                    continue;
                }

                String tempSavePath = fileProperties.getTempSavePath();
                String fileName = UUID.fastUUID().toString(true) + "." + suffix;
                String tempPath = tempSavePath + File.separator + "productImport" + File.separator + fileName;
                file = FileUtil.newFile(tempPath);
                HttpUtil.downloadFile(image, file, 5000);
                BufferedInputStream inputStream = FileUtil.getInputStream(file);
                SysOssVo sysOssVo = SystemEventUtils.uploadFile(inputStream, fileName);

                ProductSkuAttachment productSkuAttachment = new ProductSkuAttachment();
                productSkuAttachment.setOssId(sysOssVo.getOssId());
                productSkuAttachment.setAttachmentName(fileName);
                productSkuAttachment.setAttachmentOriginalName(fileName);
                productSkuAttachment.setAttachmentShowUrl(sysOssVo.getUrl());
                productSkuAttachment.setAttachmentSavePath(sysOssVo.getSavePath());
                productSkuAttachment.setAttachmentSuffix(suffix);
                productSkuAttachment.setAttachmentSort(i);
                productSkuAttachment.setAttachmentType(AttachmentTypeEnum.Image);
                skuAttachmentList.add(productSkuAttachment);
            } catch (Exception e) {
                log.error("下载图片失败 image = {}，原因 {}", image, e.getMessage(), e);
            } finally {
                FileUtil.del(file);
            }
        }
        return skuAttachmentList;
    }
}




