package com.zsmall.product.biz.service.impl;

import com.zsmall.product.biz.service.ProductSkuWholesalePriceService;
import com.zsmall.product.entity.iservice.IProductSkuWholesalePriceService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 国外现货批发商品SKU价格Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-05-29
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class ProductSkuWholesalePriceServiceImpl implements ProductSkuWholesalePriceService {

    private final IProductSkuWholesalePriceService iProductSkuWholesalePriceService;

}
