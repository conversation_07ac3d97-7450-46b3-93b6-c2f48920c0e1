package com.zsmall.product.biz.service.impl;

import cn.hutool.json.JSONUtil;
import com.hengjian.common.mybatis.core.page.PageQuery;
import com.hengjian.common.mybatis.core.page.TableDataInfo;
import com.zsmall.product.biz.service.ProductSkuPriceRuleItemService;
import com.zsmall.product.entity.domain.ProductSkuPriceRuleItem;
import com.zsmall.product.entity.domain.bo.productSkuPrice.ProductSkuPriceRuleItemBo;
import com.zsmall.product.entity.domain.vo.productSkuPrice.ProductSkuPriceRuleItemVo;
import com.zsmall.product.entity.iservice.IProductSkuPriceRuleItemService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 商品sku价格计算公式Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-05-22
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class ProductSkuPriceRuleItemServiceImpl implements ProductSkuPriceRuleItemService {

    private final IProductSkuPriceRuleItemService iProductSkuPriceRuleItemService;

    /**
     * 查询商品sku价格计算公式
     */
    @Override
    public ProductSkuPriceRuleItemVo queryById(Long id){
        return iProductSkuPriceRuleItemService.queryById(id);
    }

    /**
     * 查询商品sku价格计算公式列表
     */
    @Override
    public TableDataInfo<ProductSkuPriceRuleItemVo> queryPageList(ProductSkuPriceRuleItemBo bo, PageQuery pageQuery) {
        return iProductSkuPriceRuleItemService.queryPageList(bo, pageQuery);
    }

    /**
     * 查询商品sku价格计算公式列表
     */
    @Override
    public List<ProductSkuPriceRuleItemVo> queryList(ProductSkuPriceRuleItemBo bo) {
        return iProductSkuPriceRuleItemService.queryList(bo);
    }

    /**
     * 新增商品sku价格计算公式
     */
    @Override
    public Boolean insertByBo(ProductSkuPriceRuleItemBo bo) {
        return insertByBo(bo);
    }

    /**
     * 修改商品sku价格计算公式
     */
    @Override
    public Boolean updateByBo(ProductSkuPriceRuleItemBo bo) {
        return iProductSkuPriceRuleItemService.updateByBo(bo);
    }

    /**
     * 批量删除商品sku价格计算公式
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        return iProductSkuPriceRuleItemService.deleteWithValidByIds(ids, isValid);
    }

    @Override
    public Boolean isExistByRuleId(Long ruleId) {
        return isExistByRuleId(ruleId);
    }

    @Override
    public List<ProductSkuPriceRuleItemVo> queryListByRuleId(Long ruleId) {
        return iProductSkuPriceRuleItemService.queryListByRuleId(ruleId);
    }

    @Override
    public Boolean saveBatch(List<ProductSkuPriceRuleItem> items) {
        return iProductSkuPriceRuleItemService.saveBatch(items);
    }

    @Override
    public List<ProductSkuPriceRuleItemVo> parseRuleItemListByRuleInfo(Long productSkuPriceRuleId, Integer unitPriceCal, BigDecimal unitPriceCalValue, Integer operationFeeCal, BigDecimal operationFeeCalValue, Integer finalDeliveryFeeCal, BigDecimal finalDeliveryFeeCalValue) {
        return iProductSkuPriceRuleItemService.parseRuleItemListByRuleInfo(productSkuPriceRuleId, unitPriceCal, unitPriceCalValue, operationFeeCal, operationFeeCalValue, finalDeliveryFeeCal, finalDeliveryFeeCalValue);
    }

    @Override
    public List<ProductSkuPriceRuleItemVo> getItemListByRuleId(Long ruleId) {
        log.info("进入【通过价格公式id获取公式明细数据】方法, ruleId = {}", ruleId);
        return iProductSkuPriceRuleItemService.getItemListByRuleId(ruleId);
    }

    @Override
    public List<ProductSkuPriceRuleItemVo> getItemListByRuleIds(List<Long> ruleIds) {
        log.info("进入【通过价格公式id获取公式明细数据】方法, ruleIds = {}", JSONUtil.toJsonStr(ruleIds));
        return iProductSkuPriceRuleItemService.getItemListByRuleIds(ruleIds);
    }

    @Override
    public Map<Long, String> getPriceFormulaByRuleIds(List<Long> ruleIds) {
        log.info("进入【根据规则第三集合获取获取定价公式】方法, ruleIds = {}", JSONUtil.toJsonStr(ruleIds));
        return iProductSkuPriceRuleItemService.getPriceFormulaByRuleIds(ruleIds);
    }

    @Override
    public Boolean updateDeleteMarkByRuleId(Long ruleId) {
        log.info("进入【根据公式id批量修改删除明细信息】方法, ruleId = {}", ruleId);
        return iProductSkuPriceRuleItemService.updateDeleteMarkByRuleId(ruleId);
    }

    @Override
    public Boolean matchItems(List<ProductSkuPriceRuleItemVo> items, List<ProductSkuPriceRuleItemVo> newItems) {
        return iProductSkuPriceRuleItemService.matchItems(items, newItems);
    }
}
