package com.zsmall.product.biz.support;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.zsmall.product.entity.domain.ProductSku;
import com.zsmall.product.entity.domain.ProductSkuAttribute;
import com.zsmall.product.entity.domain.ProductSkuAttachment;
import com.zsmall.product.entity.domain.ProductSkuDetail;
import com.zsmall.product.entity.domain.ProductSkuPrice;
import com.zsmall.product.entity.domain.vo.productSkuStock.ProductSkuStockSimpleVo;
import com.zsmall.warehouse.entity.domain.LogisticsTemplate;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.stream.Collectors;

/**
 * ProductSku数据组装工具类
 * 用于将批量查询的结果转换为便于查找的Map结构，提升性能
 *
 * <AUTHOR> Assistant
 * @date 2025-01-18
 */
@Slf4j
public class ProductSkuDataSupper {

    /**
     * 将SKU详情列表转换为以SKU ID为键的Map
     * @param skuDetails SKU详情列表
     * @return 以SKU ID为键的Map
     */
    public static Map<Long, ProductSkuDetail> buildSkuDetailMap(List<ProductSkuDetail> skuDetails) {
        if (CollUtil.isEmpty(skuDetails)) {
            return new HashMap<>();
        }
        return skuDetails.stream()
                .filter(Objects::nonNull)
                .collect(Collectors.toMap(
                    ProductSkuDetail::getProductSkuId,
                    detail -> detail,
                    (existing, replacement) -> existing
                ));
    }

    /**
     * 将SKU价格列表转换为以SKU ID为键的Map（每个SKU可能有多个价格）
     * @param skuPrices SKU价格列表
     * @return 以SKU ID为键的Map，值为价格列表
     */
    public static Map<Long, List<ProductSkuPrice>> buildSkuPriceMap(List<ProductSkuPrice> skuPrices) {
        if (CollUtil.isEmpty(skuPrices)) {
            return new HashMap<>();
        }
        return skuPrices.stream()
                .filter(Objects::nonNull)
                .filter(price -> ObjectUtil.isNotNull(price.getProductSkuId()))
                .collect(Collectors.groupingBy(ProductSkuPrice::getProductSkuId));
    }

    /**
     * 将SKU属性列表转换为以SKU ID为键的Map（每个SKU可能有多个属性）
     * @param skuAttributes SKU属性列表
     * @return 以SKU ID为键的Map，值为属性列表
     */
    public static Map<Long, List<ProductSkuAttribute>> buildSkuAttributeMap(List<ProductSkuAttribute> skuAttributes) {
        if (CollUtil.isEmpty(skuAttributes)) {
            return new HashMap<>();
        }
        return skuAttributes.stream()
                .filter(Objects::nonNull)
                .filter(attr -> ObjectUtil.isNotNull(attr.getProductSkuId()))
                .collect(Collectors.groupingBy(ProductSkuAttribute::getProductSkuId));
    }

    /**
     * 将SKU附件列表转换为以SKU ID为键的Map（每个SKU可能有多个附件）
     * @param skuAttachments SKU附件列表
     * @return 以SKU ID为键的Map，值为附件列表
     */
    public static Map<Long, List<ProductSkuAttachment>> buildSkuAttachmentMap(List<ProductSkuAttachment> skuAttachments) {
        if (CollUtil.isEmpty(skuAttachments)) {
            return new HashMap<>();
        }
        return skuAttachments.stream()
                .filter(Objects::nonNull)
                .filter(attachment -> ObjectUtil.isNotNull(attachment.getProductSkuId()))
                .collect(Collectors.groupingBy(ProductSkuAttachment::getProductSkuId));
    }

    /**
     * 将SKU库存列表转换为以SKU Code为键的Map（每个SKU可能有多个库存记录）
     * @param skuStocks SKU库存列表
     * @return 以SKU Code为键的Map，值为库存列表
     */
    public static Map<String, List<ProductSkuStockSimpleVo>> buildSkuStockMap(List<ProductSkuStockSimpleVo> skuStocks) {
        if (CollUtil.isEmpty(skuStocks)) {
            return new HashMap<>();
        }
        return skuStocks.stream()
                .filter(Objects::nonNull)
                .filter(stock -> ObjectUtil.isNotEmpty(stock.getProductSkuCode()))
                .collect(Collectors.groupingBy(ProductSkuStockSimpleVo::getProductSkuCode));
    }

    /**
     * 从Map中安全获取SKU详情
     * @param skuDetailMap SKU详情Map
     * @param skuId SKU ID
     * @return SKU详情，如果不存在则返回null
     */
    public static ProductSkuDetail getSkuDetail(Map<Long, ProductSkuDetail> skuDetailMap, Long skuId) {
        if (ObjectUtil.isNull(skuId) || CollUtil.isEmpty(skuDetailMap)) {
            return null;
        }
        return skuDetailMap.get(skuId);
    }

    /**
     * 从Map中安全获取SKU价格列表
     * @param skuPriceMap SKU价格Map
     * @param skuId SKU ID
     * @return SKU价格列表，如果不存在则返回空列表
     */
    public static List<ProductSkuPrice> getSkuPrices(Map<Long, List<ProductSkuPrice>> skuPriceMap, Long skuId) {
        if (ObjectUtil.isNull(skuId) || CollUtil.isEmpty(skuPriceMap)) {
            return new ArrayList<>();
        }
        return skuPriceMap.getOrDefault(skuId, new ArrayList<>());
    }

    /**
     * 从Map中安全获取SKU属性列表
     * @param skuAttributeMap SKU属性Map
     * @param skuId SKU ID
     * @return SKU属性列表，如果不存在则返回空列表
     */
    public static List<ProductSkuAttribute> getSkuAttributes(Map<Long, List<ProductSkuAttribute>> skuAttributeMap, Long skuId) {
        if (ObjectUtil.isNull(skuId) || CollUtil.isEmpty(skuAttributeMap)) {
            return new ArrayList<>();
        }
        return skuAttributeMap.getOrDefault(skuId, new ArrayList<>());
    }

    /**
     * 从Map中安全获取SKU附件列表
     * @param skuAttachmentMap SKU附件Map
     * @param skuId SKU ID
     * @return SKU附件列表，如果不存在则返回空列表
     */
    public static List<ProductSkuAttachment> getSkuAttachments(Map<Long, List<ProductSkuAttachment>> skuAttachmentMap, Long skuId) {
        if (ObjectUtil.isNull(skuId) || CollUtil.isEmpty(skuAttachmentMap)) {
            return new ArrayList<>();
        }
        return skuAttachmentMap.getOrDefault(skuId, new ArrayList<>());
    }

    /**
     * 从Map中安全获取SKU库存列表
     * @param skuStockMap SKU库存Map
     * @param skuCode SKU编号
     * @return SKU库存列表，如果不存在则返回空列表
     */
    public static List<ProductSkuStockSimpleVo> getSkuStocks(Map<String, List<ProductSkuStockSimpleVo>> skuStockMap, String skuCode) {
        if (ObjectUtil.isEmpty(skuCode) || CollUtil.isEmpty(skuStockMap)) {
            return new ArrayList<>();
        }
        return skuStockMap.getOrDefault(skuCode, new ArrayList<>());
    }

    /**
     * 从ProductSku列表中提取所有的SKU ID
     * @param productSkuList ProductSku列表
     * @return SKU ID列表
     */
    public static List<Long> extractSkuIds(List<ProductSku> productSkuList) {
        if (CollUtil.isEmpty(productSkuList)) {
            return new ArrayList<>();
        }
        return productSkuList.stream()
                .filter(Objects::nonNull)
                .map(ProductSku::getId)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /**
     * 从ProductSku列表中提取所有的SKU Code
     * @param productSkuList ProductSku列表
     * @return SKU Code列表
     */
    public static List<String> extractSkuCodes(List<ProductSku> productSkuList) {
        if (CollUtil.isEmpty(productSkuList)) {
            return new ArrayList<>();
        }
        return productSkuList.stream()
                .filter(Objects::nonNull)
                .map(ProductSku::getProductSkuCode)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /**
     * 将物流模板列表转换为以仓库编码为键的Map
     * 注意：由于LogisticsTemplate没有warehouseSystemCode字段，这里需要配合特殊的处理逻辑
     * @param logisticsTemplates 物流模板列表（通过JOIN查询获得）
     * @param warehouseSystemCodes 仓库编码列表（用于建立映射关系）
     * @return 以仓库编码为键的Map，值为物流模板列表
     */
    public static Map<String, List<LogisticsTemplate>> buildLogisticsTemplateMap(
            List<LogisticsTemplate> logisticsTemplates, List<String> warehouseSystemCodes) {
        Map<String, List<LogisticsTemplate>> result = new HashMap<>();

        // 初始化所有仓库的空列表
        for (String warehouseCode : warehouseSystemCodes) {
            result.put(warehouseCode, new ArrayList<>());
        }

        // 由于LogisticsTemplate没有warehouseSystemCode字段，
        // 而且JOIN查询返回的结果无法直接区分属于哪个仓库，
        // 这里采用简化策略：所有仓库共享相同的物流模板列表
        // 这样可以保证功能正常，虽然不是最优的内存使用
        for (String warehouseCode : warehouseSystemCodes) {
            result.put(warehouseCode, new ArrayList<>(logisticsTemplates));
        }

        return result;
    }

    /**
     * 从Map中安全获取物流模板列表
     * @param logisticsTemplateMap 物流模板Map
     * @param warehouseSystemCode 仓库编码
     * @return 物流模板列表，如果不存在则返回空列表
     */
    public static List<LogisticsTemplate> getLogisticsTemplates(
            Map<String, List<LogisticsTemplate>> logisticsTemplateMap, String warehouseSystemCode) {
        if (ObjectUtil.isEmpty(warehouseSystemCode) || CollUtil.isEmpty(logisticsTemplateMap)) {
            return new ArrayList<>();
        }
        return logisticsTemplateMap.getOrDefault(warehouseSystemCode, new ArrayList<>());
    }

    /**
     * 从库存配置列表中提取所有的仓库编码
     * @param stockConfigList 库存配置列表
     * @return 仓库编码列表
     */
    public static List<String> extractWarehouseCodes(List<?> stockConfigList) {
        if (CollUtil.isEmpty(stockConfigList)) {
            return new ArrayList<>();
        }
        return stockConfigList.stream()
                .filter(Objects::nonNull)
                .map(config -> {
                    try {
                        // 使用反射获取WarehouseSystemCode字段
                        return (String) config.getClass().getMethod("getWarehouseSystemCode").invoke(config);
                    } catch (Exception e) {
                        log.warn("无法获取仓库编码: {}", e.getMessage());
                        return null;
                    }
                })
                .filter(Objects::nonNull)
                .distinct() // 去重
                .collect(Collectors.toList());
    }

    /**
     * 从Map中安全获取SKU的审核记录存在性
     * @param reviewExistsMap 审核记录存在性Map
     * @param skuCode SKU编码
     * @return 是否存在审核记录，如果不存在则返回false
     */
    public static Boolean getReviewExists(Map<String, Boolean> reviewExistsMap, String skuCode) {
        if (ObjectUtil.isEmpty(skuCode) || CollUtil.isEmpty(reviewExistsMap)) {
            return false;
        }
        return reviewExistsMap.getOrDefault(skuCode, false);
    }

    /**
     * 记录性能统计信息
     * @param operation 操作名称
     * @param count 处理数量
     * @param startTime 开始时间
     */
    public static void logPerformance(String operation, int count, long startTime) {
        long endTime = System.currentTimeMillis();
        long duration = endTime - startTime;
        log.info("【性能统计】{} 处理{}条记录，耗时{}ms，平均{}ms/条",
                operation, count, duration, count > 0 ? duration / count : 0);
    }
}
