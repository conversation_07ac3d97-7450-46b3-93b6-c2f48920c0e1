package com.zsmall.product.biz.service;

import com.hengjian.common.core.exception.RStatusCodeException;
import com.hengjian.common.mybatis.core.page.PageQuery;
import com.hengjian.common.mybatis.core.page.TableDataInfo;
import com.zsmall.product.entity.domain.bo.ProductGlobalAttributeBo;
import com.zsmall.product.entity.domain.bo.productGlobalAttribute.ProductGlobalAttributeSelectBo;
import com.zsmall.product.entity.domain.vo.ProductGlobalAttributeVo;
import com.zsmall.product.entity.domain.vo.productGlobalAttribute.ProductGlobalAttributeSelectVo;
import com.zsmall.product.entity.domain.vo.productGlobalAttribute.ProductGlobalAttributeSimpleVo;

import java.util.Collection;
import java.util.List;

/**
 * 商品全局属性Service接口
 *
 * <AUTHOR>
 * @date 2023-05-19
 */
public interface ProductGlobalAttributeService {

    /**
     * 查询商品全局属性
     */
    ProductGlobalAttributeVo queryById(Long id);

    /**
     * 查询商品全局属性列表
     */
    TableDataInfo<ProductGlobalAttributeVo> queryPageList(ProductGlobalAttributeBo bo, PageQuery pageQuery);

    /**
     * 查询商品全局属性列表
     */
    List<ProductGlobalAttributeVo> queryList(ProductGlobalAttributeBo bo);

    /**
     * 查询商品全局属性列表（提供给下拉选使用）
     * @param bo
     * @return
     */
    ProductGlobalAttributeSelectVo queryListForSelect(ProductGlobalAttributeSelectBo bo) throws RStatusCodeException;

    /**
     * 查询商品全局属性列表（提供给分类绑定属性使用）
     * @return
     * @throws RStatusCodeException
     */
    List<ProductGlobalAttributeSimpleVo> queryListForCategory(ProductGlobalAttributeSelectBo bo);

    /**
     * 新增商品全局属性
     */
    Boolean insertByBo(ProductGlobalAttributeBo bo) throws RStatusCodeException;

    /**
     * 修改商品全局属性
     */
    Boolean updateByBo(ProductGlobalAttributeBo bo) throws RStatusCodeException;

    /**
     * 校验并批量删除商品全局属性信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
