package com.zsmall.bma.open.member.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hengjian.common.core.utils.MapstructUtils;
import com.hengjian.common.core.utils.StringUtils;
import com.hengjian.common.mybatis.core.page.PageQuery;
import com.hengjian.common.mybatis.core.page.TableDataInfo;
import com.hengjian.common.satoken.utils.LoginHelper;
import com.zsmall.bma.open.member.mapper.MemberDiscountMapper;
import com.zsmall.bma.open.member.service.IMemberDiscountV2Service;
import com.zsmall.product.entity.domain.bo.member.MemberDiscountBo;
import com.zsmall.product.entity.domain.member.MemberDiscount;
import com.zsmall.product.entity.domain.vo.member.MemberDiscountVo;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2024/9/29 15:19
 */
@RequiredArgsConstructor
@Service
public class MemberDiscountServiceImpl implements IMemberDiscountV2Service {

    private final MemberDiscountMapper baseMapper;

    /**
     * 查询会员等级折扣
     */
    @Override
    public MemberDiscountVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询会员等级折扣列表
     */
    @Override
    public TableDataInfo<MemberDiscountVo> queryPageList(MemberDiscountBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<MemberDiscount> lqw = buildQueryWrapper(bo);
        lqw.orderByDesc(MemberDiscount::getCreateTime);
        Page<MemberDiscountVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询会员等级折扣列表
     */
    @Override
    public List<MemberDiscountVo> queryList(MemberDiscountBo bo) {
        LambdaQueryWrapper<MemberDiscount> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<MemberDiscount> buildQueryWrapper(MemberDiscountBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<MemberDiscount> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getDictCode() != null, MemberDiscount::getDictCode, bo.getDictCode());
        lqw.like(StringUtils.isNotBlank(bo.getMemberName()), MemberDiscount::getMemberName, bo.getMemberName());
        lqw.eq(bo.getMemberState() != null, MemberDiscount::getMemberState, bo.getMemberState());
        return lqw;
    }

    /**
     * 新增会员等级折扣
     */
    @Override
    public Boolean insertByBo(MemberDiscountBo bo) {
        MemberDiscount add = MapstructUtils.convert(bo, MemberDiscount.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改会员等级折扣
     */
    @Override
    public Boolean updateByBo(MemberDiscountBo bo) {
        MemberDiscount update = MapstructUtils.convert(bo, MemberDiscount.class);
        validEntityBeforeUpdate(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(MemberDiscount entity){
        //TODO 做一些数据校验,如唯一约束
        LambdaQueryWrapper<MemberDiscount> queryWrapper=new LambdaQueryWrapper<>();
        queryWrapper.eq(MemberDiscount::getDictCode,entity.getDictCode());
        queryWrapper.eq(MemberDiscount::getDelFlag,0);
        MemberDiscount memberDiscount = baseMapper.selectOne(queryWrapper);
        if (Objects.nonNull(memberDiscount)){
            throw new RuntimeException("该等级已经配置尾程加点系数，无法重复添加");
        }
        if (entity.getMemberState()!=0 &&entity.getMemberState()!=2){
            throw new RuntimeException(StrUtil.format("错误的会员折扣系数状态{}",entity.getMemberState()));
        }
        if ( entity.getMemberState() == 2){
            entity.setMemberDiscount(13);
        }else {
            entity.setMemberDiscount(entity.getBeforeMemberDiscount());
        }
        entity.setCreateBy(LoginHelper.getUserId());
    }

    /**
     * 更新前的数据校验
     */
    private void validEntityBeforeUpdate(MemberDiscount entity){
        MemberDiscount memberDiscount = baseMapper.selectById(entity.getId());
        if (Objects.isNull(memberDiscount)){
            throw new RuntimeException(StrUtil.format("会员折扣系数不存在:{}",entity.getId()));
        }
        if (entity.getMemberState()!=0 &&entity.getMemberState()!=2){
            throw new RuntimeException(StrUtil.format("错误的会员折扣系数状态{}",entity.getMemberState()));
        }
        //如果由启动变为禁用,将折扣率改为0.13
        if ( entity.getMemberState() == 2){
            entity.setMemberDiscount(13);
        }else {
            entity.setMemberDiscount(entity.getBeforeMemberDiscount());
        }
    }

    /**
     * 批量删除会员等级折扣
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    @Override
    public BigDecimal getMemberDiscountByDictCode(Long dictCode) {
        LambdaQueryWrapper<MemberDiscount> eq = new LambdaQueryWrapper<MemberDiscount>().eq(MemberDiscount::getDictCode, dictCode)
                                                                                        .eq(MemberDiscount::getDelFlag, 0);
        MemberDiscount memberDiscount = baseMapper.selectOne(eq);
        if (Objects.isNull(memberDiscount)){
            return BigDecimal.valueOf(13);
        }
        if (Objects.isNull(memberDiscount.getMemberDiscount())){
            throw new RuntimeException("会员等级折扣值不存在");
        }else {
            return BigDecimal.valueOf(memberDiscount.getMemberDiscount()) ;
        }
    }


}
