package com.zsmall.product.biz.service;

import cn.hutool.json.JSONArray;
import com.hengjian.common.core.domain.R;
import com.hengjian.common.core.exception.RStatusCodeException;
import com.hengjian.common.mybatis.core.page.PageQuery;
import com.hengjian.common.mybatis.core.page.TableDataInfo;
import com.zsmall.product.entity.domain.ProductSkuPrice;
import com.zsmall.product.entity.domain.ProductSkuPriceRule;
import com.zsmall.product.entity.domain.bo.productSkuPrice.ApplicableProductSkuRuleBo;
import com.zsmall.product.entity.domain.bo.productSkuPrice.ProductSkuPriceBo;
import com.zsmall.product.entity.domain.bo.productSkuPrice.ProductSkuPriceRuleBo;
import com.zsmall.product.entity.domain.vo.product.ApplicableProductSkuVo;
import com.zsmall.product.entity.domain.vo.productSkuPrice.ProductSkuPriceRuleItemVo;
import com.zsmall.product.entity.domain.vo.productSkuPrice.ProductSkuPriceRuleVo;
import com.zsmall.system.entity.domain.SiteCountryCurrency;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 商品sku价格计算公式Service接口
 *
 * <AUTHOR>
 * @date 2023-05-22
 */
public interface ProductSkuPriceRuleService {

    /**
     * 查询商品sku价格计算公式
     */
    ProductSkuPriceRuleVo queryById(Long id);

    /**
     * 查询商品sku价格计算公式列表
     */
    TableDataInfo<ProductSkuPriceRuleVo> queryPageList(ProductSkuPriceRuleBo bo, PageQuery pageQuery);

    /**
     * 查询商品sku价格计算公式列表
     */
    List<ProductSkuPriceRuleVo> queryList(ProductSkuPriceRuleBo bo);

    List<ProductSkuPriceRuleVo> queryListByIds(List<Long> ids);

    /**
     * 新增商品sku价格计算公式
     */
    Boolean insertByBo(ProductSkuPriceRuleBo bo);

    /**
     * 修改商品sku价格计算公式
     */
    Boolean updateByBo(ProductSkuPriceRuleBo bo);

    /**
     * 校验并批量删除商品sku价格计算公式信息
     */
    R<Void> deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 适用值校验
     *
     * @param bo
     * @return
     */
    R<Void> checkApplicableValue(ProductSkuPriceRuleBo bo);

    /**
     * 价格公式及使用产品保存
     *
     * @param bo
     * @return
     * @throws Exception
     */
    R<Void> savePriceRule(ProductSkuPriceRuleBo bo) throws RStatusCodeException;

    /**
     * 翻页获取适用产品（适用产品设置）
     * @param apiRequest
     * @return
     */
    TableDataInfo<ApplicableProductSkuVo> getApplicableProductSkuPage(ProductSkuPriceRuleBo apiRequest, PageQuery pageQuery) throws RStatusCodeException;

    /**
     * 通过公式ruleCode获取适用产品（翻页）
     * @param apiRequest
     * @return
     */
    TableDataInfo<ApplicableProductSkuVo> getApplicableProductSkuByRuleCodePage(ApplicableProductSkuRuleBo apiRequest, PageQuery pageQuery) throws RStatusCodeException;

    /**
     * 导出适用产品
     * @param apiRequest
     * @param response
     * @return
     */
//    R<Void> exportApplicableProductSku(ApplicableProductSkuRuleBo apiRequest, HttpServletResponse response);

    /**
     * 删除适用产品绑定
     * @param apiRequest
     * @return
     */
    R<Void> deleteApplicableProductSku(ApplicableProductSkuRuleBo apiRequest);

    /**
     * 根据ruleCode获取定价公式
     *
     * @param ruleCode
     * @return
     */
    ProductSkuPriceRule getByRuleCode(String ruleCode);

    /**
     * 根据商品价格计算提价后的价格（单个商品价格实体）
     *
     * @param priceEntity
     * @return
     */
    ProductSkuPrice calculatePricesByPriceEntity(ProductSkuPriceRuleBo priceEntity);

    /**
     * 根据设置公式计算商品自提价和代发价价格(集合)
     *
     * @param ruleCode
     * @param oldPrices 商品价格集合
     * @return 商品自提价和代发价价格 集合
     */
    List<ProductSkuPrice> calculateProductSkuPricesByRuleCode(String ruleCode, List<ProductSkuPrice> oldPrices);

    /**
     * 根据价格公式编码和商品费用计算商品自提价和代发价
     *
     * @param ruleCode        价格公式编码
     * @param unitPrice       原价
     * @param operationFee     操作费
     * @param finalDeliveryFee 远程配送费
     * @return
     */
    ProductSkuPrice calculateByRule(String ruleCode, BigDecimal unitPrice, BigDecimal operationFee, BigDecimal finalDeliveryFee);

    /**
     * 计算自提价和代发价
     *
     * @param itemList        单个计算公式的明细数据集合
     * @param unitPrice
     * @param operationFee
     * @param finalDeliveryFee
     * @return
     */
    ProductSkuPrice calculate(List<ProductSkuPriceRuleItemVo> itemList, BigDecimal unitPrice, BigDecimal operationFee, BigDecimal finalDeliveryFee);

    /**
     * 根据价格查询查询并计算平台价格
     *
     * @param priceEntity
     * @return
     */
    ProductSkuPrice queryByPriceRange(ProductSkuPriceBo priceEntity);


    /**
     * 根据公式类型和适用值获取符合的公式数据
     *
     * @param type
     * @param applicableValues
     * @return
     */
    List<ProductSkuPriceRuleVo> getListByTypeAndValue(Integer type, JSONArray applicableValues, String ruleCode);

    /**
     * 根据规则类型获取符合的公式数据
     *
     * @param type
     * @param ruleCode
     * @return
     */
    List<ProductSkuPriceRuleVo> getListByType(Integer type, String ruleCode);

    /**
     * 根据ruleCode判断基础公式是否存在
     *
     * @param ruleCode
     * @return
     */
    ProductSkuPriceRuleVo getBasicRuleByRuleCode(String ruleCode);

    /**
     * 匹配公式
     *
     * @param itemNo
     * @param unitPrice
     * @param operationFee
     * @param finalDeliveryFee
     * @param isMatchBase      true:只匹配基础公式 false:支持优先匹配规则
     * @return
     */
    ProductSkuPrice matchRule(String itemNo, BigDecimal unitPrice, BigDecimal operationFee, BigDecimal finalDeliveryFee, Boolean isMatchBase) throws RStatusCodeException;



    ProductSkuPrice matchRuleAndSite(String itemNo, BigDecimal unitPrice, BigDecimal operationFee,
                                     BigDecimal finalDeliveryFee, Boolean isMatchBase,
                                     Map<String, SiteCountryCurrency> siteCountryCurrencyMap,
                                     String countryCode) throws RStatusCodeException;


    /**
     * 匹配公式
     * @param productSkuPrice
     * @param isMatchBase
     * @return 匹配的定价公式id
     * @throws RStatusCodeException
     */
    Long matchRule(ProductSkuPrice productSkuPrice, Boolean isMatchBase) throws RStatusCodeException;


    /**
     * 绑定公式
     *
     * @param productSkuId
     * @param productSkuPriceId
     * @param ruleId
     * @return
     */
    Boolean bindRule(Long productSkuId, String productSkuCode, Long productSkuPriceId, Long ruleId);

    /**
     * 绑定公式
     * @param productSkuPrice
     * @param ruleId
     * @return
     */
    Boolean bindRule(ProductSkuPrice productSkuPrice, Long ruleId);


}
