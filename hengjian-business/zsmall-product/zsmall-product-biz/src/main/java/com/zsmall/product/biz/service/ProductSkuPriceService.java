package com.zsmall.product.biz.service;

import cn.hutool.json.JSONObject;
import com.hengjian.common.core.domain.R;
import com.hengjian.common.mybatis.core.page.PageQuery;
import com.zsmall.product.entity.domain.ProductSkuPrice;
import com.zsmall.product.entity.domain.bo.product.ProductPriceBo;
import com.zsmall.product.entity.domain.bo.siteBo.SiteBo;
import com.zsmall.product.entity.domain.bo.siteBo.SitePriceCleanBo;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 商品SKU定价Service接口
 *
 * <AUTHOR> Li
 * @date 2023-05-22
 */
public interface ProductSkuPriceService {


    /**
     * 上传商品价格更新Excel
     * @param file
     * @return
     */
    R<JSONObject> uploadProductPriceUpdate(MultipartFile file);

    /**
     * 功能描述：按代码获取产品 SKU 价格
     *
     * @param productSkuCode 产品 SKU 代码
     * @param siteId
     * @return {@link ProductSkuPrice }
     * <AUTHOR>
     * @date 2024/01/11
     */
    ProductSkuPrice getProductSkuPriceByCode(String productSkuCode, Long siteId);

    ProductSkuPrice getProductSkuPriceByCodeAndCountryCode(String productSkuCode,String countryCode);

    List<SiteBo> getSitePrice();

    Boolean initializePricesAndSites(List<SitePriceCleanBo> sitePriceCleanBos);

    /**
     * 功能描述：导出异步
     *
     * @param bo        bo
     * @param pageQuery 页面查询
     * @param response  响应
     * <AUTHOR>
     * @date 2025/01/06
     */
    void exportAsync(ProductPriceBo bo, PageQuery pageQuery, HttpServletResponse response);

    void exportNotAsync(ProductPriceBo bo, PageQuery pageQuery, HttpServletResponse response);
}
