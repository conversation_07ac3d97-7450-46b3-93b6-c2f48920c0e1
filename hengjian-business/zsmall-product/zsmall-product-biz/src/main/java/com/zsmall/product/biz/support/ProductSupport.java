package com.zsmall.product.biz.support;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.hengjian.common.core.enums.TenantType;
import com.hengjian.common.core.utils.MapstructUtils;
import com.hengjian.common.core.utils.StringUtils;
import com.hengjian.common.log.annotation.InMethodLog;
import com.hengjian.extend.utils.SystemEventUtils;
import com.hengjian.system.domain.dto.NoticePublishDto;
import com.zsmall.bma.open.member.service.RuleLevelProductPriceV2Service;
import com.zsmall.common.enums.BusinessParameterType;
import com.zsmall.common.enums.MyBatisTaskStateType;
import com.zsmall.common.enums.TaskStateEnum;
import com.zsmall.common.enums.common.ChannelTypeEnum;
import com.zsmall.common.enums.order.OrderExceptionEnum;
import com.zsmall.common.enums.order.OrderSourceEnum;
import com.zsmall.common.enums.order.OrderStatusEnum;
import com.zsmall.common.enums.product.AttributeTypeEnum;
import com.zsmall.common.enums.product.ProductReviewTypeEnum;
import com.zsmall.common.enums.product.ProductVerifyStateEnum;
import com.zsmall.common.enums.product.StockManagerEnum;
import com.zsmall.common.service.BusinessParameterService;
import com.zsmall.order.entity.domain.OrderItem;
import com.zsmall.order.entity.domain.Orders;
import com.zsmall.order.entity.iservice.IOrderItemService;
import com.zsmall.order.entity.iservice.IOrdersService;
import com.zsmall.product.biz.service.IProductCategoryRelationService;
import com.zsmall.product.entity.domain.*;
import com.zsmall.product.entity.domain.bo.product.ProductImportBo;
import com.zsmall.product.entity.domain.bo.productSku.ProductPriceImportBo;
import com.zsmall.product.entity.domain.bo.productSku.ProductSkuImportBo;
import com.zsmall.product.entity.domain.dto.productSkuPrice.ProductPriceChangeDto;
import com.zsmall.product.entity.iservice.*;
import com.zsmall.warehouse.entity.domain.event.ThirdWarehouseEvent;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/6/28 11:31
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class ProductSupport {

    private final BusinessParameterService businessParameterService;
    private final ITaskSkuPriceChangeService iTaskSkuPriceChangeService;
    private final ITaskStockSyncService iTaskStockSyncService;
    private final IProductMappingService iProductMappingService;
    private final IProductService iProductService;
    private final IProductSkuService iProductSkuService;
    private final IProductAttributeService iProductAttributeService;
    private final IProductSkuDetailService iProductSkuDetailService;
    private final IProductSkuPriceService iProductSkuPriceService;
    private final IProductSkuStockService iProductSkuStockService;
    private final IProductSkuAttributeService iProductSkuAttributeService;
    private final IProductSkuAttachmentService iProductSkuAttachmentService;
    private final IProductCategoryRelationService iProductCategoryRelationService;
    private final IOrdersService iOrdersService;
    private final IOrderItemService iOrderItemService;


    private final RuleLevelProductPriceV2Service iRuleLevelProductPriceService;
    private final EsProductSupport esProductSupport;

    /**
     * 保存商品导入数据
     * @param tenantId
     * @param productImportBoList
     */
    @Transactional(rollbackFor = { Exception.class, RuntimeException.class })
    public void saveProductImport(String tenantId, Collection<ProductImportBo> productImportBoList) {
        if (CollUtil.isNotEmpty(productImportBoList)) {
            for (ProductImportBo productImportBo : productImportBoList) {
                Product product = MapstructUtils.convert(productImportBo, Product.class);
                product.setTenantId(tenantId);
                iProductService.save(product);
                Long productId = product.getId();

                List<ProductCategoryRelation> categoryRelationList = productImportBo.getCategoryRelationList();
                if (CollUtil.isNotEmpty(categoryRelationList)) {
                    categoryRelationList.forEach(item -> item.setProductId(productId));
                    iProductCategoryRelationService.insertBatch(categoryRelationList);
                }

                List<ProductAttribute> productAttributeList = productImportBo.getProductAttributeList();
                if (CollUtil.isNotEmpty(productAttributeList)) {
                    productAttributeList.forEach(item -> item.setProductId(productId));
                    iProductAttributeService.saveBatch(productAttributeList);
                }

                List<ProductAttribute> optionalSpecList = productImportBo.getOptionalSpecList();
                if (CollUtil.isNotEmpty(optionalSpecList)) {
                    optionalSpecList.forEach(item -> item.setProductId(productId));
                    iProductAttributeService.saveBatch(optionalSpecList);
                }

                List<ProductSkuImportBo> productSkuVoList = productImportBo.getProductSkuVoList();
                for (ProductSkuImportBo productSkuImportBo : productSkuVoList) {
                    ProductSku productSku = MapstructUtils.convert(productSkuImportBo, ProductSku.class);
                    productSku.setProductId(productId);
                    productSku.setTenantId(tenantId);
                    iProductSkuService.save(productSku);
                    Long productSkuId = productSku.getId();
                    StockManagerEnum stockManager = productSku.getStockManager();
                    String productSkuCode = productSku.getProductSkuCode();

                    ProductSkuDetail skuDetail = productSkuImportBo.getSkuDetail();
                    ProductSkuPrice skuPrice = productSkuImportBo.getSkuPrice();
                    skuDetail.setProductSkuId(productSkuId);
                    skuPrice.setProductSkuId(productSkuId);
                    skuPrice.setProductId(productId);
                    List<ProductSkuStock> skuStockList = productSkuImportBo.getSkuStockList();
                    skuStockList.forEach(item -> item.setTenantId(tenantId));
                    List<ProductSkuAttribute> skuAttributeList = productSkuImportBo.getSkuAttributeList();
                    skuAttributeList.forEach(item -> item.setProductSkuId(productSkuId));
                    List<ProductSkuAttachment> skuAttachmentList = productSkuImportBo.getSkuAttachmentList();
                    skuAttachmentList.forEach(item -> item.setProductSkuId(productSkuId));

                    iProductSkuDetailService.save(skuDetail);
                    iProductSkuPriceService.save(skuPrice);
                    iProductSkuStockService.saveBatch(skuStockList);
                    iProductSkuAttributeService.saveBatch(skuAttributeList);
                    iProductSkuAttachmentService.saveBatch(skuAttachmentList);



                    // 非自有仓库需要查询第三方库存
                    if (!StockManagerEnum.OwnWarehouse.equals(stockManager)) {
                        ThirdWarehouseEvent.queryStock(stockManager, productSkuCode);
                    }
                }
            }
        }
    }
    @Transactional(rollbackFor = { Exception.class, RuntimeException.class })
    @Deprecated
    public void saveProductImportForPrice(String tenantId, Collection<ProductImportBo> productImportBoList) {
        if (CollUtil.isNotEmpty(productImportBoList)) {
            for (ProductImportBo productImportBo : productImportBoList) {
                boolean skipRestOfOuterLoop = false;
                List<ProductSkuImportBo> productSkuVoList = productImportBo.getProductSkuVoList();
                for (ProductSkuImportBo productSkuImportBo : productSkuVoList){
                    List<RuleLevelProductPrice> ruleLevelProductPricesForUpdate = productSkuImportBo.getRuleLevelProductPricesForUpdate();
                    List<RuleLevelProductPrice> ruleLevelProductPrices = productSkuImportBo.getRuleLevelProductPrices();
                    if(CollUtil.isNotEmpty(ruleLevelProductPricesForUpdate)){
                        skipRestOfOuterLoop = true;
                        iRuleLevelProductPriceService.updateBatchById(ruleLevelProductPricesForUpdate);
                        // 1. 商品已经录入,新增会员价格 2. 商品已经录入,更新会员价格 3. 商品已经录入,部分修改,部分新增价格 4.
                        // 存在部分改价业务场景,需要更新价格
                        if(CollUtil.isNotEmpty(ruleLevelProductPrices)){
                            // 拿出ruleLevelProductPricesForUpdate 里面的所有dictCode
//                          废弃
//                            List<Long> levelIds = ruleLevelProductPricesForUpdate.stream().map(RuleLevelProductPrice::getLevelId).collect(Collectors.toList());
//                            ruleLevelProductPrices.removeIf(item -> levelIds.contains(item.getLevelId()));
                            // 移除ruleLevelProductPrices中 levelId 在levelIds 内的元素 防止重复添加

                            ruleLevelProductPrices.forEach(item->{

                                item.setProductId(item.getProductId());
                                item.setProductSkuId(item.getProductSkuId());
                                item.setPlatformOperationFee(item.getOriginalOperationFee());
                                item.setPlatformUnitPrice(item.getOriginalUnitPrice());
                                item.setPlatformDropShippingPrice(item.getOriginalDropShippingPrice());
                            });
                            iRuleLevelProductPriceService.saveBatch(ruleLevelProductPrices);
                        }
                    }



                }
                if(skipRestOfOuterLoop){
                   continue;
                }

                Product product = MapstructUtils.convert(productImportBo, Product.class);
                product.setTenantId(tenantId);
                // 如果不存在sku,进行产品的保存,如果存在sku,上面处理了更新价格的场景,还缺少一个 sku存在,新增价格的场景
                Boolean isExists = productImportBo.getIsExists();
                if(!isExists){
                    iProductService.save(product);
                    Long productId = product.getId();

                    List<ProductCategoryRelation> categoryRelationList = productImportBo.getCategoryRelationList();
                    if (CollUtil.isNotEmpty(categoryRelationList)) {
                        categoryRelationList.forEach(item -> item.setProductId(productId));
                        iProductCategoryRelationService.insertBatch(categoryRelationList);
                    }

                    List<ProductAttribute> productAttributeList = productImportBo.getProductAttributeList();
                    if (CollUtil.isNotEmpty(productAttributeList)) {
                        productAttributeList.forEach(item -> item.setProductId(productId));
                        iProductAttributeService.saveBatch(productAttributeList);
                    }

                    List<ProductAttribute> optionalSpecList = productImportBo.getOptionalSpecList();
                    if (CollUtil.isNotEmpty(optionalSpecList)) {
                        optionalSpecList.forEach(item -> item.setProductId(productId));
                        iProductAttributeService.saveBatch(optionalSpecList);
                    }
                    for (ProductSkuImportBo productSkuImportBo : productSkuVoList) {
                        ProductSku productSku = MapstructUtils.convert(productSkuImportBo, ProductSku.class);
                        List<RuleLevelProductPrice> ruleLevelProductPrices = productSkuImportBo.getRuleLevelProductPrices();

                        productSku.setProductId(productId);
                        productSku.setTenantId(tenantId);
                        // 做过更新的 不再存储
                        if(!productSkuImportBo.isUpdated()){
                            iProductSkuService.save(productSku);

                            Long productSkuId = productSku.getId();


                            StockManagerEnum stockManager = productSku.getStockManager();
                            String productSkuCode = productSku.getProductSkuCode();

                            ProductSkuDetail skuDetail = productSkuImportBo.getSkuDetail();
                            ProductSkuPrice skuPrice = productSkuImportBo.getSkuPrice();
                            skuDetail.setProductSkuId(productSkuId);
                            skuPrice.setProductSkuId(productSkuId);

                            List<ProductSkuStock> skuStockList = productSkuImportBo.getSkuStockList();
                            skuStockList.forEach(item -> item.setTenantId(tenantId));
                            List<ProductSkuAttribute> skuAttributeList = productSkuImportBo.getSkuAttributeList();
                            skuAttributeList.forEach(item -> item.setProductSkuId(productSkuId));
                            List<ProductSkuAttachment> skuAttachmentList = productSkuImportBo.getSkuAttachmentList();
                            skuAttachmentList.forEach(item -> item.setProductSkuId(productSkuId));
                            // 如果 等级不存在的,进行新增
                            ruleLevelProductPrices.forEach(item->{
                                item.setProductId(productId);
                                item.setProductSkuId(productSkuId);
                                item.setPlatformOperationFee(item.getOriginalOperationFee());
                                item.setPlatformUnitPrice(item.getOriginalUnitPrice());
                                item.setPlatformDropShippingPrice(item.getOriginalDropShippingPrice());
                            });
                            iRuleLevelProductPriceService.saveBatch(ruleLevelProductPrices);
                            iProductSkuDetailService.save(skuDetail);
                            iProductSkuPriceService.save(skuPrice);
                            iProductSkuStockService.saveBatch(skuStockList);
                            iProductSkuAttributeService.saveBatch(skuAttributeList);
                            iProductSkuAttachmentService.saveBatch(skuAttachmentList);

                            // 非自有仓库需要查询第三方库存
                            if (!StockManagerEnum.OwnWarehouse.equals(stockManager)) {
                                ThirdWarehouseEvent.queryStock(stockManager, productSkuCode);
                            }
                        }

                    }
                }
                if(isExists){

                    for (ProductSkuImportBo productSkuImportBo : productSkuVoList) {
                        ProductSku productSku = MapstructUtils.convert(productSkuImportBo, ProductSku.class);
                        List<RuleLevelProductPrice> ruleLevelProductPrices = productSkuImportBo.getRuleLevelProductPrices();
                        // 做过更新的 不再存储
                        StockManagerEnum stockManager = productSku.getStockManager();
                        String productSkuCode = productSku.getProductSkuCode();
                        // 如果 等级不存在的,进行新增
                        ruleLevelProductPrices.forEach(item->{
                            item.setPlatformOperationFee(item.getOriginalOperationFee());
                            item.setPlatformUnitPrice(item.getOriginalUnitPrice());
                            item.setPlatformDropShippingPrice(item.getOriginalDropShippingPrice());
                        });
                        iRuleLevelProductPriceService.saveBatch(ruleLevelProductPrices);
                        // 非自有仓库需要查询第三方库存
                        if (!StockManagerEnum.OwnWarehouse.equals(stockManager)) {
                            ThirdWarehouseEvent.queryStock(stockManager, productSkuCode);
                        }

                    }
                }


            }
        }
    }
    @Transactional(rollbackFor = { Exception.class, RuntimeException.class })
    public void saveProductImportForPriceV2(String tenantId, Collection<ProductPriceImportBo> productImportBoList) {
        if (CollUtil.isNotEmpty(productImportBoList)) {
            // 盘查是否缺少了 item.setProductId(productId); item.setProductSkuId(productSkuId);
            List<RuleLevelProductPrice> allRuleLevelProductPrices = productImportBoList.stream()
                                                                                       .flatMap(bo -> Optional.ofNullable(bo.getRuleLevelProductPrices()).orElse(Collections.emptyList()).stream())
                                                                                       .collect(Collectors.toList());

            List<RuleLevelProductPrice> allRuleLevelProductPricesForUpdate = productImportBoList.stream()
                                                                                       .flatMap(bo -> Optional.ofNullable(bo.getRuleLevelProductPricesForUpdate()).orElse(Collections.emptyList()).stream())
                                                                                       .collect(Collectors.toList());
            if (CollUtil.isNotEmpty(allRuleLevelProductPrices)){
                iRuleLevelProductPriceService.saveBatch(allRuleLevelProductPrices);
            }

            if(CollUtil.isNotEmpty(allRuleLevelProductPricesForUpdate)){
                iRuleLevelProductPriceService.updateBatchById(allRuleLevelProductPricesForUpdate);
            }


        }
    }
    /**
     * 根据条件判断，生成价格变更，定时任务
     * 生成任务价格变更记录,并不影响es内的价格
     * @param effectiveType
     * @param effectiveDate
     * @param priceRecords
     */
    @InMethodLog(value = "根据条件判断生成价格变更，定时任务")
    public void generateTaskSkuPriceChange(String effectiveType, Date effectiveDate, List<ProductReviewRecord> priceRecords) {
        if (CollUtil.isNotEmpty(priceRecords)) {
            String effectiveDateString = "";
            String taskState = MyBatisTaskStateType.Todo.name();

            if (StrUtil.equals(effectiveType, "NOW")) {
                effectiveDateString = DateUtil.format(new Date(), "yyyy-MM-dd");
                taskState = MyBatisTaskStateType.Finished.name();
            } else {
                if (effectiveDate != null) {
                    effectiveDateString = DateUtil.format(effectiveDate, "yyyy-MM-dd");
                } else {
                    Integer days = businessParameterService.getValueFromInteger(BusinessParameterType.PRICE_CHANGE_EFFECTIVE_DAYS);
                    Date newExecuteDate = DateUtil.offsetDay(new Date(), days);
                    effectiveDateString = DateUtil.format(newExecuteDate, "yyyy-MM-dd");
                }
            }
            List<TaskSkuPriceChange> taskSkuPriceChanges = new ArrayList<>();
            // 指定生效日期的价格变更，需要生成定时任务
            for (ProductReviewRecord reviewRecord : priceRecords) {
                ProductReviewTypeEnum reviewType = reviewRecord.getReviewType();
                ProductVerifyStateEnum reviewState = reviewRecord.getReviewState();

                if (ProductVerifyStateEnum.Accepted.equals(reviewState) && !ProductReviewTypeEnum.NewProduct.equals(reviewType)) {
                    TaskSkuPriceChange newChangeEntity = new TaskSkuPriceChange();
                    newChangeEntity.setProductCode(reviewRecord.getProductCode());
                    newChangeEntity.setReviewRecordId(reviewRecord.getId());
                    newChangeEntity.setTaskState(taskState);
                    newChangeEntity.setExecuteDate(effectiveDateString);
                    taskSkuPriceChanges.add(newChangeEntity);
                }
            }
            if(CollUtil.isNotEmpty(taskSkuPriceChanges)) {
                iTaskSkuPriceChangeService.saveBatch(taskSkuPriceChanges);
            }
        }
    }

    /**
     * 根据条件判断，生成价格变更，定时任务
     * @param effectiveType
     * @param executeDate
     * @param reviewRecord
     */
    @InMethodLog(value = "根据条件判断生成价格变更，定时任务")
    public void generateTaskSkuPriceChange(String effectiveType, String executeDate, ProductReviewRecord reviewRecord) {
        ProductReviewTypeEnum reviewType = reviewRecord.getReviewType();
        ProductVerifyStateEnum reviewState = reviewRecord.getReviewState();
        if (ProductVerifyStateEnum.Accepted.equals(reviewState) && !ProductReviewTypeEnum.NewProduct.equals(reviewType)) {
            TaskSkuPriceChange newChangeEntity = new TaskSkuPriceChange();

            newChangeEntity.setProductCode(reviewRecord.getProductCode());
            newChangeEntity.setReviewRecordId(reviewRecord.getId());
            newChangeEntity.setTaskState(MyBatisTaskStateType.Todo.name());

            if (StrUtil.equals(effectiveType, "NOW")) {
                String format = DateUtil.format(new Date(), "yyyy-MM-dd");
                newChangeEntity.setExecuteDate(format);
                newChangeEntity.setTaskState(MyBatisTaskStateType.Finished.name());
            } else {
                newChangeEntity.setTaskState(MyBatisTaskStateType.Todo.name());
                // 传入执行时间时直接去用，不传时取出参数表中的天数，重新计算一个新的生效时间
                if (executeDate != null) {
                    newChangeEntity.setExecuteDate(executeDate);
                } else {
                    Integer days = businessParameterService.getValueFromInteger(BusinessParameterType.PRICE_CHANGE_EFFECTIVE_DAYS);
                    Date newExecuteDate = DateUtil.offsetDay(new Date(), days);
                    newChangeEntity.setExecuteDate(DateUtil.format(newExecuteDate, "yyyy-MM-dd"));
                }
            }
            iTaskSkuPriceChangeService.save(newChangeEntity);
        }
    }

    /**
     * 获取不同渠道价格
     * 注：Wayfair都是自提价，其他平台都是代发价
     * @param channelType
     * @param productSkuPrice
     * @return
     */
    @InMethodLog(value = "获取不同渠道价格")
    public BigDecimal getProductSkuPrice(ChannelTypeEnum channelType, ProductSkuPrice productSkuPrice) {
        return ChannelTypeEnum.Wayfair.equals(channelType) ? productSkuPrice.getPlatformPickUpPrice() : productSkuPrice.getPlatformDropShippingPrice();
    }

    @InMethodLog(value = "设置商品属性")
    public ProductAttribute setProductAttribute(Long id, Long productId, String key, Object value, AttributeTypeEnum attributeType, Long sourceId, Integer sort) {
        ProductAttribute productAttribute = new ProductAttribute();
        if (id != null) {
            productAttribute.setId(id);
        }

        productAttribute.setProductId(productId);
        productAttribute.setAttributeName(key);
        productAttribute.addAttributeValue(value);
        productAttribute.setAttributeType(attributeType);
        productAttribute.setAttributeSourceId(sourceId);
        productAttribute.setAttributeSort(sort);
        return productAttribute;
    }

    @InMethodLog(value = "创建库存同步任务(同步库存到各个分销商渠道)")
    public void createSyncTask(String productSkuCode) {
        ProductSku productSku = iProductSkuService.queryByProductSkuCode(productSkuCode);
        if (productSku != null) {
            Long skuId = productSku.getId();
            // 创建库存同步任务，准备同步库存到各个分销商渠道
            Boolean existsed = iTaskStockSyncService.existByStateProductSkuId(TaskStateEnum.Todo, skuId);
            // 不存在该SKU的库存同步任务时才创建同步任务
            if (!existsed) {
                TaskStockSync taskStockSync = new TaskStockSync();
                taskStockSync.setTaskState(TaskStateEnum.Todo);
                taskStockSync.setProductSkuId(skuId);
                iTaskStockSyncService.save(taskStockSync);
            }
            esProductSupport.productSkuUpload(skuId);
        }
    }

    @InMethodLog(value = "创建库存同步任务(同步库存到各个分销商渠道)")
    public void createSyncTask(Long skuId) {
        // 创建库存同步任务，准备同步库存到各个分销商渠道
        Boolean existsed = iTaskStockSyncService.existByStateProductSkuId(TaskStateEnum.Todo, skuId);
        // 不存在该SKU的库存同步任务时才创建同步任务
        if (!existsed) {
            TaskStockSync taskStockSync = new TaskStockSync();
            taskStockSync.setTaskState(TaskStateEnum.Todo);
            taskStockSync.setProductSkuId(skuId);
            iTaskStockSyncService.save(taskStockSync);
        }
        esProductSupport.productSkuUpload(skuId);
    }

    @InMethodLog(value = "创建库存同步任务(同步库存到各个分销商渠道)")
    public void createSyncTask(List<Long> skuIdList) {
        // 创建库存同步任务，准备同步库存到各个分销商渠道
        // Boolean existsed = iTaskStockSyncService.existByStateProductSkuId(TaskStateEnum.Todo, skuId);
        // 不存在该SKU的库存同步任务时才创建同步任务
        if (true) {
            TaskStockSync taskStockSync = new TaskStockSync();
            taskStockSync.setTaskState(TaskStateEnum.Todo);
            taskStockSync.setProductSkuIdList(skuIdList);
            iTaskStockSyncService.save(taskStockSync);
        }
        esProductSupport.productSkuUpload(ArrayUtil.toArray(skuIdList, Long.class));
    }

    /**
     * 封装数据，准备推送商品价格变动
     * @param productPriceChangeList
     */
    public void toPushProductPriceChangeEvent(List<ProductPriceChangeDto> productPriceChangeList) {
        log.info("价格变更List，搜集后统一发送通知 productPriceChangeList = {}", JSONUtil.toJsonStr(productPriceChangeList));

        if (CollUtil.isNotEmpty(productPriceChangeList)) {
            DateTime nowDateTime = DateTime.now();
            String nowDateStr = DateUtil.format(nowDateTime, "yyyy-MM-dd");
            String now = DateUtil.format(nowDateTime, "yyyy-MM-dd HH:mm:ss");
            String template = businessParameterService.getValueFromString(BusinessParameterType.PRICE_CHANGE_TEMPLATE);

            List<NoticePublishDto> noticePublishDtos = new ArrayList<>();
            productPriceChangeList.forEach(changeDto -> {
                //1、获取商品映射数据 2、修改映射数据的最总价格
                List<ProductMapping> productMappings = iProductMappingService.queryListByProductSkuCode(changeDto.getProductSkuCode());
                List<String> collect = productMappings.stream().map(ProductMapping::getTenantId).collect(Collectors.toList());
                changeDto.setTenantIdList(collect);

                String effectiveTime = changeDto.getEffectiveTime();
                if (StrUtil.isBlank(effectiveTime)) {
                    effectiveTime = nowDateStr;
                }

                String content = StrUtil.format(template, now,
                    changeDto.getProductSkuCode(), changeDto.getBefore_pickUpPrice(), changeDto.getBefore_dropShippingPrice(),
                    changeDto.getAfter_pickUpPrice(), changeDto.getAfter_dropShippingPrice(), effectiveTime);

                List<String> tenantTypes = new ArrayList<>();
                tenantTypes.add(TenantType.Distributor.name());
                tenantTypes.add(TenantType.Supplier.name());

                NoticePublishDto noticePublishDto = new NoticePublishDto("Price Change Notice", content, collect, tenantTypes);
                noticePublishDtos.add(noticePublishDto);
            });

            if (CollUtil.isNotEmpty(noticePublishDtos)) {
                // 创建消息公告 -推送es商城的,目前不知道是做什么用的, QueueConstants.QueueName.NOTICE  RedisConsumerListener
                SystemEventUtils.publishNotices(noticePublishDtos);
            }
        }
    }

    /**
     * 根据productSkuCode 更新商品SPU表 库存推送时间
     * @param productSkuCode  Sku唯一编号
     */
    public void updateProductInventoryPushTime(String productSkuCode) {
        iProductService.updateProductInventoryPushTime(productSkuCode);
    }



    /**
     * 根据procuctSkuCode修改订单异常状态
     *
     * @param productSkuCodeOrderList 产品SKU码列表
     * @param orderExceptionEnum 异常状态
     */
    @InMethodLog("根据productSkuCode修改订单异常状态")
    public void orderExceptionDispose(@NonNull List<String> productSkuCodeOrderList,OrderExceptionEnum orderExceptionEnum) {
        List<OrderItem> orderItemList = iOrderItemService.queryOrderItemByProductSkuCodeAndOrderState(productSkuCodeOrderList, OrderStatusEnum.pending.getDistriButionStatus());
        if (CollUtil.isNotEmpty(orderItemList)) {
            List<Long> orderIdList = orderItemList.stream().map(OrderItem::getOrderId).distinct()
                                              .collect(Collectors.toList());
            List<Orders> ordersList = iOrdersService.getListByIdListAndOrderSource(orderIdList,OrderSourceEnum.INTERFACE_ORDER,null);
            if (CollUtil.isNotEmpty(ordersList)) {
                for (Orders orders:ordersList) {
                    orders.setExceptionCode(orderExceptionEnum.getValue());
                }
                iOrdersService.batchSaveOrUpdate(ordersList);
            }
        }
    }

    @InMethodLog("根据productSkuCode修改订单异常状态")
    public void orderExceptionDisposeRecover(@NonNull List<String> productSkuCodeOrderList,OrderExceptionEnum orderExceptionEnum) {
        List<OrderItem> orderItemList = iOrderItemService.queryOrderItemByProductSkuCodeAndOrderState(productSkuCodeOrderList, OrderStatusEnum.pending.getDistriButionStatus());
        if (CollUtil.isNotEmpty(orderItemList)) {
            List<Long> orderIdList = orderItemList.stream().map(OrderItem::getOrderId).distinct()
                                                  .collect(Collectors.toList());
            List<Orders> ordersList = iOrdersService.getListByIdListAndOrderSource(orderIdList,OrderSourceEnum.INTERFACE_ORDER,orderExceptionEnum);
            if (CollUtil.isNotEmpty(ordersList)) {
                for (Orders orders : ordersList) {
                    orders.setExceptionCode(OrderExceptionEnum.normal.getValue());
                    if(null != orders.getPayErrorMessage() && StringUtils.isNotEmpty(orders.getPayErrorMessage().toString())){
                        String payErrorMessage = orders.getPayErrorMessage().toString();
                        if(payErrorMessage.contains("商品已下架，无法支付")){
                            orders.setPayErrorMessage(null);
                        }
                    }
                }
                iOrdersService.batchSaveOrUpdate(ordersList);
            }
        }
    }

    @InMethodLog("发货方式变动修改订单的异常状态")
    public void updateOrderExceptionStatusByShippingWay(Long channelId,OrderExceptionEnum orderExceptionEnum,String productSkuCode) {

    }

}
