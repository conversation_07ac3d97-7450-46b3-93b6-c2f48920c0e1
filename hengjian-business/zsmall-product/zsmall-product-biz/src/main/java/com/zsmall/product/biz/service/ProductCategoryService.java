package com.zsmall.product.biz.service;


import com.hengjian.common.core.domain.R;
import com.zsmall.product.entity.domain.bo.category.ProductCategoryBo;
import com.zsmall.product.entity.domain.bo.category.ProductCategoryParamBo;
import com.zsmall.product.entity.domain.vo.category.ProductCategorySearchVo;
import com.zsmall.product.entity.domain.vo.category.ProductCategorySelectVo;
import com.zsmall.product.entity.domain.vo.category.ProductCategoryVo;

import java.util.Collection;
import java.util.List;

/**
 * 商品分类Service接口
 *
 * <AUTHOR> Li
 * @date 2023-05-18
 */
public interface ProductCategoryService {

    /**
     * 查询商品分类
     */
    ProductCategoryVo queryById(Long id);


    /**
     * 查询商品分类列表
     */
    List<ProductCategoryVo> queryList(ProductCategoryParamBo bo);

    /**
     * 查询商品分类列表（提供给下拉选使用）
     * @param parentId
     * @return
     */
    List<ProductCategorySelectVo> queryListForSelect(Long parentId);

    List<ProductCategoryVo> queryListByIds(List<Long> ids);

    /**
     * 新增商品分类
     */
    Boolean insertByBo(ProductCategoryBo bo);

    /**
     * 修改商品分类
     */
    Boolean updateByBo(ProductCategoryBo bo);

    /**
     * 校验并批量删除商品分类信息
     */
    R<Void> deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 根据类目名称模糊搜索全部类目-树结构
     *
     * @param categoryName
     * @return
     */
    List<ProductCategorySearchVo> listForSearch(String categoryName);

    /**
     * 根据类目名称模糊搜索全部类目-拼接结构-部分拼接
     *
     * @param categoryName
     * @return
     */
    List<ProductCategorySearchVo> listForSearchForMontage(String categoryName);

    /**
     * 根据类目名称模糊搜索全部类目-拼接结构-全部拼接
     *
     * @param categoryName
     * @return
     */
    List<ProductCategorySearchVo> listForSearchForAllMontage(String categoryName);
}
