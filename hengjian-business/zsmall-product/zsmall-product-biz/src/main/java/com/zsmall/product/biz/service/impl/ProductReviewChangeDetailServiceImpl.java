package com.zsmall.product.biz.service.impl;

import cn.hutool.core.util.ReflectUtil;
import com.zsmall.product.biz.service.ProductReviewChangeDetailService;
import com.zsmall.product.entity.domain.vo.ProductReviewChangeDetailVo;
import com.zsmall.product.entity.iservice.IProductReviewChangeDetailService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 商品审核变更详情Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-05-31
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class ProductReviewChangeDetailServiceImpl implements ProductReviewChangeDetailService {

    private final IProductReviewChangeDetailService iProductReviewChangeDetailService;

    /**
     * 根据变更详情设置目标对象的相关字段
     *
     * @param targetObject
     * @param changeDetailEntities
     */
    @Override
    public void setChangeToObject(Object targetObject, List<ProductReviewChangeDetailVo> changeDetailEntities) {
        changeDetailEntities.forEach(item -> {
            String fieldName = item.getFieldName();
            String fieldValueBefore = item.getFieldValueBefore();
            String fieldValueAfter = item.getFieldValueAfter();
            ReflectUtil.setFieldValue(targetObject, "before_" + fieldName, fieldValueBefore);
            ReflectUtil.setFieldValue(targetObject, "after_" + fieldName, fieldValueAfter);
        });
    }


}
