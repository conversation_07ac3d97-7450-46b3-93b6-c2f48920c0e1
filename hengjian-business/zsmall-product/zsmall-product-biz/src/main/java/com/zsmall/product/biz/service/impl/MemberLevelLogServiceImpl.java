package com.zsmall.product.biz.service.impl;

import com.hengjian.common.satoken.utils.LoginHelper;
import com.zsmall.common.enums.common.OperationTypeEnum;
import com.zsmall.product.biz.service.MemberLevelLogService;
import com.zsmall.product.entity.domain.member.MemberLevel;
import com.zsmall.product.entity.domain.member.MemberLevelLog;
import com.zsmall.bma.open.member.mapper.MemberLevelMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2024/5/7 17:49
 */
@Slf4j
@RequiredArgsConstructor
@Deprecated
@Service
public class MemberLevelLogServiceImpl implements MemberLevelLogService {
    private final IMemberLevelLogService iMemberLevelLogService;
    private final MemberLevelMapper memberLevelMapper;
    @Override
    public void createAddLog(MemberLevel memberLevel) {
        // 新增日志
        MemberLevelLog memberLevelLog = new MemberLevelLog();
        memberLevelLog.setCreateBy(memberLevel.getCreateBy());
        memberLevelLog.setUpdateBy(memberLevel.getUpdateBy());
        memberLevelLog.setOperationType(String.valueOf(OperationTypeEnum.Add.getValue()));
        memberLevelLog.setLevelId(memberLevel.getId());
        String levelName = memberLevelMapper.getLevelName(memberLevel.getDictCode());
        memberLevelLog.setDestinationStatus(levelName);
        memberLevelLog.setTenantId(memberLevel.getTenantId());
        memberLevelLog.setStatus(String.valueOf(memberLevel.getStatus()));
        iMemberLevelLogService.save(memberLevelLog);
    }

    @Override
    public void createUpdateLog(MemberLevel old, MemberLevel now) {
        String tenantId = LoginHelper.getTenantId();
        MemberLevelLog memberLevelLog = new MemberLevelLog();
        memberLevelLog.setCreateBy(LoginHelper.getUserId());
        memberLevelLog.setUpdateBy(LoginHelper.getUserId());
        memberLevelLog.setOperationType(String.valueOf(OperationTypeEnum.Update.getValue()));
        memberLevelLog.setLevelId(old.getId());
        String levelName = memberLevelMapper.getLevelName(now.getDictCode());
        memberLevelLog.setDestinationStatus(levelName);
        memberLevelLog.setTenantId(tenantId);
        memberLevelLog.setStatus(String.valueOf(now.getStatus()));
        iMemberLevelLogService.save(memberLevelLog);
    }



}
