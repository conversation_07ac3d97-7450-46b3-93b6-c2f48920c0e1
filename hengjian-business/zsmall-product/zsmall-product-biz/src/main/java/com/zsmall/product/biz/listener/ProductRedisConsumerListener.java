package com.zsmall.product.biz.listener;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import com.hengjian.common.core.utils.SpringUtils;
import com.hengjian.common.redis.utils.QueueUtils;
import com.hengjian.stream.mqProducer.constants.QueueConstants;
import com.hengjian.stream.mqProducer.domain.MessageDto;
import com.zsmall.product.biz.support.EsProductSupport;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

@Slf4j
@Component
public class ProductRedisConsumerListener implements ApplicationRunner {
    private static final ThreadPoolTaskExecutor threadPoolTaskExecutor = SpringUtils.getBean("threadPoolTaskExecutor", ThreadPoolTaskExecutor.class);

    @Autowired
    private EsProductSupport esProductSupport;

    /**
     * 消费者 - ElasticSearch商品数据推送
     */
    public void esProductPushConsumer() {
        log.info("初始化订阅esProductPushConsumer");
        // 项目初始化设置一次即可
        QueueUtils.subscribeBlockingQueue(QueueConstants.QueueName.ES_PRODUCT_PUSH, (MessageDto messageDto) -> {
            // 观察接收时间
            log.info("【消费者 - ElasticSearch商品数据推送】通道: {}, 收到数据: {}", QueueConstants.QueueName.ES_PRODUCT_PUSH, JSONUtil.toJsonStr(messageDto));
            // 开始推送商品数据
            try {
                String msgId = messageDto.getMsgId();
                Map<String, Object> data = (Map<String, Object>) messageDto.getData();
                List<String> productCodes = (List<String>) data.get("productCodes");
                List<String> productSkuCodes = (List<String>) data.get("productSkuCodes");

                threadPoolTaskExecutor.execute(() -> {
                    if (CollUtil.isNotEmpty(productCodes)) {
                        esProductSupport.productUploadForConsumer(productCodes);
                    }

                    if (CollUtil.isNotEmpty(productSkuCodes)) {
                        esProductSupport.productSkuUploadForConsumer(productSkuCodes);
                    }
                });
            } catch (Exception e) {
                log.error("【消费者 - ElasticSearch商品数据推送】发生异常：{}", e.getMessage(), e);
            }
        });
    }

    @Override
    public void run(ApplicationArguments args) throws Exception {
        esProductPushConsumer();
        log.info("ProductRedisConsumerListener主题订阅监听器成功");
    }
}
