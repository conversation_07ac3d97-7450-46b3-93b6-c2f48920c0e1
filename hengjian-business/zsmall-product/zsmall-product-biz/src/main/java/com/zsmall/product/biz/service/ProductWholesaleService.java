package com.zsmall.product.biz.service;

import com.hengjian.common.core.domain.R;
import com.hengjian.common.mybatis.core.page.PageQuery;
import com.hengjian.common.mybatis.core.page.TableDataInfo;
import com.zsmall.product.entity.domain.bo.product.ProductQueryBo;
import com.zsmall.product.entity.domain.bo.wholesale.GetWholesaleProductInfoBo;
import com.zsmall.product.entity.domain.vo.product.ProductListVo;
import com.zsmall.product.entity.domain.vo.wholesale.WholesaleProductBo;
import com.zsmall.product.entity.domain.vo.wholesale.WholesaleProductVo;

/**
 * 国外现货批发商品SKU价格Service接口
 *
 * <AUTHOR>
 * @date 2023-05-29
 */
public interface ProductWholesaleService {

    /**
     * 查询商品SPU列表
     */
    TableDataInfo<ProductListVo> queryPageList(ProductQueryBo bo, PageQuery pageQuery);

    /**
     * 保存国外新货商品信息
     *
     * @param apiRequest
     * @return
     */
    R<GetWholesaleProductInfoBo> saveProduct(WholesaleProductBo apiRequest) throws Exception;

    /**
     * 获取国外新货商品信息
     *
     * @param apiRequest
     * @return
     */
    R<WholesaleProductVo> getWholesaleProductInfo(GetWholesaleProductInfoBo apiRequest);


}
