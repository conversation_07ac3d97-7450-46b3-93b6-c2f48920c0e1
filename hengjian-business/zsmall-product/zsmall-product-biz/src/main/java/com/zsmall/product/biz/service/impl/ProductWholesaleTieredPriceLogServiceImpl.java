package com.zsmall.product.biz.service.impl;

import com.zsmall.product.biz.service.ProductWholesaleTieredPriceLogService;
import com.zsmall.product.entity.iservice.IProductWholesaleTieredPriceLogService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * 国外现货批发商品阶梯价日志Service业务层处理
 *
 * <AUTHOR> Li
 * @date 2023-05-29
 */
@RequiredArgsConstructor
@Service
public class ProductWholesaleTieredPriceLogServiceImpl implements ProductWholesaleTieredPriceLogService {

    private final IProductWholesaleTieredPriceLogService iProductWholesaleTieredPriceLogService;


}
