package com.zsmall.product.biz.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.zsmall.common.enums.product.StockManagerEnum;
import com.zsmall.product.biz.service.ProductSkuService;
import com.zsmall.product.biz.support.ProductSupport;
import com.zsmall.product.entity.domain.ProductSku;
import com.zsmall.product.entity.iservice.IProductSkuService;
import com.zsmall.product.entity.iservice.IProductSkuStockService;
import com.zsmall.warehouse.entity.domain.event.ThirdWarehouseEvent;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 商品SKUService业务层处理
 *
 * <AUTHOR> Li
 * @date 2023-05-26
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class ProductSkuServiceImpl implements ProductSkuService {

    private final IProductSkuService iProductSkuService;
    private final IProductSkuStockService iProductSkuStockService;

    private final ProductSupport productSupport;

    /**
     * 统计SKU库存总数
     *
     * @param productSku
     * @return
     */
    @Override
    public Integer querySkuStockTotal(ProductSku productSku) {
        return this.querySkuStockTotal(productSku, false);
    }

    /**
     * 统计SKU库存总数
     *
     * @param productSku
     * @return
     */
    @Override
    public Integer querySkuStockTotal(ProductSku productSku, boolean check) {
        String productSkuCode = productSku.getProductSkuCode();
        Integer quantity;
        if (check) {
            quantity = iProductSkuStockService.sumStockTotal(productSkuCode);
        } else {
            quantity = iProductSkuService.queryStockTotal(productSkuCode);
        }
        return quantity;
    }

    /**
     * 设置SKU库存
     * @param productSku
     */
    @Override
    public void setProductSkuStock(ProductSku productSku) {
        StockManagerEnum stockManager = productSku.getStockManager();
        String productSkuCode = productSku.getProductSkuCode();
        // 暂时使用此版本
        if (!StockManagerEnum.OwnWarehouse.equals(stockManager)) {
            ThirdWarehouseEvent.queryStock(stockManager, productSkuCode);
        }

        Integer stockTotal = iProductSkuStockService.sumStockTotal(productSkuCode);
        log.info("设置SKU库存 商品SKU编号 = {}，最终数量 = {}", productSkuCode, stockTotal);
        productSku.setStockTotal(stockTotal);
        productSupport.createSyncTask(productSku.getId());
    }

    @Override
    public void setProductSkuStockNotSync(ProductSku productSku) {
//        StockManagerEnum stockManager = productSku.getStockManager();
        String productSkuCode = productSku.getProductSkuCode();
        // 暂时使用此版本
//        if (!StockManagerEnum.OwnWarehouse.equals(stockManager)) {
//            ThirdWarehouseEvent.queryStock(stockManager, productSkuCode);
//        }

        Integer stockTotal = iProductSkuStockService.sumStockTotal(productSkuCode);
        log.info("设置SKU库存 商品SKU编号 = {}，最终数量 = {}", productSkuCode, stockTotal);
        productSku.setStockTotal(stockTotal);
        productSupport.createSyncTask(productSku.getId());
    }

    /**
     * 设置SKU库存(批发商品无需同步任务),
     * @param productSku
     */
    @Override
    public void setProductSkuStockWholesale(ProductSku productSku) {
        StockManagerEnum stockManager = productSku.getStockManager();
        String productSkuCode = productSku.getProductSkuCode();

        if (!StockManagerEnum.OwnWarehouse.equals(stockManager)) {
            ThirdWarehouseEvent.queryStock(stockManager, productSkuCode);
        }

        Integer stockTotal = iProductSkuStockService.sumStockTotal(productSkuCode);
        log.info("设置SKU库存 商品SKU编号 = {}，最终数量 = {}", productSkuCode, stockTotal);
        productSku.setStockTotal(stockTotal);
    }

    /**
     * 设置SKU库存（批量）
     * @param productSkuList
     */
    @Override
    public void setProductSkuStock(List<ProductSku> productSkuList) {
        if (CollectionUtils.isNotEmpty(productSkuList)) {
            for (ProductSku productSku : productSkuList) {
                setProductSkuStockNotSync(productSku);
            }
        }
    }

    /**
     * 设置SKU库存(批发商品)
     * @param productSkuList
     */
    @Override
    public void setProductSkuStockWholesale(List<ProductSku> productSkuList) {
        if (CollectionUtils.isNotEmpty(productSkuList)) {
            for (ProductSku productSku : productSkuList) {
                setProductSkuStockWholesale(productSku);
            }
        }
    }

    @Override
    public ProductSku getProductSkuByErpSku(String erpSku) {
        return iProductSkuService.getOne(new LambdaQueryWrapper<ProductSku>().eq(ProductSku::getErpSku, erpSku));
    }

    @Override
    public ProductSku getProductSkuBySellerSku(String sellerSku) {
        return iProductSkuService.getOne(new LambdaQueryWrapper<ProductSku>().eq(ProductSku::getSku,sellerSku).eq(ProductSku::getDelFlag,0));
    }

    @Override
    public ProductSku getProductSkuByItemNo(String itemNo) {
        return iProductSkuService.getOne(new LambdaQueryWrapper<ProductSku>().eq(ProductSku::getProductSkuCode,itemNo).eq(ProductSku::getDelFlag,0));
    }

}
