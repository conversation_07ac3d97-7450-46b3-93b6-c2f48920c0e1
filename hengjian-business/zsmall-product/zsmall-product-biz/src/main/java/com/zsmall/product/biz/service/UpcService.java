package com.zsmall.product.biz.service;


import com.hengjian.common.core.domain.R;
import com.hengjian.common.mybatis.core.page.PageQuery;
import com.hengjian.common.mybatis.core.page.TableDataInfo;
import com.zsmall.product.entity.domain.dto.product.ReqUpcPageBody;
import com.zsmall.product.entity.domain.dto.product.UsageUpcBody;

import javax.servlet.http.HttpServletRequest;

/**
 * Upc接口
 * <AUTHOR>
 * @date 2021/7/8 11:37
 */
public interface UpcService {

  /**
   * UPC文件上传
   * @param request
   * @return
   * @throws Exception
   */
  R<Void> uploadUpc(HttpServletRequest request) throws Exception;

  /**
   * 翻页查询UPC信息
   * @param apiRequest
   * @return
   */
  R<TableDataInfo<UsageUpcBody>> getUpcPage(ReqUpcPageBody apiRequest, PageQuery pageQuery);
}
