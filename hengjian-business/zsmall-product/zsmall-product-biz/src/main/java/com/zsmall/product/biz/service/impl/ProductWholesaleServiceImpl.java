package com.zsmall.product.biz.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hengjian.common.core.domain.R;
import com.hengjian.common.core.enums.TenantType;
import com.hengjian.common.core.exception.RStatusCodeException;
import com.hengjian.common.log.annotation.InMethodLog;
import com.hengjian.common.mybatis.core.page.PageQuery;
import com.hengjian.common.mybatis.core.page.TableDataInfo;
import com.hengjian.common.satoken.utils.LoginHelper;
import com.hengjian.common.tenant.helper.TenantHelper;
import com.zsmall.common.enums.BusinessCodeEnum;
import com.zsmall.common.enums.common.AttachmentTypeEnum;
import com.zsmall.common.enums.common.ChannelTypeEnum;
import com.zsmall.common.enums.common.GlobalStateEnum;
import com.zsmall.common.enums.product.*;
import com.zsmall.common.enums.statuscode.ZSMallStatusCodeEnum;
import com.zsmall.common.enums.wholesale.WholesaleDeliveryType;
import com.zsmall.common.service.BusinessParameterService;
import com.zsmall.order.entity.domain.WholesaleIntentionOrderItem;
import com.zsmall.order.entity.iservice.IWholesaleIntentionOrderItemService;
import com.zsmall.product.biz.service.IProductCategoryRelationService;
import com.zsmall.product.biz.service.ProductReviewRecordService;
import com.zsmall.product.biz.service.ProductWholesaleService;
import com.zsmall.product.biz.support.ProductSupport;
import com.zsmall.product.biz.support.WholesaleSupport;
import com.zsmall.product.entity.domain.*;
import com.zsmall.product.entity.domain.bo.product.ProductAttachmentBo;
import com.zsmall.product.entity.domain.bo.product.ProductIntactInfoUpdateBo;
import com.zsmall.product.entity.domain.bo.product.ProductQueryBo;
import com.zsmall.product.entity.domain.bo.wholesale.GetWholesaleProductInfoBo;
import com.zsmall.product.entity.domain.bo.wholesale.ProductSkuWholesaleBo;
import com.zsmall.product.entity.domain.vo.ProductSkuAttachmentVo;
import com.zsmall.product.entity.domain.vo.ProductSkuStockVo;
import com.zsmall.product.entity.domain.vo.product.ProductIntactInfoVo;
import com.zsmall.product.entity.domain.vo.product.ProductListVo;
import com.zsmall.product.entity.domain.vo.productSku.ProductSkuListVo;
import com.zsmall.product.entity.domain.vo.wholesale.*;
import com.zsmall.product.entity.iservice.*;
import com.zsmall.product.entity.util.ProductCodeGenerator;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 批发商城端-实现类
 *
 * <AUTHOR>
 * @date 2023/2/22
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ProductWholesaleServiceImpl implements ProductWholesaleService {

    private final IProductService iProductService;
    private final IProductAttachmentService iProductAttachmentService;
    private final IProductSkuService iProductSkuService;
    private final IProductSkuDetailService iProductSkuDetailService;
    private final IProductSkuAttachmentService iProductSkuAttachmentService;
    private final IProductWholesaleDetailService iProductWholesaleDetailService;
    private final IProductWholesaleTieredPriceService iProductWholesaleTieredPriceService;
    private final IProductSkuWholesalePriceService iProductSkuWholesalePriceService;
    private final IProductSkuStockService iProductSkuStockService;
    private final IProductCategoryService iProductCategoryService;
    private final IProductCategoryRelationService iProductCategoryRelationService;
    private final IProductReviewRecordService iProductReviewRecordService;
    private final IProductReviewChangeDetailService iProductReviewChangeDetailService;
    private final ProductCodeGenerator productCodeGenerator;
    private final WholesaleSupport wholesaleSupport;
    private final ProductReviewRecordService productReviewRecordService;
    private final IProductAttributeService iProductAttributeService;
    private final IProductSkuAttributeService iProductSkuAttributeService;
    private final ProductSupport productSupport;
    private final IProductGlobalAttributeService iProductGlobalAttributeService;
    private final IWholesaleIntentionOrderItemService iWholesaleIntentionOrderItemService;
    private final BusinessParameterService businessParameterService;


    @InMethodLog(value = "获取批发商品列表")
    @Override
    public TableDataInfo<ProductListVo> queryPageList(ProductQueryBo bo, PageQuery pageQuery) {
        bo.setProductType(ProductTypeEnum.WholesaleProduct.name());
        IPage<Product> productIPage = iProductService.queryPageList(pageQuery.build(), bo);

        List<Product> records = productIPage.getRecords();
        List<ProductListVo> results = new ArrayList<>();
        for (Product product : records) {
            String name = product.getName();
            String productCode = product.getProductCode();
            ShelfStateEnum shelfState = product.getShelfState();
            ProductVerifyStateEnum verifyState = product.getVerifyState();
            SupportedLogisticsEnum supportedLogistics = product.getSupportedLogistics();

            Long productId = product.getId();
            List<ProductSkuListVo> productSkuList;
            if (shelfState.equals(ShelfStateEnum.ForcedOffShelf)) {
                productSkuList = iProductSkuService.queryWholesaleProductSkuListVoByProductId(productId, null);
            } else {
                productSkuList = iProductSkuService.queryWholesaleProductSkuListVoByProductId(productId, ProductVerifyStateEnum.Rejected.name());
            }

            for (ProductSkuListVo sku: productSkuList) {
                ProductSkuWholesalePrice swp = iProductSkuWholesalePriceService.getOneByProductIdAndProductSkuId(productId, sku.getSkuId());
                log.info("ProductSkuWholesalePriceEntity = {}", JSONUtil.toJsonStr(swp));
                if (ObjectUtil.isEmpty(swp)) {
                    sku.setUnitPrice(BigDecimal.ZERO.toString());
                    sku.setPickUpPrice(BigDecimal.ZERO.toString());
                    sku.setDropShippingPrice(BigDecimal.ZERO.toString());
                } else {
                    sku.setUnitPrice(swp.getOriginUnitPrice().toString());
                    sku.setPickUpPrice(swp.getOriginUnitPrice().toString());
                    sku.setDropShippingPrice(swp.getPlatformUnitPrice().toString());
                }
            }

            ProductSkuAttachmentVo productSkuAttachmentVo = iProductSkuAttachmentService.queryFirstImageByProductId(productId);
            ProductListVo productListVo = new ProductListVo();
            productListVo.setProductName(name);
            productListVo.setProductCode(productCode);
            productListVo.setProductType(product.getProductType().name());
            productListVo.setVerifyState(verifyState.name());
            productListVo.setShelfState(shelfState.name());
            productListVo.setSupportedLogistics(ObjectUtil.isNotNull(supportedLogistics) ? supportedLogistics.name() : null);

            if (ProductVerifyStateEnum.Rejected.equals(verifyState)) {
                ProductReviewRecord reviewRecord = iProductReviewRecordService.queryRecordByProductCodeAndReviewType(productCode, ProductVerifyStateEnum.Rejected);
                if (reviewRecord != null) {
                    productListVo.setReviewOpinion(reviewRecord.getReviewOpinion());
                    productListVo.setReviewOpinionOption(reviewRecord.getReviewOpinionOption());
                }
            }

            if (productSkuAttachmentVo != null) {
                String attachmentShowUrl = productSkuAttachmentVo.getAttachmentShowUrl();
                productListVo.setImageShowUrl(attachmentShowUrl);
            }

            productListVo.setSkuList(productSkuList);
            results.add(productListVo);
        }
        return TableDataInfo.build(results, productIPage.getTotal());
    }

    @InMethodLog(value = "保存国外新货商品信息")
    @Override
    @Transactional(rollbackFor = {Exception.class, RStatusCodeException.class})
    public R<GetWholesaleProductInfoBo> saveProduct(WholesaleProductBo bo) throws Exception {

        String productCode = bo.getProductCode();
        //商品正在审核
        if (StrUtil.isNotBlank(productCode)) {
            ProductReviewRecord productReviewRecord = iProductReviewRecordService.queryRecordByProductCode(productCode);
            if (ObjectUtil.isNotEmpty(productReviewRecord)) {
                return R.fail(ZSMallStatusCodeEnum.NEW_PRODUCT_VERIFY_PENDING);
            }
        }
        //国外现货数据校验
        ZSMallStatusCodeEnum resultEnum = saveProductRequiredVerification(bo);
        if (!ObjectUtil.equals(resultEnum, ZSMallStatusCodeEnum.REQUEST_SUCCESS)) {
            return R.fail(resultEnum);
        }

        TenantType tenantType = LoginHelper.getTenantTypeEnum();
        if (!TenantType.Supplier.equals(tenantType)) {
            return R.fail(ZSMallStatusCodeEnum.NO_HAS_PRODUCT_MANAGEMENT_AUTHORITY);
        }
        // 检查sku或upc是否重复
        String duplicateSkuCode = this.checkIfSkuOrUpcIsDuplicate(bo.getSkuList());
        if (StringUtils.isNotEmpty(duplicateSkuCode)) {
            String[] res = duplicateSkuCode.split(";");
            String code = "";
            // sku和upc传空字符串导致数组下标越界，治标不治本。
            if (res.length >= 2) {
                code = res[1];
            }
            return R.fail(ZSMallStatusCodeEnum.WHOLESALE_SKU_REPEAT.args(code));
        }
        Product product;
        try {
            product = this.updateProductInfo(bo);
        } catch (RStatusCodeException e) {
            log.error("保存商品信息状态码异常 => {}", e.getStatusCode());
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return R.fail(e.getStatusCode());
        } /*catch (Exception e) {
            log.error("保存商品信息异常 => {}", e.getMessage(), e);
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return R.fail(OldZSMallStatusCodeEnum.SAVE_PRODUCT_ERROR);
        }*/

        GetWholesaleProductInfoBo respBody = new GetWholesaleProductInfoBo();
        respBody.setProductCode(product.getProductCode());
//        respBody.setHasPriceChange(product.isHasPriceChange());
        if (product.isHasPriceChange()) {
            return R.ok(ZSMallStatusCodeEnum.HAS_PRICE_CHANGE, respBody);
        } else {
            return R.ok(ZSMallStatusCodeEnum.REQUEST_SUCCESS, respBody);
        }

    }

    /**
     * 获取国外新货商品信息
     *
     * @param bo
     * @return
     */
    @Override
    public R<WholesaleProductVo> getWholesaleProductInfo(GetWholesaleProductInfoBo bo) {
        try {
            log.info("调用【获取国外新货商品信息】接口");
            log.info("接口请求参数：{} ", JSONUtil.toJsonStr(bo));
            if (ObjectUtil.isEmpty(bo)) {
                return R.fail(ZSMallStatusCodeEnum.REQUIRED_CANNOT_EMPTY);
            }
            String productCode = bo.getProductCode();

            Product product = iProductService.queryByProductCode(productCode);
            Long productId = product.getId();
            Long belongCategoryId = product.getBelongCategoryId();
            List<ProductAttachment> productAttachmentList = iProductAttachmentService.queryByProductIdOrderBySortAsc(productId, AttachmentTypeEnum.File);

            WholesaleProductInfoVo productInfoVo = new WholesaleProductInfoVo();
            String name = product.getName();
            String description = product.getDescription();
            if (CollUtil.size(productAttachmentList) > 0) {
                List<ProductAttachment> paList = productAttachmentList.stream().filter(pa -> pa.getAttachmentType().equals(AttachmentTypeEnum.File)).collect(Collectors.toList());
                List<ProductAttachmentBo> paBodyList = new ArrayList<>();
                paList.stream().forEach(pa -> {
                    paBodyList.add(BeanUtil.copyProperties(pa, ProductAttachmentBo.class));
                });
                productInfoVo.setOtherAttachment(paBodyList);
            }
            ProductWholesaleDetail wholesaleDetail = iProductWholesaleDetailService.queryByProductId(productId);
            // 可选规格
            List<ProductAttribute> optionalSpecList = iProductAttributeService.queryByProductIdAndAttributeType(productId, AttributeTypeEnum.OptionalSpec);
            List<ProductIntactInfoUpdateBo.OptionalSpecList> optionalSpecListBo = new ArrayList<>();
            if (CollUtil.isNotEmpty(optionalSpecList)) {
                for (ProductAttribute productAttribute : optionalSpecList) {
                    ProductIntactInfoVo.OptionalSpecList new_vo = new ProductIntactInfoVo.OptionalSpecList();
                    Long attributeSourceId = productAttribute.getAttributeSourceId();
                    ProductGlobalAttribute productGlobalAttribute = iProductGlobalAttributeService.queryByIdNotTenant(attributeSourceId);

                    new_vo.setId(productAttribute.getId());
                    new_vo.setKey(productAttribute.getAttributeName());
                    new_vo.setValues(productAttribute.getAttributeValues());
                    new_vo.setSourceId(attributeSourceId);
                    if (productGlobalAttribute != null) {
                        new_vo.setIsSupportCustom(productGlobalAttribute.getIsSupportCustom());
                    } else {
                        new_vo.setIsSupportCustom(true);
                    }
                    optionalSpecListBo.add(BeanUtil.copyProperties(new_vo, ProductIntactInfoUpdateBo.OptionalSpecList.class));
                }
            }
            productInfoVo.setOptionalSpecList(optionalSpecListBo);
            //商品类目集合
            List<ProductCategory> productCategories = iProductCategoryService.queryCategoryChainById(belongCategoryId);
            List<Long> categoryIdList = productCategories.stream().map(ProductCategory::getId).collect(Collectors.toList());
            categoryIdList.add(-1L);
            BigDecimal depositRatio = NumberUtil.mul(wholesaleDetail.getDepositRatio(), 100);
            Integer minimumQuantity = wholesaleDetail.getMinimumQuantity();
            Integer reservedTime = wholesaleDetail.getReservedTime();
            String warehouseSystemCode = wholesaleDetail.getWarehouseSystemCode();
            String logisticsTemplateNo = wholesaleDetail.getLogisticsTemplateNo();
            JSONArray deliveryType = wholesaleDetail.getDeliveryType();
            List<WholesaleProductInfoVo.WholesaleVariant> variantList = new ArrayList<>();
            List<ProductSkuWholesaleBo> skuList = new ArrayList<>();
            Map<Long, String> attributeMap = new HashMap<>();
            List<Long> skuIds = new ArrayList<>();
            /**
             * 1、先查出该商品的所有未被拒绝SKU
             * 2、
             */
            List<ProductSku> productSkus = iProductSkuService.getNotRejectedProductSkuListByProductId(product.getId());
            //如果productSkus为空，则展示最后一次被拒绝的sku集合信息
            if (CollUtil.isEmpty(productSkus)) {
                ProductReviewRecord lastReviewRecord = iProductReviewRecordService.getLastByProductCode(productCode);
                List<ProductReviewChangeDetail> list = iProductReviewChangeDetailService.queryByReviewRecordId(lastReviewRecord.getId());
                List<String> skuCodes = list.stream().map(ProductReviewChangeDetail::getProductSkuCode).collect(Collectors.toList());
                productSkus = iProductSkuService.queryByProductSkuCodeList(skuCodes);
            }

            List<ProductWholesaleTieredPrice> tieredPrices;
            List<ProductSkuWholesalePrice> wholesalePrices = null;
            ProductReviewRecord record = iProductReviewRecordService.queryNotNewProductRecordByProductCode(productCode);
            //如果存在审核进行中的数据，则展示最新的审核数据；否则展示有效数据
            if (ObjectUtil.isEmpty(record)) {
                tieredPrices = iProductWholesaleTieredPriceService.queryListByProductId(product.getId());
                wholesalePrices = iProductSkuWholesalePriceService.queryListByProductId(product.getId());
            } else {
                if (!ProductVerifyStateEnum.Accepted.name().equals(record.getReviewState())) {
                    List<ProductReviewChangeDetail> reviewChangeDetails = iProductReviewChangeDetailService.queryByReviewRecordIdAndCodeAndFieldName(record.getId(), productCode, null, "productWholesaleTieredPrice");
                    ProductReviewChangeDetail wholesaleDetail1 = iProductReviewChangeDetailService.queryByReviewRecordIdAndCodeAndFieldName(record.getId(), productCode, null, "productWholesaleDetail").get(0);
                    if (ObjectUtil.isNotEmpty(wholesaleDetail1)) {
                        ProductWholesaleDetail productWholesaleDetailEntity = JSONUtil.toBean(wholesaleDetail1.getFieldValueAfter(), ProductWholesaleDetail.class);
                        minimumQuantity = productWholesaleDetailEntity.getMinimumQuantity();
                    }
                    String fieldValueAfter = reviewChangeDetails.get(0).getFieldValueAfter();
                    tieredPrices = JSONUtil.parseArray(fieldValueAfter).toList(ProductWholesaleTieredPrice.class);
                } else {
                    tieredPrices = iProductWholesaleTieredPriceService.queryListByProductId(product.getId());
                    wholesalePrices = iProductSkuWholesalePriceService.queryListByProductId(product.getId());
                }
            }
            //设置sku响应模块
            for (ProductSku sku : productSkus) {
                ProductSkuDetail skuDetail = iProductSkuDetailService.queryByProductSkuCode(sku.getProductSkuCode());
                List<ProductSkuAttachment> skuAttachmentList = iProductSkuAttachmentService.findBySkuIdOrderBySortAsc(sku.getId());
                Long skuId = sku.getId();
                String productSkuCode = sku.getProductSkuCode();
                skuIds.add(skuId);
                String skuCode = sku.getSku();
                String erpSku = sku.getErpSku();
                String upc = sku.getUpc();
                Integer stockNum = 0;
                List<ProductSkuStockVo> stockVos = iProductSkuStockService.queryByProductSkuCode(sku.getProductSkuCode());
                log.info("skuInventories = {}", CollUtil.size(stockVos));
                if (CollUtil.size(stockVos) > 0) {
                    stockNum = stockVos.stream().map(ProductSkuStockVo::getStockAvailable).reduce(Integer::sum).get();
                }

                BigDecimal length = skuDetail.getLength();
                BigDecimal width = skuDetail.getWidth();
                BigDecimal height = skuDetail.getHeight();
                LengthUnitEnum lengthUnit = skuDetail.getLengthUnit();
                BigDecimal weight = skuDetail.getWeight();
                WeightUnitEnum weightUnit = skuDetail.getWeightUnit();
                BigDecimal packLength = skuDetail.getPackLength();
                BigDecimal packWidth = skuDetail.getPackWidth();
                BigDecimal packHeight = skuDetail.getPackHeight();
                LengthUnitEnum packLengthUnit = skuDetail.getPackLengthUnit();
                BigDecimal packWeight = skuDetail.getPackWeight();
                WeightUnitEnum packWeightUnit = skuDetail.getPackWeightUnit();
                ProductSkuWholesaleVo skuWholesaleVo = new ProductSkuWholesaleVo();

                List<ProductSkuAttribute> skuAttributes = iProductSkuAttributeService.queryByProductSkuId(skuId);
                List<ProductIntactInfoUpdateBo.SpecComposeList> specComposeList = new ArrayList<>();
                for (ProductSkuAttribute skuAttribute : skuAttributes) {
                    ProductIntactInfoVo.SpecComposeList specComposeBo = new ProductIntactInfoVo.SpecComposeList();
                    specComposeBo.setId(skuAttribute.getId());
                    specComposeBo.setKey(skuAttribute.getAttributeName());
                    specComposeBo.setValue(skuAttribute.getAttributeValue());
                    specComposeBo.setSourceId(skuAttribute.getAttributeSourceId());
                    specComposeList.add(BeanUtil.copyProperties(specComposeBo, ProductIntactInfoUpdateBo.SpecComposeList.class));
                }
                skuWholesaleVo.setSpecComposeList(specComposeList);

                if (CollUtil.size(skuAttachmentList) > 0) {
                    List<ProductAttachmentBo> attachmentList = new ArrayList<>();
                    for (ProductSkuAttachment att : skuAttachmentList) {
                        attachmentList.add(BeanUtil.copyProperties(att, ProductAttachmentBo.class));
                    }
                    List<ProductAttachmentBo> imageList = attachmentList.stream().filter(a -> a.getAttachmentType().equals(AttachmentTypeEnum.Image.name())).collect(Collectors.toList());
                    List<ProductAttachmentBo> videoList = attachmentList.stream().filter(a -> a.getAttachmentType().equals(AttachmentTypeEnum.Video.name())).collect(Collectors.toList());
                    skuWholesaleVo.setImageList(imageList);
                    skuWholesaleVo.setVideoList(videoList);
                }
                attributeMap.put(sku.getId(), sku.getSpecValName());
                skuWholesaleVo.setId(skuId);
                skuWholesaleVo.setItemNo(productSkuCode);
                skuWholesaleVo.setSkuCode(skuCode);
                skuWholesaleVo.setErpSku(erpSku);
                skuWholesaleVo.setSpecValName(sku.getSpecValName());
                skuWholesaleVo.setSpecComposeName(sku.getSpecComposeName());
                skuWholesaleVo.setUpc(upc);
                skuWholesaleVo.setQuantity(stockNum);
                skuWholesaleVo.setLength(length);
                skuWholesaleVo.setWidth(width);
                skuWholesaleVo.setHeight(height);
                skuWholesaleVo.setLengthUnit(lengthUnit.name());
                skuWholesaleVo.setWeight(weight);
                skuWholesaleVo.setWeightUnit(weightUnit.name());
                skuWholesaleVo.setPackLength(packLength);
                skuWholesaleVo.setPackWidth(packWidth);
                skuWholesaleVo.setPackHeight(packHeight);
                skuWholesaleVo.setPackLengthUnit(packLengthUnit.name());
                skuWholesaleVo.setPackWeight(packWeight);
                skuWholesaleVo.setPackWeightUnit(packWeightUnit.name());
                skuWholesaleVo.setSamePacking(skuDetail.getSamePacking());
                skuList.add(skuWholesaleVo);
            }
            //设置价格体系响应模块
            List<ProductWholesalePriceVo> wholesalePriceBodyList = new ArrayList<>();
            for (ProductWholesaleTieredPrice tp : tieredPrices) {
                ProductWholesalePriceVo priceBody = new ProductWholesalePriceVo();
                Integer minQuantity = tp.getMinimumQuantity();
                Integer estimatedHandleTime = tp.getEstimatedHandleTime();
                BigDecimal estimatedOperationFee = tp.getEstimatedOperationFee();
                BigDecimal estimatedShippingFee = tp.getEstimatedShippingFee();
                priceBody.setDealDate(estimatedHandleTime);
                priceBody.setMinQuantity(minQuantity);
                priceBody.setEstimatedShoppingFee(estimatedShippingFee);
                priceBody.setEstimatedOperationalFee(estimatedOperationFee);
                Long tpId = tp.getId();
                List<ProductSkuWholesalePrice> wpc = null;
                if (ObjectUtil.isNotEmpty(record)) {
                    wholesalePrices = tp.getWholesalePrices();
                }
                if (ObjectUtil.isEmpty(tpId)) {
                    wpc = tp.getWholesalePrices();
                } else {
                    if (CollUtil.isEmpty(wholesalePrices)) {
                        wholesalePrices = iProductSkuWholesalePriceService.queryListByTieredPriceId(tp.getId());
                    }
                    wpc = wholesalePrices.stream()
                        .filter(w -> ObjectUtil.equals(w.getTieredPriceId(), tpId))
                        .filter(w -> CollUtil.contains(skuIds, w.getProductSkuId()))
                        .collect(Collectors.toList());

                }
                if (CollUtil.isEmpty(wpc)) {
                    continue;
                }
                List<ProductWholesalePriceVo.AttributePrice> attributePrices = new ArrayList();
                for (ProductSkuWholesalePrice wp : wpc) {
                    Long productSkuId = wp.getProductSkuId();
                    String attribute = attributeMap.get(productSkuId);
                    if (StrUtil.isBlank(attribute)) {
                        continue;
                    }
                    BigDecimal originUnitPrice = wp.getOriginUnitPrice();
                    ProductWholesalePriceVo.AttributePrice attributePrice = new ProductWholesalePriceVo.AttributePrice();
                    attributePrice.setAttribute(attribute);
                    attributePrice.setPrice(originUnitPrice);
                    attributePrices.add(attributePrice);
                }
                priceBody.setAttributePrices(attributePrices);
                wholesalePriceBodyList.add(priceBody);
            }

            productInfoVo.setCategoryIdList(CollUtil.reverse(categoryIdList));
            productInfoVo.setBelongCategoryId(belongCategoryId);
            productInfoVo.setVariantList(variantList);
            productInfoVo.setProductCode(productCode);
            productInfoVo.setProductName(name);
            productInfoVo.setDepositRatio(depositRatio);
            productInfoVo.setMinimumOrderQuantity(minimumQuantity);
            productInfoVo.setReservedDay(reservedTime);
            productInfoVo.setDeliveryTypeList(deliveryType);
            productInfoVo.setWarehouseSystemCode(warehouseSystemCode);
            productInfoVo.setLogisticsTemplateNo(logisticsTemplateNo);
            productInfoVo.setDescription(description);
            productInfoVo.setSkuList(skuList);
            productInfoVo.setWholesalePriceBodyList(wholesalePriceBodyList);

            WholesaleProductVo respBody = new WholesaleProductVo();
            respBody.setProductWholesaleBody(productInfoVo);
            return R.ok(respBody);
        } catch (RStatusCodeException e) {
            log.info("进入【获取国外新货商品信息】接口状态码异常 RStatusCodeException = {}", e.getStatusCode());
            return R.fail(e.getStatusCode());
        } catch (Exception e) {
            log.info("进入【获取国外新货商品信息】接口异常 Exception = {}", e.getMessage(), e);
            return R.fail(ZSMallStatusCodeEnum.GET_PRODUCT_DETAIL_ERROR);
        }
    }


    /**
     * 更新商品信息（商家保存商品、员工审核商品）
     *
     * @param wholesaleProductBo
     * @return
     * @throws Exception
     */
    private Product updateProductInfo(WholesaleProductBo wholesaleProductBo) throws Exception {
        Integer minimumOrderQuantity = wholesaleProductBo.getMinimumOrderQuantity();
        BigDecimal depositRatio = NumberUtil.div(wholesaleProductBo.getDepositRatio(), 100);
        Integer reservedDay = wholesaleProductBo.getReservedDay();
        JSONArray deliveryTypeList = wholesaleProductBo.getDeliveryTypeList();
        String warehouseSystemCode = wholesaleProductBo.getWarehouseSystemCode();
        String logisticsTemplateNo = wholesaleProductBo.getLogisticsTemplateNo();

        String productCode = wholesaleProductBo.getProductCode();
        boolean isEdit = false;
        if (StrUtil.isNotBlank(productCode)) {
            isEdit = true;
        }
        Product product;
        ProductWholesaleDetail wholesaleDetail = new ProductWholesaleDetail();
        boolean addNew = false;

        //判断
        boolean isSumbitReview = false;
        if (StringUtils.isEmpty(productCode)) {
            product = new Product();
            String newProductCode = productCodeGenerator.codeGenerate(BusinessCodeEnum.ProductCode);
            product.setProductCode(newProductCode);
            product.setShelfState(ShelfStateEnum.ForcedOffShelf);
            product.setVerifyState(ProductVerifyStateEnum.Draft);
            addNew = true;
        } else {
            product = iProductService.queryByProductCode(productCode);
            if (product == null) {
                throw new RStatusCodeException(ZSMallStatusCodeEnum.PRODUCT_NOT_EXIST);
            }
            if (product.getVerifyState().equals(ProductVerifyStateEnum.Pending)) {
                throw new RStatusCodeException(ZSMallStatusCodeEnum.NEW_PRODUCT_VERIFY_PENDING);
            }
            // 新商品第一次审核，会出现审核状态为驳回
            if (ObjectUtil.equals(product.getVerifyState(), ProductVerifyStateEnum.Rejected)) {
                product.setVerifyState(ProductVerifyStateEnum.Pending);
                addNew = true;
            }
            if (product.getVerifyState().equals(ProductVerifyStateEnum.Draft)) {
                addNew = true;
            }

            //国外现货批发详情，抛出异常
            wholesaleDetail = iProductWholesaleDetailService.queryByProductId(product.getId());
            if (ObjectUtil.isEmpty(wholesaleDetail)) {
                throw new RStatusCodeException(ZSMallStatusCodeEnum.PRODUCT_NOT_EXIST);
            }
        }

        /**
         * 批发商品信息处理
         */
        //保存商品基础信息
        product = saveProductBase(wholesaleProductBo, product);
        //保存商品sku相关
        List<ProductSku> productSkus = saveProductSkuRelated(wholesaleProductBo, product);
        //保存商品相关
        saveProductRelated(wholesaleProductBo, product, addNew);
        //设置批发商品详情信息
        wholesaleDetail = getProductWholesaleDetail(minimumOrderQuantity, depositRatio, reservedDay, deliveryTypeList, warehouseSystemCode, logisticsTemplateNo, product, wholesaleDetail, addNew);

        Map<String, String> attributeMap = new HashMap<>();
        for (ProductSku productSku : productSkus) {
            String specValName = productSku.getSpecValName();
            //设置shu属性和itemNo对应信息
            attributeMap.put(specValName, productSku.getProductSkuCode());
        }

        log.info("attributeMap = {}", JSONUtil.toJsonStr(attributeMap));

        /**
         * 批发商品价格信息处理
         */
        //根据商品id获取阶梯价集合
        List<ProductWholesaleTieredPrice> tieredPrices = iProductWholesaleTieredPriceService.queryListByProductId(product.getId());
        //根据商品id获取国外现货批发商品SKU价格集合
        List<ProductSkuWholesalePrice> wholesalePrices = iProductSkuWholesalePriceService.queryListByProductId(product.getId());
        //旧的价格体系数据在审核后需要存入日志
        List<ProductWholesaleTieredPrice> oldTieredPrices = CollUtil.newCopyOnWriteArrayList(tieredPrices);
        List<ProductSkuWholesalePrice> oldWholesalePrices = CollUtil.newCopyOnWriteArrayList(wholesalePrices);
        List<ProductSkuWholesalePrice> updateWholesalePrices = new ArrayList<>();
        List<ProductWholesaleTieredPrice> updateOtherTieredPrices = new ArrayList<>();
        //新的阶梯价集合
        List<ProductWholesaleTieredPrice> newTieredPrices = new ArrayList<>();
        //批发商品价格设置
        List<ProductWholesalePriceVo> wholesalePriceBodyList = wholesaleProductBo.getWholesalePriceBodyList();

        /**
         1、新创建的  - 需要审核
         2、基本数量变动 - 需要审核
         3、阶梯价格变动 - 需要审核
         4、其他 - 不需要审核
         */
        //如果是新商品保存，则同时保存商品价格体系信息并请求新商品提交审核接口，
        //如果是老商品修改，则暂不修改商品价格体系，待审核后批量更新商品价格体系
        if (addNew) {
            for (ProductWholesalePriceVo priceBody : wholesalePriceBodyList) {
                ProductWholesaleTieredPrice tieredPrice = new ProductWholesaleTieredPrice();
                Integer minQuantity = priceBody.getMinQuantity();

                List<ProductWholesalePriceVo.AttributePrice> attributePrices = priceBody.getAttributePrices();
                BigDecimal estimatedOperationalFee = priceBody.getEstimatedOperationalFee();
                BigDecimal estimatedShoppingFee = priceBody.getEstimatedShoppingFee();
                Integer dealDate = priceBody.getDealDate();

                tieredPrice.setProductId(product.getId());
                tieredPrice.setMinimumQuantity(minQuantity);
                tieredPrice.setEstimatedShippingFee(estimatedShoppingFee);
                tieredPrice.setEstimatedOperationFee(estimatedOperationalFee);
                tieredPrice.setEstimatedHandleTime(dealDate);

                newTieredPrices.add(tieredPrice);

                //新的sku单价集合
                List<ProductSkuWholesalePrice> newWholesalePrice = new ArrayList<>();
                for (ProductWholesalePriceVo.AttributePrice attributePrice : attributePrices) {
                    //商品SKU主键 和 阶梯定价表主键 确认唯一性
                    String attribute = attributePrice.getAttribute();
                    BigDecimal price = attributePrice.getPrice();
                    String productSkuCode = attributeMap.get(attribute);
                    log.info("attribute = {}, price = {}, productSkuCode ={}", attribute, price, productSkuCode);

                    Long productSkuId = productSkus.stream().filter(sku -> StrUtil.equals(productSkuCode, sku.getProductSkuCode())).collect(Collectors.toList()).get(0).getId();

                    ProductSkuWholesalePrice wholesalePrice = new ProductSkuWholesalePrice();
                    //国外现货批发商品SKU价格Id
                    wholesalePrice.setProductSkuId(productSkuId);
                    wholesalePrice.setProductId(product.getId());
                    wholesalePrice.setTieredPriceId(tieredPrice.getId());
                    wholesalePrice.setOriginUnitPrice(price);
                    wholesalePrice.setPlatformUnitPrice(wholesaleSupport.calculatePlatformPrice(price));// 平台单价需经过计算
                    newWholesalePrice.add(wholesalePrice);
                }
                tieredPrice.setWholesalePrices(newWholesalePrice);
            }
            //保存新商品价格体系信息
            savaNewProductPrice(newTieredPrices);
            //清除老的阶梯价
            List<Long> tpIds = tieredPrices.stream().map(ProductWholesaleTieredPrice::getId).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(tpIds)) {
                iProductWholesaleTieredPriceService.removeByIds(tpIds);
            }
            //清除老的商品单价
            List<Long> wpIds = wholesalePrices.stream().map(ProductSkuWholesalePrice::getId).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(wpIds)) {
                iProductSkuWholesalePriceService.removeByIds(tpIds);
            }
            //如果是编辑操作并且商品不为草稿，则调用提交审核接口
            if (isEdit && !product.getVerifyState().equals(ProductVerifyStateEnum.Draft)) {
                //调用新商品提交审核接口
                productReviewRecordService.extractedProductReview(product.getProductCode());
            }
        } else {

            for (ProductWholesalePriceVo priceBody : wholesalePriceBodyList) {
                ProductWholesaleTieredPrice tieredPrice = new ProductWholesaleTieredPrice();
                Integer minQuantity = priceBody.getMinQuantity();

                List<ProductWholesalePriceVo.AttributePrice> attributePrices = priceBody.getAttributePrices();
                BigDecimal estimatedOperationalFee = priceBody.getEstimatedOperationalFee();
                BigDecimal estimatedShoppingFee = priceBody.getEstimatedShoppingFee();
                Integer dealDate = priceBody.getDealDate();

                Long tieredPriceId = null;
                // 根据最小数量匹配已存在的阶梯价
                List<ProductWholesaleTieredPrice> existTieredPrices = tieredPrices.stream().filter(t -> t.getMinimumQuantity().equals(minQuantity)).collect(Collectors.toList());
                //移除掉存在的数据，剩余的删除（审核通过后则删除）
                tieredPrices.removeAll(existTieredPrices);

                tieredPrice.setProductId(product.getId());
                tieredPrice.setMinimumQuantity(minQuantity);
                tieredPrice.setEstimatedShippingFee(estimatedShoppingFee);
                tieredPrice.setEstimatedOperationFee(estimatedOperationalFee);
                tieredPrice.setEstimatedHandleTime(dealDate);
                if (CollUtil.isNotEmpty(existTieredPrices)) {
                    ProductWholesaleTieredPrice oldTieredPrice = existTieredPrices.get(0);
                    tieredPriceId = oldTieredPrice.getId();
                    tieredPrice.setId(tieredPriceId);
                    //如果预估操作费/预估运费/预估处理时间有变动，则即使修改，无需经过审核
                    if (!ObjectUtil.equals(estimatedOperationalFee, oldTieredPrice.getEstimatedOperationFee())
                        || !ObjectUtil.equals(estimatedShoppingFee, oldTieredPrice.getEstimatedShippingFee())
                        || !ObjectUtil.equals(dealDate, oldTieredPrice.getEstimatedHandleTime())) {

                        ProductWholesaleTieredPrice tieredPriceEntity = BeanUtil.copyProperties(tieredPrice, ProductWholesaleTieredPrice.class);
                        tieredPriceEntity.setMinimumQuantity(oldTieredPrice.getMinimumQuantity());
                        updateOtherTieredPrices.add(tieredPrice);
                    }
                }
                //新的阶梯价，审核通过后批量修改为新的阶梯价
                newTieredPrices.add(tieredPrice);
                /**
                 * 判断改商品sku是否存在价格数据：
                 * 1、如果存在则所编辑的所有价格信息先保存审核变更详情表，待审核通过后再覆盖
                 * 2、不存在，则新增sku价格信息
                 */
                //新的sku单价集合
                List<ProductSkuWholesalePrice> newWholesalePrice = new ArrayList<>();
                for (ProductWholesalePriceVo.AttributePrice attributePrice : attributePrices) {
                    //商品SKU主键 和 阶梯定价表主键 确认唯一性
                    String attribute = attributePrice.getAttribute();
                    BigDecimal price = attributePrice.getPrice();
                    String productSkuCode = attributeMap.get(attribute);
                    log.info("attribute = {}, price = {}, productSkuCode ={}", attribute, price, productSkuCode);

                    Long productSkuId = productSkus.stream().filter(sku -> StrUtil.equals(productSkuCode, sku.getProductSkuCode())).collect(Collectors.toList()).get(0).getId();
                    //根据阶梯价格Id和skuId匹配已存在的sku单价数据
                    List<ProductSkuWholesalePrice> existWholesalePrices = wholesalePrices.stream()
                        .filter(w -> ObjectUtil.equals(w.getTieredPriceId(), tieredPrice.getId()) && ObjectUtil.equals(w.getProductSkuId(), productSkuId))
                        .collect(Collectors.toList());

                    ProductSkuWholesalePrice wholesalePrice = new ProductSkuWholesalePrice();
                    //国外现货批发商品SKU价格Id
                    Long wholesalePriceId = null;
                    if (CollUtil.isNotEmpty(existWholesalePrices)) {
                        ProductSkuWholesalePrice swp = existWholesalePrices.get(0);
                        wholesalePriceId = swp.getId();
                        BigDecimal originUnitPrice = swp.getOriginUnitPrice();
                        //只要有价格变动则都要重新触发商品审核，新价格存入审核变动详情表，同时修改sku审核字段为待审核
                        if (!NumberUtil.equals(originUnitPrice, price)) {
                            isSumbitReview = true;
                        }
                    } else {
                        isSumbitReview = true;
                    }
                    wholesalePrice.setId(wholesalePriceId);
                    wholesalePrice.setProductSkuId(productSkuId);
                    wholesalePrice.setProductId(product.getId());
                    wholesalePrice.setTieredPriceId(tieredPrice.getId());
                    wholesalePrice.setOriginUnitPrice(price);
                    wholesalePrice.setPlatformUnitPrice(wholesaleSupport.calculatePlatformPrice(price));// 平台单价需经过计算
                    newWholesalePrice.add(wholesalePrice);
                    updateWholesalePrices.add(wholesalePrice);
                }
                tieredPrice.setWholesalePrices(newWholesalePrice);
                log.info("=====tieredPrice==== {}", JSONUtil.toJsonStr(tieredPrice));
            }
            if (CollUtil.isNotEmpty(tieredPrices)) {
                isSumbitReview = true;
            }
            //如果预估操作费/预估运费/预估处理时间有变动，则即使修改，无需经过审核
            if (CollUtil.isNotEmpty(updateOtherTieredPrices)) {
                if (!iProductWholesaleTieredPriceService.saveOrUpdateBatch(updateOtherTieredPrices)) {
                    throw new Exception("修改阶梯预估价格失败！");
                }
            }

            /**
             * 判断是否提交审核信息，如果需要提交审核，则修改信息需在审核通过后在修改；
             * 如无需提交审核，则立即修改价格信息
             */
            ProductVerifyStateEnum verifyState = product.getVerifyState();
            if (verifyState.equals(ProductVerifyStateEnum.Draft)) {
                isSumbitReview = false;
            }
            if (isSumbitReview) {
                //提交审核信息
                ProductReviewRecord prr = saveProductReviewRecord(productCode);
                //保存商品审核变动信息
                if (CollUtil.isNotEmpty(newTieredPrices)) {
                    saveProductReviewChangeDetail(productCode, oldTieredPrices, oldWholesalePrices, tieredPrices, newTieredPrices, prr, wholesaleDetail);
                }
                product.setHasPriceChange(isSumbitReview);
            } else {
                if (CollUtil.isNotEmpty(newTieredPrices)) {
                    boolean updateBatch = iProductWholesaleTieredPriceService.saveOrUpdateBatch(newTieredPrices);
                    if (!updateBatch) {
                        throw new Exception("批量需改价格信息失败！");
                    }
                }
            }
        }

        return product;
    }

    /**
     * 获取批发商品详情
     */
    @NotNull
    private ProductWholesaleDetail getProductWholesaleDetail(Integer minimumOrderQuantity, BigDecimal depositRatio, Integer reservedDay, JSONArray deliveryTypeList, String warehouseSystemCode, String logisticsTemplateNo, Product product, ProductWholesaleDetail wholesaleDetail, boolean addNew) throws Exception {
        Integer minimumQuantity = null;
        Long wholesaleDetailId = null;
        if (ObjectUtil.isNotEmpty(wholesaleDetail)) {
            wholesaleDetailId = wholesaleDetail.getId();
            if (addNew) {
                wholesaleDetail.setMinimumQuantity(minimumOrderQuantity);
            } else {
                minimumQuantity = wholesaleDetail.getMinimumQuantity();
            }
        } else {
            wholesaleDetail = new ProductWholesaleDetail();
            wholesaleDetail.setMinimumQuantity(minimumOrderQuantity);
        }
        wholesaleDetail.setId(wholesaleDetailId);
        wholesaleDetail.setProductId(product.getId());
        wholesaleDetail.setDepositRatio(depositRatio);
        wholesaleDetail.setReservedTime(reservedDay);
        wholesaleDetail.setDeliveryType(deliveryTypeList);
        wholesaleDetail.setWarehouseSystemCode(warehouseSystemCode);
        wholesaleDetail.setLogisticsTemplateNo(logisticsTemplateNo);
        wholesaleDetail.setLogisticsTemplateNo(logisticsTemplateNo);
        boolean wholesaleDetailSave = iProductWholesaleDetailService.saveOrUpdate(wholesaleDetail);
        if (!wholesaleDetailSave) {
            throw new Exception("修改批发商品详情信息失败！");
        }
        log.info("wholesaleDetail = {}, ", JSONUtil.toJsonStr(wholesaleDetail));
        if (ObjectUtil.isNotEmpty(minimumQuantity) && !minimumQuantity.equals(minimumOrderQuantity)) {
            wholesaleDetail.setMinimumQuantity(minimumOrderQuantity);
        }
        return wholesaleDetail;
    }

    /**
     * 保存新商品价格体系信息
     *
     * @param newTieredPrices
     * @throws Exception
     */
    private void savaNewProductPrice(List<ProductWholesaleTieredPrice> newTieredPrices) throws Exception {
        boolean b = iProductWholesaleTieredPriceService.saveBatch(newTieredPrices);
        if (!b) {
            throw new Exception("批量保存国外现货批发商品阶梯价信息失败！");
        }
        List<ProductSkuWholesalePrice> newWholesalePriceList = new ArrayList();
        for (ProductWholesaleTieredPrice pwtp : newTieredPrices) {
            List<ProductSkuWholesalePrice> wholesalePrices = pwtp.getWholesalePrices();
            wholesalePrices.stream().forEach(wp -> {
                wp.setTieredPriceId(pwtp.getId());
            });
            newWholesalePriceList.addAll(wholesalePrices);
        }
        boolean b1 = iProductSkuWholesalePriceService.saveBatch(newWholesalePriceList);
        if (!b1) {
            throw new Exception("批量保存国外现货批发商品单价信息失败！");
        }
    }

    /**
     * 保存审核记录
     *
     * @param productCode
     * @return
     * @throws Exception
     */
    private ProductReviewRecord saveProductReviewRecord(String productCode) throws Exception {
        ProductReviewRecord prr = new ProductReviewRecord();
        prr.setProductCode(productCode);
        prr.setSubmitDateTime(new Date());
        prr.setSubmitUserId(LoginHelper.getUserId());
        prr.setReviewType(ProductReviewTypeEnum.Price);
        prr.setReviewState(ProductVerifyStateEnum.Pending);
        if (!iProductReviewRecordService.save(prr)) {
            throw new Exception("提交审核信息失败！");
        }
        return prr;
    }

    /**
     * 保存商品审核变动信息
     *
     * @param productCode
     * @param oldTieredPrices
     * @param oldWholesalePrices
     * @param newTieredPrices
     * @param prr
     * @throws Exception
     */
    private void saveProductReviewChangeDetail(String productCode, List<ProductWholesaleTieredPrice> oldTieredPrices,
                                               List<ProductSkuWholesalePrice> oldWholesalePrices,
                                               List<ProductWholesaleTieredPrice> delTieredPrices,
                                               List<ProductWholesaleTieredPrice> newTieredPrices,
                                               ProductReviewRecord prr,
                                               ProductWholesaleDetail wholesaleDetail) throws Exception {
        log.info("=====newTieredPrices==== {}", JSONUtil.toJsonStr(newTieredPrices));
        List<ProductReviewChangeDetail> list = new ArrayList<>();

        ProductReviewChangeDetail prcdSupportedLogistics = new ProductReviewChangeDetail();
        prcdSupportedLogistics.setReviewRecordId(prr.getId());
        prcdSupportedLogistics.setProductCode(productCode);
        prcdSupportedLogistics.setFieldName("supportedLogistics");
        prcdSupportedLogistics.setFieldValueBefore(JSONUtil.toJsonStr(wholesaleDetail.getDeliveryType()));
        prcdSupportedLogistics.setFieldValueAfter(JSONUtil.toJsonStr(wholesaleDetail.getDeliveryType()));
        list.add(prcdSupportedLogistics);

        ProductReviewChangeDetail prcdTieredPrice = new ProductReviewChangeDetail();
        prcdTieredPrice.setReviewRecordId(prr.getId());
        prcdTieredPrice.setProductCode(productCode);
        prcdTieredPrice.setFieldName("productWholesaleTieredPrice");
        prcdTieredPrice.setFieldValueBefore(JSONUtil.toJsonStr(oldTieredPrices));
        prcdTieredPrice.setFieldValueAfter(JSONUtil.toJsonStr(newTieredPrices));
        list.add(prcdTieredPrice);

        ProductReviewChangeDetail prcdWholesalePrice = new ProductReviewChangeDetail();
        prcdWholesalePrice.setReviewRecordId(prr.getId());
        prcdWholesalePrice.setProductCode(productCode);
        prcdWholesalePrice.setFieldName("productSkuWholesalePrice");
        prcdWholesalePrice.setFieldValueBefore(JSONUtil.toJsonStr(oldWholesalePrices));
        list.add(prcdWholesalePrice);

        ProductReviewChangeDetail delWholesalePrice = new ProductReviewChangeDetail();
        delWholesalePrice.setReviewRecordId(prr.getId());
        delWholesalePrice.setProductCode(productCode);
        delWholesalePrice.setFieldName("delWholesalePrice");
        delWholesalePrice.setFieldValueBefore(JSONUtil.toJsonStr(delTieredPrices));
        list.add(delWholesalePrice);
        if (CollUtil.isNotEmpty(delTieredPrices)) {
            Product product = iProductService.queryByProductCode(productCode);
            List<ProductSku> skus = iProductSkuService.queryByProductIdNotDelete(product.getId());
            for (ProductWholesaleTieredPrice ntp : delTieredPrices) {
                for (ProductSku sku : skus) {
                    List<ProductSkuWholesalePrice> collect = oldWholesalePrices.stream().
                        filter(o -> ObjectUtil.equals(o.getProductSkuId(), sku.getId()) && ObjectUtil.equals(o.getTieredPriceId(), ntp.getId())).collect(Collectors.toList());
                    BigDecimal fieldValueBefore = BigDecimal.ZERO;
                    if (CollUtil.isNotEmpty(collect)) {
                        fieldValueBefore = collect.get(0).getOriginUnitPrice();
                    }

                    ProductReviewChangeDetail wholesalePriceEntity = new ProductReviewChangeDetail();
                    wholesalePriceEntity.setReviewRecordId(prr.getId());
                    wholesalePriceEntity.setProductCode(productCode);
                    wholesalePriceEntity.setProductSkuCode(sku.getProductSkuCode());
                    wholesalePriceEntity.setFieldName("unitPrice");
                    wholesalePriceEntity.setFieldValueAfter("0");
                    wholesalePriceEntity.setFieldValueBefore(fieldValueBefore.toString());
                    list.add(wholesalePriceEntity);
                }
            }
        }
        ProductReviewChangeDetail prcdWholesaleDetail = new ProductReviewChangeDetail();
        prcdWholesaleDetail.setReviewRecordId(prr.getId());
        prcdWholesaleDetail.setProductCode(productCode);
        prcdWholesaleDetail.setFieldName("productWholesaleDetail");
        prcdWholesaleDetail.setFieldValueAfter(JSONUtil.toJsonStr(wholesaleDetail));
        list.add(prcdWholesaleDetail);

        for (ProductWholesaleTieredPrice ntp : newTieredPrices) {
            List<ProductSkuWholesalePrice> wholesalePriceEntities = ntp.getWholesalePrices();
            for (ProductSkuWholesalePrice pswp : wholesalePriceEntities) {
                Long tieredPriceId = pswp.getTieredPriceId();
                Long productSkuId = pswp.getProductSkuId();
                ProductSku productSku = iProductSkuService.queryById(productSkuId);
                BigDecimal afterUnitPrice = pswp.getOriginUnitPrice();
                BigDecimal beforeUnitPrice = BigDecimal.ZERO;
                ProductReviewChangeDetail wholesalePriceEntity = new ProductReviewChangeDetail();
                wholesalePriceEntity.setReviewRecordId(prr.getId());
                wholesalePriceEntity.setProductCode(productCode);
                wholesalePriceEntity.setProductSkuCode(productSku.getProductSkuCode());
                wholesalePriceEntity.setFieldName("unitPrice");
                wholesalePriceEntity.setFieldValueAfter(afterUnitPrice.toString());
                if (ObjectUtil.isNotEmpty(tieredPriceId)) {
                    List<ProductSkuWholesalePrice> collect = oldWholesalePrices.stream().filter(old -> ObjectUtil.equals(old.getId(), pswp.getId())).collect(Collectors.toList());
                    if (CollUtil.size(collect) > 0) {
                        beforeUnitPrice = collect.get(0).getOriginUnitPrice();
                        if (!ObjectUtil.equals(beforeUnitPrice, afterUnitPrice)) {
                            wholesalePriceEntity.setFieldValueBefore(beforeUnitPrice.toString());
                            list.add(wholesalePriceEntity);
                        }
                    } else {
                        wholesalePriceEntity.setFieldValueBefore(beforeUnitPrice.toString());
                        list.add(wholesalePriceEntity);
                    }
                } else {
                    wholesalePriceEntity.setFieldValueBefore(beforeUnitPrice.toString());
                    list.add(wholesalePriceEntity);
                }
            }
        }

        if (!iProductReviewChangeDetailService.saveBatch(list)) {
            throw new Exception("提交审核变更详情信息失败！");
        }
    }


    /**
     * 保存商品信息
     *
     * @param wholesaleProductBo
     * @param product
     * @return
     * @throws Exception
     */
    private Product saveProductBase(WholesaleProductBo wholesaleProductBo, Product product) throws Exception {
        //设置商品基础信息
        product.setName(wholesaleProductBo.getProductName());
        //设置商品类型
        product.setProductType(ProductTypeEnum.WholesaleProduct);
        //默认设置禁售渠道为 Wayfair
        product.setForbiddenChannel(JSONUtil.parseArray(CollUtil.newArrayList(ChannelTypeEnum.Wayfair.name())));
        //处理分类
        Long belongCategoryId = wholesaleProductBo.getBelongCategoryId();
        if (ObjectUtil.isNotNull(belongCategoryId)) {
            product.setBelongCategoryId(belongCategoryId);
        } else {
            product.setBelongCategoryId(null);
        }
        product.setDescription(wholesaleProductBo.getDescription());

        //保存批发商品信息
        iProductService.saveOrUpdate(product);
        return product;
    }


    /**
     * 保存商品相关信息
     *
     * @param product
     * @param addNew
     */
    private void saveProductRelated(WholesaleProductBo wholesaleProductBo, Product product, Boolean addNew) {

        //处理分类关系
        Long belongCategoryId = product.getBelongCategoryId();
        List<ProductCategory> productCategoryList = iProductCategoryService.queryCategoryChainById(belongCategoryId);
        List<ProductCategoryRelation> oldCategoryRelationList = iProductCategoryRelationService.queryByProductId(product.getId());
        List<ProductCategoryRelation> newCategoryRelationList = new ArrayList<>();
        if (CollUtil.isNotEmpty(productCategoryList)) {
            for (ProductCategory productCategory : productCategoryList) {
                ProductCategoryRelation pcr = new ProductCategoryRelation();
                pcr.setProductId(product.getId());
                pcr.setProductCategoryId(productCategory.getId());
                newCategoryRelationList.add(pcr);
            }
        }
        iProductCategoryRelationService.insertBatch(newCategoryRelationList);
        if (CollUtil.isNotEmpty(oldCategoryRelationList)) {
            iProductCategoryRelationService.deleteByEntityList(oldCategoryRelationList);
        }
        // 第一个SKU的第一个图片，要用于SPU的主图
        ProductAttachment productAttachmentFirst = product.getProductAttachmentFirst();
        // 主动用SKU的第一张图片
        this.updateProductAttachmentsBySku(addNew, product, productAttachmentFirst);
        //设置商品其它附件信息
        List<ProductAttachmentBo> otherAttachmentBody = wholesaleProductBo.getOtherAttachment();
        //删除商品SPU旧附件
        if (product.getId() != null) {
            List<ProductAttachment> otherAttachments = iProductAttachmentService.queryByProductIdOrderBySortAsc(product.getId(), AttachmentTypeEnum.File);
            if (CollUtil.isNotEmpty(otherAttachments)) {
                iProductAttachmentService.removeByIds(otherAttachments);
            }
        }
        //新增商品SPU新附件
        if (CollUtil.isNotEmpty(otherAttachmentBody)) {
            List<ProductAttachment> saveProductAttachmentList = new ArrayList<>();
            for (ProductAttachmentBo oa : otherAttachmentBody) {
                ProductAttachment productAttachment = this.updateProductAttachments(product.getId(), oa);
                if (productAttachment != null) {
                    saveProductAttachmentList.add(BeanUtil.toBean(productAttachment, ProductAttachment.class));
                }
            }
            iProductAttachmentService.saveOrUpdateBatch(saveProductAttachmentList);
        }
        List<ProductIntactInfoUpdateBo.OptionalSpecList> optionalSpecList = wholesaleProductBo.getOptionalSpecList();
        List<ProductAttribute> productAttributeList = new ArrayList<>();
        List<Long> existsAttributeIds = iProductAttributeService.queryIdsByProductId(product.getId());
        // 处理可选规格
        if (CollUtil.isNotEmpty(optionalSpecList)) {
            for (int i = 0; i < optionalSpecList.size(); i++) {
                ProductIntactInfoUpdateBo.OptionalSpecList optionalSpec = optionalSpecList.get(i);
                Long id = optionalSpec.getId();
                String key = optionalSpec.getKey();
                Long sourceId = optionalSpec.getSourceId();
                List<String> values = optionalSpec.getValues();
                productAttributeList.add(productSupport.setProductAttribute(id, product.getId(), key, values, AttributeTypeEnum.OptionalSpec, sourceId, i));
                // 编辑时依然存在的属性需要从已存在数组中排除，该数组最后需要把仍然在其中的数据逻辑删除
                existsAttributeIds.remove(id);
            }
        }
        iProductAttributeService.insertOrUpdateBatch(productAttributeList);
        if (CollUtil.isNotEmpty(existsAttributeIds)) {
            iProductAttributeService.deleteWithValidByIds(existsAttributeIds, false);
        }
    }

    /**
     * 保存商品SKU及SKU相关信息
     *
     * @param wholesaleProductBo
     * @param product
     * @return
     */
    private List<ProductSku> saveProductSkuRelated(WholesaleProductBo wholesaleProductBo, Product product) {
        String warehouseSystemCode = wholesaleProductBo.getWarehouseSystemCode();
        String logisticsTemplateNo = wholesaleProductBo.getLogisticsTemplateNo();
        String productCode = product.getProductCode();
        Long productId = product.getId();
        List<ProductSku> productSkus = new ArrayList<>();
        ProductAttachment productAttachmentFirst = null;
        //SKU入参处理
        List<ProductSkuWholesaleBo> skuList = wholesaleProductBo.getSkuList();
        // 每次都按照前端传过来的集合顺序重新排序
        Map<String, List<ProductIntactInfoUpdateBo.SpecComposeList>> specComposeListMap = new HashMap<>();
        Map<String, String> oldSpecValNameMap = new HashMap<>();
        //获取所有SKU信息
        List<Long> delProductSkuIds = iProductSkuService.queryIdsByProductId(productId);
        for (int s = 0; s < skuList.size(); s++) {
            ProductSkuWholesaleBo skuBody = skuList.get(s);
            String skuCode = StrUtil.trim(skuBody.getSkuCode());
            Integer quantity = skuBody.getQuantity();
            Long id = skuBody.getId();
            ProductSku productSku;
            String specValName = skuBody.getSpecValName();
            ProductSkuDetail productSkuDetail = new ProductSkuDetail();
            String specComposeName = skuBody.getSpecComposeName();
            List<ProductSkuAttribute> attributeVos = new ArrayList<>();

            String oldSpecValName = "";
            if (id != null) {
                productSku = iProductSkuService.queryById(id);
                oldSpecValName = productSku.getSpecValName();
                if (productSku == null) {
                    throw new RStatusCodeException(ZSMallStatusCodeEnum.PRODUCT_SKU_NOT_EXIST);
                }
                delProductSkuIds.remove(productSku.getId());
                productSkuDetail = iProductSkuDetailService.queryByProductSkuId(productSku.getId());
            } else {
                productSku = new ProductSku();
                productSku.setProductSkuCode(productCodeGenerator.codeGenerate(BusinessCodeEnum.ProductSkuCode));
                productSku.setProductId(productId);
                productSku.setProductCode(productCode);
                productSku.setShelfState(ShelfStateEnum.ForcedOffShelf);
                productSku.setVerifyState(ProductVerifyStateEnum.Draft);
            }
            oldSpecValNameMap.put(productSku.getProductCode(), oldSpecValName);
            productSku.setSpecValName(specValName);
            productSku.setSpecComposeName(specComposeName);
            productSku.setStockTotal(quantity);
            productSku.setName(product.getName());
            Boolean notForSale = false;
            productSku.setSort(s);
            // erpSku
            String erpSku = skuBody.getErpSku();
            if (StrUtil.isNotBlank(erpSku)) {
                productSku.setErpSku(erpSku);
            } else {
                productSku.setErpSku(null);
            }

            productSku.setSku(skuCode);
            // UPC
            productSku.setUpc(StrUtil.trim(skuBody.getUpc()));
            //产品已审核
            if (ObjectUtil.equals(product.getVerifyState(), ProductVerifyStateEnum.Accepted)) {
                //sku未过审或被拒绝时
                if (ObjectUtil.equals(productSku.getVerifyState(), ProductVerifyStateEnum.Draft)
                    || ObjectUtil.equals(productSku.getVerifyState(), ProductVerifyStateEnum.Rejected.name())) {
                    productSku.setVerifyState(ProductVerifyStateEnum.Pending);
                    notForSale = true;
                } else if (ObjectUtil.equals(productSku.getVerifyState(), ProductVerifyStateEnum.Pending)) {
                    //sku过审时
                    notForSale = true;
                }
            }
            //sku附件信息
            if (CollUtil.isNotEmpty(skuBody.getImageList()) || CollUtil.isNotEmpty(skuBody.getVideoList())) {
                List<ProductSkuAttachment> skuAttachmentList = new ArrayList<>();
                if (CollUtil.isNotEmpty(skuBody.getImageList())) {
                    List<ProductSkuAttachment> skuAttachments =
                        this.updateProductSkuAttachments(productSku.getId(), skuBody.getImageList(), AttachmentTypeEnum.Image);
                    if (CollUtil.isNotEmpty(skuAttachments)) {
                        skuAttachmentList.addAll(skuAttachments);
                        productSku.setSkuAttachmentList(skuAttachmentList);
                        if (productAttachmentFirst == null) {
                            productAttachmentFirst = BeanUtil.copyProperties(skuAttachments.get(0), ProductAttachment.class);
                            productAttachmentFirst.setId(null);
                            productAttachmentFirst.setProductId(productId);
                            product.setProductAttachmentFirst(productAttachmentFirst);
                        }
                    }
                }
                if (CollUtil.isNotEmpty(skuBody.getVideoList())) {
                    List<ProductSkuAttachment> skuAttachments =
                        this.updateProductSkuAttachments(productSku.getId(), skuBody.getVideoList(), AttachmentTypeEnum.Video);
                    if (CollUtil.isNotEmpty(skuAttachments)) {
                        skuAttachmentList.addAll(skuAttachments);
                        productSku.setSkuAttachmentList(skuAttachmentList);
                        if (productAttachmentFirst == null) {
                            productAttachmentFirst = BeanUtil.copyProperties(skuAttachments.get(0), ProductAttachment.class);
                            productAttachmentFirst.setId(null);
                            productAttachmentFirst.setProductId(productId);
                            product.setProductAttachmentFirst(productAttachmentFirst);
                        }
                    }
                }
            } else {
                // SKU附件
                if (!notForSale) {
                    // 在售的商品sku至少需要一张图片
                    throw new RStatusCodeException(ZSMallStatusCodeEnum.SKU_REQUIRES_ONE_IMAGE);
                }
            }
            // 批发商品库存管理方默认
            productSku.setStockManager(StockManagerEnum.OwnWarehouse);
            // 库存配置
            log.info("skuBody.getQuantity() = {}", skuBody.getQuantity());
            this.setInventoryConfig(skuBody.getQuantity(), warehouseSystemCode, logisticsTemplateNo, productSku);
            //sku规格设置
            BigDecimal length = skuBody.getLength();
            BigDecimal width = skuBody.getWidth();
            BigDecimal height = skuBody.getHeight();
            BigDecimal weight = skuBody.getWeight();
            String lengthUnit = skuBody.getLengthUnit();
            String weightUnit = skuBody.getWeightUnit();
            productSkuDetail.setLength(length);
            productSkuDetail.setWidth(width);
            productSkuDetail.setHeight(height);
            productSkuDetail.setLengthUnit(LengthUnitEnum.valueOf(lengthUnit));
            productSkuDetail.setWeight(weight);
            productSkuDetail.setWeightUnit(WeightUnitEnum.valueOf(weightUnit));
            Boolean samePacking = skuBody.getSamePacking();
            productSkuDetail.setSamePacking(samePacking);
            BigDecimal packLength = samePacking ? length : skuBody.getPackLength();
            BigDecimal packWidth = samePacking ? width : skuBody.getPackWidth();
            BigDecimal packHeight = samePacking ? height : skuBody.getPackHeight();
            BigDecimal packWeight = samePacking ? length : skuBody.getPackWeight();
            String packLengthUnit = samePacking ? lengthUnit : skuBody.getPackLengthUnit();
            String packWeightUnit = samePacking ? weightUnit : skuBody.getPackWeightUnit();
            productSkuDetail.setPackLength(packLength);
            productSkuDetail.setPackWidth(packWidth);
            productSkuDetail.setPackHeight(packHeight);
            productSkuDetail.setPackLengthUnit(LengthUnitEnum.valueOf(packLengthUnit));
            productSkuDetail.setPackWeight(packWeight);
            productSkuDetail.setPackWeightUnit(WeightUnitEnum.valueOf(packWeightUnit));

            productSku.setSkuDetail(productSkuDetail);
            productSkus.add(productSku);

            //SKU规格集合
            List<ProductIntactInfoUpdateBo.SpecComposeList> specComposeList = skuBody.getSpecComposeList();
            if (CollUtil.isEmpty(specComposeList)) {
                throw new RStatusCodeException(ZSMallStatusCodeEnum.PRODUCT_SKU_REQUIRES_AT_LEAST_ONE_SPECIFICATION);
            }
            specComposeListMap.put(productSku.getProductSkuCode(), specComposeList);
        }
        //设置商品SKU库存
        setProductSkuStock(productSkus, product.getShelfState().equals(ShelfStateEnum.ForcedOffShelf));

        iProductSkuService.saveOrUpdateBatch(productSkus);
        if (CollUtil.isNotEmpty(delProductSkuIds)) {
            // validSkuId不为空时，则说明有的SKU已被前端删除，此时要删除数据库中的数据
            List<ProductSku> delSku = TenantHelper.ignore(() -> iProductSkuService.listByIds(delProductSkuIds));
            List<String> itemNoList = delSku.stream().map(ProductSku::getProductSkuCode).collect(Collectors.toList());
            //判断是否 存在进行中的意向单，存在则无法删除
            List<WholesaleIntentionOrderItem> wholesaleIntentionOrderItems = iWholesaleIntentionOrderItemService.existsProcessingOrderItem(itemNoList);
            if (CollUtil.isNotEmpty(wholesaleIntentionOrderItems)) {
                List<String> collect = wholesaleIntentionOrderItems.stream().map(WholesaleIntentionOrderItem::getProductSkuCode).distinct().collect(Collectors.toList());
                throw new RStatusCodeException(ZSMallStatusCodeEnum.HAS_INTENTION_NOT_DEL_ERROR.args(collect));
            }
            iProductSkuService.deleteWithValidByIds(delProductSkuIds, false);
            iProductSkuDetailService.deleteByProductSkuIdList(delProductSkuIds);
            iProductSkuStockService.deleteByProductSkuIdList(delProductSkuIds);
        }
        List<ProductSkuDetail> skuDetailList = new ArrayList<>();
        List<ProductSkuAttachment> attachmentList = new ArrayList<>();
        List<ProductSkuAttribute> delSkuAttributeList = new ArrayList<>();
        List<ProductSkuAttribute> saveSkuAttributeList = new ArrayList<>();
        for (ProductSku sku : productSkus) {
            ProductSkuDetail skuDetail = sku.getSkuDetail();
            skuDetail.setProductSkuId(sku.getId());
            skuDetailList.add(skuDetail);

            List<ProductSkuAttachment> skuAttachmentList = sku.getSkuAttachmentList();
            skuAttachmentList.stream().forEach(attachment -> attachment.setProductSkuId(sku.getId()));
            attachmentList.addAll(skuAttachmentList);

            // 处理规格属性，编辑时，如果之前的规格组合和现在的不一样，才需要新增记录
            String oldSpecValName = oldSpecValNameMap.get(sku.getProductSkuCode());
            List<ProductIntactInfoUpdateBo.SpecComposeList> specComposeLists = specComposeListMap.get(sku.getProductSkuCode());
            List<String> values = specComposeLists.stream().map(ProductIntactInfoUpdateBo.SpecComposeList::getValue).collect(Collectors.toList());
            String specValName = StrUtil.join("/", values);
            if (!StrUtil.equals(oldSpecValName, specValName)) {
                for (int i = 0; i < specComposeLists.size(); i++) {
                    ProductIntactInfoUpdateBo.SpecComposeList specCompose = specComposeLists.get(i);
                    Long sourceId = specCompose.getSourceId();
                    String key = specCompose.getKey();
                    String value = specCompose.getValue();

                    ProductSkuAttribute skuAttribute = new ProductSkuAttribute();
                    skuAttribute.setProductSkuId(sku.getId());
                    skuAttribute.setAttributeType(AttributeTypeEnum.OptionalSpec);
                    skuAttribute.setAttributeSort(i);
                    skuAttribute.setAttributeName(key);
                    skuAttribute.setAttributeValue(value);
                    skuAttribute.setAttributeSourceId(sourceId);
                    saveSkuAttributeList.add(skuAttribute);
                }
                List<ProductSkuAttribute> oldSkuAttributeList = iProductSkuAttributeService.queryByProductSkuId(sku.getId());
                if (CollUtil.isNotEmpty(oldSkuAttributeList)) {
                    delSkuAttributeList.addAll(oldSkuAttributeList);
                }
            }
        }
        if (CollUtil.isNotEmpty(delSkuAttributeList)) {
            iProductSkuAttributeService.deleteByEntityList(delSkuAttributeList);

        }
        iProductSkuAttributeService.insertBatch(saveSkuAttributeList);
        iProductSkuDetailService.saveOrUpdateBatch(skuDetailList);
        iProductSkuAttachmentService.saveOrUpdateBatch(attachmentList);
        return productSkus;
    }

    /**
     * 处理批发商品附件
     *
     * @param addNew
     * @param product
     * @param productAttachment
     */
    private void updateProductAttachmentsBySku(boolean addNew, Product product,
                                               ProductAttachment productAttachment) {
        if (productAttachment != null) {
            if (!addNew) {
                boolean exist = false;
                List<ProductAttachment> attachments = iProductAttachmentService.queryByProductIdOrderBySortAsc(product.getId(), null);
                if (CollUtil.isNotEmpty(attachments)) {
                    List<ProductAttachment> delProductAttachments = new ArrayList<>();
                    for (ProductAttachment pa : attachments) {
                        if (StringUtils.equals(pa.getAttachmentSavePath(), productAttachment.getAttachmentSavePath())) {
                            exist = true;
                        } else {
                            delProductAttachments.add(BeanUtil.copyProperties(pa, ProductAttachment.class));
                        }
                    }
                    iProductAttachmentService.removeByIds(delProductAttachments);
                }
                if (!exist) {
                    iProductAttachmentService.saveOrUpdate(productAttachment);
                }
            }
        }
    }

    /**
     * 根据上/下架状态设置SKU库存
     *
     * @param productSkuList
     * @param forcedOffShelf       是否下架
     */
    private void setProductSkuStock(List<ProductSku> productSkuList, boolean forcedOffShelf) {
        if (CollUtil.isNotEmpty(productSkuList)) {
            for (ProductSku productSku : productSkuList) {
                Long productSkuId = productSku.getId();
                if (productSkuId != null) {
                    if (forcedOffShelf) {  // 强制下架，需要清空该SKU的库存
                        productSku.setStockTotal(0);
                    } else {  // 上架，需要重新计算库存
                        reStatisticalStock(productSku);
                    }
                    //创建同步任务
                    productSupport.createSyncTask(productSkuId);
                }
            }
        }
    }

    /**
     * 重新统计库存
     */
    private void reStatisticalStock(ProductSku productSku) {
        StockManagerEnum stockManager = productSku.getStockManager();

        if (Objects.equals(stockManager, StockManagerEnum.OwnWarehouse)) {
            Integer sumStockTotal = 0;
            if (Objects.equals(productSku.getShelfState(), ShelfStateEnum.OnShelf)) {
                sumStockTotal = iProductSkuStockService.sumStockTotal(productSku.getProductSkuCode());
            }
            productSku.setStockTotal(sumStockTotal);
        } else if (Objects.equals(stockManager, StockManagerEnum.BizArk)) {

        } else {
            productSku.setStockTotal(0);
        }
    }


    /**
     * 设置库存配置
     *
     * @param quantity
     * @param warehouseSystemCode
     * @param logisticsTemplateNo
     * @param productSku
     * @return
     * @throws RStatusCodeException
     */
    private void setInventoryConfig(Integer quantity, String warehouseSystemCode, String logisticsTemplateNo, ProductSku productSku) throws RStatusCodeException {

        String productSkuCode = productSku.getProductSkuCode();
        String erpSku = productSku.getErpSku();
        String productCode = productSku.getProductCode();
        ShelfStateEnum shelfState = productSku.getShelfState();
        List<Long> deleteSkuStockIds = iProductSkuStockService.queryIdsByProductSkuCode(productSkuCode);
        // 处理库存
        if (quantity == null) {
            quantity = 0;
        }

        ProductSkuStock productSkuStock = iProductSkuStockService.queryByProductSkuCode(productSkuCode, warehouseSystemCode);
        if (productSkuStock == null) {
            productSkuStock = new ProductSkuStock();
            String stockCode = productCodeGenerator.codeGenerate(BusinessCodeEnum.StockCode);
            productSkuStock.setStockCode(stockCode);
            productSkuStock.setStockReserved(0);
        } else {
            deleteSkuStockIds.remove(productSkuStock.getId());
        }

        productSkuStock.setStockTotal(quantity);
        productSkuStock.setStockAvailable(quantity);
        productSkuStock.setStockState(GlobalStateEnum.Valid);
        productSkuStock.setErpSku(erpSku);
        productSkuStock.setProductCode(productCode);
        productSkuStock.setProductSkuCode(productSkuCode);
        productSkuStock.setWarehouseSystemCode(warehouseSystemCode);
        productSkuStock.setLogisticsTemplateNo(StrUtil.emptyToNull(logisticsTemplateNo));

        iProductSkuStockService.saveOrUpdate(productSkuStock);
        if (CollUtil.isNotEmpty(deleteSkuStockIds)) {
            iProductSkuStockService.deleteWithValidByIds(deleteSkuStockIds, false);
        }
        if (Objects.equals(shelfState, ShelfStateEnum.ForcedOffShelf)) {
            // 如果满足主商品已下架，Sku不为有效，则强制库存为0
            productSku.setStockTotal(0);
        }
    }


    /**
     * 商品sku附件信息修改
     *
     * @param productSkuId
     * @param attachmentList
     * @return
     */
    public List<ProductSkuAttachment> updateProductSkuAttachments(Long productSkuId,
                                                                  List<ProductAttachmentBo> attachmentList, AttachmentTypeEnum attachmentType) {
        List<ProductSkuAttachment> productSkuAttachments = new ArrayList<>();
        List<Long> oldIds = new ArrayList<>();

        for (int i = 0; i < attachmentList.size(); i++) {
            ProductAttachmentBo attachmentBo = attachmentList.get(i);
            Long oldId = attachmentBo.getId();
            if (oldId != null) {
                oldIds.add(oldId);
                ProductSkuAttachment pa = iProductSkuAttachmentService.queryById(oldId);
                pa.setAttachmentSort(i);
                productSkuAttachments.add(pa);
            } else {
                ProductSkuAttachment pa = new ProductSkuAttachment();
                pa.setProductSkuId(productSkuId);
                pa.setAttachmentName(attachmentBo.getAttachmentName());
                pa.setAttachmentSavePath(attachmentBo.getAttachmentSavePath());
                pa.setAttachmentSort(i);
                pa.setAttachmentShowUrl(attachmentBo.getAttachmentShowUrl());
                pa.setOssId(Long.valueOf(attachmentBo.getOssId()));
                pa.setAttachmentType(attachmentType);
                pa.setAttachmentSuffix(attachmentBo.getAttachmentType());
                productSkuAttachments.add(pa);
            }
        }

        log.info("oldIds = {}", JSONUtil.toJsonStr(oldIds));
        //清除需要的图片
        List<ProductSkuAttachment> delAttachmentList = iProductSkuAttachmentService.queryBySkuIdAndAttachmentTypeOrderBySortAsc(productSkuId, attachmentType);
        if (CollUtil.isNotEmpty(delAttachmentList)) {
            List<ProductSkuAttachment> cDelAttachmentList = CollUtil.newCopyOnWriteArrayList(delAttachmentList);
            for (ProductSkuAttachment pa : cDelAttachmentList) {
                if (oldIds.contains(pa.getId())) {
                    delAttachmentList.remove(pa);
                }
            }
        }
        if (CollUtil.isNotEmpty(delAttachmentList)) {
            iProductSkuAttachmentService.removeByIds(delAttachmentList);
        }
        return productSkuAttachments;
    }


    /**
     * 处理商品其他附件
     *
     * @param productId
     * @param attachmentBo
     * @return
     */
    public ProductAttachment updateProductAttachments(Long productId, ProductAttachmentBo attachmentBo) {
        ProductAttachment otherAttachment = new ProductAttachment();
        otherAttachment.setAttachmentSort(999);
        otherAttachment.setAttachmentType(AttachmentTypeEnum.File);
        otherAttachment.setProductId(productId);
        otherAttachment.setAttachmentName(attachmentBo.getAttachmentName());
        otherAttachment.setOssId(Long.valueOf(attachmentBo.getOssId()));
        otherAttachment.setAttachmentSavePath(attachmentBo.getAttachmentSavePath());
        otherAttachment.setAttachmentShowUrl(attachmentBo.getAttachmentShowUrl());
        return otherAttachment;
    }


    /**
     * 必填校验 (保存国外新货商品信息)
     *
     * @return
     */
    private ZSMallStatusCodeEnum saveProductRequiredVerification(WholesaleProductBo bo) {
        if (ObjectUtil.isEmpty(bo)) {
            return ZSMallStatusCodeEnum.REQUIRED_CANNOT_EMPTY;
        }
        //商品信息必填 校验
        String name = bo.getProductName();
        Integer minimumOrderQuantity = bo.getMinimumOrderQuantity();
        BigDecimal depositRatio = bo.getDepositRatio();
        Integer reservedDay = bo.getReservedDay();
        JSONArray deliveryTypeList = bo.getDeliveryTypeList();
        String warehouseCode = bo.getWarehouseSystemCode();
        List<ProductWholesalePriceVo> wholesalePriceBodyList = bo.getWholesalePriceBodyList();
        String logisticsTemplateNo = bo.getLogisticsTemplateNo();
        if (!ObjectUtil.isAllNotEmpty(minimumOrderQuantity, depositRatio, warehouseCode, name, logisticsTemplateNo,
            reservedDay) || CollUtil.size(deliveryTypeList) == 0) {
            //商品规格属性相关信息必填
            return ZSMallStatusCodeEnum.SKU_ATTRIBUTES_REQUIRE;
        }
        //发货类型校验
        for (Object deliveryType : JSONUtil.parseArray(deliveryTypeList)) {
            try {
                WholesaleDeliveryType.valueOf(ObjectUtil.toString(deliveryType));
            } catch (Exception e) {
                return ZSMallStatusCodeEnum.WHOLESALE_DELIVERY_TYPE_NOT_EXIST;
            }
        }
        List<ProductSkuWholesaleBo> skuList = bo.getSkuList();
        Integer totalInventory = 0;
        if (CollUtil.size(skuList) > 0) {
            for (ProductSkuWholesaleBo skuBo : skuList) {
                totalInventory += skuBo.getQuantity();
                if (!ObjectUtil.isAllNotEmpty(skuBo.getQuantity(), skuBo.getSkuCode(), skuBo.getLength(), skuBo.getHeight(), skuBo.getWidth(),
                    skuBo.getLengthUnit(), skuBo.getWeight(), skuBo.getWeightUnit())) {
                    //商品规格属性相关信息必填
                    return ZSMallStatusCodeEnum.SKU_ATTRIBUTES_REQUIRE;
                }
                if (!skuBo.getSamePacking() && !ObjectUtil.isAllNotEmpty(skuBo.getPackLength(), skuBo.getPackWidth(),
                    skuBo.getPackHeight(), skuBo.getPackLengthUnit(), skuBo.getPackWeight(), skuBo.getPackWeightUnit())) {
                    return ZSMallStatusCodeEnum.SKU_ATTRIBUTES_REQUIRE;
                }
                //sku附件校验：至少要有一张图片
                if (CollUtil.isEmpty(skuBo.getImageList()) && CollUtil.isEmpty(skuBo.getVideoList())) {
                    return ZSMallStatusCodeEnum.SKU_REQUIRES_ONE_IMAGE;
                }
            }
        }
        //总库存不能小于最小起订量
        if (totalInventory < minimumOrderQuantity) {
            return ZSMallStatusCodeEnum.WHOLESALE_INVENTORY_ERROR;
        }
        //批发价格体系校验
        if (CollUtil.size(wholesalePriceBodyList) == 0) {
            //批发价格必填
            return ZSMallStatusCodeEnum.WHOLESALE_PRICE_SETUP_ERROR;
        }
        Integer minValue = null;
        List<Integer> quantityList = new ArrayList<>();
        for (ProductWholesalePriceVo wholesalePriceBody : wholesalePriceBodyList) {
            Integer minQuantity = wholesalePriceBody.getMinQuantity();
            if (CollUtil.contains(quantityList, minQuantity)) {
                //商品数量区间设置有误
                return ZSMallStatusCodeEnum.WHOLESALE_PRICE_SETUP_ERROR;
            }
            quantityList.add(minQuantity);
            if (minQuantity == null) {
                //商品数量范围设置有误
                return ZSMallStatusCodeEnum.WHOLESALE_PRICE_SETUP_ERROR;
            }
            if (!ObjectUtil.isAllNotEmpty(minQuantity, wholesalePriceBody.getDealDate(), wholesalePriceBody.getEstimatedOperationalFee(),
                wholesalePriceBody.getEstimatedShoppingFee()) || CollUtil.size(wholesalePriceBody.getAttributePrices()) == 0) {
                //价格体系数据必填
                return ZSMallStatusCodeEnum.WHOLESALE_PRICE_SETUP_ERROR;
            }
            minValue = minValue == null ? minQuantity : minValue > minQuantity ? minQuantity : minValue;
        }
        //数量范围校验：最小起订范围>=最小起订量
        if (minimumOrderQuantity > minValue) {
            //数量范围设置有误
            return ZSMallStatusCodeEnum.WHOLESALE_PRICE_SETUP_ERROR;
        }
        return ZSMallStatusCodeEnum.REQUEST_SUCCESS;
    }

    /**
     * 检查sku是否重复
     *
     * @param skuList
     * @return
     */
    private String checkIfSkuOrUpcIsDuplicate(List<ProductSkuWholesaleBo> skuList) {
        if (CollUtil.isNotEmpty(skuList)) {
            List<String> skuCodes = new ArrayList<>();
            List<String> skuUpcs = new ArrayList<>();
            for (ProductSkuWholesaleBo body : skuList) {
                String skuCode = StrUtil.trim(body.getSkuCode());
                String skuUpc = StrUtil.trim(body.getUpc());
                // 检查本次提交的sku数据中是否有重复sku
                if (skuCodes.contains(skuCode)) {
                    return "SKU;" + skuCode;
                } else if (StrUtil.isNotBlank(skuUpc) && skuUpcs.contains(skuUpc)) {
                    return "UPC;" + skuUpc;
                } else {
                    // 检查数据库中是否有未删除的除了自身以外的重复的sku
                    Long id = body.getId();
                    boolean existSku;
                    boolean existUpc;
                    if (id == null) {
                        existSku = iProductSkuService.existSku(skuCode, LoginHelper.getTenantId(), null);
                        existUpc = StrUtil.isNotBlank(skuUpc) ? iProductSkuService.existUpc(skuUpc, null) : false;
                    } else {
                        existSku = iProductSkuService.existSku(skuCode, LoginHelper.getTenantId(), id);
                        existUpc = StrUtil.isNotBlank(skuUpc) ? iProductSkuService.existUpc(skuUpc, id) : false;
                    }
                    if (existSku) {
                        return "SKU;" + skuCode;
                    }
                    if (existUpc) {
                        return "UPC;" + skuUpc;
                    }
                    skuCodes.add(skuCode);
                    if (StrUtil.isNotBlank(skuUpc)) {
                        skuUpcs.add(skuUpc);
                    }
                }
            }
        }
        return null;
    }


}
