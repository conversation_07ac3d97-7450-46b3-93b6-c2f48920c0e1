package com.zsmall.product.biz.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.hengjian.common.core.exception.RStatusCodeException;
import com.hengjian.common.core.utils.MapstructUtils;
import com.hengjian.common.mybatis.core.page.PageQuery;
import com.hengjian.common.mybatis.core.page.TableDataInfo;
import com.hengjian.common.satoken.utils.LoginHelper;
import com.hengjian.common.tenant.helper.TenantHelper;
import com.zsmall.common.enums.product.AttributeBelongEnum;
import com.zsmall.common.enums.product.AttributeScopeEnum;
import com.zsmall.common.enums.product.BindingCategoryEnum;
import com.zsmall.common.enums.statuscode.ZSMallStatusCodeEnum;
import com.zsmall.product.biz.service.ProductGlobalAttributeService;
import com.zsmall.product.entity.domain.ProductCategoryGlobalAttribute;
import com.zsmall.product.entity.domain.ProductGlobalAttribute;
import com.zsmall.product.entity.domain.bo.ProductGlobalAttributeBo;
import com.zsmall.product.entity.domain.bo.productGlobalAttribute.ProductGlobalAttributeSelectBo;
import com.zsmall.product.entity.domain.vo.ProductGlobalAttributeVo;
import com.zsmall.product.entity.domain.vo.productGlobalAttribute.ProductGlobalAttributeSelectVo;
import com.zsmall.product.entity.domain.vo.productGlobalAttribute.ProductGlobalAttributeSimpleVo;
import com.zsmall.product.entity.iservice.IProductCategoryGlobalAttributeService;
import com.zsmall.product.entity.iservice.IProductGlobalAttributeService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

/**
 * 商品全局属性Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-05-19
 */
@RequiredArgsConstructor
@Service
public class ProductGlobalAttributeServiceImpl implements ProductGlobalAttributeService {

    private final IProductGlobalAttributeService iProductGlobalAttributeService;
    private final IProductCategoryGlobalAttributeService iProductCategoryGlobalAttributeService;

    /**
     * 查询商品全局属性
     */
    @Override
    public ProductGlobalAttributeVo queryById(Long id) {
        ProductGlobalAttributeVo productGlobalAttributeVo = iProductGlobalAttributeService.getBaseMapper().selectVoById(id);
        String bindingCategory = productGlobalAttributeVo.getBindingCategory();
        if (BindingCategoryEnum.SpecifyCategory.name().equals(bindingCategory)) {
            List<Long> specifyCategoryIds = iProductCategoryGlobalAttributeService.queryCategoryIdsByGlobalAttributeId(id);
            productGlobalAttributeVo.setSpecifyCategoryIds(specifyCategoryIds);
        }
        return productGlobalAttributeVo;
    }

    /**
     * 查询商品全局属性（无视租户）
     */
    public ProductGlobalAttribute queryByIdNotTenant(Long id) {
        return iProductGlobalAttributeService.queryByIdNotTenant(id);
    }

    /**
     * 查询商品全局属性列表
     */
    @Override
    public TableDataInfo<ProductGlobalAttributeVo> queryPageList(ProductGlobalAttributeBo bo, PageQuery pageQuery) {
        return iProductGlobalAttributeService.queryPageList(bo, pageQuery);
    }

    /**
     * 查询商品全局属性列表
     */
    @Override
    public List<ProductGlobalAttributeVo> queryList(ProductGlobalAttributeBo bo) {
        return iProductGlobalAttributeService.queryList(bo);
    }

    /**
     * 查询商品全局属性列表（提供给下拉选使用）
     *
     * @return
     */
    @Override
    public ProductGlobalAttributeSelectVo queryListForSelect(ProductGlobalAttributeSelectBo bo) throws RStatusCodeException {
        Long categoryId = bo.getCategoryId();
        if (categoryId == null) {
            throw new RStatusCodeException(ZSMallStatusCodeEnum.REQUIRED_CANNOT_EMPTY);
        }

        // 查询供货商自己设置的可选规格
        List<ProductGlobalAttributeSimpleVo> s_optionalSpecList = iProductGlobalAttributeService.queryByProductCategoryId(categoryId, AttributeBelongEnum.Supplier.getValue(), AttributeScopeEnum.OptionalSpec.getValue());
        // 查询供货商自己设置的通用规格
        List<ProductGlobalAttributeSimpleVo> s_genericSpecList = iProductGlobalAttributeService.queryByProductCategoryId(categoryId, AttributeBelongEnum.Supplier.getValue(), AttributeScopeEnum.GenericSpec.getValue());

        // 查询平台设置的通用规格
        List<ProductGlobalAttributeSimpleVo> p_genericSpecList = TenantHelper.ignore(() -> iProductGlobalAttributeService.queryByProductCategoryId(categoryId,
            AttributeBelongEnum.Platform.getValue(),
            AttributeScopeEnum.GenericSpec.getValue()));

        List<ProductGlobalAttributeSimpleVo> optionalSpec = BeanUtil.copyToList(s_optionalSpecList, ProductGlobalAttributeSimpleVo.class);
        List<ProductGlobalAttributeSimpleVo> genericSpec = BeanUtil.copyToList(s_genericSpecList, ProductGlobalAttributeSimpleVo.class);
        if (p_genericSpecList == null) {
            p_genericSpecList = new ArrayList<>();
        }
        p_genericSpecList.addAll(genericSpec);

        return new ProductGlobalAttributeSelectVo(optionalSpec, p_genericSpecList);
    }

    /**
     * 查询商品全局属性列表（提供给分类绑定属性使用）
     *
     * @return
     * @throws RStatusCodeException
     */
    @Override
    public List<ProductGlobalAttributeSimpleVo> queryListForCategory(ProductGlobalAttributeSelectBo bo) {
        Long categoryId = bo.getCategoryId();
        List<ProductGlobalAttributeSimpleVo> p_genericSpecList = iProductGlobalAttributeService.queryForCategory(AttributeBelongEnum.Platform.getValue(),
            AttributeScopeEnum.GenericSpec.getValue());
        return p_genericSpecList;
    }

    /**
     * 新增商品全局属性
     */
    @Override
    public Boolean insertByBo(ProductGlobalAttributeBo bo) throws RStatusCodeException {
        String tenantType = LoginHelper.getTenantType();
        List<Long> specifyCategoryIds = bo.getSpecifyCategoryIds();
        ProductGlobalAttribute add = MapstructUtils.convert(bo, ProductGlobalAttribute.class);

        if (StrUtil.equals(tenantType, "Manager")) {
            add.setAttributeScope(AttributeScopeEnum.GenericSpec);
            add.setAttributeBelong(AttributeBelongEnum.Platform);
        } else {
            add.setAttributeBelong(AttributeBelongEnum.Supplier);
        }

        iProductGlobalAttributeService.validEntityBeforeSave(add, specifyCategoryIds);
        boolean flag = iProductGlobalAttributeService.save(add);
        Long globalAttributeId = add.getId();

        BindingCategoryEnum bindingCategory = add.getBindingCategory();
        if (BindingCategoryEnum.SpecifyCategory.equals(bindingCategory) && CollUtil.isNotEmpty(specifyCategoryIds)) {
            List<ProductCategoryGlobalAttribute> categoryGlobalAttributeList = new ArrayList<>();
            for (Long specifyCategoryId : specifyCategoryIds) {
                ProductCategoryGlobalAttribute categoryGlobalAttribute = new ProductCategoryGlobalAttribute();
                categoryGlobalAttribute.setProductCategoryId(specifyCategoryId);
                categoryGlobalAttribute.setGlobalAttributeId(globalAttributeId);
                categoryGlobalAttribute.setIsRequired(bo.getIsRequired());
                categoryGlobalAttributeList.add(categoryGlobalAttribute);
            }
            iProductCategoryGlobalAttributeService.saveBatch(categoryGlobalAttributeList);
        }
        return flag;
    }

    /**
     * 修改商品全局属性
     */
    @Override
    public Boolean updateByBo(ProductGlobalAttributeBo bo) throws RStatusCodeException {
        String tenantType = LoginHelper.getTenantType();
        List<Long> specifyCategoryIds = bo.getSpecifyCategoryIds();
        ProductGlobalAttribute update = MapstructUtils.convert(bo, ProductGlobalAttribute.class);

        if (StrUtil.equals(tenantType, "Manager")) {
            update.setAttributeBelong(AttributeBelongEnum.Platform);
            update.setAttributeScope(AttributeScopeEnum.GenericSpec);
        }

        iProductGlobalAttributeService.validEntityBeforeSave(update, specifyCategoryIds);
        boolean flag = iProductGlobalAttributeService.updateById(update);
        Long globalAttributeId = update.getId();

        BindingCategoryEnum bindingCategory = update.getBindingCategory();
        if (BindingCategoryEnum.SpecifyCategory.equals(bindingCategory) && CollUtil.isNotEmpty(specifyCategoryIds)) {
            List<ProductCategoryGlobalAttribute> categoryGlobalAttributeList = new ArrayList<>();
            boolean rebuild = false;

            for (Long specifyCategoryId : specifyCategoryIds) {
                ProductCategoryGlobalAttribute cga = iProductCategoryGlobalAttributeService.queryByCategoryIdAndGlobalAttributeId(specifyCategoryId, globalAttributeId);
                // 出现一个分类关联不正确，说明改动了关联的分类，需要全删全增
                if (cga == null) {
                    rebuild = true;
                    categoryGlobalAttributeList.clear();
                    break;
                }
                cga.setProductCategoryId(specifyCategoryId);
                cga.setGlobalAttributeId(globalAttributeId);
                cga.setIsRequired(bo.getIsRequired());
                categoryGlobalAttributeList.add(cga);
            }

            // 全删全增
            if (rebuild) {
                for (Long specifyCategoryId : specifyCategoryIds) {
                    ProductCategoryGlobalAttribute cga = new ProductCategoryGlobalAttribute();
                    cga.setProductCategoryId(specifyCategoryId);
                    cga.setGlobalAttributeId(globalAttributeId);
                    cga.setIsRequired(bo.getIsRequired());
                    categoryGlobalAttributeList.add(cga);
                }
                iProductCategoryGlobalAttributeService.deleteByGlobalAttributeId(globalAttributeId);
            }
            iProductCategoryGlobalAttributeService.saveOrUpdateBatch(categoryGlobalAttributeList);
        } else {
            iProductCategoryGlobalAttributeService.deleteByGlobalAttributeId(globalAttributeId);
        }
        return flag;
    }

    /**
     * 批量删除商品全局属性
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        return iProductGlobalAttributeService.deleteWithValidByIds(ids, isValid);
    }
}
