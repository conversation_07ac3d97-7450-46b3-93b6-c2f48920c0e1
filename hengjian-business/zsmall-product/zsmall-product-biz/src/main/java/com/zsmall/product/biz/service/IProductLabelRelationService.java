package com.zsmall.product.biz.service;

import com.zsmall.product.entity.domain.ProductLabel;
import com.zsmall.product.entity.domain.ProductLabelRelation;

import java.util.List;

/**
 * 商品标签关联Service接口
 *
 * <AUTHOR> Li
 * @date 2023-05-31
 */
public interface IProductLabelRelationService {


    void removeByProductId(Long productId);

    void saveBatch(List<ProductLabelRelation> relationEntityList);

    ProductLabelRelation getByProductIdAndLabelId(Long productId, Long labelId);

    void removeByLabelIdAndProductId(Long labelId, Long productId);

    /**
     * 查询商品对应的标签 --  一件商品绑定多个标签
     *
     * @param productId
     * @return
     */
    List<ProductLabel> getLabelByProductId(Long productId);

    /**
     * 根据标签id集合移除绑定的标签
     *
     * @param labelIdList
     */
    void removeByLabelIdList(List<Long> labelIdList);

    /**
     * 根据标签id集合查询绑定到商品
     *
     * @param labelIdList
     * @return
     */
    List<ProductLabelRelation> getByLabelIdList(List<Long> labelIdList);

    /**
     * 根据标签id查询商品信息
     *
     * @param labelId
     * @return
     */
    List<Long> getProductIdListByLabelId(Long labelId);

}
