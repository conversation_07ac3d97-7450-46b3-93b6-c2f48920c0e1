package com.zsmall.product.biz.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;
import com.hengjian.common.core.domain.R;
import com.hengjian.common.core.exception.RStatusCodeException;
import com.hengjian.common.core.utils.MapstructUtils;
import com.hengjian.common.mybatis.core.page.PageQuery;
import com.hengjian.common.mybatis.core.page.TableDataInfo;
import com.zsmall.common.enums.BusinessCodeEnum;
import com.zsmall.common.enums.common.ChannelTypeEnum;
import com.zsmall.common.enums.priceLog.PriceOperateLog;
import com.zsmall.common.enums.productMapping.MarkUpTypeEnum;
import com.zsmall.common.enums.statuscode.ZSMallStatusCodeEnum;
import com.zsmall.product.biz.service.ProductSkuPriceRuleService;
import com.zsmall.product.biz.support.EsProductSupport;
import com.zsmall.product.biz.support.ProductSupport;
import com.zsmall.product.entity.domain.*;
import com.zsmall.product.entity.domain.bo.productSkuPrice.ApplicableProductSkuRuleBo;
import com.zsmall.product.entity.domain.bo.productSkuPrice.ProductSkuPriceBo;
import com.zsmall.product.entity.domain.bo.productSkuPrice.ProductSkuPriceRuleBo;
import com.zsmall.product.entity.domain.dto.productSku.ApplicableProductSkuDTO;
import com.zsmall.product.entity.domain.dto.productSku.ApplicableProductSkuParamDTO;
import com.zsmall.product.entity.domain.dto.productSkuPrice.ProductPriceChangeDto;
import com.zsmall.product.entity.domain.vo.product.ApplicableProductSkuVo;
import com.zsmall.product.entity.domain.vo.productSkuPrice.ProductSkuPriceRuleItemVo;
import com.zsmall.product.entity.domain.vo.productSkuPrice.ProductSkuPriceRuleRelationVo;
import com.zsmall.product.entity.domain.vo.productSkuPrice.ProductSkuPriceRuleVo;
import com.zsmall.product.entity.domain.vo.productSkuPrice.ProductSkuPriceVo;
import com.zsmall.product.entity.iservice.*;
import com.zsmall.product.entity.util.ProductCodeGenerator;
import com.zsmall.system.entity.domain.SiteCountryCurrency;
import com.zsmall.system.entity.iservice.ISiteCountryCurrencyService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 商品sku价格计算公式Service业务层处理
 *
 * <AUTHOR> Li
 * @date 2023-05-22
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class ProductSkuPriceRuleServiceImpl implements ProductSkuPriceRuleService {

    private final ISiteCountryCurrencyService iSiteCountryCurrencyService;
    private final IProductSkuPriceRuleService iProductSkuPriceRuleService;
    private final IProductSkuPriceService iProductSkuPriceService;
    private final IProductSkuPriceRuleItemService iProductSkuPriceRuleItemService;
    private final IProductSkuPriceRuleRelationService iProductSkuPriceRuleRelationService;
    private final ProductCodeGenerator productCodeGenerator;
    private final IProductSkuService iProductSkuService;
    private final IProductSkuPriceLogService iProductSkuPriceLogService;
    private final IProductMappingService iProductMappingService;

    private final ProductSupport productSupport;
    private final EsProductSupport esProductSupport;

    /**
     * 查询商品sku价格计算公式
     */
    @Override
    public ProductSkuPriceRuleVo queryById(Long id) {
        return iProductSkuPriceRuleService.queryById(id);
    }

    /**
     * 查询商品sku价格计算公式列表
     */
    @Override
    public TableDataInfo<ProductSkuPriceRuleVo> queryPageList(ProductSkuPriceRuleBo bo, PageQuery pageQuery) {
        return iProductSkuPriceRuleService.queryPageList(bo, pageQuery);
    }


    /**
     * 查询商品sku价格计算公式列表
     */
    @Override
    public List<ProductSkuPriceRuleVo> queryList(ProductSkuPriceRuleBo bo) {
        return iProductSkuPriceRuleService.queryList(bo);
    }

    /**
     * 根据ids查询商品sku价格计算公式列表
     *
     * @param ids
     * @return
     */
    @Override
    public List<ProductSkuPriceRuleVo> queryListByIds(List<Long> ids) {
        return iProductSkuPriceRuleService.queryListByIds(ids);
    }

    /**
     * 新增商品sku价格计算公式
     */
    @Override
    public Boolean insertByBo(ProductSkuPriceRuleBo bo) {
        return iProductSkuPriceRuleService.insertByBo(bo);
    }

    /**
     * 修改商品sku价格计算公式
     */
    @Override
    public Boolean updateByBo(ProductSkuPriceRuleBo bo) {
        return iProductSkuPriceRuleService.updateByBo(bo);
    }

    /**
     * 批量删除商品sku价格计算公式
     */
    @Override
    public R<Void> deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        return iProductSkuPriceRuleService.deleteWithValidByIds(ids, isValid);
    }

    /**
     * 适用值校验
     *
     * @param bo
     * @return
     */
    @Override
    public R<Void> checkApplicableValue(ProductSkuPriceRuleBo bo) {
        return iProductSkuPriceRuleService.checkApplicableValue(bo);
    }

    /**
     * 翻页获取适用产品（适用产品设置）
     *
     * @param bo
     * @param pageQuery
     * @return
     */
    @Override
    public TableDataInfo<ApplicableProductSkuVo> getApplicableProductSkuPage(ProductSkuPriceRuleBo bo, PageQuery pageQuery) throws RStatusCodeException {
        return iProductSkuPriceRuleService.getApplicableProductSkuPage(bo, pageQuery);

    }

    /**
     * 价格公式及使用产品保存
     *
     * @param bo
     * @return
     * @throws Exception
     */
    @Override
    @Transactional(rollbackFor = {Exception.class, RStatusCodeException.class})
    public R<Void> savePriceRule(ProductSkuPriceRuleBo bo) throws RStatusCodeException {
        log.info("调用【价格公式及使用产品保存】方法");
        log.info("接口请求参数：{} ", JSONUtil.toJsonStr(bo));

        try {
            String originalRuleCode = bo.getOriginalRuleCode();

            String ruleName = bo.getRuleName();
            Integer applicableType = bo.getApplicableType();
            JSONArray applicableValue = bo.getApplicableValue();
            Integer unitPriceCal = bo.getUnitPriceCal();
            BigDecimal unitPriceCalValue = bo.getUnitPrice();
            Integer operationFeeCal = bo.getOperationFeeCal();
            BigDecimal operationFeeCalValue = bo.getOperationFee();
            Integer finalDeliveryFeeCal = bo.getFinalDeliveryFeeCal();
            BigDecimal finalDeliveryFeeCalValue = bo.getFinalDeliveryFee();
            List<String> itemNoList = bo.getItemNoList();

            if (unitPriceCalValue == null || operationFeeCalValue == null || finalDeliveryFeeCalValue == null) {
                return R.fail(ZSMallStatusCodeEnum.REQUIRED_CANNOT_EMPTY);
            }

            ApplicableProductSkuParamDTO dto = new ApplicableProductSkuParamDTO();
            switch (applicableType) {
                case 1:
                    List<BigDecimal> priceRange = applicableValue.toList(BigDecimal.class);
                    dto.setStartPrice(priceRange.get(0));
                    dto.setEndPrice(priceRange.get(1));
                    break;
                case 2:
                    List<String> itemNos = applicableValue.toList(String.class);
                    dto.setItemNos(itemNos);
                    break;
                default:

            }

            ProductSkuPriceRule rule = null;

            //originalRuleCode不为空，则为编辑操作：如修改了规则，则原绑定的数据中不符合的解除绑定并重新匹配规则
            if (StrUtil.isNotBlank(originalRuleCode)) {
                //修改操作
                rule = updateProductSkuPriceRule(originalRuleCode, ruleName, applicableType, applicableValue, unitPriceCal, unitPriceCalValue, operationFeeCal, operationFeeCalValue, finalDeliveryFeeCal, finalDeliveryFeeCalValue, dto);
            } else {
                //新增操作
                rule = addProductSkuPriceRule(ruleName, applicableType, applicableValue, unitPriceCal, unitPriceCalValue, operationFeeCal, operationFeeCalValue, finalDeliveryFeeCal, finalDeliveryFeeCalValue);
            }

            //如果itemNos为空，则只添加公式不绑定关系
            if (CollUtil.isEmpty(itemNoList)) {
                return R.ok();
            }
            //根据itemNoList获取productSkuIds
            List<ProductSku> productSkuList = iProductSkuService.queryListByProductSkuCodes(itemNoList);
            List<Long> productSkuIds = productSkuList.stream().map(ProductSku::getId).collect(Collectors.toList());
            //如果没有如要绑定的商品，则只添加公式不绑定关系
            if (CollUtil.isEmpty(productSkuIds)) {
                return R.ok();
            }
            //新增公式后绑定公式
            bindFormulasAfterAdd(productSkuIds, rule);
            return R.ok();
        } catch (Exception e) {
            log.error("调用【价格公式及使用产品保存】方法 异常 error = {}", e.getMessage(), e);
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return R.fail(ZSMallStatusCodeEnum.PRODUCT_SKU_PRICE_RULE_SAVE_ERROR);
        }

    }

    /**
     * 修改公式
     *
     * @param originalRuleCode
     * @param ruleName
     * @param applicableType
     * @param applicableValue
     * @param unitPriceCal
     * @param unitPriceCalValue
     * @param operationFeeCal
     * @param operationFeeCalValue
     * @param finalDeliveryFeeCal
     * @param finalDeliveryFeeCalValue
     * @param dto
     * @return
     * @throws Exception
     */
    @NotNull
    private ProductSkuPriceRule updateProductSkuPriceRule(String originalRuleCode, String ruleName, Integer applicableType, JSONArray applicableValue, Integer unitPriceCal, BigDecimal unitPriceCalValue, Integer operationFeeCal, BigDecimal operationFeeCalValue, Integer finalDeliveryFeeCal, BigDecimal finalDeliveryFeeCalValue, ApplicableProductSkuParamDTO dto) throws Exception {
        ProductSkuPriceRule rule;
        rule = this.getByRuleCode(originalRuleCode);
        rule.setRuleName(ruleName);
        iProductSkuPriceRuleService.updateById(rule);
        //获取原计算公式和新计算公式
        List<ProductSkuPriceRuleItemVo> itemList = iProductSkuPriceRuleItemService.getItemListByRuleId(rule.getId());
        List<ProductSkuPriceRuleItemVo> newItemList = iProductSkuPriceRuleItemService.parseRuleItemListByRuleInfo(rule.getId(),
            unitPriceCal, unitPriceCalValue, operationFeeCal, operationFeeCalValue, finalDeliveryFeeCal, finalDeliveryFeeCalValue);
        //规则不变，判断计算公式是否变动，如果未变动则不修改原绑定商品价格，否则重新计算商品价格
        Boolean aBoolean = iProductSkuPriceRuleItemService.matchItems(itemList, newItemList);

        //根据适用类型和适用值匹配是否修改适用值，如果修改则修改公式、删除原先绑定的关系数据
        if (iProductSkuPriceRuleService.isChange(rule.getApplicableType(), applicableType, rule.getApplicableValue(), applicableValue)) {
            ProductSkuPriceRuleBo ruleBo = new ProductSkuPriceRuleBo(rule.getId(), ruleName, originalRuleCode, applicableType, applicableValue);
            boolean b = this.updateByBo(ruleBo);
            if (!b) {
                throw new Exception("Failed to update ProductSkuPriceRule.");
            }

            //获取原绑定该公式的商品价格数据
            List<Long> boundProductSkuPriceIds = iProductSkuPriceRuleRelationService.getByRuleId(rule.getId()).stream().map(ProductSkuPriceRuleRelationVo::getProductSkuPriceId).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(boundProductSkuPriceIds)) {
                //设置参数
                dto.setProductSkuPriceIds(boundProductSkuPriceIds);
                //通过筛选条件获取获取符合绑定关系的商品价格
                List<ApplicableProductSkuDTO> records = iProductSkuService.getApplicableProductSkuList(dto);
                log.info("通过筛选条件获取获取符合绑定关系的商品价格 size = {}, records = {}", CollUtil.size(records), JSONUtil.toJsonStr(records));
                List<Long> productSkuPriceIds = records.stream().map(ApplicableProductSkuDTO::getProductSkuPriceId).collect(Collectors.toList());
                boundProductSkuPriceIds.removeAll(productSkuPriceIds);
                //不符合当前公式的商品价格则匹配基础公式
                if (CollUtil.isNotEmpty(boundProductSkuPriceIds)) {
                    Boolean b1 = iProductSkuPriceRuleRelationService.deleteRelationByPriceIds(boundProductSkuPriceIds);
                    if (!b1) {
                        throw new Exception("Failed to delete ProductSkuPriceRuleRelation data.");
                    }
                    List<ProductSkuPriceRuleRelation> relations = new ArrayList<>();
                    //解绑的商品价格数据重新匹配并绑定新公式
                    List<ProductSkuPrice> productSkuPrices = new ArrayList<>();
                    List<ProductSkuPrice> oldPrices = iProductSkuPriceService.queryListByIds(boundProductSkuPriceIds);
                    //原商品价格若匹配当前修改的公式则绑定，否则自动匹配其他公式
                    for (ProductSkuPrice oldPrice : oldPrices) {
                        //匹配新公式
                        ProductSkuPrice price = this.matchRule(null, oldPrice.getOriginalUnitPrice(), oldPrice.getOriginalOperationFee(), oldPrice.getOriginalFinalDeliveryFee(), false);
                        if (price == null) {
                            break;
                        }
                        ProductSkuPriceRuleRelation relation = new ProductSkuPriceRuleRelation();
                        relation.setProductSkuPriceRuleId(price.getProductSkuPriceRuleId());
                        relation.setProductSkuPriceId(oldPrice.getId());
                        relation.setProductSkuId(oldPrice.getProductSkuId());
                        relation.setProductSkuCode(oldPrice.getProductSkuCode());
                        relations.add(relation);
                        ProductSkuPrice skuPriceDTO = this.calculateByRule(price.getProductSkuPriceRuleCode(), oldPrice.getOriginalUnitPrice(), oldPrice.getOriginalOperationFee(), oldPrice.getOriginalFinalDeliveryFee());
                        skuPriceDTO.setProductSkuId(oldPrice.getProductSkuId());
                        skuPriceDTO.setId(oldPrice.getId());
                        skuPriceDTO.setProductSkuId(oldPrice.getProductSkuId());
                        skuPriceDTO.setProductSkuCode(oldPrice.getProductSkuCode());
                        productSkuPrices.add(skuPriceDTO);
                    }

                    if (CollUtil.isNotEmpty(relations)) {
                        boolean saveRelation = iProductSkuPriceRuleRelationService.batchInsert(relations);
                        if (!saveRelation) {
                            throw new Exception("Failed to save ProductSkuPriceRuleRelation data.");
                        }
                        //修改新绑定的商品价格
                        iProductSkuPriceService.batchInsert(productSkuPrices);
                        subsequentPriceChanges(oldPrices, productSkuPrices);
                    }
                }
            }
        }

        //如果商品公式明细变动,则将继续符合该公式的商品价格重新计算
        if (!aBoolean) {
            //如果商品公式明细变动，则删除原先公式明细并添加新的公式明细
            Boolean result = iProductSkuPriceRuleItemService.updateDeleteMarkByRuleId(rule.getId());
            if (!result) {
                throw new Exception("Failed to delete ProductSkuPriceRuleItem.");
            }
            List<ProductSkuPriceRuleItem> newItems = new ArrayList<>();
            newItemList.forEach(item -> newItems.add(MapstructUtils.convert(item, ProductSkuPriceRuleItem.class)));
            boolean ruleItemResult = iProductSkuPriceRuleItemService.saveBatch(newItems);
            if (!ruleItemResult) {
                throw new Exception("Failed to update ProductSkuPriceRuleItem.");
            }
            //通过筛选条件获取获取符合绑定关系的商品价格
            List<ApplicableProductSkuDTO> records = iProductSkuService.getApplicableProductSkuList(dto);
            log.info("通过筛选条件获取获取符合绑定关系的商品价格 size = {}, records = {}", CollUtil.size(records), JSONUtil.toJsonStr(records));
            List<Long> productSkuPriceIds = records.stream().map(ApplicableProductSkuDTO::getProductSkuPriceId).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(productSkuPriceIds)) {
                List<ProductSkuPrice> oldPriceList = iProductSkuPriceService.queryListByIds(productSkuPriceIds);
                //记录价格变化日志
                for (ProductSkuPrice oldPrice : oldPriceList) {
                    iProductSkuPriceLogService.recordPriceChanges(oldPrice,  PriceOperateLog.Update.name());
                }
                List<ProductSkuPrice> dtoList = this.calculateProductSkuPricesByRuleCode(rule.getRuleCode(), oldPriceList);
                iProductSkuPriceService.saveOrUpdateBatch(dtoList);

                subsequentPriceChanges(oldPriceList, dtoList);
            }
        }
        return rule;
    }

    /**
     * 新增公式
     *
     * @param ruleName
     * @param applicableType
     * @param applicableValue
     * @param unitPriceCal
     * @param unitPriceCalValue
     * @param operationFeeCal
     * @param operationFeeCalValue
     * @param finalDeliveryFeeCal
     * @param finalDeliveryFeeCalValue
     * @return
     * @throws Exception
     */
    @NotNull
    private ProductSkuPriceRule addProductSkuPriceRule(String ruleName, Integer applicableType, JSONArray applicableValue, Integer unitPriceCal, BigDecimal unitPriceCalValue, Integer operationFeeCal, BigDecimal operationFeeCalValue, Integer finalDeliveryFeeCal, BigDecimal finalDeliveryFeeCalValue) throws Exception {
        ProductSkuPriceRule rule;
        //生成公式编码
        String newRuleCode = productCodeGenerator.codeGenerate(BusinessCodeEnum.CalculationRuleCode);
        ProductSkuPriceRule ruleEntity = new ProductSkuPriceRule(null, ruleName, newRuleCode, applicableType, applicableValue, "0");
        //创建价格公式
        if (!iProductSkuPriceRuleService.save(ruleEntity)) {
            throw new Exception("Failed to save new ProductSkuPriceRule.");
        }
        rule = ruleEntity;
        //设置价格公式明细信息
        List<ProductSkuPriceRuleItemVo> ruleItemVoList = iProductSkuPriceRuleItemService.parseRuleItemListByRuleInfo(ruleEntity.getId(),
            unitPriceCal, unitPriceCalValue, operationFeeCal, operationFeeCalValue, finalDeliveryFeeCal, finalDeliveryFeeCalValue);
        List<ProductSkuPriceRuleItem> itemList = new ArrayList<>();
        ruleItemVoList.forEach(ruleItemVo -> itemList.add(MapstructUtils.convert(ruleItemVo, ProductSkuPriceRuleItem.class)));
        boolean ruleItemResult = iProductSkuPriceRuleItemService.saveBatch(itemList);
        if (!ruleItemResult) {
            throw new Exception("Failed to save ProductSkuPriceRuleItem.");
        }
        return rule;
    }

    /**
     * 新增公式后绑定公式
     *
     * @param productSkuIds
     * @param rule
     * @return
     * @throws Exception
     */
    @Nullable
    private void bindFormulasAfterAdd(List<Long> productSkuIds, ProductSkuPriceRule rule) throws Exception {

        //通过productSkuIds删除绑定关系数据
        List<ProductSkuPriceRuleRelationVo> ruleRelationEntities = iProductSkuPriceRuleRelationService.getListByProductSkuIds(productSkuIds);
        if (CollUtil.isNotEmpty(ruleRelationEntities)) {
            boolean b = iProductSkuPriceRuleRelationService.batchDeletePriceRelationByProductSkuIds(productSkuIds);
        }
        //根据productSkuIds获取商品价格数据
        List<ProductSkuPrice> oldPrices = iProductSkuPriceService.getProductSkuPriceListByProductSkuIds(productSkuIds);
        //记录价格变化日志
        for (ProductSkuPrice oldPrice : oldPrices) {
            iProductSkuPriceLogService.recordPriceChanges(oldPrice,  PriceOperateLog.Update.name());
        }
        //计算商品价格
        List<ProductSkuPrice> dtoList = this.calculateProductSkuPricesByRuleCode(rule.getRuleCode(), oldPrices);
        iProductSkuPriceService.saveOrUpdateBatch(dtoList);
        subsequentPriceChanges(oldPrices, dtoList);
        //开始正常流程的绑定关系操作
        List<ProductSkuPriceRuleRelation> relationEntities = new ArrayList<>();
        Long ruleId = rule.getId();
        oldPrices.stream().forEach(p -> {
            ProductSkuPriceRuleRelation relationEntity = new ProductSkuPriceRuleRelation(p.getId(), p.getProductSkuId(), p.getProductSkuCode(), ruleId);
            relationEntities.add(relationEntity);
        });
        //添加新的商品sku价格计算公式关联数据
        boolean br = iProductSkuPriceRuleRelationService.batchInsert(relationEntities);
        if (!br) {
            throw new Exception("Failed to batch add new ProductSkuPriceRuleRelation data");
        }
    }

    /**
     * 价格变更后续（同步映射商品操作及发送价格变更通知等）
     *
     * @param oldPrices
     * @param newPrices
     */
    private void subsequentPriceChanges(List<ProductSkuPrice> oldPrices, List<ProductSkuPrice> newPrices) {

        // 价格变更List，搜集后统一发送通知
        List<ProductPriceChangeDto> productPriceChangeList = new ArrayList<>();
        // 所有还未执行的价格更新任务
        List<ProductMapping> productMappingList = new ArrayList<>();
        List<String> productSkuCodeList = new ArrayList<>();
        for (ProductSkuPrice newPrice : newPrices) {
            String productSkuCode = newPrice.getProductSkuCode();

            ProductSku productSku = iProductSkuService.queryByProductSkuCode(productSkuCode);
            //1、获取商品映射数据 2、修改映射数据的最总价格
            List<String> tenantIdList = new ArrayList<>();
            List<ProductMapping> productMappings = iProductMappingService.queryListByProductSkuCode(productSkuCode);
            for (ProductMapping productMapping : productMappings) {
                tenantIdList.add(productMapping.getTenantId());
                ChannelTypeEnum channelType = productMapping.getChannelType();
                MarkUpTypeEnum markUpType = productMapping.getMarkUpType();
                BigDecimal markUpValue = productMapping.getMarkUpValue();
                // Wayfair都是自提价，其他平台都是代发价
                BigDecimal basePrice = productSupport.getProductSkuPrice(channelType, newPrice);
                BigDecimal finalPrice = iProductMappingService.calculateFinalPrice(markUpType, markUpValue, basePrice);
                productMapping.setFinalPrice(finalPrice);
                productMappingList.add(productMapping);
            }

            List<ProductSkuPrice> collect = oldPrices.stream().filter(old -> old.getId().equals(newPrice.getId())).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(collect)) {
                ProductSkuPrice oldPrice = collect.get(0);
                ProductPriceChangeDto productPriceChangeDto = new ProductPriceChangeDto();
                productPriceChangeDto.setProductSkuCode(oldPrice.getProductSkuCode());
                productPriceChangeDto.setAfter_dropShippingPrice(newPrice.getPlatformDropShippingPrice());
                productPriceChangeDto.setBefore_dropShippingPrice(oldPrice.getPlatformDropShippingPrice());
                productPriceChangeDto.setAfter_unitPrice(newPrice.getPlatformUnitPrice());
                productPriceChangeDto.setBefore_unitPrice(oldPrice.getPlatformUnitPrice());
                productPriceChangeDto.setBefore_pickUpPrice(oldPrice.getPlatformPickUpPrice());
                productPriceChangeDto.setAfter_pickUpPrice(newPrice.getPlatformPickUpPrice());
                productPriceChangeDto.setBefore_operationFee(oldPrice.getPlatformOperationFee());
                productPriceChangeDto.setAfter_operationFee(newPrice.getPlatformOperationFee());
                productPriceChangeDto.setBefore_finalDeliveryFee(oldPrice.getPlatformFinalDeliveryFee());
                productPriceChangeDto.setAfter_finalDeliveryFee(newPrice.getPlatformFinalDeliveryFee());
                productPriceChangeDto.setBefore_msrp(oldPrice.getMsrp());
                productPriceChangeDto.setAfter_msrp(newPrice.getMsrp());
                productPriceChangeDto.setSupplierTenantId(productSku.getTenantId());
                productPriceChangeDto.setTenantIdList(tenantIdList);
                productPriceChangeList.add(productPriceChangeDto);
            }
            productSkuCodeList.add(productSkuCode);
        }

        if (CollUtil.isNotEmpty(productMappingList)) {
            iProductMappingService.batchSaveOrUpdateNoTenant(productMappingList);
        }

        // 同步至ES
        if (CollUtil.isNotEmpty(productSkuCodeList)) {
            esProductSupport.productSkuUpload(ArrayUtil.toArray(productSkuCodeList, String.class));
        }

//        log.info("价格变更List，搜集后统一发送通知 productPriceChangeList = {}", JSONUtil.toJsonStr(productPriceChangeList));
        productSupport.toPushProductPriceChangeEvent(productPriceChangeList);

    }

    /**
     * 通过公式ruleCode获取适用产品（翻页）
     *
     * @param bo
     * @param pageQuery
     * @return
     */
    @Override
    public TableDataInfo<ApplicableProductSkuVo> getApplicableProductSkuByRuleCodePage(ApplicableProductSkuRuleBo bo, PageQuery pageQuery) throws RStatusCodeException {
        return iProductSkuPriceRuleService.getApplicableProductSkuByRuleCodePage(bo, pageQuery);

    }


    /**
     * 导出适用产品
     *
     * @param bo
     * @param response
     * @return
     */
   /* @Override
    public R<Void> exportApplicableProductSku(ApplicableProductSkuRuleBo bo, HttpServletResponse response) {
        return iProductSkuPriceRuleService.exportApplicableProductSku(bo, response);
    }
*/

    /**
     * 删除适用产品绑定(基础公式绑定的数据无法删除，优先级高的数据删除后需自动匹配基础公式进行绑定，如果没有则抛出异常)
     *
     * @param bo
     * @return
     */
    @Transactional(rollbackFor = {Exception.class, RStatusCodeException.class})
    public R<Void> deleteApplicableProductSku(ApplicableProductSkuRuleBo bo) {
        try {
            String itemNo = bo.getItemNo();
            if (StrUtil.isBlank(itemNo)) {
                return R.fail(ZSMallStatusCodeEnum.REQUIRED_CANNOT_EMPTY);
            }

            List<ProductSkuPrice> oldPrices = iProductSkuPriceService.queryListByProductSkuCode(itemNo);
            for (ProductSkuPrice oldPrice : oldPrices) {
                Long productSkuId = oldPrice.getProductSkuId();
                //获取商品绑定信息
                ProductSkuPriceRuleRelationVo relationVo = iProductSkuPriceRuleRelationService.getByProductSkuId(productSkuId);
                Long productSkuPriceRuleId = relationVo.getProductSkuPriceRuleId();
                //如果是基础公式的绑定数据，则不可删除
                if (productSkuPriceRuleId == 1l) {
                    return R.fail(ZSMallStatusCodeEnum.APPLICABLE_PRODUCT_CANNOT_BE_DELETED);
                }
                Boolean aBoolean = iProductSkuPriceRuleRelationService.deleteRelationByProductSkuId(productSkuId);
                if (!aBoolean) {
                    return R.fail(ZSMallStatusCodeEnum.PRODUCT_SKU_APPLICABLE_PRODUCT_DELETE_ERROR);
                }
                //删除公式后匹配基础公式
                ProductSkuPriceVo price = iProductSkuPriceService.getBySkuId(productSkuId);
                ProductSkuPrice dto = this.matchRule(itemNo, price.getOriginalUnitPrice(), price.getOriginalOperationFee(), price.getOriginalFinalDeliveryFee(), true);
                if (dto == null) {
                    throw new RStatusCodeException(ZSMallStatusCodeEnum.SYSTEM_ERROR_E10028);
                }
                Boolean b = this.bindRule(price.getProductSkuId(), price.getProductSkuCode(), price.getId(), dto.getProductSkuPriceRuleId());
                if (!b) {
                    throw new Exception("删除商品绑定后重新绑定新公式失败。");
                }
                //调用商品价格修改接口
                List<ProductSkuPrice> dtoList = new ArrayList<>();
                dto.setProductSkuId(price.getProductSkuId());
                dto.setProductSkuCode(price.getProductSkuCode());
                dto.setId(oldPrice.getId());
                dtoList.add(dto);
                iProductSkuPriceService.saveOrUpdate(dto);
                iProductSkuPriceLogService.recordPriceChanges(oldPrice,  PriceOperateLog.Update.name());

                List<ProductSkuPrice> oldPriceList = new ArrayList<>();
                oldPriceList.add(oldPrice);
                subsequentPriceChanges(oldPriceList, dtoList);
            }

            return R.ok();
        } catch (RStatusCodeException e) {
            log.error("调用【删除适用产品绑定】方法 状态码异常 error => {}", JSONUtil.toJsonStr(e.getStatusCode()));
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return R.fail(e.getStatusCode());
        } catch (Exception e) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            log.error("调用【删除适用产品绑定】方法异常 error => {}", e.getMessage(), e);
            return R.fail();
        }
    }


    @Override
    public ProductSkuPriceRule getByRuleCode(String ruleCode) {
        return iProductSkuPriceRuleService.getByRuleCode(ruleCode);
    }

    @Override
    public ProductSkuPrice calculatePricesByPriceEntity(ProductSkuPriceRuleBo priceEntity) {
        return iProductSkuPriceRuleService.calculatePricesByPriceEntity(priceEntity);
    }

    @Override
    public List<ProductSkuPrice> calculateProductSkuPricesByRuleCode(String ruleCode, List<ProductSkuPrice> oldPrices) {
        return iProductSkuPriceRuleService.calculateProductSkuPricesByRuleCode(ruleCode, oldPrices);
    }

    @Override
    public ProductSkuPrice calculateByRule(String ruleCode, BigDecimal unitPrice, BigDecimal
        operationFee, BigDecimal finalDeliveryFee) {
        return iProductSkuPriceRuleService.calculateByRule(ruleCode, unitPrice, operationFee, finalDeliveryFee);
    }

    @Override
    public ProductSkuPrice calculate(List<ProductSkuPriceRuleItemVo> itemList, BigDecimal
        unitPrice, BigDecimal operationFee, BigDecimal finalDeliveryFee) {
        return iProductSkuPriceRuleService.calculate(itemList, unitPrice, operationFee, finalDeliveryFee);
    }

    @Override
    public ProductSkuPrice queryByPriceRange(ProductSkuPriceBo priceEntity) {
        return iProductSkuPriceRuleService.queryByPriceRange(priceEntity);
    }

    @Override
    public List<ProductSkuPriceRuleVo> getListByTypeAndValue(Integer type, JSONArray applicableValues, String ruleCode) {
        return iProductSkuPriceRuleService.getListByTypeAndValue(type, applicableValues, ruleCode);
    }

    @Override
    public List<ProductSkuPriceRuleVo> getListByType(Integer type, String ruleCode) {
        return iProductSkuPriceRuleService.getListByType(type, ruleCode);
    }

    @Override
    public ProductSkuPriceRuleVo getBasicRuleByRuleCode(String ruleCode) {
        return iProductSkuPriceRuleService.getBasicRuleByRuleCode(ruleCode);
    }

    @Override
    public ProductSkuPrice matchRule(String itemNo, BigDecimal unitPrice, BigDecimal
        operationFee, BigDecimal finalDeliveryFee, Boolean isMatchBase) throws RStatusCodeException {
        return iProductSkuPriceRuleService.matchRule(itemNo, unitPrice, operationFee, finalDeliveryFee, isMatchBase);
    }

    @Override
    public ProductSkuPrice matchRuleAndSite(String itemNo, BigDecimal unitPrice, BigDecimal operationFee,
                                            BigDecimal finalDeliveryFee, Boolean isMatchBase,
                                            Map<String, SiteCountryCurrency> siteCountryCurrencyMap,
                                            String countryCode) throws RStatusCodeException {
        ProductSkuPrice price = this.matchRule(itemNo, unitPrice, operationFee, finalDeliveryFee, isMatchBase);
        SiteCountryCurrency currency = siteCountryCurrencyMap.get(countryCode);
        price.setSiteId(currency.getId());
        price.setCountryCode(currency.getCountryCode());
        price.setCurrency(currency.getCurrencyCode());
        price.setCurrencySymbol(currency.getCurrencySymbol());

        return price;
    }


    /**
     * 匹配公式
     *
     * @param productSkuPrice
     * @param isMatchBase
     * @return
     * @throws RStatusCodeException
     */
    @Override
    public Long matchRule(ProductSkuPrice productSkuPrice, Boolean isMatchBase) throws RStatusCodeException {
        return iProductSkuPriceRuleService.matchRule(productSkuPrice, isMatchBase);
    }

    @Override
    public Boolean bindRule(Long productSkuId, String productSkuCode, Long productSkuPriceId, Long ruleId) {
        return iProductSkuPriceRuleService.bindRule(productSkuId, productSkuCode, productSkuPriceId, ruleId);
    }

    /**
     * 绑定公式
     *
     * @param productSkuPrice
     * @param ruleId
     * @return
     */
    @Override
    public Boolean bindRule(ProductSkuPrice productSkuPrice, Long ruleId) {
        return iProductSkuPriceRuleService.bindRule(productSkuPrice, ruleId);
    }

}
