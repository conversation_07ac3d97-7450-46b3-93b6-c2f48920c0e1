package com.zsmall.product.biz.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.hengjian.common.core.domain.R;
import com.hengjian.common.mybatis.core.page.PageQuery;
import com.zsmall.product.entity.domain.RuleLevelProductPrice;
import com.zsmall.product.entity.domain.bo.member.RulePriceQueryBo;
import com.zsmall.product.entity.domain.member.MemberLevel;
import com.zsmall.product.entity.domain.vo.member.MemberLevelVO;
import com.zsmall.product.entity.domain.vo.member.MemberSkuPriceVo;
import com.zsmall.product.entity.domain.vo.member.RuleLevelProductPriceTableInfoVo;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.http.ResponseEntity;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;


/**
 * <AUTHOR>
 * @date 2024/05/15
 */
@Deprecated
public interface RuleLevelProductPriceService extends IService<RuleLevelProductPrice> {

    RuleLevelProductPriceTableInfoVo queryPageList(RulePriceQueryBo bo, PageQuery pageQuery);

    R<Void> edit(MemberSkuPriceVo vo);

    R<Void> add(List<MemberSkuPriceVo> vo);
    R<Void> add(MemberSkuPriceVo vo);

    MemberSkuPriceVo getSkuPrice(List<String> ruleLevelPriceId);

    R<Void> uploadProductExcel(MultipartFile file) throws IOException;
    R<Void> uploadProductExcelV2(MultipartFile file) throws IOException;

    void export(RulePriceQueryBo bo, PageQuery pageQuery, HttpServletResponse response) throws IOException;

    /**
     * 功能描述：获取会员价格
     *
     * @param supplierTenantId    供应商租户id
     * @param distributorTenantId 分销商租户id
     * @param productSkuId        产品sku id
     * @return {@link RuleLevelProductPrice }
     * <AUTHOR>
     * @date 2024/05/24
     */
    RuleLevelProductPrice getMemberPrice(String supplierTenantId,String distributorTenantId,Long productSkuId);

    ResponseEntity<byte[]> analyzeExcel(Workbook file, Integer firstCol, Integer lastCol, Integer colNum, Integer colNumPlus, List<MemberLevel> memberLevels, Integer interval, Integer width);

    /**
     * 功能描述：导出模板
     *
     * @return {@link ResponseEntity }<{@link byte[] }>
     * <AUTHOR>
     * @date 2024/07/09
     */
    ResponseEntity<byte[]> exportTemplate();

    List<MemberLevelVO> getMemberRule();
}
