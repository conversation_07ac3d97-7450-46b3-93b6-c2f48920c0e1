package com.zsmall.product.biz.service;

import com.hengjian.common.mybatis.core.page.PageQuery;
import com.hengjian.common.mybatis.core.page.TableDataInfo;
import com.zsmall.product.entity.domain.bo.member.MemberDiscountBo;
import com.zsmall.product.entity.domain.vo.member.MemberDiscountVo;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.List;

/**
 * 会员等级折扣Service接口
 *
 * <AUTHOR> Li
 * @date 2024-07-25
 */
@Deprecated
public interface IMemberDiscountService {

    /**
     * 查询会员等级折扣
     */
    MemberDiscountVo queryById(Long id);

    /**
     * 查询会员等级折扣列表
     */
    TableDataInfo<MemberDiscountVo> queryPageList(MemberDiscountBo bo, PageQuery pageQuery);

    /**
     * 查询会员等级折扣列表
     */
    List<MemberDiscountVo> queryList(MemberDiscountBo bo);

    /**
     * 新增会员等级折扣
     */
    Boolean insertByBo(MemberDiscountBo bo);

    /**
     * 修改会员等级折扣
     */
    Boolean updateByBo(MemberDiscountBo bo);

    /**
     * 校验并批量删除会员等级折扣信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    BigDecimal getMemberDiscountByDictCode(Long dictCode);
}
