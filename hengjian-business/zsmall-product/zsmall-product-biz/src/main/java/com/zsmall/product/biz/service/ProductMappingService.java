package com.zsmall.product.biz.service;

import com.hengjian.common.core.domain.R;
import com.hengjian.common.core.exception.RStatusCodeException;
import com.hengjian.common.mybatis.core.page.PageQuery;
import com.hengjian.common.mybatis.core.page.TableDataInfo;
import com.zsmall.product.entity.domain.ProductMapping;
import com.zsmall.product.entity.domain.bo.distributorProduct.BatchImportToStoreBo;
import com.zsmall.product.entity.domain.bo.distributorProduct.ImportToStoreBo;
import com.zsmall.product.entity.domain.bo.productMapping.*;
import com.zsmall.product.entity.domain.vo.productMapping.*;
import java.io.IOException;
import java.util.List;
import org.springframework.web.multipart.MultipartFile;

/**
 * 业务接口层：分销商商品
 */
public interface ProductMappingService {

    /**
     * 分页查询商品映射信息列表
     * @param bo
     * @return
     */
    ProductMappingListVo queryProductMappingPage(ProductMappingTableQueryBo bo, PageQuery pageQuery);

    /**
     * 获取excel导出数据
     *
     * @param bo
     * @param pageQuery
     * @return
     */
    List<ProductMappingExportVo> listProductMappingExportVo(ProductMappingTableQueryBo bo, PageQuery pageQuery);

    /**
     * 查询商品映射信息详情
     * @param bo
     * @return
     */
    R<ProductMappingDetailVo> queryProductMappingDetail(ProductMappingDetailBo bo);

    /**
     * 更新Sku映射商品编码
     */
    R<List<ProductMapping>> updateProductMappingChannelSku(SaveSkuMappingBo bo) throws RStatusCodeException;

    /**
     * 更新Sku映射商品编码-商品站点价格
     */
    R<List<ProductMapping>> updateProductMappingChannelSkuSite(SaveSkuMappingBo bo) throws RStatusCodeException;

    /**
     * 单独设置分销商商品MarkUp
     */
    R<Void> setMarkUp(SetMarkUpBo bo);

    /**
     * 批量设置分销商商品MarkUp
     */
    R<Void> batchSetMarkUp(BatchSetMarkUpBo bo);

    /**
     * 上传SKU映射
     * @param file
     * @return
     */
    R<List<ProductMapping>> uploadSkuMapping(MultipartFile file) throws RStatusCodeException, IOException;

    /**
     * 推送商品映射至销售渠道
     * @param bo
     * @return
     */
    R<Void> pushProductMapping(PushProductBo bo);

    /**
     * 删除商品映射信息
     * @return
     */
    R<Void> deleteProductMapping(ProductMappingDeleteBo bo);

    /**
     * 商品批量铺货至渠道
     */
    R<Void> productBatchImportToChannel(BatchImportToStoreBo bo);

    /**
     * 商品铺货至渠道
     */
    R<Void> productImportToChannel(ImportToStoreBo bo);

    /**
     * 分页查询履约仓库映射
     * @param bo
     * @return
     */
    TableDataInfo<WarehouseMappingVo> queryWarehouseMappingPage(WarehouseMappingBo bo, PageQuery pageQuery);

    /**
     * 设置履约仓库映射
     * @param bo
     * @return
     */
    R<Void> setWarehouseMapping(FulfillWarehouseBo bo);

    /**
     * 查询铺货准备信息
     * @param productCode
     * @param channelType
     * @return
     */
    R<ImportReadyVo> queryImportReadyInfo(String productCode, String channelType);

    /**
     * 功能描述：保存 Tik Tok  SKU 映射
     *
     * @param bo 博
     * @return {@link R }<{@link Void }>
     * <AUTHOR>
     * @date 2024/02/05
     */
    R<Void> saveSkuMappingForTikTok(SaveSkuMappingBo bo);

    /**
     * 更新商品映射名称
     * @param id
     * @param name
     * @return
     */
    R<Void> setProductMappingName(String id, String name);

    /**
     *  新增产品映射
     * @param channelId
     * @param itemNo
     * @return
     */
    R<ProductMapping> addProductMapping(Long channelId, String itemNo, String channelSku,String orderNo);

    /**
     * 新增产品映射，按照站点的维度
     *
     * @param channelId
     * @param itemNo
     * @param channelSku
     * @param orderNo
     * @return
     */
    R<ProductMapping> addProductMappingSite(Long channelId, String itemNo, String channelSku,String orderNo);

    /**
     * 映射表清洗数据
     */
    void dealProductMappingDate();

}
