package com.zsmall.product.biz.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.read.listener.ReadListener;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.hengjian.common.core.domain.R;
import com.hengjian.common.core.domain.model.LoginUser;
import com.hengjian.common.core.enums.TenantType;
import com.hengjian.common.core.exception.RStatusCodeException;
import com.hengjian.common.mybatis.core.page.PageQuery;
import com.hengjian.common.mybatis.core.page.TableDataInfo;
import com.hengjian.common.satoken.utils.LoginHelper;
import com.hengjian.common.tenant.helper.TenantHelper;
import com.hengjian.system.service.ISysConfigService;
import com.hengjian.system.service.ISysTenantService;
import com.zsmall.bma.open.member.iservice.IMemberLevelV2ServiceImpl;
import com.zsmall.bma.open.member.iservice.IMemberRuleRelationV2ServiceImpl;
import com.zsmall.bma.open.member.service.RuleLevelProductPriceV2Service;
import com.zsmall.common.domain.LocaleMessage;
import com.zsmall.common.enums.common.ChannelTypeEnum;
import com.zsmall.common.enums.common.GlobalStateEnum;
import com.zsmall.common.enums.order.LogisticsTypeEnum;
import com.zsmall.common.enums.order.OrderExceptionEnum;
import com.zsmall.common.enums.product.*;
import com.zsmall.common.enums.productActivity.ProductActivityTypeEnum;
import com.zsmall.common.enums.productMapping.MarkUpTypeEnum;
import com.zsmall.common.enums.productMapping.SyncStateEnum;
import com.zsmall.common.enums.productMapping.SyncStateNameEnum;
import com.zsmall.common.enums.statuscode.ZSMallStatusCodeEnum;
import com.zsmall.common.exception.AppRuntimeException;
import com.zsmall.common.util.FormatUtil;
import com.zsmall.lottery.support.PriceSupportV2;
import com.zsmall.order.entity.domain.Orders;
import com.zsmall.order.entity.iservice.IOrdersService;
import com.zsmall.order.entity.mapper.OrdersMapper;
import com.zsmall.product.biz.service.ProductMappingService;
import com.zsmall.product.biz.support.ProductSupport;
import com.zsmall.product.entity.domain.*;
import com.zsmall.product.entity.domain.bo.distributorProduct.BatchImportToStoreBo;
import com.zsmall.product.entity.domain.bo.distributorProduct.ImportToStoreBo;
import com.zsmall.product.entity.domain.bo.distributorProduct.ImportToStoreSkuBo;
import com.zsmall.product.entity.domain.bo.productMapping.*;
import com.zsmall.product.entity.domain.bo.productSku.ProductSkuBo;
import com.zsmall.product.entity.domain.dto.productMapping.ProductMappingImportDTO;
import com.zsmall.product.entity.domain.vo.ProductSkuAttachmentVo;
import com.zsmall.product.entity.domain.vo.productMapping.*;
import com.zsmall.product.entity.domain.vo.productSku.ProductSkuVo;
import com.zsmall.product.entity.iservice.*;
import com.zsmall.system.entity.domain.ChannelExtendRakutenGenre;
import com.zsmall.system.entity.domain.SiteCountryCurrency;
import com.zsmall.system.entity.domain.TenantSalesChannel;
import com.zsmall.system.entity.domain.vo.salesChannel.TenantSalesChannelVo;
import com.zsmall.system.entity.iservice.IChannelExtendRakutenGenreService;
import com.zsmall.system.entity.iservice.ITenantSalesChannelService;
import com.zsmall.system.entity.mapper.SiteCountryCurrencyMapper;
import com.zsmall.warehouse.entity.domain.Warehouse;
import com.zsmall.warehouse.entity.domain.WarehouseAddress;
import com.zsmall.warehouse.entity.iservice.IWarehouseAddressService;
import com.zsmall.warehouse.entity.iservice.IWarehouseService;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 业务实现层：分销商商品
 *
 * <AUTHOR>
 * @date 2023/6/19
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ProductMappingServiceImpl implements ProductMappingService {
    private final RuleLevelProductPriceV2Service ruleLevelProductPriceService;
    private final IMemberLevelV2ServiceImpl iMemberLevelService;
    private final IProductService iProductService;
    private final IProductMappingService iProductMappingService;
    private final IProductSkuService iProductSkuService;
    private final IProductSkuDetailService iProductSkuDetailService;
    private final IProductSkuPriceService iProductSkuPriceService;
    private final IProductSkuAttachmentService iProductSkuAttachmentService;
    private final ITenantSalesChannelService iTenantSalesChannelService;
    private final IWarehouseService iWarehouseService;
    private final IWarehouseAddressService iWarehouseAddressService;
//    private final IProductActivityItemService iProductActivityItemService;
//    private final IProductActivityPriceItemService iProductActivityPriceItemService;
    private final IProductMappingExtendRakutenInfoService iProductMappingExtendRakutenInfoService;
    private final IProductMappingExtendWayfairWarehouseService iProductMappingExtendWayfairWarehouseService;
    private final IChannelExtendRakutenGenreService iChannelExtendRakutenGenreService;
    private final IMemberRuleRelationV2ServiceImpl memberRuleRelationService;
//    private final IProductActivityService iProductActivityService;
    private final ProductSupport productSupport;
    private final OrdersMapper ordersMapper;
    private final ISysTenantService sysTenantService;
    private final PriceSupportV2 priceSupportV2;
    private final SiteCountryCurrencyMapper siteCountryCurrencyMapper;
    private List<ProductMapping> returnProductMappings = new ArrayList<>();
    private final IOrdersService iOrdersService;
    private final ISysConfigService sysConfigService;
    /**
     * 分页查询分销商映射商品列表
     *
     * @param bo
     * @return
     */
    @Override
    public ProductMappingListVo queryProductMappingPage(ProductMappingTableQueryBo bo, PageQuery pageQuery) {
        IPage<ProductMappingTableVo> iPage;
        if (ChannelTypeEnum.TikTok.name().equals(bo.getChannelType())) {
            iPage = iProductMappingService.queryProductMappingPageForTikTok(bo, pageQuery);
        } else {
            iPage = iProductMappingService.queryProductMappingPage(bo, pageQuery);
        }
        //是否开启测算
        Boolean isApprovedTenant = sysTenantService.getIsApprovedTenant(bo.getTenantId(), 1);
        List<ProductMappingTableVo> productMappingTableVos = iPage.getRecords();
        Map<String, SkuWarehouseMappingVo> warehouseConfigMap=new HashMap<>();
        Map<Long, TenantSalesChannel> tenantSalesChannelMap=new HashMap<>();
        Map<String, ProductSku> productSkuMap=new HashMap<>();
        Map<Long, Product> productMap=new HashMap<>();
        Map<String, ProductSkuPrice> productSkuSitePriceMapByCode=new HashMap<>();
        Map<String, RuleLevelProductPrice> ruleLevelProductPriceSitePriceMapByCode=new HashMap<>();
        if (CollectionUtils.isNotEmpty(productMappingTableVos)){
            //如果为空
            warehouseConfigMap = new HashMap<>();
            // 获取所有的商品编码
            Set<String> productSkuCodeSet = productMappingTableVos.stream().map(ProductMappingTableVo::getProductSkuCode)
                                                                  .collect(Collectors.toSet());

            //查询Product_sku表
            List<ProductSku> productSkuList = TenantHelper.ignore(() -> iProductSkuService.queryByProductSkuCodeList(Arrays.asList(productSkuCodeSet.toArray(new String[0]))));
            productSkuMap= productSkuList.stream()
                                                          .collect(Collectors.toMap(ProductSku::getProductSkuCode, productSku -> productSku));
            Set<Long> productIdSet = productSkuList.stream()
                                              .map(ProductSku::getProductId)
                                              .collect(Collectors.toSet());
            //查询Product表
            LambdaQueryWrapper<Product> lambdaQueryWrapper =new LambdaQueryWrapper<>();
            lambdaQueryWrapper.in(Product::getId,productIdSet);
            List<Product> productList = TenantHelper.ignore(() -> iProductService.getBaseMapper().selectList(lambdaQueryWrapper));
            productMap=productList.stream()
                                  .collect(Collectors.toMap(Product::getId, product -> product));
            //查询成本价/会员价格
            productSkuSitePriceMapByCode = iProductSkuPriceService.getProductSkuSitePriceMapByCode(productSkuCodeSet);
            //查询会员价格
            ruleLevelProductPriceSitePriceMapByCode = iProductSkuPriceService.getRuleLevelProductPriceSitePriceMapByCode(productSkuCodeSet, bo.getTenantId());

            //查询所有的渠道
            Set<Long> channelIdSet = productMappingTableVos.stream().map(ProductMappingTableVo::getChannelId)
                                                           .collect(Collectors.toSet());
            List<TenantSalesChannel> tenantSalesChannelList = TenantHelper.ignore(() -> iTenantSalesChannelService.getBaseMapper()
                                                                                                                  .selectList(new LambdaQueryWrapper<TenantSalesChannel>().in(TenantSalesChannel::getId, channelIdSet)));
             tenantSalesChannelMap = tenantSalesChannelList.stream()
                                                                                        .collect(Collectors.toMap(TenantSalesChannel::getId, tenantSalesChannel -> tenantSalesChannel));
        }

        for (ProductMappingTableVo vo : productMappingTableVos) {
            Long channelId = vo.getChannelId();
            String channelType = vo.getChannelType();
            String productSkuCode = vo.getProductSkuCode();
            if (ObjectUtil.isEmpty(productSkuCode)) {
                throw new RuntimeException("Item.no不存在" + productSkuCode);
            }
            BigDecimal finalPrice = vo.getFinalPrice();
            TenantSalesChannel tenantSalesChannel = tenantSalesChannelMap.get(channelId);
            if (ObjectUtil.isNull(tenantSalesChannel)) {
                throw new RuntimeException("渠道ID不存在" + channelId);
            }
            vo.setChannelAlias(tenantSalesChannel.getChannelName());
            ProductSku productSku = productSkuMap.get(productSkuCode);
            if (ObjectUtil.isNull(productSku)) {
                throw new RuntimeException("商品不存在" + productSkuCode);
            }
           // log.info("站点：{}", vo.getSite());
            ProductSkuPrice productSkuPrice = productSkuSitePriceMapByCode.get(productSkuCode+"-"+vo.getSite());
            RuleLevelProductPrice ruleLevelProductPrice = ruleLevelProductPriceSitePriceMapByCode.get(productSkuCode+"-"+vo.getSite());
            Product product = productMap.get(productSku.getProductId());
            if (ObjectUtil.isNull(product)) {
                throw new RuntimeException("产品不存在" + productSku.getProductId());
            }
            //设置最新的规格信息
            vo.setSpecComposeName(productSku.getSpecComposeName());
            vo.setSpecValName(FormatUtil.transformString(productSku.getSpecComposeName()));
            // 查询仓库配置
//            List<SkuWarehouseMappingVo> warehouseConfigs=getSkuWarehouseMappingVo(productSkuCode,channelId,channelType);
//            vo.setWarehouseConfigs(warehouseConfigs);
            //校验渠道店铺发货方式和商品发货方式是否不一致
            boolean isSupport = product.getSupportedLogistics()
                                       .allowShipping(LogisticsTypeEnum.getLogisticsTypeEnumByName(String.valueOf(tenantSalesChannel.getLogisticsType())));
            if (isSupport){
                //获取成本价
                if (ObjectUtil.isNotNull(vo.getSupplierTenantId()) && ObjectUtil.isNotNull(tenantSalesChannel.getLogisticsType()) &&  ObjectUtil.isNotNull(tenantSalesChannel.getLogisticsType())){
                    BigDecimal productMappingCostPrice = getProductMappingCostPrice(isApprovedTenant, vo.getProductSkuCode(), LogisticsTypeEnum.valueOf(tenantSalesChannel.getLogisticsType().name()),productSkuPrice,ruleLevelProductPrice);
                    vo.setCostPrice(productMappingCostPrice);
                    //如果渠道销售价为null则收益也为空
                    if (ObjectUtil.isNotNull(finalPrice) && ObjectUtil.isNotNull(productMappingCostPrice)){
                            vo.setMargin(NumberUtil.sub(finalPrice, productMappingCostPrice));
                    }else {
                         vo.setMargin(null);
                    }
                }
            }else {
                vo.setCostPrice(null);
                vo.setMargin(null);
            }
        }
        TableDataInfo<ProductMappingTableVo> tableDataInfo = TableDataInfo.build(productMappingTableVos, iPage.getTotal());
        ProductMappingListVo vos = BeanUtil.toBean(tableDataInfo, ProductMappingListVo.class);
        vos.setWarehouseConfigs(warehouseConfigMap);
        return vos;
    }

    @Override
    public List<ProductMappingExportVo> listProductMappingExportVo(ProductMappingTableQueryBo bo, PageQuery pageQuery) {
        IPage<ProductMappingTableVo> iPage;
        if (ChannelTypeEnum.TikTok.name().equals(bo.getChannelType())) {
            iPage = iProductMappingService.queryProductMappingPageForTikTok(bo, pageQuery);
        } else {
            iPage = iProductMappingService.queryProductMappingPage(bo, pageQuery);
        }
        //是否开启测算
        Boolean isApprovedTenant = sysTenantService.getIsApprovedTenant(bo.getTenantId(), 1);
        List<ProductMappingTableVo> productMappingTableVos = iPage.getRecords();
        Map<Long, TenantSalesChannel> tenantSalesChannelMap=new HashMap<>();
        Map<String, ProductSku> productSkuMap=new HashMap<>();
        Map<Long, Product> productMap=new HashMap<>();
        Map<String, ProductSkuPrice> productSkuSitePriceMapByCode=new HashMap<>();
        Map<String, RuleLevelProductPrice> ruleLevelProductPriceSitePriceMapByCode=new HashMap<>();
        if (CollectionUtils.isNotEmpty(productMappingTableVos)){
            // 获取所有的商品编码
            Set<String> productSkuCodeSet = productMappingTableVos.stream().map(ProductMappingTableVo::getProductSkuCode)
                                                                  .collect(Collectors.toSet());
            //查询Product_sku表
            List<ProductSku> productSkuList = TenantHelper.ignore(() -> iProductSkuService.queryByProductSkuCodeList(Arrays.asList(productSkuCodeSet.toArray(new String[0]))));
            productSkuMap= productSkuList.stream()
                                         .collect(Collectors.toMap(ProductSku::getProductSkuCode, productSku -> productSku));
            Set<Long> productIdSet = productSkuList.stream()
                                                   .map(ProductSku::getProductId)
                                                   .collect(Collectors.toSet());
            //查询Product表
            LambdaQueryWrapper<Product> lambdaQueryWrapper =new LambdaQueryWrapper<>();
            lambdaQueryWrapper.in(Product::getId,productIdSet);
            List<Product> productList = TenantHelper.ignore(() -> iProductService.getBaseMapper().selectList(lambdaQueryWrapper));
            productMap=productList.stream()
                                  .collect(Collectors.toMap(Product::getId, product -> product));
            //查询成本价/自提价格
             productSkuSitePriceMapByCode = iProductSkuPriceService.getProductSkuSitePriceMapByCode(productSkuCodeSet);
             ruleLevelProductPriceSitePriceMapByCode = iProductSkuPriceService.getRuleLevelProductPriceSitePriceMapByCode(productSkuCodeSet, bo.getTenantId());
            //查询所有的渠道
            Set<Long> channelIdSet = productMappingTableVos.stream().map(ProductMappingTableVo::getChannelId)
                                                           .collect(Collectors.toSet());
            List<TenantSalesChannel> tenantSalesChannelList = TenantHelper.ignore(() -> iTenantSalesChannelService.getBaseMapper()
                                                                                                                  .selectList(new LambdaQueryWrapper<TenantSalesChannel>().in(TenantSalesChannel::getId, channelIdSet)));
            tenantSalesChannelMap = tenantSalesChannelList.stream()
                                                          .collect(Collectors.toMap(TenantSalesChannel::getId, tenantSalesChannel -> tenantSalesChannel));
        }

        for (ProductMappingTableVo vo : productMappingTableVos) {
            Long channelId = vo.getChannelId();
            String productSkuCode = vo.getProductSkuCode();
            if (ObjectUtil.isEmpty(productSkuCode)) {
                throw new RuntimeException("Item.no不存在" + productSkuCode);
            }
            BigDecimal finalPrice = vo.getFinalPrice();
            TenantSalesChannel tenantSalesChannel = tenantSalesChannelMap.get(channelId);
            if (ObjectUtil.isNull(tenantSalesChannel)) {
                throw new RuntimeException("渠道ID不存在" + channelId);
            }
            vo.setChannelAlias(tenantSalesChannel.getChannelName());
            ProductSku productSku = productSkuMap.get(productSkuCode);
            if (ObjectUtil.isNull(productSku)) {
                throw new RuntimeException("商品不存在" + productSkuCode);
            }
            ProductSkuPrice productSkuPrice = productSkuSitePriceMapByCode.get(productSkuCode+"-"+vo.getSite());
            RuleLevelProductPrice ruleLevelProductPrice = ruleLevelProductPriceSitePriceMapByCode.get(productSkuCode+"-"+vo.getSite());
            Product product = productMap.get(productSku.getProductId());
            if (ObjectUtil.isNull(product)) {
                throw new RuntimeException("产品不存在" + productSku.getProductId());
            }
            //设置最新的规格信息
            vo.setSpecComposeName(productSku.getSpecComposeName());
            vo.setSpecValName(productSku.getSpecValName());
            // 查询仓库配置
            //校验渠道店铺发货方式和商品发货方式是否不一致
            boolean isSupport = product.getSupportedLogistics()
                                       .allowShipping(LogisticsTypeEnum.getLogisticsTypeEnumByName(String.valueOf(tenantSalesChannel.getLogisticsType())));
            if (isSupport){
                //获取成本价
                if (ObjectUtil.isNotNull(vo.getSupplierTenantId()) && ObjectUtil.isNotNull(tenantSalesChannel.getLogisticsType()) &&  ObjectUtil.isNotNull(tenantSalesChannel.getLogisticsType())){
                    BigDecimal productMappringCostPrice = getProductMappingCostPrice(isApprovedTenant, vo.getProductSkuCode(), LogisticsTypeEnum.valueOf(tenantSalesChannel.getLogisticsType().name()),productSkuPrice,ruleLevelProductPrice);
                    vo.setCostPrice(productMappringCostPrice);
                    //如果渠道销售价为null则收益也为空
                    if (ObjectUtil.isNotNull(finalPrice) && ObjectUtil.isNotNull(productMappringCostPrice)){
                        vo.setMargin(NumberUtil.sub(finalPrice, productMappringCostPrice));
                    }else {
                        vo.setMargin(null);
                    }
                }
            }else {
                vo.setCostPrice(null);
                vo.setMargin(null);
            }
            if(StringUtils.isNotEmpty(vo.getSyncState())){
                vo.setSyncState(SyncStateNameEnum.getByName(vo.getSyncState()).getDesc());
            }
        }
        List<ProductMappingExportVo> productMappingExportVoList = new ArrayList<>();
        if(CollUtil.isNotEmpty(productMappingTableVos)){
            for (ProductMappingTableVo productMappingTableVo : productMappingTableVos) {
                ProductMappingExportVo productMappingExportVo = new ProductMappingExportVo();
                BeanUtil.copyProperties(productMappingTableVo, productMappingExportVo);
                productMappingExportVoList.add(productMappingExportVo);
            }
            productMappingTableVos.clear();
        }
        return productMappingExportVoList;
    }

    private List<SkuWarehouseMappingVo> getSkuWarehouseMappingVo(String productSkuCode,Long channelId,String channelType) {
        Map<String, SkuWarehouseMappingVo> finalWarehouseConfigMap=new HashMap<>();
        // 查询仓库配置
        Map<Long, String> warehouseAddressMap = getWarehouseAddressMap(productSkuCode);
        // 当前商品的仓库映射
        List<SkuWarehouseMappingVo> warehouseConfigs = new ArrayList<>();
        warehouseAddressMap.forEach((warehouseId, address) -> {
            Warehouse warehouse = iWarehouseService.getByIdNotTenant(warehouseId);
            String warehouseSystemCode = warehouse.getWarehouseSystemCode();
            String key = channelId + "_" + warehouseSystemCode;

            SkuWarehouseMappingVo skuWarehouseMappingVo = finalWarehouseConfigMap.get(key);
            SkuWarehouseMappingVo newSkuWarehouseMappingVo;
            if (skuWarehouseMappingVo != null) {
                newSkuWarehouseMappingVo = BeanUtil.toBean(skuWarehouseMappingVo, SkuWarehouseMappingVo.class);
            } else {
                newSkuWarehouseMappingVo = new SkuWarehouseMappingVo();
                ProductMappingExtendWayfairWarehouse wayfairWarehouse = iProductMappingExtendWayfairWarehouseService.queryByWarehouseAndChannel(warehouseId, channelId);
                if (wayfairWarehouse != null) {
                    newSkuWarehouseMappingVo.setKeyId(wayfairWarehouse.getThirdWarehouseId());
                }
                newSkuWarehouseMappingVo.setChannelType(channelType);
                newSkuWarehouseMappingVo.setChannelId(channelId);
                newSkuWarehouseMappingVo.setWarehouseAddress(address);
                newSkuWarehouseMappingVo.setWarehouseSystemCode(warehouseSystemCode);
                finalWarehouseConfigMap.put(key, newSkuWarehouseMappingVo);
            }
            warehouseConfigs.add(newSkuWarehouseMappingVo);
        });
        return warehouseConfigs;
    }


    /**
     * 获取成本价
     * @param supplierTenantId 供应商租户ID
     * @param distributorTenantId 分销商租户ID
     * @param productSkuCode 商品编码
     * @param logisticsTypeEnum 物流类型
     * @return
     */
//    public BigDecimal getProductMappringCostPrice(String supplierTenantId,String distributorTenantId, String productSkuCode ,LogisticsTypeEnum logisticsTypeEnum){
//        BigDecimal costPrice=null;
//        if (ObjectUtil.isNull(productSkuCode)) {
//            throw  new RuntimeException("商品映射 item.no 不能为空");
//        }
//        //根据商品编码查询商品
//        ProductSku productSku = iProductSkuService.queryByProductSkuCode(productSkuCode);
//        if (ObjectUtil.isNull(productSku)){
//            throw  new RuntimeException("商品信息不存在"+productSkuCode);
//        }
//        //先去查询会员价格
//        //去查询会员定价
//        RuleLevelProductPrice memberPrice = ruleLevelProductPriceService.getMemberPrice(supplierTenantId ,distributorTenantId, productSku.getId());
//        if (ObjectUtil.isNotNull(memberPrice)){
//            //自提
//            if (LogisticsTypeEnum.PickUp.equals(logisticsTypeEnum)){
//                if (TenantType.Supplier.name().equals(LoginHelper.getTenantType())) {
//                    costPrice=memberPrice.getOriginalPickUpPrice();
//                }else {
//                    costPrice=memberPrice.getPlatformPickUpPrice();
//                }
//            }
//            //代发
//            if (LogisticsTypeEnum.DropShipping.equals(logisticsTypeEnum)){
//                if (TenantType.Supplier.name().equals(LoginHelper.getTenantType())) {
//                    costPrice=memberPrice.getOriginalDropShippingPrice();
//                }else {
//                    costPrice=memberPrice.getPlatformDropShippingPrice();
//                }
//            }
//            return  costPrice;
//        }
//
//
//        ProductSkuPrice productSkuPrice = TenantHelper.ignore(() -> iProductSkuPriceService.getBaseMapper()
//                                                                                  .selectOne(new LambdaQueryWrapper<ProductSkuPrice>().eq(ObjectUtil.isNotEmpty(productSkuCode), ProductSkuPrice::getProductSkuCode, productSkuCode)));
//        if (ObjectUtil.isNull(productSkuPrice)){
//            throw  new RuntimeException("商品为查询到单价："+productSkuCode);
//        }
//        //自提
//        if (LogisticsTypeEnum.PickUp.equals(logisticsTypeEnum)){
//            if (TenantType.Supplier.name().equals(LoginHelper.getTenantType())) {
//                costPrice=productSkuPrice.getOriginalPickUpPrice();
//            }else {
//                costPrice=productSkuPrice.getPlatformPickUpPrice();
//            }
//        }
//        //代发
//        if (LogisticsTypeEnum.DropShipping.equals(logisticsTypeEnum)){
//            if (TenantType.Supplier.name().equals(LoginHelper.getTenantType())) {
//                costPrice=productSkuPrice.getOriginalDropShippingPrice();
//            }else {
//                costPrice=productSkuPrice.getPlatformDropShippingPrice();
//            }
//        }
//        return  costPrice;
//    }

    /**
     * 获取成本价
     * @param isApprovedTenant 是否开启测算
     * @param productSkuCode 商品编码
     * @param logisticsTypeEnum 物流类型
     * @param productSkuPrice 商品价格
     * @param memberPrice 会员价格
     * @return 成本价
     */
    public BigDecimal getProductMappingCostPrice( Boolean isApprovedTenant,String productSkuCode ,LogisticsTypeEnum logisticsTypeEnum,ProductSkuPrice productSkuPrice,RuleLevelProductPrice memberPrice){

        BigDecimal costPrice=null;
        if (ObjectUtil.isNull(productSkuCode)) {
            throw  new RuntimeException("商品映射 item.no 不能为空");
        }
        if (ObjectUtil.isNotNull(memberPrice)){
                //自提 都是单价+操作费
                if (LogisticsTypeEnum.PickUp.equals(logisticsTypeEnum)){
                    if (TenantType.Supplier.name().equals(LoginHelper.getTenantType())) {
                        costPrice=memberPrice.getOriginalPickUpPrice();
                    }else {
                        costPrice=memberPrice.getPlatformPickUpPrice();
                    }
                }
                //代发 如果配置了测算 则不加尾程费
                if (LogisticsTypeEnum.DropShipping.equals(logisticsTypeEnum)){
                    //如果配置了测算
                    if (isApprovedTenant){
                        if (TenantType.Supplier.name().equals(LoginHelper.getTenantType())) {
                            costPrice=memberPrice.getOriginalPickUpPrice();
                        }else {
                            costPrice=memberPrice.getPlatformPickUpPrice();
                        }
                    }else {
                        if (TenantType.Supplier.name().equals(LoginHelper.getTenantType())) {
                            costPrice=memberPrice.getOriginalDropShippingPrice();
                        }else {
                            costPrice=memberPrice.getPlatformDropShippingPrice();
                        }
                    }
                }
                if (ObjectUtil.isNotNull(costPrice)){
                    return  costPrice;
                }
        }
        if (ObjectUtil.isNull(productSkuPrice)){
            return null;
        }
        //自提都是单价+操作费
        if (LogisticsTypeEnum.PickUp.equals(logisticsTypeEnum)){
            if (TenantType.Supplier.name().equals(LoginHelper.getTenantType())) {
                costPrice=productSkuPrice.getOriginalUnitPrice().add(productSkuPrice.getOriginalOperationFee());
            }else {
                costPrice=productSkuPrice.getPlatformUnitPrice().add(productSkuPrice.getPlatformOperationFee());
            }
        }
        //代发
        if (LogisticsTypeEnum.DropShipping.equals(logisticsTypeEnum)){
            if (isApprovedTenant){
                if (TenantType.Supplier.name().equals(LoginHelper.getTenantType())) {
                    costPrice=productSkuPrice.getOriginalUnitPrice().add(productSkuPrice.getOriginalOperationFee());
                }else {
                    costPrice=productSkuPrice.getPlatformUnitPrice().add(productSkuPrice.getPlatformOperationFee());
                }
            }else {
                if (TenantType.Supplier.name().equals(LoginHelper.getTenantType())) {
                    costPrice=productSkuPrice.getOriginalUnitPrice().add(productSkuPrice.getOriginalOperationFee()).add(productSkuPrice.getOriginalFinalDeliveryFee());
                }else {
                    costPrice=productSkuPrice.getPlatformUnitPrice().add(productSkuPrice.getPlatformOperationFee()).add(productSkuPrice.getOriginalFinalDeliveryFee());
                }
            }
        }
        return  costPrice;
    }


    /**
     * 查询商品映射信息详情
     *
     * @param bo
     * @return
     */
    @Override
    public R<ProductMappingDetailVo> queryProductMappingDetail(ProductMappingDetailBo bo) {
        Long id = bo.getId();
        ProductMapping productMapping = iProductMappingService.getById(id);
        if (productMapping == null) {
            return R.fail(ZSMallStatusCodeEnum.PRODUCT_MAPPING_NOT_EXISTS);
        }
        if (ObjectUtil.isEmpty(productMapping.getProductSkuCode())) {
            throw new AppRuntimeException("请先完成商品映射后再进行商品编辑!");
        }
        Long channelId = productMapping.getChannelId();
        ChannelTypeEnum channelType = productMapping.getChannelType();

        String productSkuCode = productMapping.getProductSkuCode();
        if (ObjectUtil.isEmpty(productSkuCode)){
            throw  new RuntimeException("产品映射,商品编码 item.no  不能为空");
        }

        TenantSalesChannelVo tenantSalesChannelVo = iTenantSalesChannelService.getBaseMapper().selectVoById(channelId);
        if (ObjectUtil.isNull(tenantSalesChannelVo)){
            throw  new RuntimeException("商品映射为查询到渠道店铺"+channelId);
        }
        String activityCode = productMapping.getActivityCode();
        ProductSkuDetail productSkuDetail = iProductSkuDetailService.queryByProductSkuCode(productSkuCode);
        LambdaQueryWrapper<ProductSkuPrice> lqw = new LambdaQueryWrapper<>();
        lqw.eq(ProductSkuPrice::getProductSkuCode, productSkuCode);
        lqw.eq(ProductSkuPrice::getCountryCode, productMapping.getSite());
        ProductSkuPrice productSkuPrice = iProductSkuPriceService.getBaseMapper().selectOne(lqw);
         //if (ObjectUtil.isNull(productSkuPrice)){
//            throw new RuntimeException(StrUtil.format("当前商品:{}在{}站点未查询到价格",productSkuCode,productMapping.getSite()));
//        }
        //ProductSkuPrice productSkuPrice = iProductSkuPriceService.queryByProductSkuCodeAndSite(productSkuCode);

        ProductMappingDetailVo vo = BeanUtil.toBean(productMapping, ProductMappingDetailVo.class);
        if (ObjectUtil.isNotNull(productSkuPrice)){
            vo.setOriginPrice(productSkuPrice.getPlatformPickUpPrice());
        }
        // 查询仓库配置
 //       List<SkuWarehouseMappingVo> skuWarehouseMappingVo = getSkuWarehouseMappingVo(productSkuCode, channelId, channelType.name());
//        vo.setWarehouseConfigs(skuWarehouseMappingVo);
        BigDecimal length = productSkuDetail.getLength();
        BigDecimal width = productSkuDetail.getWidth();
        BigDecimal height = productSkuDetail.getHeight();
        LengthUnitEnum lengthUnit = productSkuDetail.getLengthUnit();

        BigDecimal weight = productSkuDetail.getWeight();
        WeightUnitEnum weightUnit = productSkuDetail.getWeightUnit();

        BigDecimal packLength = productSkuDetail.getPackLength();
        BigDecimal packWidth = productSkuDetail.getPackWidth();
        BigDecimal packHeight = productSkuDetail.getPackHeight();
        LengthUnitEnum packLengthUnit = productSkuDetail.getPackLengthUnit();

        BigDecimal packWeight = productSkuDetail.getPackWeight();
        WeightUnitEnum packWeightUnit = productSkuDetail.getPackWeightUnit();

        StringBuilder sizeBuilder = new StringBuilder().append(length).append(" x ").append(width).append(" x ")
                                                       .append(height).append(" ").append(lengthUnit.name());
        StringBuilder packSizeBuilder = new StringBuilder().append(packLength).append(" x ").append(packWidth)
                                                           .append(" x ").append(packHeight).append(" ")
                                                           .append(packLengthUnit.name());
        StringBuilder weightBuilder = new StringBuilder().append(weight).append(" ").append(weightUnit.name());
        StringBuilder packWeightBuilder = new StringBuilder().append(packWeight).append(" ")
                                                             .append(packWeightUnit.name());
        //获取成本价
      //  BigDecimal costPrice = getProductMappringCostPrice(productMapping.getSupplierTenantId(), productMapping.getTenantId(), productMapping.getProductSkuCode(), tenantSalesChannelVo.getLogisticsType());
      //  vo.setBasePrice(costPrice);
     //   vo.setMarkUpTotal(iProductMappingService.calculateFinalPrice(productMapping.getMarkUpType(), productMapping.getMarkUpValue(), costPrice));
        vo.setSize(sizeBuilder.toString());
        vo.setPackSize(packSizeBuilder.toString());
        vo.setWeight(weightBuilder.toString());
        vo.setPackWeight(packWeightBuilder.toString());
        vo.setChannelId(tenantSalesChannelVo.getId());


//        if (StrUtil.isNotBlank(activityCode)) {
//            vo.setActivityCode(activityCode);
//            vo.setSelectActivity(CollUtil.newLinkedList(productMapping.getActivityType().name(), activityCode));
//            ProductActivityItem activityItem = iProductActivityItemService.queryOneByEntity(ProductActivityItem.builder()
//                                                                                                               .activityCode(activityCode)
//                                                                                                               .build());
//            if (activityItem != null) {
//                ProductActivityItemStateEnum activityState = activityItem.getActivityState();
//                vo.setActivityState(activityState.name());
//            }
//        }

//        List<MappingAvailableActivityVo> availableActivities = new ArrayList<>();
//        ProductActivityTypeEnum[] values = ProductActivityTypeEnum.values();
//        for (ProductActivityTypeEnum typeEnum : values) {
//            List<ActivityItemSimpleVo> simpleVoList = iProductActivityItemService.querySelectByProductSkuCode(productSkuCode, typeEnum, channelType);
//            if (CollUtil.isNotEmpty(simpleVoList)) {
//                List<MappingAvailableActivityInfoVo> children = BeanUtil.copyToList(simpleVoList, MappingAvailableActivityInfoVo.class);
//                availableActivities.add(new MappingAvailableActivityVo(typeEnum.name(), typeEnum.name(), children));
//            }
//        }
//        vo.setAvailableActivities(availableActivities);

        // 乐天专属品类信息
        if (ChannelTypeEnum.Rakuten.equals(channelType)) {
            ProductMappingExtendRakutenInfo rakutenInfo = iProductMappingExtendRakutenInfoService.queryByProductMappingId(productMapping.getId());
            if (rakutenInfo != null) {
                String genreId = rakutenInfo.getGenreId();
                if (StrUtil.isNotBlank(genreId)) {
                    ChannelExtendRakutenGenre rakutenGenre = iChannelExtendRakutenGenreService.queryByGenreId(genreId);
                    vo.setCurRakutenGenreId(rakutenGenre.getGenreId());
                    vo.setCurRakutenGenreName(rakutenGenre.getNameJa());
                }
            }
        }
        return R.ok(vo);
    }

    /**
     * 更新Sku映射商品编码
     * @param bo
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public R<List<ProductMapping>> updateProductMappingChannelSku(SaveSkuMappingBo bo) throws RuntimeException {
        List<ProductMapping> productMappingList=new ArrayList<>();
        List<SkuMappingBo> skuMappings = bo.getSkuMappings();
        List<FulfillWarehouseBo> batchWarehouseConfigs = bo.getWarehouseConfigs();
        if (CollUtil.isNotEmpty(batchWarehouseConfigs)) {
            saveWarehouseMapping(batchWarehouseConfigs);
        }
        if (CollUtil.isNotEmpty(skuMappings)) {
            for (SkuMappingBo skuMapping : skuMappings) {
                String id = skuMapping.getId();
                if (ObjectUtil.isNull(id)){
                    throw  new RuntimeException("商品映射id不能为空"+skuMapping.getId());
                }
                ProductMapping productMappingDB =TenantHelper.ignore(()->iProductMappingService.getById(id));
                if (ObjectUtil.isNull(productMappingDB)){
                    throw  new RuntimeException("商品映射不存在"+skuMapping.getId());
                }
                if (ObjectUtil.isEmpty(skuMapping.getMappingSku())){
                    throw  new RuntimeException("商品映射SKU不能为空"+skuMapping.getMappingSku());
                }
                //如果一样的话，跳出循环
                if (productMappingDB.getMappingSku().equals(skuMapping.getMappingSku())){
                    //一样的话，不处理
                    throw new RuntimeException("映射前后商品编码一致，无需更新映射！");
                }else {
                    Boolean isTkOrTm;
                    //校验店铺是否存在
                    LambdaQueryWrapper<TenantSalesChannel> lqw = Wrappers.lambdaQuery();
                    lqw.eq(TenantSalesChannel::getId, productMappingDB.getChannelId());
                    lqw.eq(TenantSalesChannel::getState, GlobalStateEnum.Valid.getValue());
                    TenantSalesChannel tenantSalesChannel = TenantHelper.ignore(()->iTenantSalesChannelService.getBaseMapper().selectOne(lqw));
                    if (ObjectUtil.isNull(tenantSalesChannel)){
                        throw  new RuntimeException(" 店铺不存在/已禁用: "+productMappingDB.getChannelId());
                    }
                    //校验映射是否已经存在
                    //校验数据库 当前映射是否已经存在
//                    LambdaQueryWrapper<ProductMapping> wrapper = new LambdaQueryWrapper<>();
//                    wrapper.eq(ProductMapping::getProductSkuCode,skuMapping.getMappingSku());
//                    wrapper.eq(ProductMapping::getChannelSku,productMappingDB.getChannelSku());
//                    wrapper.eq(ProductMapping::getChannelId,productMappingDB.getChannelId());
//                    ProductMapping mapping = iProductMappingService.getBaseMapper().selectOne(wrapper);
//                    if (ObjectUtil.isNotNull(mapping)){
//                        throw  new RuntimeException(" 店铺名称："+tenantSalesChannel.getChannelName()+",渠道SKU："+productMappingDB.getChannelSku()+" 商品编码："+skuMapping.getMappingSku()+"的商品映射已存在,请勿重复映射!");
//                    }

                    //校验商品是否存在
                    ProductSku productSku = iProductSkuService.queryByProductSkuCode(skuMapping.getMappingSku());
                    if (ObjectUtil.isNull(productSku)){
                        throw  new RuntimeException("item.No信息不存在"+skuMapping.getMappingSku());
                    }
                    //校验产品信息是否存在
                    Product product = iProductService.queryByProductSkuCode(productSku.getProductSkuCode());
                    if (ObjectUtil.isNull(product)){
                        throw  new RuntimeException("产品信息不存在"+productSku.getProductSkuCode());
                    }

                    if (ObjectUtil.isNotNull(tenantSalesChannel)){
                        if (ObjectUtil.isEmpty(tenantSalesChannel.getChannelType())){
                            throw  new RuntimeException(" 店铺渠道类型为空 "+productMappingDB.getChannelId());
                        }
                        //校验渠道店铺发货方式和商品发货方式是否不一致
                        boolean isSupport = product.getSupportedLogistics()
                                                   .allowShipping(LogisticsTypeEnum.getLogisticsTypeEnumByName(String.valueOf(tenantSalesChannel.getLogisticsType())));
                        if (!isSupport){
                            throw  new RuntimeException(" 商品 "+skuMapping.getMappingSku()+"发货方式和渠道店铺配置发货方式不一致!");
                        }
                        if (ChannelTypeEnum.Temu.name().equals(tenantSalesChannel.getChannelType()) || ChannelTypeEnum.TikTok.name().equals(tenantSalesChannel.getChannelType())){
                            isTkOrTm=true;
                        } else {
                            isTkOrTm = false;
                        }
                    } else {
                        isTkOrTm = false;
                    }

                    // 查询当前映射的渠道Sku,最新的商品编码,好未删除的记录
                    LambdaQueryWrapper<ProductMapping> lqw1 = new LambdaQueryWrapper<>();
                    lqw1.eq(ProductMapping::getChannelSku,productMappingDB.getChannelSku());
                    lqw1.eq(ProductMapping::getSyncState, SyncStateEnum.Mapped);
                    lqw1.eq(ProductMapping::getProductSkuCode,productMappingDB.getProductSkuCode());
                    lqw1.eq(ProductMapping::getTenantId,LoginHelper.getTenantId());
                    List<ProductMapping> productMappings = iProductMappingService.getBaseMapper().selectList(lqw1);
                    productMappings.forEach(s->{
                        // 站点没有产品价格的数据跳过
                        ProductSkuPrice productSkuPrice = iProductSkuPriceService.queryByProductSkuCodeAndCountryCode(productSku.getProductSkuCode(), s.getSite());
                        if (ObjectUtil.isNull(productSkuPrice)){
                            return;
                        }
                        //更新映射信息
                        s.setSupplierTenantId(productSku.getTenantId());
                        s.setTenantId(LoginHelper.getTenantId());
                        s.setProductSkuCode(productSku.getProductSkuCode());
                        s.setMappingSku(skuMapping.getMappingSku());
                        s.setProductCode(productSku.getProductCode());
                        s.setProductSkuId(productSku.getId());
                        s.setSpecComposeName(productSku.getSpecComposeName());
                        s.setSpecValName(productSku.getSpecValName());
                        s.setChannelId(tenantSalesChannel.getId());
                        s.setChannelType(ChannelTypeEnum.valueOf(tenantSalesChannel.getChannelType()));
                        s.setCreateBy(10000L);
                        s.setSyncState(SyncStateEnum.Mapped);
                        if (isTkOrTm) {
                            s.setChannelSkuId(skuMapping.getMappingSku());
                        }
                        iProductMappingService.updateById(s);
                        productMappingList.add(productMappingDB);
                    });

                }
            }
        }
        return R.ok(productMappingList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R<List<ProductMapping>> updateProductMappingChannelSkuSite(SaveSkuMappingBo bo) throws RuntimeException {
        List<ProductMapping> productMappingList=new ArrayList<>();
        List<SkuMappingBo> skuMappings = bo.getSkuMappings();
        List<FulfillWarehouseBo> batchWarehouseConfigs = bo.getWarehouseConfigs();
        if (CollUtil.isNotEmpty(batchWarehouseConfigs)) {
            saveWarehouseMapping(batchWarehouseConfigs);
        }
        if (CollUtil.isNotEmpty(skuMappings)) {
            for (SkuMappingBo skuMapping : skuMappings) {
                String id = skuMapping.getId();
                if (ObjectUtil.isNull(id)){
                    throw  new RuntimeException("商品映射id不能为空"+skuMapping.getId());
                }
                ProductMapping productMappingDB =TenantHelper.ignore(()->iProductMappingService.getById(id));
                if (ObjectUtil.isNull(productMappingDB)){
                    throw  new RuntimeException("商品映射不存在"+skuMapping.getId());
                }
                if (ObjectUtil.isEmpty(skuMapping.getMappingSku())){
                    throw  new RuntimeException("商品映射SKU不能为空"+skuMapping.getMappingSku());
                }
                //如果一样的话，跳出循环
                if (productMappingDB.getMappingSku().equals(skuMapping.getMappingSku())){
                    //一样的话，不处理
                    throw new RuntimeException("映射前后商品编码一致，无需更新映射！");
                }else {
                    Boolean isTkOrTm;
                    //校验店铺是否存在
                    LambdaQueryWrapper<TenantSalesChannel> lqw = Wrappers.lambdaQuery();
                    lqw.eq(TenantSalesChannel::getId, productMappingDB.getChannelId());
                    lqw.eq(TenantSalesChannel::getState, GlobalStateEnum.Valid.getValue());
                    TenantSalesChannel tenantSalesChannel = TenantHelper.ignore(()->iTenantSalesChannelService.getBaseMapper().selectOne(lqw));
                    if (ObjectUtil.isNull(tenantSalesChannel)){
                        throw  new RuntimeException(" 店铺不存在/已禁用: "+productMappingDB.getChannelId());
                    }
                    //校验映射是否已经存在
                    //校验数据库 当前映射是否已经存在
//                    LambdaQueryWrapper<ProductMapping> wrapper = new LambdaQueryWrapper<>();
//                    wrapper.eq(ProductMapping::getProductSkuCode,skuMapping.getMappingSku());
//                    wrapper.eq(ProductMapping::getChannelSku,productMappingDB.getChannelSku());
//                    wrapper.eq(ProductMapping::getChannelId,productMappingDB.getChannelId());
//                    ProductMapping mapping = iProductMappingService.getBaseMapper().selectOne(wrapper);
//                    if (ObjectUtil.isNotNull(mapping)){
//                        throw  new RuntimeException(" 店铺名称："+tenantSalesChannel.getChannelName()+",渠道SKU："+productMappingDB.getChannelSku()+" 商品编码："+skuMapping.getMappingSku()+"的商品映射已存在,请勿重复映射!");
//                    }

                    //校验商品是否存在
                    ProductSku productSku = iProductSkuService.queryByProductSkuCode(skuMapping.getMappingSku());
                    if (ObjectUtil.isNull(productSku)){
                        throw  new RuntimeException("item.No信息不存在"+skuMapping.getMappingSku());
                    }
                    //校验产品信息是否存在
                    Product product = iProductService.queryByProductSkuCode(productSku.getProductSkuCode());
                    if (ObjectUtil.isNull(product)){
                        throw  new RuntimeException("产品信息不存在"+productSku.getProductSkuCode());
                    }

                    if (ObjectUtil.isNotNull(tenantSalesChannel)){
                        if (ObjectUtil.isEmpty(tenantSalesChannel.getChannelType())){
                            throw  new RuntimeException(" 店铺渠道类型为空 "+productMappingDB.getChannelId());
                        }
                        //校验渠道店铺发货方式和商品发货方式是否不一致
                        boolean isSupport = product.getSupportedLogistics()
                                                   .allowShipping(LogisticsTypeEnum.getLogisticsTypeEnumByName(String.valueOf(tenantSalesChannel.getLogisticsType())));
                        if (!isSupport){
                            throw  new RuntimeException(" 商品 "+skuMapping.getMappingSku()+"发货方式和渠道店铺配置发货方式不一致!");
                        }
                        if (ChannelTypeEnum.Temu.name().equals(tenantSalesChannel.getChannelType()) || ChannelTypeEnum.TikTok.name().equals(tenantSalesChannel.getChannelType())){
                            isTkOrTm=true;
                        } else {
                            isTkOrTm = false;
                        }
                    } else {
                        isTkOrTm = false;
                    }

                    // 查询当前映射的渠道Sku,最新的商品编码,好未删除的记录
                    LambdaQueryWrapper<ProductMapping> lqw1 = new LambdaQueryWrapper<>();
                    lqw1.eq(ProductMapping::getChannelSku,productMappingDB.getChannelSku());
                    lqw1.eq(ProductMapping::getSyncState, SyncStateEnum.Mapped);
                    lqw1.eq(ProductMapping::getProductSkuCode,productMappingDB.getProductSkuCode());
                    lqw1.eq(ProductMapping::getTenantId,LoginHelper.getTenantId());
                    List<ProductMapping> productMappings = iProductMappingService.getBaseMapper().selectList(lqw1);
                    // 没有产品价格的产品映射
                    List<ProductMapping> noProductPriceSiteProductMappingList = new ArrayList<>();
                    List<String> productMappingSiteCode = new ArrayList<>();
                    productMappings.forEach(s->{
                        productMappingSiteCode.add(s.getSite());
                        // 站点没有产品价格的数据跳过
                        ProductSkuPrice productSkuPrice = iProductSkuPriceService.queryByProductSkuCodeAndCountryCode(productSku.getProductSkuCode(), s.getSite());
                        if (ObjectUtil.isNull(productSkuPrice)){
                            noProductPriceSiteProductMappingList.add(s);
                            return;
                        }
                        //更新映射信息
                        s.setSupplierTenantId(productSku.getTenantId());
                        s.setTenantId(LoginHelper.getTenantId());
                        s.setProductSkuCode(productSku.getProductSkuCode());
                        s.setMappingSku(skuMapping.getMappingSku());
                        s.setProductCode(productSku.getProductCode());
                        s.setProductSkuId(productSku.getId());
                        s.setSpecComposeName(productSku.getSpecComposeName());
                        s.setSpecValName(productSku.getSpecValName());
                        s.setChannelId(tenantSalesChannel.getId());
                        s.setChannelType(ChannelTypeEnum.valueOf(tenantSalesChannel.getChannelType()));
                        s.setCreateBy(10000L);
                        s.setSyncState(SyncStateEnum.Mapped);
                        if (isTkOrTm) {
                            s.setChannelSkuId(skuMapping.getMappingSku());
                        }
                        iProductMappingService.updateById(s);
                        productMappingList.add(productMappingDB);
                    });
                    // 产品新站点价格映射数据
                    List<SiteCountryCurrency> list = siteCountryCurrencyMapper.selectList();
                    for (SiteCountryCurrency siteCountryCurrency : list){
                        if (productMappingSiteCode.contains(siteCountryCurrency.getCountryCode())){
                            continue;
                        }
                        ProductSkuPrice productSkuPrice = iProductSkuPriceService.queryByProductSkuCodeAndCountryCode(productSku.getProductSkuCode(), siteCountryCurrency.getCountryCode());
                        if(null != productSkuPrice){
                            ProductMapping productMapping = new ProductMapping();
                            productMapping.setSite(siteCountryCurrency.getCountryCode());
                            productMapping.setCurrency(siteCountryCurrency.getCurrencySymbol());
                            productMapping.setSupplierTenantId(productSku.getTenantId());
                            productMapping.setTenantId(LoginHelper.getTenantId());
                            productMapping.setProductSkuCode(productSku.getProductSkuCode());
                            productMapping.setMappingSku(productSku.getProductSkuCode());
                            productMapping.setProductCode(productSku.getProductCode());
                            productMapping.setProductSkuId(productSku.getId());
                            productMapping.setSpecComposeName(productSku.getSpecComposeName());
                            productMapping.setSpecValName(productSku.getSpecValName());
                            productMapping.setChannelId(tenantSalesChannel.getId());
                            productMapping.setChannelType(ChannelTypeEnum.valueOf(tenantSalesChannel.getChannelType()));
                            if (isTkOrTm) {
                                productMapping.setChannelSkuId(productMappingDB.getChannelSku());
                            }
                            productMapping.setCreateBy(10000L);
                            productMapping.setSyncState(SyncStateEnum.Mapped);
                            productMapping.setChannelSku(productMappingDB.getChannelSku());
                            iProductMappingService.save(productMapping);
                            productMappingList.add(productMappingDB);
                        }
                    }
                    // 删除没有站点价格的映射数据
                    if(CollUtil.isNotEmpty(noProductPriceSiteProductMappingList)){
                        List<Long> productMappingIds = noProductPriceSiteProductMappingList.stream().map(ProductMapping::getId)
                                                               .collect(Collectors.toList());
                        if(CollUtil.isNotEmpty(productMappingIds)){
                            iProductMappingService.batchUpdateSyncStateByIds(productMappingIds, SyncStateEnum.Deleting);
                        }
                    }
                }
            }
        }
        return R.ok(productMappingList);
    }

    /**
     * 单独设置分销商商品MarkUp
     *
     * @param bo
     */
    @Override
    public R<Void> setMarkUp(SetMarkUpBo bo) {
        Long productMappingId = bo.getId();
        String name = bo.getName();
        String markUpType = bo.getMarkUpType();
        String activityCode = bo.getActivityCode();
        BigDecimal markUpValue = bo.getMarkUpValue();
        BigDecimal markUpTotal = bo.getMarkUpTotal();

        if (StrUtil.isBlank(name)) {
            return R.fail(ZSMallStatusCodeEnum.REQUIRED_CANNOT_EMPTY);
        }

        ProductMapping productMapping = iProductMappingService.getById(productMappingId);
        if (productMapping != null) {
            ChannelTypeEnum channelType = productMapping.getChannelType();
            String productSkuCode = productMapping.getProductSkuCode();

            if (productMapping.getSyncState().unable()) {
                return R.fail(ZSMallStatusCodeEnum.PRODUCT_SYNCHRONIZING.args(productSkuCode));
            }

            boolean changeActivity = false;
            String originActivityCode = productMapping.getActivityCode();
            if (!StrUtil.equals(activityCode, originActivityCode)) {
                changeActivity = true;
            }

            // Wayfair都是自提价，其他平台都是代发价
            BigDecimal basePrice = null;
            if (StrUtil.isNotBlank(activityCode)) {  // 判断是否参加活动
//                ProductActivityItem activityItem = iProductActivityItemService.queryOneByEntity(ProductActivityItem.builder()
//                                                                                                                   .activityCode(activityCode)
//                                                                                                                   .build());
//                if (ObjectUtil.isNull(activityItem)) {
//                    return R.fail(ZSMallStatusCodeEnum.PRODUCT_ACTIVITY_NOT_EXIST.args(activityCode));
//                }
//
//                ProductActivityPriceItem activityPriceItem = iProductActivityPriceItemService.queryByActivityItemId(activityItem.getId());
//                basePrice = ChannelTypeEnum.Wayfair.equals(channelType) ? activityPriceItem.getPlatformPickUpPrice() : activityPriceItem.getPlatformDropShippingPrice();
//                ;
//                productMapping.setActivityCode(activityCode);
//                productMapping.setActivityType(activityItem.getActivityType());
            } else {
                ProductSkuPrice productSkuPrice = iProductSkuPriceService.queryByProductSkuCode(productSkuCode);
                basePrice = productSupport.getProductSkuPrice(channelType, productSkuPrice);
                productMapping.setActivityCode(null);
                productMapping.setActivityType(null);
            }

            MarkUpTypeEnum markUpTypeEnum;
            if (ChannelTypeEnum.Shopify.equals(channelType) || ChannelTypeEnum.Rakuten.equals(channelType)) {
                if (ObjectUtil.isEmpty(markUpType)){
                    return R.fail("Shopify / Rakuten渠道,涨价类型不能为空");
                }
                markUpTypeEnum = MarkUpTypeEnum.valueOf(markUpType);
                if (NumberUtil.isLessOrEqual(markUpValue, BigDecimal.ZERO)) {
                    return R.fail(ZSMallStatusCodeEnum.PRODUCT_SKU_MARK_UP_NOT_ZERO);
                }
            } else {  // 除了Shopify之外的渠道不能设置MarkUp，所以此处固定涨价值为0元
                markUpTypeEnum = MarkUpTypeEnum.Amount;
                markUpValue = BigDecimal.ZERO;
                markUpTotal = basePrice;
            }

            BigDecimal finalPrice = iProductMappingService.calculateFinalPrice(markUpTypeEnum, markUpValue, basePrice);

            if (!NumberUtil.equals(finalPrice, markUpTotal)) {
                return R.fail(ZSMallStatusCodeEnum.COST_PRICE_HAS_CHANGED);
            }

            String originProductName = productMapping.getProductName();
            BigDecimal originFinalPrice = productMapping.getFinalPrice();

            productMapping.setProductName(name);
            productMapping.setMarkUpType(markUpTypeEnum);
            productMapping.setMarkUpValue(markUpValue);
            productMapping.setFinalPrice(finalPrice);
            productMapping.setPriceChanges(false);

            // 商品名或者金额发生变更或者活动发生改变，且是已经同步的商品，需要更新数据
            if (!StrUtil.equals(originProductName, name) || !NumberUtil.equals(originFinalPrice, finalPrice) || changeActivity) {
                if (ObjectUtil.equals(productMapping.getSyncState(), SyncStateEnum.Synced)) {
                    productMapping.setSyncState(SyncStateEnum.Updating);
                }
            }

            iProductMappingService.updateById(productMapping);

            // 乐天相关信息保存
            if (ChannelTypeEnum.Rakuten.equals(channelType)) {
                String curRakutenGenreId = bo.getCurRakutenGenreId();
                ProductMappingExtendRakutenInfo rakutenInfo = iProductMappingExtendRakutenInfoService.queryByProductMappingId(productMappingId);
                if (rakutenInfo == null) {
                    rakutenInfo = new ProductMappingExtendRakutenInfo();
                    rakutenInfo.setProductMappingId(productMappingId);
                }

                rakutenInfo.setGenreId(bo.getCurRakutenGenreId());
                iProductMappingExtendRakutenInfoService.saveOrUpdate(rakutenInfo);
            }
        } else {
            return R.fail(ZSMallStatusCodeEnum.PRODUCT_MAPPING_NOT_EXISTS);
        }
        return R.ok();
    }

    /**
     * 批量设置分销商商品MarkUp
     *
     * @param bo
     */
    @Override
    public R<Void> batchSetMarkUp(BatchSetMarkUpBo bo) {
        List<Long> idList = bo.getIdList();
        String markUpType = bo.getMarkUpType();
        BigDecimal markUpValue = bo.getMarkUpValue();
        if (CollUtil.isEmpty(idList) || StrUtil.isBlank(markUpType) || markUpValue == null) {
            return R.fail(ZSMallStatusCodeEnum.REQUIRED_CANNOT_EMPTY);
        }

        if (NumberUtil.isLessOrEqual(markUpValue, BigDecimal.ZERO)) {
            return R.fail(ZSMallStatusCodeEnum.PRODUCT_SKU_MARK_UP_NOT_ZERO);
        }

        List<ProductMapping> productMappingList = new ArrayList<>();
        MarkUpTypeEnum markUpTypeEnum = MarkUpTypeEnum.valueOf(markUpType);
        for (Long id : idList) {
            ProductMapping productMapping = iProductMappingService.getById(id);
            if (productMapping != null) {
                if (productMapping.getSyncState().unable()) {
                    return R.fail(ZSMallStatusCodeEnum.PRODUCT_SYNCHRONIZING.args(productMapping.getProductSkuCode()));
                }
                productMappingList.add(productMapping);
            }
        }

        for (ProductMapping productMapping : productMappingList) {
            String activityCode = productMapping.getActivityCode();
            ChannelTypeEnum channelType = productMapping.getChannelType();
            BigDecimal originFinalPrice = productMapping.getFinalPrice();


            BigDecimal basePrice = null;
            if (StrUtil.isNotBlank(activityCode)) {  // 判断是否参加活动
//                ProductActivityItem activityItem = iProductActivityItemService.queryOneByEntity(ProductActivityItem.builder()
//                                                                                                                   .activityCode(activityCode)
//                                                                                                                   .build());
//                if (ObjectUtil.isNull(activityItem)) {
//                    return R.fail(ZSMallStatusCodeEnum.PRODUCT_ACTIVITY_NOT_EXIST.args(activityCode));
//                }
//                ProductActivityPriceItem activityPriceItem = iProductActivityPriceItemService.queryByActivityItemId(activityItem.getId());
//                basePrice = ChannelTypeEnum.Wayfair.equals(channelType) ? activityPriceItem.getPlatformPickUpPrice() : activityPriceItem.getPlatformDropShippingPrice();
                ;
            } else {
                ProductSkuPrice productSkuPrice = iProductSkuPriceService.queryByProductSkuCode(productMapping.getProductSkuCode());
                basePrice = productSupport.getProductSkuPrice(channelType, productSkuPrice);
            }

            productMapping.setMarkUpType(markUpTypeEnum);
            productMapping.setMarkUpValue(markUpValue);
            // 计算sku finalPrice
            productMapping.setFinalPrice(iProductMappingService.calculateFinalPrice(markUpTypeEnum, markUpValue, basePrice));

            if (ObjectUtil.equals(productMapping.getSyncState(), SyncStateEnum.Synced) && !NumberUtil.equals(originFinalPrice, productMapping.getFinalPrice())) {
                productMapping.setSyncState(SyncStateEnum.Updating);
            }
        }
        iProductMappingService.updateBatchById(productMappingList);
        return R.ok();
    }


    /**
     * 上传SKU映射
     *
     * @param file
     * @return
     */
    @Transactional
    @Override
    public R<List<ProductMapping>> uploadSkuMapping(MultipartFile file) throws IOException,RuntimeException {
        if (file == null) {
            return R.fail(ZSMallStatusCodeEnum.UPLOAD_FILE_IS_EMPTY);
        }
        returnProductMappings=new ArrayList<>();
        InputStream inputStream = file.getInputStream();
        EasyExcel.read(inputStream, ProductMappingImportDTO.class, new ReadListener<ProductMappingImportDTO>() {
            /**
             * 单次缓存的数据量
             */
            public static final int BATCH_COUNT = 300;
            /**
             * 临时存储
             */
            //private List<ProductMapping> cachedDataList = ListUtils.newArrayListWithExpectedSize(BATCH_COUNT);

            @Override
            public void invoke(ProductMappingImportDTO data, AnalysisContext context) {
                //校验表格字段
                checkProductMappingImportDTOSite(data, context);
//                if (cachedDataList.size() >= BATCH_COUNT) {
//                    saveData();
//                    // 存储完成清理 list
//                    cachedDataList = ListUtils.newArrayListWithExpectedSize(BATCH_COUNT);
//                }
            }

            @Override
            public void doAfterAllAnalysed(AnalysisContext context) {
                saveData();
            }

            /**
             * 保存数据
             */
            private void saveData() {
//                if (CollectionUtil.isEmpty(cachedDataList)) {
//                    return;
//                }
//                List<ProductMapping> uniqueProductMappings = new ArrayList<>(cachedDataList.stream()
//                                                                                           .collect(Collectors.toMap(
//                                                                                               // key为ChannelSku和ChannelId的组合
//                                                                                               productMapping -> productMapping.getChannelSku() + productMapping.getChannelId() + productMapping.getProductSkuCode(),
//                                                                                               // value为ProductMapping对象
//                                                                                               Function.identity(),
//                                                                                               // 如果key重复，则取第一个值
//                                                                                               (existing, replacement) -> existing
//                                                                                           ))
//                                                                                           .values());
//                //封装数据库存储对象
//                if (!uniqueProductMappings.isEmpty()) {
//                    iProductMappingService.saveBatch(uniqueProductMappings);
//                    returnProductMappings.addAll(uniqueProductMappings);
//                }
//                log.info("{}条数据，开始存储数据库！", uniqueProductMappings.size());
//                log.info("存储数据库成功！");
            }
        }).sheet().headRowNumber(10).doRead();
        return R.ok(returnProductMappings);
    }

    public  void  cheackProductMappingImportDTO(ProductMappingImportDTO data, AnalysisContext context){
        Boolean isTkOrTm=false;
        List<String> errors = new ArrayList<>();
        if (StrUtil.isBlank(data.getChannelSku())) {
            errors.add("渠道SKU不能为空");
        }
        if (StrUtil.isBlank(data.getProductSkuCode())) {
            errors.add("Item.No不能为空");
        }
        if (StrUtil.isBlank(data.getChannelName())) {
            errors.add("店铺名称不能为空");
        }

        //校验商品是否存在
        ProductSku productSku = iProductSkuService.queryByProductSkuCode(data.getProductSkuCode());
        if (ObjectUtil.isNull(productSku)){
            errors.add("item.No信息不存在: "+data.getProductSkuCode());
        }

        //校验产品信息是否存在
        Product product = iProductService.queryByProductSkuCode(data.getProductSkuCode());
        if (ObjectUtil.isNull(product)){
            errors.add("产品信息不存在: "+data.getProductSkuCode());
        }

        if (ObjectUtil.isNotNull(product)){
            if (!product.getShelfState().equals(ShelfStateEnum.OnShelf)){
                throw  new RuntimeException(StrUtil.format("商品：{}已经下架，不能被映射。",data.getProductSkuCode()));
            }
        }

        //校验店铺是否存在
        LambdaQueryWrapper<TenantSalesChannel> lqw = Wrappers.lambdaQuery();
        lqw.eq(TenantSalesChannel::getChannelName, data.getChannelName());
        lqw.eq(TenantSalesChannel::getState, GlobalStateEnum.Valid.getValue());
        TenantSalesChannel tenantSalesChannel =iTenantSalesChannelService.getBaseMapper().selectOne(lqw);
         if (ObjectUtil.isNull(tenantSalesChannel)){
            errors.add(" 店铺名称不存在/已禁用: "+data.getChannelName());
        }
        if (ObjectUtil.isNotNull(tenantSalesChannel)){
            if (ObjectUtil.isEmpty(tenantSalesChannel.getChannelType())){
                errors.add(" 店铺渠道类型为空: "+tenantSalesChannel.getId());
            }
            if (ChannelTypeEnum.Temu.name().equals(tenantSalesChannel.getChannelType()) || ChannelTypeEnum.TikTok.name().equals(tenantSalesChannel.getChannelType())){
                //校验数字
                boolean match = ReUtil.isMatch("^\\d+$", data.getChannelSku());
                if (!match){
                    errors.add("Temu /TikTok 渠道SKU应该为纯数字");
                }
                isTkOrTm=true;
            }
            //校验渠道店铺发货方式和商品发货方式是否不一致
            if (ObjectUtil.isNotNull(product) && ObjectUtil.isNotNull(tenantSalesChannel) ){
                boolean isSupport = product.getSupportedLogistics()
                                           .allowShipping(LogisticsTypeEnum.getLogisticsTypeEnumByName(String.valueOf(tenantSalesChannel.getLogisticsType())));
                if (!isSupport){
                    errors.add("商品: "+data.getProductSkuCode()+"发货方式和渠道店铺配置发货方式不一致");
                }
            }
        }

        if (ObjectUtil.isNotNull(tenantSalesChannel)){
            //校验数据库 当前映射是否已经存在
            LambdaQueryWrapper<ProductMapping> wrapper = new LambdaQueryWrapper<>();
           // wrapper.eq(ProductMapping::getProductSkuCode,data.getProductSkuCode());
            wrapper.eq(ProductMapping::getChannelSku,data.getChannelSku());
            wrapper.eq(ProductMapping::getChannelId,tenantSalesChannel.getId());
            wrapper.eq(ProductMapping::getSyncState,SyncStateEnum.Mapped.name());
            boolean   mapping = iProductMappingService.getBaseMapper().exists(wrapper);
            if (mapping){
                //errors.add(" 店铺名称："+tenantSalesChannel.getChannelName()+",渠道SKU："+data.getChannelSku()+" 商品编码："+data.getProductSkuCode()+"的商品不能重复！");
              //  throw new RuntimeException("同一个店铺下相同的渠道SKU不能映射到不同的item no");
                throw new RuntimeException("表格第" + (context.getCurrentRowNum() + 1) + "行:  店铺名称："+tenantSalesChannel.getChannelName()+",渠道SKU："+data.getChannelSku()+" 商品编码："+data.getProductSkuCode()+"数据已存在不能重复！");
            }
        }

        if (!errors.isEmpty()) {
            throw new RuntimeException("表格第" + (context.getCurrentRowNum() + 1) + "行: " + String.join("; ", errors));
        }
        //如果数据不存在
        //查询站点信息
        List<SiteCountryCurrency> list = siteCountryCurrencyMapper.selectList();
        for (SiteCountryCurrency s : list) {
            ProductMapping productMapping = BeanUtil.copyProperties(data, ProductMapping.class);
            productMapping.setSite(s.getCountryCode());
            productMapping.setCurrency(s.getCurrencySymbol());
            productMapping.setSupplierTenantId(productSku.getTenantId());
            productMapping.setTenantId(LoginHelper.getTenantId());
            productMapping.setProductSkuCode(productSku.getProductSkuCode());
            productMapping.setMappingSku(productSku.getProductSkuCode());
            productMapping.setProductCode(productSku.getProductCode());
            productMapping.setProductSkuId(productSku.getId());
            productMapping.setSpecComposeName(productSku.getSpecComposeName());
            productMapping.setSpecValName(productSku.getSpecValName());
            productMapping.setChannelId(tenantSalesChannel.getId());
            productMapping.setChannelType(ChannelTypeEnum.valueOf(tenantSalesChannel.getChannelType()));
            if (isTkOrTm) {
                productMapping.setChannelSkuId(data.getChannelSku());
            }
          //  productMapping.setCreateBy(Long.valueOf(LoginHelper.getTenantId()));
            productMapping.setSyncState(SyncStateEnum.Mapped);
           // cachedDataList.add(productMapping);
           iProductMappingService.getBaseMapper().insert(productMapping);
           returnProductMappings.add(productMapping);

        }
    }

    /**
     * 商品映射导入-产品站点
     * @param data
     * @param context
     */
    public void checkProductMappingImportDTOSite(ProductMappingImportDTO data, AnalysisContext context){
        Boolean isTkOrTm=false;
        List<String> errors = new ArrayList<>();
        if (StrUtil.isBlank(data.getChannelSku())) {
            errors.add("渠道SKU不能为空");
        }
        if (StrUtil.isBlank(data.getProductSkuCode())) {
            errors.add("Item.No不能为空");
        }
        if (StrUtil.isBlank(data.getChannelName())) {
            errors.add("店铺名称不能为空");
        }
        //校验商品是否存在
        ProductSku productSku = iProductSkuService.queryByProductSkuCode(data.getProductSkuCode());
        if (ObjectUtil.isNull(productSku)){
            errors.add("item.No信息不存在: "+data.getProductSkuCode());
        }
        //校验产品信息是否存在
        Product product = iProductService.queryByProductSkuCode(data.getProductSkuCode());
        if (ObjectUtil.isNull(product)){
            errors.add("产品信息不存在: "+data.getProductSkuCode());
        }

        if (ObjectUtil.isNotNull(product)){
            if (!product.getShelfState().equals(ShelfStateEnum.OnShelf)){
                throw  new RuntimeException(StrUtil.format("商品：{}已经下架，不能被映射。",data.getProductSkuCode()));
            }
        }
        //校验店铺是否存在
        LambdaQueryWrapper<TenantSalesChannel> lqw = Wrappers.lambdaQuery();
        lqw.eq(TenantSalesChannel::getChannelName, data.getChannelName());
        lqw.eq(TenantSalesChannel::getState, GlobalStateEnum.Valid.getValue());
        TenantSalesChannel tenantSalesChannel =iTenantSalesChannelService.getBaseMapper().selectOne(lqw);
        if (ObjectUtil.isNull(tenantSalesChannel)){
            errors.add(" 店铺名称不存在/已禁用: "+data.getChannelName());
        }
        if (ObjectUtil.isNotNull(tenantSalesChannel)){
            if (ObjectUtil.isEmpty(tenantSalesChannel.getChannelType())){
                errors.add(" 店铺渠道类型为空: "+tenantSalesChannel.getId());
            }
            if (ChannelTypeEnum.Temu.name().equals(tenantSalesChannel.getChannelType()) || ChannelTypeEnum.TikTok.name().equals(tenantSalesChannel.getChannelType())){
                //校验数字
                boolean match = ReUtil.isMatch("^\\d+$", data.getChannelSku());
                if (!match){
                    errors.add("Temu /TikTok 渠道SKU应该为纯数字");
                }
                isTkOrTm=true;
            }
            //校验渠道店铺发货方式和商品发货方式是否不一致
            if (ObjectUtil.isNotNull(product) && ObjectUtil.isNotNull(tenantSalesChannel) ){
                boolean isSupport = product.getSupportedLogistics()
                                           .allowShipping(LogisticsTypeEnum.getLogisticsTypeEnumByName(String.valueOf(tenantSalesChannel.getLogisticsType())));
                if (!isSupport){
                    errors.add("商品: "+data.getProductSkuCode()+"发货方式和渠道店铺配置发货方式不一致");
                }
            }
        }
        String cacheMapValue = sysConfigService.selectConfigByKey("Generate_Data_With_CN_Currency_Code");
        List<String> ignore= JSONUtil.toList(cacheMapValue, String.class);
        String tenantId = LoginHelper.getTenantId();
        boolean isIgnoredTenant = ignore.contains(tenantId);
        if (ObjectUtil.isNotNull(tenantSalesChannel)){
            //查询站点信息
            List<SiteCountryCurrency> list = siteCountryCurrencyMapper.selectList();
            for (SiteCountryCurrency s : list) {
                LambdaQueryWrapper<ProductMapping> wrapper = new LambdaQueryWrapper<>();
                wrapper.eq(ProductMapping::getProductSkuCode,data.getProductSkuCode());
                wrapper.eq(ProductMapping::getChannelSku,data.getChannelSku());
                wrapper.eq(ProductMapping::getChannelId,tenantSalesChannel.getId());
                wrapper.eq(ProductMapping::getSyncState,SyncStateEnum.Mapped.name());
                wrapper.eq(ProductMapping::getSite,s.getCountryCode());
                boolean mapping = iProductMappingService.getBaseMapper().exists(wrapper);
                // 已存在不生成
                if(mapping){
                    continue;
                }

                ProductSkuPrice productSkuPrice = iProductSkuPriceService.queryByProductSkuCodeAndCountryCode(productSku.getProductSkuCode(), s.getCountryCode());
                if (ObjectUtil.isNull(productSkuPrice)){
                    continue;
                }
                if (isIgnoredTenant || !"CN".equals(s.getCountryCode())) {
                    ProductMapping productMapping = BeanUtil.copyProperties(data, ProductMapping.class);
                    productMapping.setSite(s.getCountryCode());
                    productMapping.setCurrency(s.getCurrencySymbol());
                    productMapping.setSupplierTenantId(productSku.getTenantId());
                    productMapping.setTenantId(LoginHelper.getTenantId());
                    productMapping.setProductSkuCode(productSku.getProductSkuCode());
                    productMapping.setMappingSku(productSku.getProductSkuCode());
                    productMapping.setProductCode(productSku.getProductCode());
                    productMapping.setProductSkuId(productSku.getId());
                    productMapping.setSpecComposeName(productSku.getSpecComposeName());
                    productMapping.setSpecValName(productSku.getSpecValName());
                    productMapping.setChannelId(tenantSalesChannel.getId());
                    productMapping.setChannelType(ChannelTypeEnum.valueOf(tenantSalesChannel.getChannelType()));
                    if (isTkOrTm) {
                        productMapping.setChannelSkuId(data.getChannelSku());
                    }
                    //  productMapping.setCreateBy(Long.valueOf(LoginHelper.getTenantId()));
                    productMapping.setSyncState(SyncStateEnum.Mapped);
                    // cachedDataList.add(productMapping);
                    iProductMappingService.getBaseMapper().insert(productMapping);
                    returnProductMappings.add(productMapping);
                }
            }
        }
        if (!errors.isEmpty()) {
            throw new RuntimeException("表格第" + (context.getCurrentRowNum() + 1) + "行: " + String.join("; ", errors));
        }
    }

    /**
     * 推送商品映射至销售渠道
     *
     * @param bo
     * @return
     */
    @Override
    public R<Void> pushProductMapping(PushProductBo bo) {
        Long id = bo.getId();
        String syncState = bo.getSyncState();
        if (id == null || StrUtil.isBlank(syncState)) {
            return R.fail(ZSMallStatusCodeEnum.REQUIRED_CANNOT_EMPTY);
        }
        ProductMapping productMapping = iProductMappingService.getById(id);
        if (productMapping == null) {
            return R.fail(ZSMallStatusCodeEnum.PRODUCT_MAPPING_NOT_EXISTS);
        }

        if (productMapping.getSyncState().unable()) {
            return R.fail(ZSMallStatusCodeEnum.PRODUCT_SYNCHRONIZING.args(productMapping.getProductSkuCode()));
        }

        Long productMappingId = productMapping.getId();
        Long channelId = productMapping.getChannelId();
        SyncStateEnum syncStateEnum = productMapping.getSyncState();
        String productSkuCode = productMapping.getProductSkuCode();
        ChannelTypeEnum channelType = productMapping.getChannelType();

        TenantSalesChannelVo salesChannelVo = iTenantSalesChannelService.queryById(channelId);
        if (salesChannelVo == null || !GlobalStateEnum.Valid.getValue().equals(salesChannelVo.getState())) {
            return R.fail(ZSMallStatusCodeEnum.SALES_CHANNEL_CANNOT_SYNC);
        }

        ProductSku productSku = iProductSkuService.queryByProductSkuCode(productSkuCode);
        if (productSku == null || ObjectUtil.equals(productSku.getStockTotal(), 0)) {
            return R.fail(ZSMallStatusCodeEnum.OUT_OF_STORE_SYNC_FAILED);
        }

        String mappingSku = productMapping.getMappingSku();
        if (StringUtils.isBlank(mappingSku)) {
            productMapping.setMappingSku(productSkuCode);
        }

        Boolean existed = iProductMappingService.existsSameMappingSku(productMappingId, channelId, productMapping.getMappingSku());
        if (existed) {
            return R.fail(ZSMallStatusCodeEnum.MAPPING_SKU_REPEAT.args(productMapping.getMappingSku()));
        }

        if (StrUtil.equals(syncState, "Synced")) {
            if (Objects.equals(syncStateEnum, SyncStateEnum.NotSynced)
                || Objects.equals(syncStateEnum, SyncStateEnum.SyncFailed)
                || Objects.equals(syncStateEnum, SyncStateEnum.Mapped)) {

                // Rakuten渠道需要判断是否选择了分类
                rakutenGenre:
                {
                    if (ChannelTypeEnum.Rakuten.equals(channelType)) {
                        ProductMappingExtendRakutenInfo rakutenInfo = iProductMappingExtendRakutenInfoService.queryByProductMappingId(productMappingId);
                        if (rakutenInfo != null && StrUtil.isNotBlank(rakutenInfo.getGenreId())) {
                            break rakutenGenre;
                        }
                        return R.fail(ZSMallStatusCodeEnum.PLEASE_SELECT_RAKUTEN_GENRE_FIRST);
                    }
                }

                productMapping.setSyncState(SyncStateEnum.Synchronizing);
                iProductMappingService.updateById(productMapping);
            } else {
                return R.fail(ZSMallStatusCodeEnum.SYNC_STATUS_NOT_CORRECT);
            }
        } else if (StrUtil.equals(syncState, "NotSynced")) {

            if (Objects.equals(syncStateEnum, SyncStateEnum.Synced)) {
                productMapping.setSyncState(SyncStateEnum.Cancelling);
                iProductMappingService.updateById(productMapping);
            } else {
                return R.fail(ZSMallStatusCodeEnum.SYNC_STATUS_NOT_CORRECT);
            }

        } else {
            return R.fail(ZSMallStatusCodeEnum.REQUEST_SYNC_STATUS_UNKNOWN);
        }
        return R.ok();
    }

    /**
     * 删除商品映射信息
     *
     * @param bo
     * @return
     */
    @Override
    public R<Void> deleteProductMapping(ProductMappingDeleteBo bo) {
        List<Long> ids = bo.getIds();
        if (CollUtil.isEmpty(ids)) {
            return R.fail(ZSMallStatusCodeEnum.REQUIRED_CANNOT_EMPTY);
        }
        iProductMappingService.batchUpdateSyncStateByIds(ids, SyncStateEnum.Deleting);
        // 删除映射，订单添加异常状态
        List<ProductMapping> productMappings = iProductMappingService.listByIds(ids);
        if(CollUtil.isNotEmpty(productMappings)){
            List<String> productSkuCodeList = productMappings.stream().map(ProductMapping::getProductSkuCode).distinct()
                                                  .collect(Collectors.toList());
            if(CollUtil.isNotEmpty(productSkuCodeList)){
                productSupport.orderExceptionDispose(productSkuCodeList, OrderExceptionEnum.product_mapping_exception);
            }
        }
        return R.ok();
    }

    /**
     * 商品批量铺货至渠道
     *
     * @param bo
     */
    @Override
    public R<Void> productBatchImportToChannel(BatchImportToStoreBo bo) {
        List<String> productCodeList = bo.getProductCodeList();
        List<Long> channelIdList = bo.getChannelIdList();

        String channelType = bo.getChannelType();
        ChannelTypeEnum channelTypeEnum = ChannelTypeEnum.valueOf(channelType);

        MarkUpTypeEnum markUpType = bo.getMarkUpType();
        BigDecimal markUpValue = bo.getMarkUpValue();

        if (ChannelTypeEnum.Shopify.equals(channelTypeEnum)) {
            if (markUpType == null || markUpValue == null) {
                return R.fail(ZSMallStatusCodeEnum.REQUIRED_CANNOT_EMPTY);
            }

            if (NumberUtil.isLessOrEqual(markUpValue, BigDecimal.ZERO)) {
                return R.fail(ZSMallStatusCodeEnum.PRODUCT_SKU_MARK_UP_NOT_ZERO);
            }
        } else {
            // Shopify之外的渠道不能提价
            bo.setMarkUpType(null);
            bo.setMarkUpValue(null);
        }

        List<TenantSalesChannel> channels = new ArrayList<>();
        for (Long channelId : channelIdList) {
            TenantSalesChannel channel = iTenantSalesChannelService.getById(channelId);
            if (channel == null) {
                continue;
            }

            if (GlobalStateEnum.Invalid.getValue().equals(channel.getState())) {
                return R.fail(ZSMallStatusCodeEnum.SALES_CHANNEL_NOT_EXIST);
            }
            channels.add(channel);
        }

        List<ProductMapping> productMappingList = new ArrayList<>();
        for (String productCode : productCodeList) {
            ProductSkuBo bo1 = new ProductSkuBo();
            bo1.setProductCode(productCode);
            bo1.setShelfState(SkuShelfStateEnum.OnShelf.name());
            List<ProductSkuVo> skuVoList = iProductSkuService.queryList(bo1);

            List<ImportToStoreSkuBo> skuList = new ArrayList<>();
            if (CollUtil.isNotEmpty(skuVoList)) {
                for (ProductSkuVo productSkuVo : skuVoList) {
                    MappingGenerateBo newGenerateBo = new MappingGenerateBo()
                        .setMarkUpValue(markUpValue)
                        .setMarkUpType(markUpType)
                        .setChannelType(channelTypeEnum)
                        .setProductSkuCode(productSkuVo.getProductSkuCode());
                    productMappingList.addAll(generateProductMapping(channels, newGenerateBo));
                }
            }
        }

        if (CollUtil.isEmpty(productMappingList)) {
            return R.fail(ZSMallStatusCodeEnum.SELECT_AT_LEAST_ONE_SKU);
        }
        iProductMappingService.saveBatch(productMappingList);

        // Shopify的直接同步至商店
        if (ChannelTypeEnum.Shopify.equals(channelTypeEnum)) {
            for (ProductMapping productMapping : productMappingList) {
                Long channelId = productMapping.getChannelId();
                String productSkuCode = productMapping.getProductSkuCode();
                Boolean existsed = iProductMappingService.existsSameMappingSku(null, channelId, productSkuCode);
                if (existsed) {
                    productMapping.setSyncState(SyncStateEnum.SyncFailed);
                    productMapping.setSyncMessage(LocaleMessage.byStatusCodeToJSON(ZSMallStatusCodeEnum.MAPPING_SKU_REPEAT_SHOPIFY));
                } else {
                    productMapping.setMappingSku(productSkuCode);
                    productMapping.setSyncState(SyncStateEnum.Synchronizing);
                }
            }
            iProductMappingService.updateBatchById(productMappingList);
        }
        return R.ok();
    }

    /**
     * 商品铺货至渠道
     *
     * @param bo
     * @return
     */
    @Override
    public R<Void> productImportToChannel(ImportToStoreBo bo) {
        String productName = bo.getProductName();
        String productCode = bo.getProductCode();
        String channelType = bo.getChannelType();
        List<Long> channelIdList = bo.getChannelIdList();
        List<ImportToStoreSkuBo> skuList = bo.getSkuList();

        // 必填校验
        if (StrUtil.hasBlank(productCode)) {
            return R.fail(ZSMallStatusCodeEnum.REQUIRED_CANNOT_EMPTY);
        }

        if (CollUtil.isEmpty(channelIdList)) {
            return R.fail(ZSMallStatusCodeEnum.SELECT_AT_LEAST_ONE_CHANNEL);
        }

        if (CollUtil.isEmpty(skuList)) {
            return R.fail(ZSMallStatusCodeEnum.SELECT_AT_LEAST_ONE_SKU);
        }

        // 选中的SKU
        List<ImportToStoreSkuBo> checkSkuList = skuList.stream().filter(item -> item.isCheck())
                                                       .collect(Collectors.toList());
        if (CollUtil.isEmpty(checkSkuList)) {
            return R.fail(ZSMallStatusCodeEnum.SELECT_AT_LEAST_ONE_SKU);
        }

        List<TenantSalesChannel> channels = new ArrayList<>();
        ChannelTypeEnum channelTypeEnum = ChannelTypeEnum.valueOf(channelType);
        for (Long channelId : channelIdList) {
            TenantSalesChannel channel = iTenantSalesChannelService.getById(channelId);
            if (channel == null) {
                continue;
            }

            if (GlobalStateEnum.Invalid.getValue().equals(channel.getState())) {
                return R.fail(ZSMallStatusCodeEnum.SALES_CHANNEL_NOT_EXIST);
            }
            channels.add(channel);
        }

        if (CollUtil.isEmpty(channels)) {
            return R.fail(ZSMallStatusCodeEnum.SELECT_AT_LEAST_ONE_CHANNEL);
        }

        Product product = iProductService.queryByProductCodeNotTenant(productCode);
        if (product == null) {
            return R.fail(ZSMallStatusCodeEnum.PRODUCT_NOT_EXIST);
        }

        if (StrUtil.isBlank(productName)) {
            productName = product.getName();
        }
        SupportedLogisticsEnum supportedLogistics = product.getSupportedLogistics();

        String supplierTenantId = product.getTenantId();
        if (CollUtil.isNotEmpty(channels) && product != null) {
            if (ObjectUtil.equals(product.getProductType(), ProductTypeEnum.WholesaleProduct)) {
                return R.fail(ZSMallStatusCodeEnum.CANNOT_IMPORT_TO_SALES_CHANNEL);
            }

            List<ProductMapping> productMappingList = new ArrayList<>();
            for (ImportToStoreSkuBo checkSku : checkSkuList) {
                MappingGenerateBo newGenerateBo = new MappingGenerateBo()
                    .setChannelType(channelTypeEnum)
                    .setProductName(productName)
                    .setProductSkuCode(checkSku.getProductSkuCode())
                    .setActivityCode(checkSku.getActivityCode())
                    .setFinalPrice(checkSku.getFinalPrice());
                productMappingList.addAll(generateProductMapping(channels, newGenerateBo));
            }

            if (CollUtil.isEmpty(productMappingList)) {
                return R.fail(ZSMallStatusCodeEnum.SELECT_AT_LEAST_ONE_SKU);
            }
            iProductMappingService.saveBatch(productMappingList);

            // Shopify的直接同步至商店
            if (ChannelTypeEnum.Shopify.equals(channelTypeEnum)) {
                for (ProductMapping productMapping : productMappingList) {
                    Long channelId = productMapping.getChannelId();
                    String productSkuCode = productMapping.getProductSkuCode();
                    Boolean existsed = iProductMappingService.existsSameMappingSku(null, channelId, productSkuCode);
                    if (existsed) {
                        productMapping.setSyncState(SyncStateEnum.SyncFailed);
                        productMapping.setSyncMessage(LocaleMessage.byStatusCodeToJSON(ZSMallStatusCodeEnum.MAPPING_SKU_REPEAT_SHOPIFY));
                    } else {
                        productMapping.setMappingSku(productSkuCode);
                        productMapping.setSyncState(SyncStateEnum.Synchronizing);
                    }
                }
                iProductMappingService.updateBatchById(productMappingList);
            }
        }
        return R.ok();
    }

    /**
     * 分页查询履约仓库映射
     */
    @Override
    public TableDataInfo<WarehouseMappingVo> queryWarehouseMappingPage(WarehouseMappingBo bo, PageQuery pageQuery) {
        LoginUser loginUser = LoginHelper.getLoginUser(TenantType.Distributor);
        Long channelId = bo.getChannelId();

        if (channelId == null) {
            throw new RStatusCodeException(ZSMallStatusCodeEnum.REQUIRED_CANNOT_EMPTY);
        }

        IPage<WarehouseAddress> iPage = iWarehouseAddressService.queryByProductMappingChannel(pageQuery.build(), channelId, bo.getQueryValue());
        List<WarehouseAddress> records = iPage.getRecords();

        List<WarehouseMappingVo> voList = new ArrayList<>();
        for (WarehouseAddress warehouseAddress : records) {
            WarehouseMappingVo vo = new WarehouseMappingVo();
            Long warehouseId = warehouseAddress.getWarehouseId();

            List<String> addressList = new ArrayList<>();
            addressList.add(warehouseAddress.getAddress1());
            addressList.add(warehouseAddress.getAddress2());
            addressList.add(warehouseAddress.getCity());
            addressList.add(warehouseAddress.getState());
            addressList.add(warehouseAddress.getZipCode());
            CollUtil.removeEmpty(addressList);
            String warehouseAddressIntact = CollUtil.join(addressList, ", ");

            Warehouse warehouse = iWarehouseService.getByIdNotTenant(warehouseId);
            ProductMappingExtendWayfairWarehouse wayfairWarehouse = iProductMappingExtendWayfairWarehouseService.queryByWarehouseAndChannel(warehouseId, channelId);
            if (wayfairWarehouse != null) {
                vo.setKeyId(wayfairWarehouse.getThirdWarehouseId());
            }

            vo.setWarehouseAddress(warehouseAddressIntact);
            vo.setWarehouseName(warehouse.getWarehouseName());
            vo.setWarehouseSystemCode(warehouse.getWarehouseSystemCode());
            voList.add(vo);
        }

        return TableDataInfo.build(voList, iPage.getTotal());
    }

    /**
     * 设置履约仓库映射
     */
    @Override
    public R<Void> setWarehouseMapping(FulfillWarehouseBo bo) {
        String keyId = bo.getKeyId();
        Long channelId = bo.getChannelId();
        String warehouseSystemCode = bo.getWarehouseSystemCode();

        if (StrUtil.isBlank(warehouseSystemCode) || channelId == null) {
            throw new RStatusCodeException(ZSMallStatusCodeEnum.REQUIRED_CANNOT_EMPTY);
        }

        saveWarehouseMapping(CollUtil.newArrayList(bo));
        return R.ok();
    }

    /**
     * 查询铺货准备信息
     *
     * @param productCode
     * @param channelType
     * @return
     */
    @Override
    public R<ImportReadyVo> queryImportReadyInfo(String productCode, String channelType) {
        LoginUser loginUser = LoginHelper.getLoginUser(TenantType.Distributor);
        ChannelTypeEnum channelTypeEnum = ChannelTypeEnum.valueOf(channelType);

        ImportReadyVo vo = iProductService.queryImportReadyInfo(loginUser.getTenantId(), productCode, channelType);
        List<OptionalSkuVo> skuList = vo.getSkuList();
        for (OptionalSkuVo optionalSkuVo : skuList) {

            BigDecimal basePrice;
            // Wayfair只支持自提，取自提价
            if (ChannelTypeEnum.Wayfair.equals(channelTypeEnum)) {
                basePrice = optionalSkuVo.getPlatformPickUpPrice();
            } else {  // 其他渠道都取代发价
                basePrice = optionalSkuVo.getPlatformDropShippingPrice();
            }

            List<MappingAvailableActivityVo> availableActivities = new ArrayList<>();
            ProductActivityTypeEnum[] values = ProductActivityTypeEnum.values();
            for (ProductActivityTypeEnum typeEnum : values) {
//                List<ActivityItemSimpleVo> simpleVoList = iProductActivityItemService.querySelectByProductSkuCode(optionalSkuVo.getProductSkuCode(), typeEnum, channelTypeEnum);
//                if (CollUtil.isNotEmpty(simpleVoList)) {
//                    List<MappingAvailableActivityInfoVo> children = BeanUtil.copyToList(simpleVoList, MappingAvailableActivityInfoVo.class);
//                    availableActivities.add(new MappingAvailableActivityVo(typeEnum.name(), typeEnum.name(), children));
//                }
            }
            optionalSkuVo.setBasePrice(basePrice);
            optionalSkuVo.setOriginPrice(basePrice);
            optionalSkuVo.setOptionalActivityList(availableActivities);
        }
        return R.ok(vo);
    }

    @Override
    public R<Void> saveSkuMappingForTikTok(SaveSkuMappingBo bo) {
        List<SkuMappingBo> skuMappings = bo.getSkuMappings();
        List<FulfillWarehouseBo> batchWarehouseConfigs = bo.getWarehouseConfigs();

        if (CollUtil.isNotEmpty(batchWarehouseConfigs)) {
            saveWarehouseMapping(batchWarehouseConfigs);
        }

        // 重复的MappingSku
        List<ProductMapping> repeatMappingSku = new ArrayList<>();
        // 判断本次循环是否有重复
        Map<Long, Set<String>> channelMappingSku = new HashMap<>();
        //  本次需要同步库存的ProductSku
        List<Long> syncInventorySku = new ArrayList<>();
        // 分销商商品映射信息
        List<ProductMapping> productMappingList = new ArrayList<>();

        if (CollUtil.isNotEmpty(skuMappings)) {
            for (SkuMappingBo skuMapping : skuMappings) {
                String id = skuMapping.getId();
                String mappingSku = StrUtil.emptyToNull(StrUtil.trim(skuMapping.getMappingSku()));

                ProductMapping productMapping = iProductMappingService.getById(id);
                if (productMapping != null) {
                    String originMappingSku = mappingSku;
                    ProductSku productSku = TenantHelper.ignore(() -> iProductSkuService.getOne(new LambdaQueryWrapper<ProductSku>().eq(ProductSku::getProductSkuCode, originMappingSku)
                                                                                                                                    .last("limit 1")));
                   ProductSkuAttachmentVo productSkuAttachmentVo = TenantHelper.ignore(() -> iProductSkuAttachmentService.queryFirstImageByProductSkuId(productSku.getId()));
                    if (ObjectUtil.isNull(productSku)){
                        throw new RuntimeException("所映射的商品编码:" +originMappingSku+"不存在");
                    }
                    productMapping.setSupplierTenantId(productSku.getTenantId());
                    productMapping.setProductSkuCode(productSku.getProductSkuCode());
                    productMapping.setProductCode(productSku.getProductCode());
                    productMapping.setProductSkuId(productSku.getId());
                   productMapping.setImageSavePath(productSkuAttachmentVo.getAttachmentSavePath());
                    productMapping.setImageShowUrl(productSkuAttachmentVo.getAttachmentShowUrl());

                    productMapping.setCreateBy(10000L);


                    Long channelId = productMapping.getChannelId();
                    Long productMappingId = productMapping.getId();
                    Set<String> mappingSkuSet = channelMappingSku.get(channelId);

                    if (productMapping.getSyncState().unable()) {
                        throw new RStatusCodeException(ZSMallStatusCodeEnum.PRODUCT_SYNCHRONIZING.args(productMapping.getProductCode()));
                    }

                    Boolean existsed = iProductMappingService.existsSameMappingSku(productMappingId, channelId, mappingSku);
                    if (existsed) repeatMappingSku.add(productMapping);

                    if (CollUtil.contains(mappingSkuSet, mappingSku)) {
                        throw new RStatusCodeException(ZSMallStatusCodeEnum.MAPPING_SKU_REPEAT.args(mappingSku));
                    }

                    if (!StrUtil.equals(mappingSku, originMappingSku)) {
                        syncInventorySku.add(productMapping.getId());
                    }


                    SyncStateEnum syncState = productMapping.getSyncState();
                    if (SyncStateEnum.Synced.equals(syncState)) {
                        if (!StrUtil.equals(mappingSku, originMappingSku)) {
                            productMapping.setSyncState(SyncStateEnum.Updating);
                        }
                    } else {
                        productMapping.setSyncState(SyncStateEnum.Mapped);
                    }

                    if (mappingSkuSet == null) {
                        mappingSkuSet = new HashSet<>();
                    }
                    mappingSkuSet.add(mappingSku);
                    channelMappingSku.put(channelId, mappingSkuSet);

                    productMapping.setMappingSku(mappingSku);
                    productMappingList.add(productMapping);

                    // 单个设置Mapping时，需要检查是否需要设置仓库配置
                    if (CollUtil.size(skuMappings) == 1 && CollUtil.isEmpty(batchWarehouseConfigs)) {
                        saveWarehouseMapping(skuMapping.getWarehouseConfigs());
                    }
                }
            }

            if (CollectionUtils.isNotEmpty(productMappingList)) {
                if (repeatMappingSku.size() > 0) {
                    // 再次判断是否还有重复
                    for (ProductMapping repeat : repeatMappingSku) {

                        Boolean existsed = iProductMappingService.existsSameChannelSku(repeat.getId(), repeat.getChannelId(), repeat.getChannelSku());

                        if (existsed) {
                            throw new RStatusCodeException(ZSMallStatusCodeEnum.MAPPING_SKU_REPEAT.args(repeat.getMappingSku()));
                        }
                    }
                }

                iProductMappingService.saveOrUpdateBatch(productMappingList);

            }
        }
        return R.ok();
    }

    /**
     * 生成商品映射信息入参Bo
     */
    @Getter
    @Setter
    @Accessors(chain = true)
    class MappingGenerateBo {
        private ChannelTypeEnum channelType;
        private String productName;
        private String productSkuCode;
        private String activityCode;
        private MarkUpTypeEnum markUpType;
        private BigDecimal markUpValue;
        private BigDecimal finalPrice;
    }

    /**
     * 生成商品映射信息
     *
     * @param channelList
     * @return
     */
    private List<ProductMapping> generateProductMapping(List<TenantSalesChannel> channelList,
                                                        MappingGenerateBo generateBo) {
        ChannelTypeEnum channelType = generateBo.getChannelType();
        String productName = generateBo.getProductName();
        String productSkuCode = generateBo.getProductSkuCode();
        String activityCode = generateBo.getActivityCode();
        MarkUpTypeEnum markUpType = generateBo.getMarkUpType();
        BigDecimal markUpValue = generateBo.getMarkUpValue();
        BigDecimal finalPrice = generateBo.getFinalPrice();

        ProductSku productSku = iProductSkuService.queryByProductSkuCode(productSkuCode);
        if (productSku == null) {
            throw new RStatusCodeException(ZSMallStatusCodeEnum.PRODUCT_SKU_NOT_EXISTS.args(productSkuCode));
        }

        Product product = iProductService.queryById(productSku.getProductId());
        SupportedLogisticsEnum supportedLogistics = product.getSupportedLogistics();

        // Wayfair仅支持自提，需要判断商品是否支持
        if (ChannelTypeEnum.Wayfair.equals(channelType)) {
            if (!supportedLogistics.allowShipping(LogisticsTypeEnum.PickUp)) {
                throw new RStatusCodeException(ZSMallStatusCodeEnum.PRODUCT_LOGISTICS_METHOD_NOT_APPLICABLE.args(productSkuCode, channelType.name()));
            }
        } else {  // 其他的第三方销售渠道目前仅支持代发，需要判断商品是否支持
            if (!supportedLogistics.allowShipping(LogisticsTypeEnum.DropShipping)) {
                throw new RStatusCodeException(ZSMallStatusCodeEnum.PRODUCT_LOGISTICS_METHOD_NOT_APPLICABLE.args(productSkuCode, channelType.name()));
            }
        }

        String supplierTenantId = productSku.getTenantId();
        String productCode = productSku.getProductCode();

        if (StrUtil.isBlank(productName)) {
            productName = productSku.getName();
        }

        Long productSkuId = productSku.getId();
        String specComposeName = productSku.getSpecComposeName();
        String specValName = productSku.getSpecValName();

        ProductSkuAttachmentVo productSkuAttachmentVo = iProductSkuAttachmentService.queryFirstImageByProductSkuId(productSkuId);
        String attachmentShowUrl = productSkuAttachmentVo.getAttachmentShowUrl();
        String attachmentSavePath = productSkuAttachmentVo.getAttachmentSavePath();

        BigDecimal platformPickUpPrice = null;
        BigDecimal platformDropShippingPrice = null;
        ProductActivityTypeEnum activityTypeEnum = null;
        if (StrUtil.isNotBlank(activityCode)) {
//            ProductActivityPriceItem activityPriceItem = iProductActivityPriceItemService.getByActivityCode(activityCode);
//            platformPickUpPrice = activityPriceItem.getPlatformPickUpPrice();
//            platformDropShippingPrice = activityPriceItem.getPlatformDropShippingPrice();
//            activityTypeEnum = ProductActivityTypeEnum.identifyByActivityCode(activityCode);
        } else {
            //铺货功能,分销暂不支持,此处默认us站点处理
            ProductSkuPrice productSkuPrice = iProductSkuPriceService.queryByProductSkuId(productSkuId);
            platformPickUpPrice = productSkuPrice.getPlatformPickUpPrice();
            platformDropShippingPrice = productSkuPrice.getPlatformDropShippingPrice();
            activityTypeEnum = null;
        }

        BigDecimal basePrice;
        // Wayfair只支持自提，取自提价
        if (ChannelTypeEnum.Wayfair.equals(channelType)) {
            basePrice = platformPickUpPrice;
            finalPrice = platformPickUpPrice;
        } else {  // 其他渠道都取代发价
            basePrice = platformDropShippingPrice;
        }

        // 如果未填写价格，则默认根据商品支持的物流类型取自提/代发价
        if (finalPrice == null || NumberUtil.isLessOrEqual(finalPrice, BigDecimal.ZERO)) {
            finalPrice = basePrice;
        }

        // 如果提价类型和提价值不为空，在此处需要计算最终价格
        if (markUpType != null && markUpValue != null) {
            finalPrice = iProductMappingService.calculateFinalPrice(markUpType, markUpValue, basePrice);
        } else {
            // 提价类型默认都是金额，不使用百分比
            markUpType = MarkUpTypeEnum.Amount;
            // 计算提价值
            markUpValue = NumberUtil.sub(finalPrice, basePrice);
        }

        List<ProductMapping> productMappingList = new ArrayList<>();
        for (TenantSalesChannel channel : channelList) {
            ProductMapping productMapping = new ProductMapping();
            productMapping.setSupplierTenantId(supplierTenantId);
            productMapping.setProductCode(productCode);
            productMapping.setProductSkuId(productSkuId);
            productMapping.setProductSkuCode(productSkuCode);
            productMapping.setActivityCode(activityCode);
            productMapping.setActivityType(activityTypeEnum);
            productMapping.setChannelType(channelType);
            productMapping.setChannelId(channel.getId());
            productMapping.setProductName(productName);
            productMapping.setSpecComposeName(specComposeName);
            productMapping.setSpecValName(specValName);
            productMapping.setMarkUpType(markUpType);
            productMapping.setMarkUpValue(markUpValue);
            productMapping.setFinalPrice(finalPrice);
            productMapping.setImageSavePath(attachmentSavePath);
            productMapping.setImageShowUrl(attachmentShowUrl);
            productMappingList.add(productMapping);
        }

        return productMappingList;
    }

    /**
     * 获取仓库地址
     *
     * @param productSkuCode
     * @return
     */
    private Map<Long, String> getWarehouseAddressMap(String productSkuCode) {
        Map<Long, String> warehouseAddressMap = new HashMap<>();
        List<WarehouseAddress> warehouseAddresses = iWarehouseAddressService.queryByProductSkuCode(productSkuCode);
        if (CollUtil.isNotEmpty(warehouseAddresses)) {
            for (WarehouseAddress warehouseAddress : warehouseAddresses) {
                Long warehouseId = warehouseAddress.getWarehouseId();

                List<String> addressList = new ArrayList<>();
                addressList.add(warehouseAddress.getAddress1());
                addressList.add(warehouseAddress.getAddress2());
                addressList.add(warehouseAddress.getCity());
                addressList.add(warehouseAddress.getState());
                addressList.add(warehouseAddress.getZipCode());
                CollUtil.removeEmpty(addressList);

                warehouseAddressMap.put(warehouseId, CollUtil.join(addressList, ", "));
            }
        }
        return warehouseAddressMap;
    }

    /**
     * 保存仓库映射
     *
     * @param warehouseConfigs
     */
    private void saveWarehouseMapping(List<FulfillWarehouseBo> warehouseConfigs) {
        if (CollectionUtils.isNotEmpty(warehouseConfigs)) {
            for (FulfillWarehouseBo warehouseConfig : warehouseConfigs) {
                String keyId = warehouseConfig.getKeyId();
                String warehouseSystemCode = warehouseConfig.getWarehouseSystemCode();
                Long channelId = warehouseConfig.getChannelId();

                if (keyId == null) {
                    keyId = "";
                }

                keyId = StringUtils.replace(keyId, "；", ";");

                ProductMappingExtendWayfairWarehouse wayfairWarehouse = iProductMappingExtendWayfairWarehouseService.queryByWarehouseAndChannel(warehouseSystemCode, channelId);
                log.info("保存仓库映射 wayfairWarehouse = {}", JSONUtil.toJsonStr(wayfairWarehouse));
                if (wayfairWarehouse == null) {
                    Warehouse warehouse = iWarehouseService.queryByWarehouseSystemCodeNotTenant(warehouseSystemCode);
                    if (warehouse != null) {
                        wayfairWarehouse = new ProductMappingExtendWayfairWarehouse();
                        wayfairWarehouse.setChannelId(channelId);
                        wayfairWarehouse.setWarehouseId(warehouse.getId());
                        wayfairWarehouse.setWarehouseSystemCode(warehouse.getWarehouseSystemCode());
                        wayfairWarehouse.setThirdWarehouseId(keyId);
                        iProductMappingExtendWayfairWarehouseService.save(wayfairWarehouse);
                    }
                } else {
                    wayfairWarehouse.setThirdWarehouseId(keyId);
                    iProductMappingExtendWayfairWarehouseService.updateById(wayfairWarehouse);
                }
            }
        }
    }

    @Override
    public R<Void> setProductMappingName(String id, String name) {
        try {
            ProductMapping productMapping = iProductMappingService.getById(id);
            if (productMapping == null) {
                throw new RuntimeException("商品映射ID不存在:" + id);
            }
            UpdateWrapper<ProductMapping> updateWrapper=new UpdateWrapper<>();
            updateWrapper.eq("id",id);
            updateWrapper.set("product_name",name);
            productMapping.setProductName(name);
            iProductMappingService.update(null,updateWrapper);
            return R.ok("更新成功");
        }catch (Exception e){
            return R.fail("更新失败："+e.getMessage());
        }

    }
    @Transactional
    @Override
    public R<ProductMapping> addProductMapping(Long channelId, String itemNo,String channelSku,String orderNo) {
        if (ObjectUtil.isNull( channelId)){
            throw  new RuntimeException("渠道ID不能为空");
        }
        if (ObjectUtil.isEmpty( itemNo)){
            throw  new RuntimeException("SkuId不能为空");
        }
        if (ObjectUtil.isEmpty( channelSku)){
            throw  new RuntimeException("渠道商品编码不能为空");
        }

        boolean isTkOrTm=false;
        //校验商品是否存在
        ProductSku productSku = iProductSkuService.queryByProductSkuCode(itemNo);
        if (ObjectUtil.isNull(productSku)){
            throw  new RuntimeException("SkuId信息不存在: "+itemNo);
        }

        //校验产品信息是否存在
        Product product = iProductService.queryByProductSkuCode(itemNo);
        if (ObjectUtil.isNull(product)){
            throw  new RuntimeException("产品信息不存在: "+itemNo);
        }
        if (!product.getShelfState().equals(ShelfStateEnum.OnShelf)){
            throw  new RuntimeException(StrUtil.format("商品：{}已经下架，不能被映射。",itemNo));
        }
        //校验店铺是否存在
        LambdaQueryWrapper<TenantSalesChannel> lqw = Wrappers.lambdaQuery();
        lqw.eq(TenantSalesChannel::getId, channelId);
        lqw.eq(TenantSalesChannel::getState, GlobalStateEnum.Valid.getValue());
        TenantSalesChannel tenantSalesChannel =iTenantSalesChannelService.getBaseMapper().selectOne(lqw);
        if (ObjectUtil.isNull(tenantSalesChannel)){
            throw  new RuntimeException(" 店铺不存在/已禁用: "+channelId);
        }


        if (ObjectUtil.isNotNull(tenantSalesChannel)){
            if (ObjectUtil.isEmpty(tenantSalesChannel.getChannelType())){
                throw  new RuntimeException(" 店铺渠道类型为空: "+tenantSalesChannel.getId());
            }
            if (ChannelTypeEnum.Temu.name().equals(tenantSalesChannel.getChannelType()) || ChannelTypeEnum.TikTok.name().equals(tenantSalesChannel.getChannelType())){
                //校验数字
                boolean match = ReUtil.isMatch("^\\d+$", channelSku);
                if (!match){
                    throw  new RuntimeException("Temu /TikTok 渠道SKU应该为纯数字");
                }
                isTkOrTm=true;
            }
            //校验渠道店铺发货方式和商品发货方式是否不一致
            boolean isSupport = product.getSupportedLogistics()
                                       .allowShipping(LogisticsTypeEnum.getLogisticsTypeEnumByName(String.valueOf(tenantSalesChannel.getLogisticsType())));
            if (!isSupport){
                throw  new RuntimeException("商品编码: "+ itemNo+"发货方式和渠道店铺："+tenantSalesChannel.getChannelName()+"配置发货方式不一致");
            }
        }

        //校验数据库 当前映射是否已经存在
        LambdaQueryWrapper<ProductMapping> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProductMapping::getChannelSku,channelSku);
       // wrapper.eq(ProductMapping::getProductSkuCode,itemNo);
        wrapper.eq(ProductMapping::getChannelId,tenantSalesChannel.getId());
        wrapper.eq(ProductMapping::getSyncState,SyncStateEnum.Mapped.name());
        boolean exists = iProductMappingService.getBaseMapper().exists(wrapper);
        //如果数据不存在
        if (!exists){
            List<SiteCountryCurrency> list = siteCountryCurrencyMapper.selectList();
            ProductMapping returnProductMapping = new ProductMapping();
            for (int i = 0; i < list.size(); i++) {
                ProductMapping productMapping = new ProductMapping();
                productMapping.setSite(list.get(i).getCountryCode());
                productMapping.setCurrency(list.get(i).getCurrencySymbol());
                productMapping.setSupplierTenantId(productSku.getTenantId());
                productMapping.setTenantId(LoginHelper.getTenantId());
                productMapping.setProductSkuCode(productSku.getProductSkuCode());
                productMapping.setMappingSku(productSku.getProductSkuCode());
                productMapping.setProductCode(productSku.getProductCode());
                productMapping.setProductSkuId(productSku.getId());
                productMapping.setSpecComposeName(productSku.getSpecComposeName());
                productMapping.setSpecValName(productSku.getSpecValName());
                productMapping.setChannelId(tenantSalesChannel.getId());
                productMapping.setChannelType(ChannelTypeEnum.valueOf(tenantSalesChannel.getChannelType()));
                if (isTkOrTm) {
                    productMapping.setChannelSkuId(channelSku);
                }
                productMapping.setCreateBy(10000L);
                productMapping.setSyncState(SyncStateEnum.Mapped);
                productMapping.setChannelSku(channelSku);
                iProductMappingService.save(productMapping);
                if (i==0){
                    returnProductMapping=productMapping;
                }
            }
            return R.ok(returnProductMapping);
        }else {
            throw  new RuntimeException(" 店铺名称："+tenantSalesChannel.getChannelName()+",渠道SKU："+channelSku+" 商品编码："+itemNo+"的商品映射已存在,请勿重复映射!");
        }
    }

    @Override
    public R<ProductMapping> addProductMappingSite(Long channelId, String itemNo, String channelSku, String orderNo) {
        if (ObjectUtil.isNull( channelId)){
            throw  new RuntimeException("渠道ID不能为空");
        }
        if (ObjectUtil.isEmpty( itemNo)){
            throw  new RuntimeException("SkuId不能为空");
        }
        if (ObjectUtil.isEmpty( channelSku)){
            throw  new RuntimeException("渠道商品编码不能为空");
        }

        boolean isTkOrTm=false;
        //校验商品是否存在
        ProductSku productSku = iProductSkuService.queryByProductSkuCode(itemNo);
        if (ObjectUtil.isNull(productSku)){
            throw  new RuntimeException("SkuId信息不存在: "+itemNo);
        }

        //校验产品信息是否存在
        Product product = iProductService.queryByProductSkuCode(itemNo);
        if (ObjectUtil.isNull(product)){
            throw  new RuntimeException("产品信息不存在: "+itemNo);
        }
        if (!product.getShelfState().equals(ShelfStateEnum.OnShelf)){
            throw  new RuntimeException(StrUtil.format("商品：{}已经下架，不能被映射。",itemNo));
        }
        //校验店铺是否存在
        LambdaQueryWrapper<TenantSalesChannel> lqw = Wrappers.lambdaQuery();
        lqw.eq(TenantSalesChannel::getId, channelId);
        lqw.eq(TenantSalesChannel::getState, GlobalStateEnum.Valid.getValue());
        TenantSalesChannel tenantSalesChannel =iTenantSalesChannelService.getBaseMapper().selectOne(lqw);
        if (ObjectUtil.isNull(tenantSalesChannel)){
            throw  new RuntimeException(" 店铺不存在/已禁用: "+channelId);
        }


        if (ObjectUtil.isNotNull(tenantSalesChannel)){
            if (ObjectUtil.isEmpty(tenantSalesChannel.getChannelType())){
                throw  new RuntimeException(" 店铺渠道类型为空: "+tenantSalesChannel.getId());
            }
            if (ChannelTypeEnum.Temu.name().equals(tenantSalesChannel.getChannelType()) || ChannelTypeEnum.TikTok.name().equals(tenantSalesChannel.getChannelType())){
                //校验数字
                boolean match = ReUtil.isMatch("^\\d+$", channelSku);
                if (!match){
                    throw  new RuntimeException("Temu /TikTok 渠道SKU应该为纯数字");
                }
                isTkOrTm=true;
            }
            //校验渠道店铺发货方式和商品发货方式是否不一致
            boolean isSupport = product.getSupportedLogistics()
                                       .allowShipping(LogisticsTypeEnum.getLogisticsTypeEnumByName(String.valueOf(tenantSalesChannel.getLogisticsType())));
            if (!isSupport){
                throw  new RuntimeException("商品编码: "+ itemNo+"发货方式和渠道店铺："+tenantSalesChannel.getChannelName()+"配置发货方式不一致");
            }
        }
        Orders orders = iOrdersService.getByOrderNo(orderNo);
        if (ObjectUtil.isNull(orders)){
            throw  new RuntimeException("订单不存在: "+orderNo);
        }
        if(StringUtils.isEmpty(orders.getCountryCode())){
            throw  new RuntimeException("订单国家不存在: "+orderNo);
        }
        String cacheMapValue = sysConfigService.selectConfigByKey("Generate_Data_With_CN_Currency_Code");
        List<String> ignore= JSONUtil.toList(cacheMapValue, String.class);
        String tenantId = LoginHelper.getTenantId();
        boolean isIgnoredTenant = ignore.contains(tenantId);

        //校验数据库 当前映射是否已经存在
        LambdaQueryWrapper<ProductMapping> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProductMapping::getChannelSku,channelSku);
        wrapper.eq(ProductMapping::getChannelId,tenantSalesChannel.getId());
        wrapper.eq(ProductMapping::getSite,orders.getCountryCode());
        wrapper.eq(ProductMapping::getSyncState,SyncStateEnum.Mapped.name());
        boolean exists = iProductMappingService.getBaseMapper().exists(wrapper);
        //如果数据不存在
        if (!exists){
            ProductSkuPrice productSkuPrice = iProductSkuPriceService.queryByProductSkuCodeAndCountryCode(productSku.getProductSkuCode(), orders.getCountryCode());
            if (ObjectUtil.isNull(productSkuPrice)){
                throw  new RuntimeException("商品: "+productSku.getProductSkuCode()+"站点: "+orders.getCountryCode()+"价格信息不存在");
            }
            if (isIgnoredTenant || !"CN".equals(productSkuPrice.getCountryCode())) {
                ProductMapping productMapping = new ProductMapping();
                productMapping.setSite(orders.getCountryCode());
                productMapping.setCurrency(orders.getCurrencySymbol());
                productMapping.setSupplierTenantId(productSku.getTenantId());
                productMapping.setTenantId(LoginHelper.getTenantId());
                productMapping.setProductSkuCode(productSku.getProductSkuCode());
                productMapping.setMappingSku(productSku.getProductSkuCode());
                productMapping.setProductCode(productSku.getProductCode());
                productMapping.setProductSkuId(productSku.getId());
                productMapping.setSpecComposeName(productSku.getSpecComposeName());
                productMapping.setSpecValName(productSku.getSpecValName());
                productMapping.setChannelId(tenantSalesChannel.getId());
                productMapping.setChannelType(ChannelTypeEnum.valueOf(tenantSalesChannel.getChannelType()));
                if (isTkOrTm) {
                    productMapping.setChannelSkuId(channelSku);
                }
                productMapping.setCreateBy(10000L);
                productMapping.setSyncState(SyncStateEnum.Mapped);
                productMapping.setChannelSku(channelSku);
                iProductMappingService.save(productMapping);
                return R.ok(productMapping);
            }
            return null;
        }else {
            throw  new RuntimeException(" 店铺名称："+tenantSalesChannel.getChannelName()+",渠道SKU："+channelSku+" 商品编码："+itemNo+"的商品映射已存在,请勿重复映射!");
        }
    }

    @Override
    public void dealProductMappingDate() {
        //美国站点已经存在
        List<SiteCountryCurrency> sites = siteCountryCurrencyMapper.selectList();
        sites = sites.stream()
                     .filter(site -> !"US".equals(site.getCountryCode()))
                     .collect(Collectors.toList());
        String cacheMapValue = sysConfigService.selectConfigByKey("Generate_Data_With_CN_Currency_Code");
        List<String> ignore= JSONUtil.toList(cacheMapValue, String.class);

        List<ProductMapping> productMappings = iProductMappingService.getBaseMapper().selectList();
        List<SiteCountryCurrency> finalSites1 = sites;
        productMappings.forEach(pm->{
            for (SiteCountryCurrency site : finalSites1) {
                boolean isIgnoredTenant = ignore.contains(pm.getTenantId());
                ProductSkuPrice productSkuPrice = iProductSkuPriceService.queryByProductSkuCodeAndCountryCode(pm.getProductSkuCode(), site.getCountryCode());
                if (ObjectUtil.isNotNull(productSkuPrice)){
                    if (isIgnoredTenant || !"CN".equals(productSkuPrice.getCountryCode())) {
                        ProductMapping productMapping = new ProductMapping();
                        productMapping.setSite(site.getCountryCode());
                        productMapping.setCurrency(site.getCurrencySymbol());
                        productMapping.setSupplierTenantId(pm.getSupplierTenantId());
                        productMapping.setTenantId(pm.getTenantId());
                        productMapping.setProductSkuCode(pm.getProductSkuCode());
                        productMapping.setMappingSku(pm.getProductSkuCode());
                        productMapping.setProductCode(pm.getProductCode());
                        productMapping.setProductSkuId(pm.getProductSkuId());
                        productMapping.setSpecComposeName(pm.getSpecComposeName());
                        productMapping.setSpecValName(pm.getSpecValName());
                        productMapping.setChannelId(pm.getChannelId());
                        productMapping.setChannelType(pm.getChannelType());
                        productMapping.setChannelSkuId(pm.getChannelSkuId());
                        productMapping.setCreateBy(10000L);
                        productMapping.setSyncState(SyncStateEnum.Mapped);
                        productMapping.setChannelSku(pm.getChannelSku());
                        iProductMappingService.save(productMapping);
                    }
                }
            }
        });
    }
}
