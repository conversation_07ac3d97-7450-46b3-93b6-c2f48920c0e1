package com.zsmall.product.biz.service;


import com.hengjian.common.core.domain.R;
import com.hengjian.common.mybatis.core.page.PageQuery;
import com.hengjian.common.mybatis.core.page.TableDataInfo;
import com.zsmall.product.entity.domain.bo.productChannelControl.ProductChannelControlBo;
import com.zsmall.product.entity.domain.bo.productChannelControl.SwitchControlTypeBo;
import com.zsmall.product.entity.domain.vo.productChannelControl.ProductChannelControlBase;
import com.zsmall.product.entity.domain.vo.productChannelControl.ProductChannelControlListVo;

/**
 * 商品渠道管控-Service
 *
 * <AUTHOR>
 * @create 2022/5/3 22:43
 */
public interface ProductChannelControlService {

    /**
     * 分页查询商品管控列表
     */
    TableDataInfo<ProductChannelControlListVo> list(ProductChannelControlBo bo, PageQuery pageQuery);

    /**
     * 切换商品渠道管控类型
     */
    R<Void> switchProductChannelControl(SwitchControlTypeBo bo);

    /**
     * 设置商品管控
     */
    R<Void> setProductChannelControl(ProductChannelControlBase bo);

}
