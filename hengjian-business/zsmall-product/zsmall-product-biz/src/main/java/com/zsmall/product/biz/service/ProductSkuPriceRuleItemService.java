package com.zsmall.product.biz.service;

import com.hengjian.common.mybatis.core.page.PageQuery;
import com.hengjian.common.mybatis.core.page.TableDataInfo;
import com.zsmall.product.entity.domain.ProductSkuPriceRuleItem;
import com.zsmall.product.entity.domain.bo.productSkuPrice.ProductSkuPriceRuleItemBo;
import com.zsmall.product.entity.domain.vo.productSkuPrice.ProductSkuPriceRuleItemVo;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 商品sku价格计算公式Service接口
 *
 * <AUTHOR> Li
 * @date 2023-05-22
 */
public interface ProductSkuPriceRuleItemService {

    /**
     * 查询商品sku价格计算公式
     */
    ProductSkuPriceRuleItemVo queryById(Long id);

    /**
     * 查询商品sku价格计算公式列表
     */
    TableDataInfo<ProductSkuPriceRuleItemVo> queryPageList(ProductSkuPriceRuleItemBo bo, PageQuery pageQuery);

    /**
     * 查询商品sku价格计算公式列表
     */
    List<ProductSkuPriceRuleItemVo> queryList(ProductSkuPriceRuleItemBo bo);

    /**
     * 新增商品sku价格计算公式
     */
    Boolean insertByBo(ProductSkuPriceRuleItemBo bo);

    /**
     * 修改商品sku价格计算公式
     */
    Boolean updateByBo(ProductSkuPriceRuleItemBo bo);

    /**
     * 校验并批量删除商品sku价格计算公式信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 根据查询商品sku价格计算公式是否存在
     */
    Boolean isExistByRuleId(Long ruleId);

    /**
     * 根据查询商品sku价格计算公式
     */
    List<ProductSkuPriceRuleItemVo> queryListByRuleId(Long ruleId);

    Boolean saveBatch(List<ProductSkuPriceRuleItem> items);

    /**
     *
     * @param productSkuPriceRuleId 价格公式id
     * @param unitPriceCal 原价
     * @param unitPriceCalValue 提价值
     * @param operationFeeCal 操作费
     * @param operationFeeCalValue 提价值
     * @param finalDeliveryFeeCal 尾程派送费
     * @param finalDeliveryFeeCalValue 提价值
     * @return
     */
    List<ProductSkuPriceRuleItemVo> parseRuleItemListByRuleInfo(Long productSkuPriceRuleId, Integer unitPriceCal, BigDecimal unitPriceCalValue,
                                                                    Integer operationFeeCal, BigDecimal operationFeeCalValue,
                                                                    Integer finalDeliveryFeeCal, BigDecimal finalDeliveryFeeCalValue);


    /**
     * 通过价格公式id获取公式明细数据
     * @param ruleId
     * @return
     */
    List<ProductSkuPriceRuleItemVo> getItemListByRuleId(Long ruleId);

    /**
     * 通过价格公式id集合获取公式明细数据
     * @param ruleIds
     * @return
     */
    List<ProductSkuPriceRuleItemVo> getItemListByRuleIds(List<Long> ruleIds);

    /**
     * 根据规则第三集合获取获取定价公式 格式如：(产品单价+1.00) + (操作费+2.00) + (尾程派送费+3.00)
     * @param ruleIds
     * @return {ruleId: "(产品单价+1.00) + (操作费+2.00) + (尾程派送费+3.00)"}
     */
    Map<Long, String> getPriceFormulaByRuleIds(List<Long> ruleIds);

    /**
     * 根据公式id批量修改公式明细信息
     * @param ruleId
     * @return
     */
    Boolean updateDeleteMarkByRuleId(Long ruleId);

    /**
     * 匹配计算公式是否一致
     * @param items
     * @param newItems
     * @return
     */
    Boolean matchItems(List<ProductSkuPriceRuleItemVo> items, List<ProductSkuPriceRuleItemVo> newItems);

}
