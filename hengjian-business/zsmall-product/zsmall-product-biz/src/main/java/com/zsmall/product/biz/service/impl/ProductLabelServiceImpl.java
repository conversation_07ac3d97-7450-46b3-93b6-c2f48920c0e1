package com.zsmall.product.biz.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hengjian.common.core.domain.R;
import com.hengjian.common.core.exception.RStatusCodeException;
import com.hengjian.common.core.utils.MapstructUtils;
import com.hengjian.common.core.utils.StringUtils;
import com.hengjian.common.log.annotation.InMethodLog;
import com.hengjian.common.mybatis.core.page.PageQuery;
import com.hengjian.common.mybatis.core.page.TableDataInfo;
import com.zsmall.common.enums.product.SupportedLogisticsEnum;
import com.zsmall.common.enums.statuscode.ZSMallStatusCodeEnum;
import com.zsmall.product.biz.service.IProductLabelRelationService;
import com.zsmall.product.biz.service.IProductLabelService;
import com.zsmall.product.biz.support.EsProductSupport;
import com.zsmall.product.entity.domain.Product;
import com.zsmall.product.entity.domain.ProductLabel;
import com.zsmall.product.entity.domain.ProductLabelRelation;
import com.zsmall.product.entity.domain.ProductSkuPrice;
import com.zsmall.product.entity.domain.bo.ProductLabelBo;
import com.zsmall.product.entity.domain.bo.label.ReqLabelBindingBatchBody;
import com.zsmall.product.entity.domain.bo.label.ReqLabelBindingBody;
import com.zsmall.product.entity.domain.bo.product.ReqProductBody;
import com.zsmall.product.entity.domain.vo.ProductLabelVo;
import com.zsmall.product.entity.domain.vo.ProductSkuAttachmentVo;
import com.zsmall.product.entity.domain.vo.product.ProductToLabelBody;
import com.zsmall.product.entity.iservice.*;
import com.zsmall.product.entity.mapper.ProductLabelMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.StopWatch;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 标签Service业务层处理
 *
 * <AUTHOR> Li
 * @date 2023-05-31
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class ProductLabelServiceImpl implements IProductLabelService {

    private final ProductLabelMapper baseMapper;
    private final IProductLabelRelationService iProductLabelRelationService;
    private final IProductService iProductService;
    private final IProductAttachmentService iProductAttachmentService;
    private final IProductSkuAttachmentService iProductSkuAttachmentService;
    private final IProductSkuPriceService iProductSkuPriceService;
    private final IViewProductStockService iViewProductStockService;
    private final EsProductSupport esProductSupport;

    /**
     * 查询标签
     */
    @Override
    public ProductLabelVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询标签列表
     */
    @Override
    public TableDataInfo<ProductLabelVo> queryPageList(ProductLabelBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<ProductLabel> lqw = buildQueryWrapper(bo);
        Page<ProductLabelVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询标签列表
     */
    @Override
    public List<ProductLabelVo> queryList(ProductLabelBo bo) {
        LambdaQueryWrapper<ProductLabel> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    @Override
    @InMethodLog("查询所有标签，根据序号升序")
    public List<ProductLabel> queryAllOrderBySort() {
        LambdaQueryWrapper<ProductLabel> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(ProductLabel::getSort);
        return baseMapper.selectList(lqw);
    }

    /**
     * 根据标签名查询
     */
    @Override
    @InMethodLog("根据标签名查询")
    public ProductLabel queryByLabelName(String labelName) {
        LambdaQueryWrapper<ProductLabel> lqw = Wrappers.lambdaQuery();
        lqw.eq(ProductLabel::getLabelName, labelName);
        return CollUtil.get(baseMapper.selectList(lqw), 0);
    }

    /**
     * 新增标签
     */
    @Override
    public Boolean insertByBo(ProductLabelBo bo) throws Exception {
        ProductLabel add = MapstructUtils.convert(bo, ProductLabel.class);
        validEntityBeforeSave(add);
        long sort = getSort();
        add.setSort(sort);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }


    /**
     * 修改标签
     */
    @Override
    public Boolean updateByBo(ProductLabelBo bo) throws Exception {
        ProductLabel update = MapstructUtils.convert(bo, ProductLabel.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }


    /**
     * 批量删除标签
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            // TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }


    @Override
    public boolean updateSort(ProductLabelBo bo) {
        ProductLabel newEntity = new ProductLabel();
        newEntity.setId(bo.getId());
        newEntity.setLabelName(bo.getLabelName());
        newEntity.setSort(bo.getSort());

        return reorder(newEntity);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public R<Void> binding(ReqLabelBindingBody requestBody) throws Exception {
        log.info("进入【商品绑定标签】方法，{}", JSONUtil.toJsonStr(requestBody));

        List<Long> labelIdList = requestBody.getLabelIdList();
        Long productId = requestBody.getProductId();
        String labelName = requestBody.getLabelName();

        if (StrUtil.isBlank(labelName) && CollUtil.isEmpty(labelIdList)) {
            iProductLabelRelationService.removeByProductId(productId);
            esProductSupport.productUpload(productId);
            return R.ok();
        }

        for (Long labelId : labelIdList) {
            ProductLabel labelEntity = baseMapper.selectById(labelId);
            if (ObjectUtil.isNull(labelEntity)) {
                log.info("id为【{}】的标签不存在", labelId);
                return R.fail(ZSMallStatusCodeEnum.LABEL_NOT_EXIST_ERROR.getMessageCode());
            }
        }

        if (StrUtil.isNotBlank(labelName)) {
            long sort = getSort();
            ProductLabel labelEntity = new ProductLabel();
            labelEntity.setLabelName(labelName);
            labelEntity.setSort(sort);
            //标签名称不能重复
            validEntityBeforeSave(labelEntity);

            baseMapper.insert(labelEntity);
            Long newId = labelEntity.getId();
            if (ObjectUtil.isNotNull(newId)) {
                log.info("new label id = {} ", newId);
                labelIdList.add(newId);
            }
        }

        List<ProductLabelRelation> relationEntityList = new ArrayList<>();
        for (Long labelId : labelIdList) {
            ProductLabel labelEntity = baseMapper.selectById(labelId);
            if (ObjectUtil.isNotNull(labelEntity)) {
                ProductLabelRelation relationEntity = new ProductLabelRelation();
                relationEntity.setProductId(productId);
                relationEntity.setLabelId(labelId);
                relationEntityList.add(relationEntity);
            }
        }

        iProductLabelRelationService.removeByProductId(productId);
        iProductLabelRelationService.saveBatch(relationEntityList);
        esProductSupport.productUpload(productId);

        return R.ok();
    }


    @Override
    public R<Void> bindingBatch(ReqLabelBindingBatchBody requestBody) throws Exception {
        log.info("进入【商品批量绑定标签】方法，{}", JSONUtil.toJsonStr(requestBody));

        List<Long> labelIdList = requestBody.getLabelIdList();
        List<Long> productIdList = requestBody.getProductIdList();
        String labelName = requestBody.getLabelName();

        if (StrUtil.isBlank(labelName) && CollUtil.isEmpty(labelIdList)) {
            return R.ok();
        }

        for (Long labelId : labelIdList) {
            ProductLabel labelEntity = baseMapper.selectById(labelId);
            if (ObjectUtil.isNull(labelEntity)) {
                log.info("id为【{}】的标签不存在", labelId);
                return R.fail(ZSMallStatusCodeEnum.LABEL_NOT_EXIST_ERROR.getMessageCode());
            }
        }

        List<ProductLabelRelation> relationEntityList = new ArrayList<>();
        if (StrUtil.isNotBlank(labelName)) {
            long sort = getSort();
            ProductLabel productLabel = new ProductLabel();
            productLabel.setLabelName(labelName);
            productLabel.setSort(sort);

            //标签名称不能重复
            validEntityBeforeSave(productLabel);

            baseMapper.insert(productLabel);

            Long newId = productLabel.getId();
            if (ObjectUtil.isNotNull(newId)) {
                log.info("new label id = {} ", newId);
                labelIdList.add(newId);
            }
        }

        productIdList.forEach(productId -> {
            for (Long labelId : labelIdList) {
                ProductLabel labelEntity = baseMapper.selectById(labelId);
                ProductLabelRelation relationEntity = iProductLabelRelationService.getByProductIdAndLabelId(productId, labelId);
                if (ObjectUtil.isNotNull(labelEntity) && ObjectUtil.isNull(relationEntity)) {
                    relationEntity = new ProductLabelRelation();
                    relationEntity.setProductId(productId);
                    relationEntity.setLabelId(labelId);
                    relationEntityList.add(relationEntity);
                }
            }
        });
        iProductLabelRelationService.saveBatch(relationEntityList);

        List<Long> productIds = relationEntityList.stream().map(ProductLabelRelation::getProductId).collect(Collectors.toList());
        esProductSupport.productUpload(ArrayUtil.toArray(productIds, Long.class));

        return R.ok();
    }


    @Override
    public R<Void> unbindBatch(ReqLabelBindingBatchBody requestBody) {
        log.info("进入【商品批量解绑标签】方法，{}", JSONUtil.toJsonStr(requestBody));

        List<Long> labelIdList = requestBody.getLabelIdList();
        List<Long> productIdList = requestBody.getProductIdList();

        if (CollUtil.isEmpty(labelIdList)) {
            return R.ok();
        }

        for (Long labelId : labelIdList) {
            ProductLabel labelEntity = baseMapper.selectById(labelId);
            if (ObjectUtil.isNull(labelEntity)) {
                log.info("id为【{}】的标签不存在", labelId);
                return R.fail(ZSMallStatusCodeEnum.LABEL_NOT_EXIST_ERROR.getMessageCode());
            }
        }

        List<Long> saveProductIdList = new ArrayList<>();
        productIdList.forEach(productId -> {
            for (Long labelId : labelIdList) {
                ProductLabelRelation relationEntity = iProductLabelRelationService.getByProductIdAndLabelId(productId, labelId);
                if (ObjectUtil.isNotNull(relationEntity)) {
                    saveProductIdList.add(productId);
                    iProductLabelRelationService.removeByLabelIdAndProductId(labelId, productId);
                }
            }
        });

        esProductSupport.productUpload(ArrayUtil.toArray(saveProductIdList, Long.class));
        return R.ok();
    }


    @Override
    public TableDataInfo<ProductToLabelBody> getProductPageToLabel(ReqProductBody productBody, PageQuery pageQuery) {
        log.info("进入【翻页查询商品列表（标签）】方法，{} {}", JSONUtil.toJsonStr(productBody), JSONUtil.toJsonStr(pageQuery));

        String queryValue = productBody.getQueryValue();
        String sortType = productBody.getSortType();
        List<Long> labelIdList = productBody.getLabelIdList();
        Integer pageNo = pageQuery.getPageNum();
        Integer pageSize = pageQuery.getPageSize();

        //查询
        Page<Product> queryPage = new Page<>(pageNo, pageSize);
        OrderItem orderItem;
        if (StrUtil.equals("PriceAsc", sortType)) {
            orderItem = OrderItem.asc("platform_pick_up_price");
        } else if (StrUtil.equals("PriceDesc", sortType)) {
            orderItem = OrderItem.desc("platform_pick_up_price");
        } else if (StrUtil.equals("StockAsc", sortType)) {
            orderItem = OrderItem.asc("stock_total");
        } else if (StrUtil.equals("StockDesc", sortType)) {
            orderItem = OrderItem.desc("stock_total");
        } else if (StrUtil.equals("SaleDesc", sortType)) {
            orderItem = OrderItem.desc("sold_quantity");
        } else if (StrUtil.equals("SaleAsc", sortType)) {
            orderItem = OrderItem.asc("sold_quantity");
        } else {
            orderItem = OrderItem.desc("create_time");
        }
        queryPage.addOrder(orderItem);
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        IPage<Product> productPage2Label = iProductService.getProductPage2Label(queryValue, labelIdList, queryPage);
        stopWatch.stop();
        log.info("视图已售商品查询结束,用时信息:{}秒",stopWatch.getTime(TimeUnit.SECONDS));
        List<Product> productList = productPage2Label.getRecords();
        List<ProductToLabelBody> results = new ArrayList<>();
        for (Product product : productList) {
            ProductToLabelBody body = new ProductToLabelBody();
            Long productId = product.getId();
            String name = product.getName();
            body.setName(name);
            body.setProductId(productId);
            body.setProductCode(product.getProductCode());
            SupportedLogisticsEnum supportedLogistics = product.getSupportedLogistics();

            //Sku集合
            List<ProductSkuPrice> skuPriceList = iProductSkuPriceService.getListByProductId(productId);
            //价格区间
            BigDecimal minPrice = null;
            BigDecimal maxPrice = null;
            if (CollUtil.isNotEmpty(skuPriceList)) {
                log.info("skuPriceList =========== {}", JSONUtil.toJsonStr(skuPriceList));
                List<BigDecimal> priceList;
                if (supportedLogistics == SupportedLogisticsEnum.All || supportedLogistics == SupportedLogisticsEnum.PickUpOnly) {
                    priceList = skuPriceList.stream().map(ProductSkuPrice::getPlatformPickUpPrice).collect(Collectors.toList());
                } else {
                    priceList = skuPriceList.stream().map(ProductSkuPrice::getPlatformDropShippingPrice).collect(Collectors.toList());
                }

                BigDecimal max = CollUtil.max(priceList);
                if (ObjectUtil.isNotNull(max)) {
                    maxPrice = max;
                }
                BigDecimal min = CollUtil.min(priceList);
                if (ObjectUtil.isNotNull(min) && !min.equals(max)) {
                    minPrice = min;
                }
            } else {
                minPrice = BigDecimal.ZERO;
            }
            body.setMinPrice(minPrice);
            body.setMaxPrice(maxPrice);

            // 销量和库存
            body.setQuantity(product.getStockTotal());
            body.setSold(product.getSoldQuantity());

            //获取标签
            List<ProductLabel> labelEntityList = iProductLabelRelationService.getLabelByProductId(productId);
            if (CollUtil.isNotEmpty(labelEntityList)) {
                List<Long> labelEntityIdList = labelEntityList.stream().map(ProductLabel::getId).collect(Collectors.toList());
                List<String> labelNameList = labelEntityList.stream().map(ProductLabel::getLabelName).collect(Collectors.toList());
                body.setLabelIdList(labelEntityIdList);
                body.setLabelNameList(labelNameList);
            }

            // 取第一张图片展示
            ProductSkuAttachmentVo productSkuAttachmentVo = iProductSkuAttachmentService.queryFirstImageByProductId(productId);
            if (ObjectUtil.isNotNull(productSkuAttachmentVo)) {
                body.setProductImageShowUrl(productSkuAttachmentVo.getAttachmentShowUrl());
            }
            results.add(body);
        }

        Page<ProductToLabelBody> result = new Page<>();
        result.setTotal(productPage2Label.getTotal());
        result.setRecords(results);
        return TableDataInfo.build(result);
    }


    private LambdaQueryWrapper<ProductLabel> buildQueryWrapper(ProductLabelBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<ProductLabel> lqw = Wrappers.lambdaQuery();
        lqw.like(StringUtils.isNotBlank(bo.getLabelName()), ProductLabel::getLabelName, bo.getLabelName());
        return lqw;
    }


    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(ProductLabel entity) throws Exception {
        // 做一些数据校验,如唯一约束
        String labelName = entity.getLabelName();
        Long id = entity.getId();
        LambdaQueryWrapper<ProductLabel> lqw = new LambdaQueryWrapper<>();
        lqw.eq(ProductLabel::getLabelName, labelName).eq(ProductLabel::getDelFlag, 0);
        if (ObjectUtil.isNotNull(id)) {
            lqw.ne(ProductLabel::getId, id);
        }
        List<ProductLabel> list = baseMapper.selectList(lqw);
        if (CollUtil.isNotEmpty(list)) {
            log.info("标签名【{}】已存在", labelName);
            throw new RStatusCodeException(ZSMallStatusCodeEnum.LABEL_EXIST_ERROR.args(labelName));
        }
    }


    /**
     * 标签重新排序
     *
     * @param newEntity
     */
    private boolean reorder(ProductLabel newEntity) {
        Long id = newEntity.getId();
        Long newSort = newEntity.getSort();
        //取出旧的数据
        ProductLabel oldEntity = baseMapper.selectById(id);
        Long oldSort = oldEntity.getSort();
        LambdaQueryWrapper<ProductLabel> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProductLabel::getDelFlag, 0);
        //更换排序
        List<ProductLabel> list = null;
        if (ObjectUtil.equals(newSort, oldSort)) {
            return true;
        } else if (newSort > oldSort) {
            queryWrapper.between(ProductLabel::getSort, oldSort, newSort);
            list = baseMapper.selectList(queryWrapper);
            list.forEach(entity -> {
                if (ObjectUtil.equals(id, entity.getId())) {
                    entity.setSort(newSort);
                } else {
                    entity.setSort(entity.getSort() - 1);
                }
            });
        } else if (oldSort > newSort) {
            queryWrapper.between(ProductLabel::getSort, newSort, oldSort);
            list = baseMapper.selectList(queryWrapper);
            list.forEach(entity -> {
                if (ObjectUtil.equals(id, entity.getId())) {
                    entity.setSort(newSort);
                } else {
                    entity.setSort(entity.getSort() + 1);
                }
            });
        }
        if (CollUtil.isNotEmpty(list)) {
            return baseMapper.updateBatchById(list);
        }
        return true;
    }


    /**
     * 获取最大排序值
     *
     * @return
     */
    private long getSort() {
        long sort = 1L;
        LambdaQueryWrapper<ProductLabel> lqw = new LambdaQueryWrapper<>();
        lqw.eq(ProductLabel::getDelFlag, 0);
        List<ProductLabel> list = baseMapper.selectList(lqw);
        if (CollUtil.isNotEmpty(list)) {
            ProductLabel labelEntity = list.stream().max(Comparator.comparing(ProductLabel::getSort)).get();
            sort = labelEntity.getSort() + 1;
        }
        return sort;
    }

}
