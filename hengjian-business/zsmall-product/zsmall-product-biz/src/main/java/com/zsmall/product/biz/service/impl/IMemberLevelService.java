package com.zsmall.product.biz.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hengjian.system.domain.SysUser;
import com.zsmall.product.entity.domain.bo.member.MemberLevelQueryBo;
import com.zsmall.product.entity.domain.member.MemberLevel;
import com.zsmall.product.entity.domain.vo.member.LevelPriceDTO;
import com.zsmall.bma.open.member.mapper.MemberLevelMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2024/5/7 11:31
 */
@Slf4j
@Service
@RequiredArgsConstructor
@Deprecated
public class IMemberLevelService extends ServiceImpl<MemberLevelMapper, MemberLevel>{

    public IPage<MemberLevel> queryPageList(Page<MemberLevel> queryPage, @Param("queryBo") MemberLevelQueryBo queryBo) {
        return baseMapper.queryPageList(queryPage, queryBo);
    }

    public SysUser getUserName(Long createBy) {
        return baseMapper.getUserName(createBy);
    }

    public SysUser checkTenantId(@Param("tenantId")String tenantId) {
        return baseMapper.checkTenantId(tenantId);
    }

    public String getLevelName(Long dictCode) {
        return baseMapper.getLevelName(dictCode);
    }

    public List<LevelPriceDTO> getLevelNames(List<Long> dictCodes) {
        return baseMapper.getLevelNames(dictCodes);
    }
}
