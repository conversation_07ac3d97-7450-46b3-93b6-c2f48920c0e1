package com.zsmall.product.biz.service;

import com.hengjian.common.core.domain.R;
import com.hengjian.common.core.exception.RStatusCodeException;
import com.hengjian.common.mybatis.core.page.PageQuery;
import com.hengjian.common.mybatis.core.page.TableDataInfo;
import com.zsmall.product.entity.domain.bo.ProductReviewRecordBo;
import com.zsmall.product.entity.domain.bo.SubmitReviewRecordBo;
import com.zsmall.product.entity.domain.dto.product.ProductReviewDTO;
import com.zsmall.product.entity.domain.vo.productReview.ProductReviewPageVo;

import java.util.List;
import java.util.concurrent.ExecutionException;

/**
 * 商品审核记录Service接口
 *
 * <AUTHOR>
 * @date 2023-05-31
 */
public interface ProductReviewRecordService {

    /**
     * 查询商品审核记录列表
     */
    TableDataInfo<ProductReviewPageVo> queryPageList(ProductReviewRecordBo bo, PageQuery pageQuery) throws ExecutionException, InterruptedException;

    /**
     * 审核通过
     */
    R<Void> productReview(ProductReviewRecordBo bo) throws Exception;

    /**
     * 提交商品审核
     *
     * @param bo
     * @return
     */
    R<Void> submitProductReview(SubmitReviewRecordBo bo) throws RStatusCodeException;

    void extractedProductReview(String productCode);
    /**
     * 查询指定商品的未提交记录，提交审核
     *
     * @param productCode
     */
    void submitReview(String productCode);

    /**
     * 生成审核记录
     *
     * @param productReviewDTO
     */
    void generateReviewRecord(ProductReviewDTO productReviewDTO);

    R<Void> submitProductsReview(List<String> bos);
}
