package com.zsmall.product.biz.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hengjian.common.core.domain.R;
import com.hengjian.common.core.domain.model.LoginUser;
import com.hengjian.common.core.enums.TenantType;
import com.hengjian.common.mybatis.core.page.PageQuery;
import com.hengjian.common.mybatis.core.page.TableDataInfo;
import com.hengjian.common.satoken.utils.LoginHelper;
import com.zsmall.common.enums.BusinessParameterType;
import com.zsmall.common.enums.common.ChannelTypeEnum;
import com.zsmall.common.enums.statuscode.ZSMallStatusCodeEnum;
import com.zsmall.common.service.BusinessParameterService;
import com.zsmall.product.biz.service.ProductChannelControlService;
import com.zsmall.product.biz.support.EsProductSupport;
import com.zsmall.product.entity.domain.ProductChannelControl;
import com.zsmall.product.entity.domain.ProductSku;
import com.zsmall.product.entity.domain.bo.productChannelControl.ProductChannelControlBo;
import com.zsmall.product.entity.domain.bo.productChannelControl.SwitchControlTypeBo;
import com.zsmall.product.entity.domain.vo.ProductSkuAttachmentVo;
import com.zsmall.product.entity.domain.vo.productChannelControl.ProductChannelControlBase;
import com.zsmall.product.entity.domain.vo.productChannelControl.ProductChannelControlListVo;
import com.zsmall.product.entity.iservice.IProductChannelControlService;
import com.zsmall.product.entity.iservice.IProductSkuAttachmentService;
import com.zsmall.product.entity.iservice.IProductSkuService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 商品渠道管控-业务实现层
 *
 * <AUTHOR>
 * @date 2023/7/14
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ProductChannelControlServiceImpl implements ProductChannelControlService {

    private final IProductSkuService iProductSkuService;
    private final IProductChannelControlService iProductChannelControlService;
    private final IProductSkuAttachmentService iProductSkuAttachmentService;

    private final BusinessParameterService businessParameterService;
    private final EsProductSupport esProductSupport;

    /**
     * 分页查询商品管控列表
     * @param bo
     * @param pageQuery
     * @return
     */
    @Override
    public TableDataInfo<ProductChannelControlListVo> list(ProductChannelControlBo bo, PageQuery pageQuery) {
        LoginUser loginUser = LoginHelper.getLoginUser(TenantType.Manager);

        IPage<ProductSku> page = iProductSkuService.queryProductSkuControlPage(bo.getQueryType(), bo.getQueryValue(), pageQuery.build());
        List<ProductChannelControlListVo> results = new ArrayList<>();
        List<ProductSku> skuList = page.getRecords();
        for (ProductSku productSku : skuList) {
            ProductChannelControlListVo newBody = new ProductChannelControlListVo();
            String sku = productSku.getSku();
            String productName = productSku.getName();
            String productSkuCode = productSku.getProductSkuCode();
            ChannelTypeEnum[] channelTypes = ChannelTypeEnum.values();

            // 查询是否是全渠道管控
            ProductChannelControl allChannelControl =
                iProductChannelControlService.queryAllChannelControl(productSkuCode);
            if (allChannelControl != null) {  // 全渠道管控则使用单独的管控信息
                ProductChannelControlBase newControlBody = new ProductChannelControlBase();
                newControlBody.setControlId(allChannelControl.getId());
                newControlBody.setProductSkuCode(productSkuCode);
                newControlBody.setAllowCodes(allChannelControl.getAllowTenantId());
                newBody.setAllChannelControl(true);
                newBody.setAllChannelControlInfo(newControlBody);
            } else {  // 各渠道单独管控则使用数组形式的管控信息
                List<ProductChannelControlBase> controlInfoList = new ArrayList<>();
                for (ChannelTypeEnum channelType : channelTypes) {
                    if (ObjectUtil.equal(channelType, ChannelTypeEnum.OneLink) || ObjectUtil.equal(channelType, ChannelTypeEnum.Marketplace)) {
                        continue;
                    }

                    String channelTypeName = channelType.name();
                    ProductChannelControlBase newControlBody = new ProductChannelControlBase();
                    newControlBody.setChannelType(channelTypeName);
                    newControlBody.setProductSkuCode(productSkuCode);

                    ProductChannelControl channelControl =
                        iProductChannelControlService.queryByProductSkuCodeAndChannel(productSkuCode, channelTypeName);
                    if (channelControl != null) {
                        newControlBody.setControlId(channelControl.getId());
                        newControlBody.setAllowCodes(channelControl.getAllowTenantId());
                    }
                    controlInfoList.add(newControlBody);
                }
                newBody.setControlInfoList(controlInfoList);
            }

            ProductSkuAttachmentVo skuAttachmentVo = iProductSkuAttachmentService.queryFirstImageByProductSkuCode(productSkuCode);

            newBody.setSku(sku);
            newBody.setProductName(productName);
            newBody.setProductImg(skuAttachmentVo.getAttachmentShowUrl());
            newBody.setProductSkuCode(productSkuCode);
            results.add(newBody);
        }
        return TableDataInfo.build(results, page.getTotal());
    }

    /**
     * 切换商品渠道管控类型
     * @param bo
     * @return
     */
    @Override
    public R<Void> switchProductChannelControl(SwitchControlTypeBo bo) {
        LoginUser loginUser = LoginHelper.getLoginUser(TenantType.Manager);
        String productSkuCode = bo.getProductSkuCode();
        Boolean allChannelControl = bo.getAllChannelControl();

        iProductChannelControlService.deleteControlByChannel(productSkuCode, null);
        if (allChannelControl) {
            ProductChannelControl controlEntity = new ProductChannelControl();
            controlEntity.setProductSkuCode(productSkuCode);
            controlEntity.setAllChannelControl(allChannelControl);
            iProductChannelControlService.save(controlEntity);
        }

        esProductSupport.productSkuUpload(productSkuCode);
        return R.ok();
    }

    /**
     * 设置商品管控
     *
     * @param apiRequest
     * @return
     */
    @Override
    public R<Void> setProductChannelControl(ProductChannelControlBase bo) {
        JSONObject controlLimit = businessParameterService.getValueFromJSONObject(BusinessParameterType.PRODUCT_CONTROL_LIMIT_QUANTITY);
        Long controlId = bo.getControlId();
        JSONArray allowCodes = bo.getAllowCodes();
        String channelType = bo.getChannelType();

        if (allowCodes != null) {
            CollUtil.removeAny(allowCodes, "", null);
        }

        if (CollUtil.isNotEmpty(allowCodes)) {
            if (!StrUtil.equals(channelType, "ALL")) {
                Integer limitQuantity = controlLimit.getInt(channelType);
                if (limitQuantity < CollUtil.size(allowCodes)) {
                    return R.fail(ZSMallStatusCodeEnum.MEMBER_ID_EXCEED_LIMIT.args(limitQuantity.toString()));
                }
            }
        } else {
            allowCodes = null;
        }

        ProductChannelControl channelControl;
        if (controlId != null) {
            channelControl = iProductChannelControlService.getById(controlId);
            channelControl.setAllowTenantId(allowCodes);
            esProductSupport.productSkuUpload(channelControl.getProductSkuCode());
        } else {
            channelControl = new ProductChannelControl();
            channelControl.setAllowTenantId(allowCodes);
            channelControl.setChannelType(ChannelTypeEnum.valueOf(channelType));
            channelControl.setProductSkuCode(bo.getProductSkuCode());
        }
        iProductChannelControlService.saveOrUpdate(channelControl);
        return R.ok();
    }
}
