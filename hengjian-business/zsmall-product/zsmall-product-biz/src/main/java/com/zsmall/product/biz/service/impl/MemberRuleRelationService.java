package com.zsmall.product.biz.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hengjian.common.core.domain.R;
import com.zsmall.product.entity.domain.bo.member.MemberLevelQueryBo;
import com.zsmall.product.entity.domain.member.MemberRuleRelation;
import com.zsmall.product.entity.domain.vo.member.MemberLevelVO;
import com.zsmall.bma.open.member.mapper.MemberRuleRelationMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2024/5/8 15:50
 */
@Service
@RequiredArgsConstructor
@Slf4j
@Deprecated
public class MemberRuleRelationService extends ServiceImpl<MemberRuleRelationMapper, MemberRuleRelation> {

    public void add(MemberRuleRelation ruleRelation) {

        baseMapper.insert(ruleRelation);
    }

    public R<Void> edit(MemberLevelVO vo) {
        return null;
    }

    public IPage<MemberRuleRelation> queryPageList(Page<MemberRuleRelation> queryPage, MemberLevelQueryBo queryBo) {
        return baseMapper.queryPageList(queryPage, queryBo);

    }

    /**
     * 功能描述：del
     *
     * @param id 身份证件
     * <AUTHOR>
     * @date 2024/05/10
     */
    public void del(Long id) {
        baseMapper.logicDel(id);
    }
}
