package com.zsmall.product.biz.listener;

import cn.hutool.core.util.StrUtil;
import com.zsmall.extend.event.product.EsProductUploadEvent;
import com.zsmall.extend.event.product.SetProductSkuStockEvent;
import com.zsmall.extend.event.product.TaskStockSyncEvent;
import com.zsmall.product.biz.service.ProductSkuService;
import com.zsmall.product.biz.support.EsProductSupport;
import com.zsmall.product.biz.support.ProductSupport;
import com.zsmall.product.entity.domain.ProductSku;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;

/**
 * 商品SKU相关事件监听
 *
 * <AUTHOR>
 * @date 2023/7/24
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ProductSkuEventListener {

    private final ProductSkuService productSkuService;
    private final ProductSupport productSupport;
    private final EsProductSupport esProductSupport;

    /**
     * 监听设置SKU库存事件
     * @param event
     */
    @EventListener
    public void listenInSetProductSkuStock(SetProductSkuStockEvent event) {
        ProductSku productSku = event.getProductSku();
        productSkuService.setProductSkuStock(productSku);
    }

    /**
     * 监听设置SKU库存事件
     * @param event
     */
    @EventListener
    public void listenInTaskStockSync(TaskStockSyncEvent event) {
        Long productSkuId = event.getProductSkuId();
        String productSkuCode = event.getProductSkuCode();
        if (productSkuId != null) {
            productSupport.createSyncTask(productSkuId);
        } else if (StrUtil.isNotBlank(productSkuCode)) {
            productSupport.createSyncTask(productSkuCode);
        }
    }

    /**
     * 监听Es商品数据上传事件
     * @param event
     */
    @EventListener
    public void listenInEsProductUpload(EsProductUploadEvent event) {
        EsProductUploadEvent.Type type = event.getType();
        switch (type) {
            case ProductCode:
                esProductSupport.productUpload(event.getProductCode());
                break;
            case ProductSkuCode:
                esProductSupport.productSkuUpload(event.getProductSkuCodes());
                break;
            default:
                break;
        }

    }

}
