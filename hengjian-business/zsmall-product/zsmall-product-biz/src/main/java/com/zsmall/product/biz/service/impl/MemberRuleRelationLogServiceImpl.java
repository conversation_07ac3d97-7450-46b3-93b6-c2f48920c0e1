package com.zsmall.product.biz.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hengjian.common.satoken.utils.LoginHelper;
import com.zsmall.bma.open.member.iservice.IMemberLevelV2ServiceImpl;
import com.zsmall.common.enums.common.OperationTypeEnum;
import com.zsmall.product.entity.domain.member.MemberRuleRelation;
import com.zsmall.product.entity.domain.member.MemberRuleRelationLog;
import com.zsmall.product.entity.domain.vo.member.MemberRuleRelationVO;
import com.zsmall.bma.open.member.mapper.MemberLevelMapper;
import com.zsmall.bma.open.member.mapper.MemberRuleRelationLogMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2024/5/9 15:59
 */
@Service
@Slf4j
@RequiredArgsConstructor
@Deprecated
public class MemberRuleRelationLogServiceImpl extends ServiceImpl<MemberRuleRelationLogMapper, MemberRuleRelationLog> {
    private final IMemberLevelV2ServiceImpl iMemberLevelService;
    private final MemberLevelMapper memberLevelMapper;

    /**
     * 功能描述：创建添加日志
     *
     * @param memberRuleRelation 成员规则关系
     * <AUTHOR>
     * @date 2024/05/09
     */
    public void createAddLog(MemberRuleRelation memberRuleRelation) {
        Long levelId = memberRuleRelation.getLevelId();

        MemberRuleRelationLog memberRuleRelationLog = new MemberRuleRelationLog();
        BeanUtil.copyProperties(memberRuleRelation,memberRuleRelationLog);
        memberRuleRelationLog.setId(null);
        memberRuleRelationLog.setDestinationPhoneNumber(memberRuleRelation.getPhoneNumber());
        memberRuleRelationLog.setMemberRuleRelationId(memberRuleRelation.getId());
        memberRuleRelationLog.setDestinationLevelId(String.valueOf(levelId));


        String levelName = memberLevelMapper.getLevelName(levelId);
        memberRuleRelationLog.setDestinationLevel(levelName);
        memberRuleRelationLog.setOperationType(OperationTypeEnum.Add.getValue());
        save(memberRuleRelationLog);
    }

    public void createDelLog(MemberRuleRelationVO vo) {

        MemberRuleRelationLog memberRuleRelationLog = new MemberRuleRelationLog();

//        memberRuleRelationLog.setDepartureLevelId();
//        memberRuleRelationLog.setDestinationLevelId();
//        memberRuleRelationLog.setDepartureLevel();
//        memberRuleRelationLog.setDestinationLevel();
//        memberRuleRelationLog.setEmailAddress();
//        memberRuleRelationLog.setNickName();


//        memberRuleRelationLog.setDeparturePhoneNumber();
//        memberRuleRelationLog.setDestinationPhoneNumber();
        memberRuleRelationLog.setRuleFollowerTenantId(vo.getRuleFollowerTenantId());
        memberRuleRelationLog.setRuleCustomizerTenantId(vo.getRuleCustomizerTenantId());

        memberRuleRelationLog.setMemberRuleRelationId(vo.getId());
        memberRuleRelationLog.setOperationType(OperationTypeEnum.Del.getValue());
        memberRuleRelationLog.setCreateBy(LoginHelper.getUserId());
        memberRuleRelationLog.setUpdateBy(LoginHelper.getUserId());
        save(memberRuleRelationLog);
    }

    public void createEditLog(MemberRuleRelationVO vo, MemberRuleRelation old) {
        MemberRuleRelationLog memberRuleRelationLog = new MemberRuleRelationLog();
        BeanUtil.copyProperties(vo,memberRuleRelationLog);
        memberRuleRelationLog.setId(null);
        memberRuleRelationLog.setDepartureLevelId(String.valueOf(old.getLevelId()));
        memberRuleRelationLog.setDestinationLevelId(String.valueOf(vo.getLevelId()));

        memberRuleRelationLog.setOperationType(OperationTypeEnum.Update.getValue());
        memberRuleRelationLog.setMemberRuleRelationId(vo.getId());
        if(ObjectUtil.isNotEmpty(vo.getPhoneNumber())){
            memberRuleRelationLog.setDeparturePhoneNumber(old.getPhoneNumber());
            memberRuleRelationLog.setDestinationPhoneNumber(vo.getPhoneNumber());
        }

        memberRuleRelationLog.setRuleFollowerTenantId(old.getRuleFollowerTenantId());
        memberRuleRelationLog.setRuleCustomizerTenantId(old.getRuleCustomizerTenantId());
        memberRuleRelationLog.setCreateBy(LoginHelper.getUserId());

        memberRuleRelationLog.setUpdateBy(LoginHelper.getUserId());


        save(memberRuleRelationLog);
    }
}
