package com.zsmall.product.biz.service;

import com.hengjian.common.mybatis.core.page.PageQuery;
import com.hengjian.common.mybatis.core.page.TableDataInfo;
import com.zsmall.product.entity.domain.ProductCategoryRelation;
import com.zsmall.product.entity.domain.bo.category.ProductCategoryRelationBo;
import com.zsmall.product.entity.domain.vo.category.ProductCategoryRelationVo;

import java.util.Collection;
import java.util.List;

/**
 * 商品SPU-商品分类关联Service接口
 *
 * <AUTHOR> Li
 * @date 2023-05-22
 */
public interface IProductCategoryRelationService {

    /**
     * 查询商品SPU-商品分类关联
     */
    ProductCategoryRelationVo queryById(Long id);

    /**
     * 查询商品SPU-商品分类关联列表
     */
    TableDataInfo<ProductCategoryRelationVo> queryPageList(ProductCategoryRelationBo bo, PageQuery pageQuery);

    /**
     * 查询商品SPU-商品分类关联列表
     */
    List<ProductCategoryRelationVo> queryList(ProductCategoryRelationBo bo);

    /**
     * 新增商品SPU-商品分类关联
     */
    Boolean insertByBo(ProductCategoryRelationBo bo);

    /**
     * 批量新增商品SPU-商品分类关联
     * @param entityList
     * @return
     */
    Boolean insertBatch(List<ProductCategoryRelation> entityList);

    /**
     * 修改商品SPU-商品分类关联
     */
    Boolean updateByBo(ProductCategoryRelationBo bo);

    /**
     * 校验并批量删除商品SPU-商品分类关联信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 根据实体类删除
     * @param entityList
     * @return
     */
    Boolean deleteByEntityList(List<ProductCategoryRelation> entityList);

    /**
     * 批量保存商品类目关系数据
     * @param productId
     * @param ids
     */
    void batchUpdateProductCategoryRelation(Long productId, List<Long> ids);

    /**
     * 根据类目ID集合查询是否存在商品绑定关系
     * @param categoryIds
     * @return
     */
    Boolean existRelationByCategoryIds(List<Long> categoryIds);

    /**
     * 根据类目ID集合查询所有关联的商品编号
     * @param categoryIds
     * @return
     */
    List<String> existProductCodeRelationByCategoryIds(List<Long> categoryIds);

    /**
     * 根据商品主键查询分类关系集合
     * @return
     */
    List<ProductCategoryRelation> queryByProductId(Long productId);

    /**
     * 根据商品主键删除
     */
    Boolean deleteByProductId(Long productId);

}
