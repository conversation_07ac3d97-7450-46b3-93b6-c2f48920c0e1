package com.zsmall.product.biz.mq;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import com.hengjian.stream.mq.constant.RabbitMqConstant;
import com.hengjian.stream.mqProducer.constants.QueueConstants;
import com.hengjian.stream.mqProducer.domain.MessageDto;
import com.rabbitmq.client.Channel;
import com.zsmall.product.biz.support.EsProductSupport;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class ProductEsSynchronizationListener {
    @Resource
    private RabbitTemplate rabbitTemplate;
    @Resource
    private  EsProductSupport esProductSupport;


    @RabbitHandler
    @RabbitListener(queues = RabbitMqConstant.ES_PRODUCT_SYNCHRONIZATION_QUEUE)
    public void productEsSynchronizationListener(Message message, @Header(AmqpHeaders.CHANNEL) Channel channel) throws IOException, InterruptedException {
        //休息1秒，保证上游事务已经提交
        Thread.sleep(1000);
        String messageContext = new String(message.getBody());
        log.info("【消费者 - ElasticSearch商品数据推送】通道: {}, 收到数据: {}", QueueConstants.QueueName.ES_PRODUCT_PUSH, messageContext);
        try {
            MessageDto messageDto = JSONUtil.toBean(messageContext, MessageDto.class);
            String msgId = messageDto.getMsgId();
            Map<String, Object> data = (Map<String, Object>) messageDto.getData();
            List<String> productCodes = (List<String>) data.get("productCodes");
            List<String> productSkuCodes = (List<String>) data.get("productSkuCodes");
            if (CollUtil.isNotEmpty(productCodes)) {
                esProductSupport.productUploadForConsumer(productCodes);
            }
            if (CollUtil.isNotEmpty(productSkuCodes)) {
                esProductSupport.productSkuUploadForConsumer(productSkuCodes);
            }
        } catch (Exception e) {
            log.error("【消费者 - ElasticSearch商品数据推送】发生异常：{}", e.getMessage(), e);
        }finally {
            channel.basicAck(message.getMessageProperties().getDeliveryTag(),false);
        }

    }
}

