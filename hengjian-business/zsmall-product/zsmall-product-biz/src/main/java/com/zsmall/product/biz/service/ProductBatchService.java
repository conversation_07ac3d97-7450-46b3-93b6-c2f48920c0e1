package com.zsmall.product.biz.service;

import cn.hutool.json.JSONObject;
import com.hengjian.common.core.domain.R;
import com.zsmall.product.entity.domain.bo.productBatch.ReqExcelTypeBody;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * 商品批量处理相关
 *
 * <AUTHOR>
 * @create 2022/6/1 12:18
 */
public interface ProductBatchService {

    /**
     * 根据类型下载导入模板
     *
     * @param apiRequest
     * @param response
     */
    R<Void> downloadExcelByType(ReqExcelTypeBody apiRequest, HttpServletResponse response);

    /**
     * 上传商品库存更新Excel
     *
     * @param request
     * @return
     */
    R<JSONObject> uploadProductQuantityUpdate(HttpServletRequest request) throws Exception;

    /**
     * 上传商品价格更新Excel
     *
     * @param request
     * @return
     */
    R<JSONObject> uploadProductPriceUpdate(HttpServletRequest request) throws Exception;

    /**
     * 批量下架商品
     *
     * @param request
     * @return
     */
    R<Void> batchOffShelfProduct(HttpServletRequest request) throws Exception;

}
