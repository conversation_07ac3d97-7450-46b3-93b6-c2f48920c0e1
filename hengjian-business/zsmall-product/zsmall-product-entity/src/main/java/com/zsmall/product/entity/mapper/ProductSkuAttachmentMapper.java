package com.zsmall.product.entity.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.hengjian.common.mybatis.core.mapper.BaseMapperPlus;
import com.zsmall.product.entity.domain.ProductSkuAttachment;
import com.zsmall.product.entity.domain.vo.ProductSkuAttachmentVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 商品SKU附件Mapper接口
 *
 * <AUTHOR> Li
 * @date 2023-05-26
 */
public interface ProductSkuAttachmentMapper extends BaseMapperPlus<ProductSkuAttachment, ProductSkuAttachmentVo> {

    List<ProductSkuAttachment> getFirstByProductSkuCode(@Param("productSkuCode") String productSkuCode);

    /**
     * 根据商品主键查询首图 queryFirstImageByProductId
     * @param productId
     * @return
     */
    @InterceptorIgnore(tenantLine = "true")
    ProductSkuAttachmentVo queryFirstImageByProductId(@Param("productId") Long productId);

    /**
     * 根据商品SKU主键查询首图
     * @param productSkuId
     * @return
     */
    @InterceptorIgnore(tenantLine = "true")
    ProductSkuAttachmentVo queryFirstImageByProductSkuId(@Param("productSkuId") Long productSkuId);

    /**
     * 根据商品SKU编号查询首图
     * @param productSkuCode
     * @return
     */
    @InterceptorIgnore(tenantLine = "true")
    ProductSkuAttachmentVo queryFirstImageByProductSkuCode(@Param("productSkuCode") String productSkuCode);

    /**
     * 根据商品SKU编号查询所有图片
     * @param productSkuCode
     * @return
     */
    @InterceptorIgnore(tenantLine = "true")
    List<String> queryAllImageByProductSkuCode(@Param("productSkuCode") String productSkuCode);

    /**
     * 根据商品SKU主键查询所有有效的附件
     * @param productSkuId
     * @return
     */
    List<Long> queryIdsByProductSkuId(@Param("productSkuId") Long productSkuId);

    /**
     * 功能描述：按产品id查询第一张图片
     *
     * @param productIds 产品ID
     * @return {@link ProductSkuAttachmentVo }
     * <AUTHOR>
     * @date 2024/07/24
     */
    List<ProductSkuAttachmentVo> queryFirstImageByProductIds(@Param("productIds") List<Long> productIds,@Param("tenantId") String tenantId);
    /**
     * @description: 查询productSkuCode的主图信息
     * @author: Len
     * @date: 2024/10/14 16:22
     * @param: productSkuCode
     * @return: java.util.List<com.zsmall.product.entity.domain.vo.ProductSkuAttachmentVo>
     **/
    List<ProductSkuAttachmentVo> queryFirstImageByProductSkuCodes(@Param("productSkuCode") List<String> productSkuCode);

}
