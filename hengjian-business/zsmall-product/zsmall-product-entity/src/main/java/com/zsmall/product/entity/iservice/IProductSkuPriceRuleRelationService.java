package com.zsmall.product.entity.iservice;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hengjian.common.core.enums.TenantType;
import com.hengjian.common.core.utils.MapstructUtils;
import com.hengjian.common.mybatis.core.page.PageQuery;
import com.hengjian.common.mybatis.core.page.TableDataInfo;
import com.hengjian.common.satoken.utils.LoginHelper;
import com.hengjian.common.tenant.helper.TenantHelper;
import com.zsmall.product.entity.domain.ProductSkuPriceRuleRelation;
import com.zsmall.product.entity.domain.bo.productSkuPrice.ProductSkuPriceRuleRelationBo;
import com.zsmall.product.entity.domain.vo.productSkuPrice.ProductSkuPriceRuleRelationVo;
import com.zsmall.product.entity.mapper.ProductSkuPriceRuleRelationMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 商品sku价格计算公式关联Service业务层处理
 * todo 所有的api需要改造支持站点
 * <AUTHOR> Li
 * @date 2023-05-22
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class IProductSkuPriceRuleRelationService extends ServiceImpl<ProductSkuPriceRuleRelationMapper, ProductSkuPriceRuleRelation> {

    private final ProductSkuPriceRuleRelationMapper baseMapper;

    /**
     * 查询商品sku价格计算公式关联
     */
    public ProductSkuPriceRuleRelationVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询商品sku价格计算公式关联列表
     */
    public TableDataInfo<ProductSkuPriceRuleRelationVo> queryPageList(ProductSkuPriceRuleRelationBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<ProductSkuPriceRuleRelation> lqw = buildQueryWrapper(bo);
        Page<ProductSkuPriceRuleRelationVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询商品sku价格计算公式关联列表
     */
    public List<ProductSkuPriceRuleRelationVo> queryList(ProductSkuPriceRuleRelationBo bo) {
        LambdaQueryWrapper<ProductSkuPriceRuleRelation> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<ProductSkuPriceRuleRelation> buildQueryWrapper(ProductSkuPriceRuleRelationBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<ProductSkuPriceRuleRelation> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getProductSkuPriceId() != null, ProductSkuPriceRuleRelation::getProductSkuPriceId, bo.getProductSkuPriceId());
        lqw.eq(bo.getProductSkuId() != null, ProductSkuPriceRuleRelation::getProductSkuId, bo.getProductSkuId());
        lqw.eq(bo.getProductSkuPriceRuleId() != null, ProductSkuPriceRuleRelation::getProductSkuPriceRuleId, bo.getProductSkuPriceRuleId());
        return lqw;
    }

    /**
     * 新增商品sku价格计算公式关联
     */
    public Boolean insertByBo(ProductSkuPriceRuleRelationBo bo) {
        ProductSkuPriceRuleRelation add = MapstructUtils.convert(bo, ProductSkuPriceRuleRelation.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    public Boolean insert(ProductSkuPriceRuleRelation entity) {
        log.info("进入【新增商品sku价格计算公式关联】 entity = {}", JSONUtil.toJsonStr(entity));
        return baseMapper.insert(entity) > 0;
    }

    /**
     * 新增商品sku价格计算公式关联
     * @param list
     * @return
     */
    public Boolean batchInsert(List<ProductSkuPriceRuleRelation> list) {
        return baseMapper.insertOrUpdateBatch(list);
    }

    /**
     * 修改商品sku价格计算公式关联
     */
    public Boolean updateByBo(ProductSkuPriceRuleRelationBo bo) {
        ProductSkuPriceRuleRelation update = MapstructUtils.convert(bo, ProductSkuPriceRuleRelation.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(ProductSkuPriceRuleRelation entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除商品sku价格计算公式关联
     */
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }


    public List<ProductSkuPriceRuleRelationVo> getListByProductSkuIds(List<Long> productSkuIds) {
        log.info("进入【根据productSkuIds获取关系数据集】方法, productSkuIds = {}", JSONUtil.toJsonStr(productSkuIds));
        LambdaQueryWrapper<ProductSkuPriceRuleRelation> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(ProductSkuPriceRuleRelation::getProductSkuId, productSkuIds);
        return baseMapper.selectVoList(queryWrapper);
    }

    /**
     * 根据productSkuCodes获取关系数据集
     * @param productSkuCodes
     * @return
     */
    public List<ProductSkuPriceRuleRelationVo> getListByProductSkuCodes(List<String> productSkuCodes) {
        log.info("进入【根据productSkuIds获取关系数据集】方法, productSkuCodes = {}", JSONUtil.toJsonStr(productSkuCodes));
        LambdaQueryWrapper<ProductSkuPriceRuleRelation> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(ProductSkuPriceRuleRelation::getProductSkuCode, productSkuCodes);
        return baseMapper.selectVoList(queryWrapper);
    }

    public ProductSkuPriceRuleRelationVo getByProductSkuId(Long productSkuId) {
        log.info("进入【根据productSkuId获取关系数据】方法, productSkuId = {}", productSkuId);
        LambdaQueryWrapper<ProductSkuPriceRuleRelation> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProductSkuPriceRuleRelation::getProductSkuId, productSkuId);
        return baseMapper.selectVoOne(queryWrapper);
    }

    public List<ProductSkuPriceRuleRelationVo> getByRuleId(Long ruleId) {
        log.info("进入【根据公式id获取商品价格id集合】方法, ruleId = {}", ruleId);
        LambdaQueryWrapper<ProductSkuPriceRuleRelation> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProductSkuPriceRuleRelation::getProductSkuPriceRuleId, ruleId);
        return baseMapper.selectVoList(queryWrapper);
    }

    public ProductSkuPriceRuleRelation getByProductSkuCode(String productSkuCode) {
        log.info("进入【根据productSkuId获取关系数据】方法, productSkuCode = {}", productSkuCode);
        return baseMapper.getByProductSkuCode(productSkuCode);
    }

    public Long getCountByRuleId(Long ruleId) {
        log.info("进入【根据ruleId获取已绑定的关系数量】方法, ruleId = {}", ruleId);
        LambdaQueryWrapper<ProductSkuPriceRuleRelation> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProductSkuPriceRuleRelation::getProductSkuPriceRuleId, ruleId);
        return baseMapper.selectCount(queryWrapper);
    }

    public boolean batchDeletePriceRelationByProductSkuIds(List<Long> productSkuIds) {
        log.info("进入【通过productSkuIds删除相应的价格计算公式关联数据】方法, productSkuIds = {}", JSONUtil.toJsonStr(productSkuIds));
        LambdaQueryWrapper<ProductSkuPriceRuleRelation> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(ProductSkuPriceRuleRelation::getProductSkuId, productSkuIds);
        List<ProductSkuPriceRuleRelationVo> voList = getListByProductSkuIds(productSkuIds);
        if (CollUtil.isEmpty(voList)) {
            return true;
        }
        List<Long> ids = voList.stream().map(ProductSkuPriceRuleRelationVo::getId).collect(Collectors.toList());
        return deleteWithValidByIds(ids, false);
    }

    public List<String> getItemNosByRuleCode(String ruleCode) {
        log.info("进入【根据ruleCode获取itemNos】方法, ruleCode = {}", ruleCode);
        if (ObjectUtil.equals(LoginHelper.getTenantTypeEnum(), TenantType.Supplier)) {
            return baseMapper.getItemNosByRuleCode(ruleCode);
        }else {
            return TenantHelper.ignore(() -> baseMapper.getItemNosByRuleCode(ruleCode));
        }
    }

    public Boolean deleteRelationByProductSkuId(Long productSkuId) {
        log.info("进入【根据productSkuId删除关系数据】方法, productSkuId = {}", productSkuId);
        ProductSkuPriceRuleRelationVo vo = this.getByProductSkuId(productSkuId);
        if (vo == null) {
            return true;
        }
        return baseMapper.deleteById(vo.getId()) > 0 ? true : false;

    }

    public Boolean deleteRelationByRuleId(Long ruleId) {
        log.info("进入【根据productSkuId删除关系数据】方法, ruleId = {}", ruleId);
        List<ProductSkuPriceRuleRelationVo> voList = this.getByRuleId(ruleId);
        if (CollUtil.isEmpty(voList)) {
            return true;
        }
        List<Long> ids = voList.stream().map(ProductSkuPriceRuleRelationVo::getId).collect(Collectors.toList());
        return this.deleteWithValidByIds(ids, false);

    }

    public Boolean deleteRelationByPriceIds(List<Long> productSkuPriceIds) {
        log.info("进入【根据商品价格删除绑定数据】方法, productSkuPriceIds = {}", JSONUtil.toJsonStr(productSkuPriceIds));
        LambdaQueryWrapper<ProductSkuPriceRuleRelation> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(ProductSkuPriceRuleRelation::getProductSkuPriceId, productSkuPriceIds);
        List<ProductSkuPriceRuleRelationVo> voList = baseMapper.selectVoList(queryWrapper);
        if (CollUtil.isEmpty(voList)) {
            return true;
        }
        List<Long> ids = voList.stream().map(ProductSkuPriceRuleRelationVo::getId).collect(Collectors.toList());
        return this.deleteWithValidByIds(ids, true);
    }

    public Boolean deleteByProductSkuIdList(List<Long> productSkuIdList) {
        log.info("进入【根据Sku主键数组删除】 productSkuIdList = {}", JSONUtil.toJsonStr(productSkuIdList));
        LambdaQueryWrapper<ProductSkuPriceRuleRelation> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(ProductSkuPriceRuleRelation::getProductSkuId, productSkuIdList);
        return baseMapper.delete(queryWrapper) > 0;
    }
}
