package com.zsmall.product.entity.mapper;


import com.hengjian.common.mybatis.core.mapper.BaseMapperPlus;
import com.zsmall.product.entity.domain.TaskSkuPriceChange;
import com.zsmall.product.entity.domain.vo.TaskSkuPriceChangeVo;

/**
* <AUTHOR>
* @description 针对表【task_sku_price_change(定时任务-sku价格变更)】的数据库操作Mapper
* @createDate 2023-06-28 12:31:09
* @Entity generator.domain.TaskSkuPriceChange
*/
public interface TaskSkuPriceChangeMapper extends BaseMapperPlus<TaskSkuPriceChange, TaskSkuPriceChangeVo> {

}




