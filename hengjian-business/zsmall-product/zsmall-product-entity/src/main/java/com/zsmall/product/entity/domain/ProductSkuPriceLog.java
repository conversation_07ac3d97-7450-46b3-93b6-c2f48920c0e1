package com.zsmall.product.entity.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.zsmall.common.domain.SortEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;



/**
 * 商品sku价格变动日志对象 product_sku_price_log
 *
 * <AUTHOR> Li
 * @date 2023-05-22
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("product_sku_price_log")
public class ProductSkuPriceLog extends SortEntity {


    private static final long serialVersionUID = 1L;

    /**
     *
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    private Long siteId;
    /**
     * 日志类型:1-价格编辑 2-价格删除
     */
    private String logType;

    /**
     * 商品SKU表主键
     */
    private Long productSkuPriceId;

    /**
     * 商品SKU表主键
     */
    private Long productSkuId;

    /**
     * 原始产品单价（供货商）
     */
    private BigDecimal originalUnitPrice;

    /**
     * 原始操作费（供货商）
     */
    private BigDecimal originalOperationFee;

    /**
     * 原始尾程派送费（供货商）
     */
    private BigDecimal originalFinalDeliveryFee;

    /**
     * 原始自提价（供货商，产品单价+操作费）
     */
    private BigDecimal originalPickUpPrice;

    /**
     * 原始代发价（供货商，产品单价+操作费+尾程派送费）
     */
    private BigDecimal originalDropShippingPrice;

    /**
     * 平台产品单价（平台+分销商）
     */
    private BigDecimal platformUnitPrice;

    /**
     * 平台操作费（平台+分销商）
     */
    private BigDecimal platformOperationFee;

    /**
     * 平台尾程派送费（平台+分销商）
     */
    private BigDecimal platformFinalDeliveryFee;

    /**
     * 平台自提价（平台+分销商，产品单价+操作费）
     */
    private BigDecimal platformPickUpPrice;

    /**
     * 平台代发价（平台+分销商，产品单价+操作费+尾程派送费）
     */
    private BigDecimal platformDropShippingPrice;

    /**
     * 建议零售价
     */
    private BigDecimal msrp;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;


}
