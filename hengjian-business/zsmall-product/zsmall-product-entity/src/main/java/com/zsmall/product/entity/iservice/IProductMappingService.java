package com.zsmall.product.entity.iservice;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hengjian.common.core.enums.TenantType;
import com.hengjian.common.log.annotation.InMethodLog;
import com.hengjian.common.mybatis.core.page.PageQuery;
import com.hengjian.common.mybatis.core.page.TableDataInfo;
import com.hengjian.common.satoken.utils.LoginHelper;
import com.hengjian.common.tenant.helper.TenantHelper;
import com.zsmall.common.enums.common.ChannelTypeEnum;
import com.zsmall.common.enums.productMapping.MarkUpTypeEnum;
import com.zsmall.common.enums.productMapping.SyncStateEnum;
import com.zsmall.product.entity.domain.ProductMapping;
import com.zsmall.product.entity.domain.bo.productMapping.ProductMappingBo;
import com.zsmall.product.entity.domain.bo.productMapping.ProductMappingTableQueryBo;
import com.zsmall.product.entity.domain.vo.productMapping.ProductMappingTableVo;
import com.zsmall.product.entity.domain.vo.productMapping.ProductMappingVo;
import com.zsmall.product.entity.mapper.ProductMappingMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 商品映射Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-06-20
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class IProductMappingService extends ServiceImpl<ProductMappingMapper, ProductMapping> {


    /**
     * 批量保存商品映射数据（员工或供应商操作）
     * @param mappings
     * @return
     */
    public Boolean batchSaveOrUpdateNoTenant(List<ProductMapping> mappings) {
        return TenantHelper.ignore(() -> this.saveOrUpdateBatch(mappings));
    }

    public IPage<ProductMappingTableVo> queryProductMappingPage(ProductMappingTableQueryBo bo, PageQuery pageQuery) {
        if (ObjectUtil.isEmpty(bo.getTenantId())){
            return baseMapper.queryProductMappingPage(pageQuery.build(), bo, LoginHelper.getTenantId());
        }else {
            return   baseMapper.queryProductMappingPage(pageQuery.build(), bo, bo.getTenantId());
        }
    }

    /**
     * 查询商品映射
     */
    public ProductMappingVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询商品映射列表
     */
    public TableDataInfo<ProductMappingVo> queryPageList(ProductMappingBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<ProductMapping> lqw = buildQueryWrapper(bo);
        Page<ProductMappingVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 通过ProductSkuCode获取映射商品集合
     * @param productSkuCode
     * @return
     */
    public List<ProductMapping> queryListByProductSkuCode(String productSkuCode) {
        log.info("进入【通过ProductSkuCode获取映射商品集合】 productSkuCode = {}", productSkuCode);
        LambdaQueryWrapper<ProductMapping> lqw = Wrappers.lambdaQuery();
        lqw.eq(ProductMapping::getProductSkuCode, productSkuCode);
        if (LoginHelper.getTenantTypeEnum() != null && LoginHelper.getTenantTypeEnum().equals(TenantType.Distributor)) {
            return baseMapper.selectList(lqw);
        }
        return TenantHelper.ignore(() -> baseMapper.selectList(lqw));
    }

    /**
     * 查询同个店铺是否存在相同的Mapping SKU
     * @return
     */
    public Boolean existsSameMappingSku(Long selfId, Long channelId, String mappingSku) {
        log.info("进入【查询同个店铺是否存在相同的MappingSKU】 channelId = {}, mappingSku = {}", channelId, mappingSku);
        LambdaQueryWrapper<ProductMapping> lqw = Wrappers.lambdaQuery();
        lqw.eq(ProductMapping::getChannelId, channelId).eq(ProductMapping::getMappingSku, mappingSku)
            .ne(selfId != null, ProductMapping::getId, selfId);
        return baseMapper.exists(lqw);
    }


    /**
     * 计算商品映射MarkUp后最终价格
     */
    public BigDecimal calculateFinalPrice(MarkUpTypeEnum markUpType, BigDecimal markUpValue, BigDecimal basePrice) {
        // 提价类型默认都是金额，不使用百分比
        if(ObjectUtil.isEmpty(markUpType)){
            markUpType = MarkUpTypeEnum.Amount;
        }
        log.info("计算商品映射MarkUp后最终价格 基础价 = {}，涨价类型 = {}，涨价值 = {}", basePrice, markUpType.name(), markUpValue);
        BigDecimal finalPrice = BigDecimal.ZERO;
        if (basePrice != null && markUpValue != null) {
            if (MarkUpTypeEnum.Percent.equals(markUpType)) {
                finalPrice = NumberUtil.add(basePrice, NumberUtil.mul(basePrice, NumberUtil.div(markUpValue, 100)));
            } else if (MarkUpTypeEnum.Amount.equals(markUpType)) {
                finalPrice = NumberUtil.add(basePrice, markUpValue);
            }
        }
        log.info("计算商品映射MarkUp后最终价格 最终价 = {}", finalPrice);
        return finalPrice.setScale(2);
    }

    private LambdaQueryWrapper<ProductMapping> buildQueryWrapper(ProductMappingBo bo) {
        LambdaQueryWrapper<ProductMapping> lqw = Wrappers.lambdaQuery();
        return lqw;
    }

    @InMethodLog("根据渠道类型查询指定状态的商品映射信息")
    public List<ProductMapping> queryListByChannelAndState(ChannelTypeEnum channelType, SyncStateEnum syncState) {
        return baseMapper.queryListByChannelAndItemNoAndState(channelType.name(), null, syncState.name());
    }

    @InMethodLog("根据ItemNo、渠道类型、指定状态查询的商品映射信息")
    public List<ProductMapping> queryListByChannelAndItemNoAndState(ChannelTypeEnum channelType, String productSkuCode, SyncStateEnum syncState) {
        return baseMapper.queryListByChannelAndItemNoAndState(channelType.name(), productSkuCode, syncState.name());
    }

    /**
     * 根据id和同步状态查询
     * @param ids
     * @param syncState
     * @return
     */
    public List<ProductMapping> queryByIdsAndSyncState(List<Long> ids, SyncStateEnum... syncState) {
        log.info("进入【根据id和同步状态查询】 ids = {}, syncState = {}", ids, syncState);
        LambdaQueryWrapper<ProductMapping> lqw = Wrappers.lambdaQuery();
        lqw.in(ProductMapping::getId, ids);
        lqw.in(ProductMapping::getSyncState, syncState);
        return baseMapper.selectList(lqw);
    }

    /**
     * 根据id集合批量删除
     * @param ids
     * @param syncState
     * @return
     */
    public Boolean batchUpdateSyncStateByIds(List<Long> ids, SyncStateEnum syncState) {
        log.info("进入【根据id集合批量删除】 ids = {}, syncState = {}", ids, syncState);
        LambdaUpdateWrapper<ProductMapping> luw = new LambdaUpdateWrapper<>();
        luw.set(ProductMapping::getSyncState, syncState);
        luw.in(ProductMapping::getId, ids);
        luw.set(ProductMapping::getDelFlag, 2);
        luw.set(ProductMapping::getUpdateTime,new Date());
        return baseMapper.update(null, luw) > 0;
    }

    @InMethodLog("根据租户、渠道和映射SKU查询映射信息")
    public ProductMapping queryByTenantAndMappingSku(String tenantId, Long channelId, String mappingSku) {
        LambdaQueryWrapper<ProductMapping> lqw = Wrappers.lambdaQuery();
        lqw.eq(ProductMapping::getTenantId, tenantId);
        lqw.eq(ProductMapping::getChannelId, channelId);
        lqw.eq(ProductMapping::getMappingSku, mappingSku);
        lqw.in(ProductMapping::getSyncState, SyncStateEnum.Mapped, SyncStateEnum.Synced, SyncStateEnum.Updating);
        return baseMapper.selectOne(lqw);
    }

    @InMethodLog("统计指定渠道有效的商品数量")
    public Long countByChannelIdNotDelete(Long channelId) {
        LambdaQueryWrapper<ProductMapping> lqw = Wrappers.lambdaQuery();
        lqw.eq(ProductMapping::getChannelId, channelId);
        return baseMapper.selectCount(lqw);
    }

    @InMethodLog("查询指定渠道有效的商品")
    public List<ProductMapping> queryByChannelIdNotDelete(Long channelId) {
        LambdaQueryWrapper<ProductMapping> lqw = Wrappers.lambdaQuery();
        lqw.eq(ProductMapping::getChannelId, channelId);
        return baseMapper.selectList(lqw);
    }

    public IPage<ProductMappingTableVo> queryProductMappingPageForTikTok(ProductMappingTableQueryBo bo, PageQuery pageQuery) {
        if (ObjectUtil.isEmpty(bo.getTenantId())){
          return   baseMapper.queryProductMappingPageForTikTok(pageQuery.build(), bo, LoginHelper.getTenantId());
        }else {
            return   baseMapper.queryProductMappingPageForTikTok(pageQuery.build(), bo, bo.getTenantId());
        }
    }

    /**
     * 功能描述：存在相同通道 SKU
     *
     * @param selfId     自我 ID
     * @param channelId  通道 ID
     * @param channelSku 频道 SKU
     * @return {@link Boolean }
     * <AUTHOR>
     * @date 2024/02/05
     */
    public Boolean existsSameChannelSku(Long selfId, Long channelId, String channelSku) {
        log.info("进入【查询同个店铺是否存在相同的Channel】 channelId = {}, channelSku = {}", channelId, channelSku);
        LambdaQueryWrapper<ProductMapping> lqw = Wrappers.lambdaQuery();
        lqw.eq(ProductMapping::getChannelId, channelId).eq(ProductMapping::getChannelSku, channelSku)
           .ne(selfId != null, ProductMapping::getId, selfId);
        return baseMapper.exists(lqw);
    }

    /**
     * 根据channelSku和映射状态获取对象集合
     *
     * @param channelSku
     * @param syncState
     * @return
     */
    public List<ProductMapping> getListProductMappingByChannelSkuAndSyncState(String tenantId, Long channelId, String channelSku,SyncStateEnum syncState){
        LambdaQueryWrapper<ProductMapping> lqw = Wrappers.lambdaQuery();
        lqw.eq(ProductMapping::getChannelSku,channelSku).eq(ProductMapping::getSyncState,syncState).eq(ProductMapping::getTenantId,tenantId).eq(ProductMapping::getChannelId,channelId).eq(ProductMapping::getDelFlag,0);
        return baseMapper.selectList(lqw);
    }

    /**
     * 根据租户id 渠道id  channelSku 映射状态获取产品映射信息
     *
     * @param tenantId
     * @param channelId
     * @param channelSku
     * @param syncState
     * @return
     */
    public List<ProductMapping> getProductMappingByChannelSkuAndSyncState(String tenantId, Long channelId, String channelSku,SyncStateEnum syncState){
        LambdaQueryWrapper<ProductMapping> lqw = Wrappers.lambdaQuery();
        lqw.eq(ProductMapping::getChannelSku,channelSku).eq(ProductMapping::getSyncState,syncState).eq(ProductMapping::getTenantId,tenantId).eq(ProductMapping::getChannelId,channelId).eq(ProductMapping::getDelFlag,0);
        return baseMapper.selectList(lqw);
    }

    /**
     * 根据租户id 渠道id  channelSku 映射状态 站点 获取产品映射信息
     * @param tenantId
     * @param channelId
     * @param channelSku
     * @param syncState
     * @param country
     * @return
     */
    public ProductMapping getProductMappingByChannelSkuAndSyncStateAndCountry(String tenantId, Long channelId, String channelSku,SyncStateEnum syncState,String country){
        LambdaQueryWrapper<ProductMapping> lqw = Wrappers.lambdaQuery();
        lqw.eq(ProductMapping::getChannelSku,channelSku).eq(ProductMapping::getSyncState,syncState).eq(ProductMapping::getTenantId,tenantId)
           .eq(ProductMapping::getChannelId,channelId).eq(ProductMapping::getDelFlag,0).eq(ProductMapping::getSite,country);
        return baseMapper.selectOne(lqw);
    }

    public ProductMapping getProductMappingByChannelSkuAndSyncStateAndCountryV2(String tenantId, Long channelId, String channelSku,SyncStateEnum syncState,String country){
        LambdaQueryWrapper<ProductMapping> lqw = Wrappers.lambdaQuery();
        lqw.eq(ProductMapping::getChannelSku,channelSku)
           .eq(ProductMapping::getSyncState,syncState)
           .eq(ProductMapping::getTenantId,tenantId)
           .eq(ProductMapping::getChannelId,channelId)
           .eq(ProductMapping::getDelFlag,0)
           .eq(ProductMapping::getSite,country);
        return baseMapper.selectOne(lqw);
    }

    /**
     * 根据ProductSkuCode修改映射状态
     *
     * @param productSkuCodeList 产品SKU码列表
     * @param syncState 同步状态枚举
     */
    @InMethodLog("根据ProductSkuCode修改映射状态")
    public void updateProductMapping(List<String> productSkuCodeList, SyncStateEnum syncState) {
        LambdaQueryWrapper<ProductMapping> lqw = Wrappers.lambdaQuery();
        lqw.in(ProductMapping::getProductSkuCode,productSkuCodeList).eq(ProductMapping::getDelFlag,0);
        List<ProductMapping> productMappingList = TenantHelper.ignore(() -> baseMapper.selectList(lqw));
        if (CollUtil.isNotEmpty(productMappingList)){
            for (ProductMapping productMapping : productMappingList) {
                productMapping.setSyncState(syncState);
                productMapping.setUpdateTime(new Date());
            }
            TenantHelper.ignore(() ->baseMapper.updateBatchById(productMappingList));
        }
    }

    /**
     * 根据ProductSkuCode修改映射状态
     *
     * @param productSkuCodeList 产品SKU码列表
     * @param syncState 同步状态枚举
     */
    @InMethodLog("根据ProductSkuCode修改映射状态,查询时添加异常状态")
    public void updateProductMappingRecover(List<String> productSkuCodeList, SyncStateEnum syncState) {
        LambdaQueryWrapper<ProductMapping> lqw = Wrappers.lambdaQuery();
        lqw.in(ProductMapping::getProductSkuCode,productSkuCodeList).eq(ProductMapping::getSyncState,syncState).eq(ProductMapping::getDelFlag,0);
        List<ProductMapping> productMappingList = TenantHelper.ignore(() -> baseMapper.selectList(lqw));
        if (CollUtil.isNotEmpty(productMappingList)){
            for (ProductMapping productMapping : productMappingList) {
                productMapping.setSyncState(SyncStateEnum.Mapped);
                productMapping.setUpdateTime(new Date());
            }
            TenantHelper.ignore(() -> baseMapper.updateBatchById(productMappingList));
        }
    }
}
