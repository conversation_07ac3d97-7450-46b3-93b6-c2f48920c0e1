package com.zsmall.product.entity.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hengjian.common.mybatis.core.mapper.BaseMapperPlus;
import com.zsmall.product.entity.domain.ProductReviewRecord;
import com.zsmall.product.entity.domain.dto.product.ProductReviewPageDTO;
import com.zsmall.product.entity.domain.vo.ProductReviewRecordVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 商品审核记录Mapper接口
 *
 * <AUTHOR>
 * @date 2023-05-31
 */
public interface ProductReviewRecordMapper extends BaseMapperPlus<ProductReviewRecord, ProductReviewRecordVo> {


    void setAllPendingToAbandoned(@Param("productCode")String productCode,@Param("nowRecordId")Long nowRecordId);

    Page<ProductReviewRecordVo> queryPage(Page<ProductReviewPageDTO> page, @Param("query") ProductReviewPageDTO query);

    List<String> queryProductSkuCodeByRecord(@Param("recordId") Long recordId);

    Long existsByProductSkuCode(@Param("productSkuCode") String productSkuCode, @Param("reviewType") String reviewType, @Param("verifyState") String verifyState);

    /**
     * 批量查询SKU的审核记录存在性
     * @param productSkuCodes SKU编码列表
     * @param reviewType 审核类型
     * @param verifyState 审核状态
     * @return Map<String, Boolean> SKU编码 -> 是否存在审核记录
     */
    List<String> existsByProductSkuCodes(@Param("productSkuCodes") List<String> productSkuCodes, @Param("reviewType") String reviewType, @Param("verifyState") String verifyState);

    ProductReviewRecordVo queryNewestRecordByProductSkuCode(@Param("productSkuCode") String productSkuCode, @Param("reviewType") String reviewType, @Param("verifyState") String verifyState);


    List<ProductReviewRecord> queryRecordByProductCodesAndReviewType(@Param("productCodes")List<String> productCodes,@Param("reviewType") String reviewType);

    Long getIdByProductSkuCode(@Param("productSkuCode") String productSkuCode, @Param("reviewType") String reviewType, @Param("verifyState") String verifyState);
}
