package com.zsmall.product.entity.iservice;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hengjian.common.core.utils.MapstructUtils;
import com.hengjian.common.core.utils.StringUtils;
import com.hengjian.common.log.annotation.InMethodLog;
import com.hengjian.common.mybatis.core.page.PageQuery;
import com.hengjian.common.mybatis.core.page.TableDataInfo;
import com.zsmall.product.entity.domain.UserShippingCart;
import com.zsmall.product.entity.domain.bo.UserShippingCartBo;
import com.zsmall.product.entity.domain.vo.UserShippingCartVo;
import com.zsmall.product.entity.mapper.UserShippingCartMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 用户购物车Service业务层处理
 *
 * <AUTHOR> Li
 * @date 2023-05-26
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class IUserShippingCartService extends ServiceImpl<UserShippingCartMapper, UserShippingCart> {


    /**
     * 查询用户购物车列表
     */
    public TableDataInfo<UserShippingCartVo> queryPageList(UserShippingCartBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<UserShippingCart> lqw = buildQueryWrapper(bo);
        Page<UserShippingCartVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询用户购物车列表
     */
    public List<UserShippingCartVo> queryList(UserShippingCartBo bo) {
        LambdaQueryWrapper<UserShippingCart> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<UserShippingCart> buildQueryWrapper(UserShippingCartBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<UserShippingCart> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getUserId() != null, UserShippingCart::getUserId, bo.getUserId());
        lqw.eq(StringUtils.isNotBlank(bo.getProductSkuCode()), UserShippingCart::getProductSkuCode, bo.getProductSkuCode());
        lqw.eq(bo.getQuantity() != null, UserShippingCart::getQuantity, bo.getQuantity());
        lqw.eq(StringUtils.isNotBlank(bo.getBeforeImageShowUrl()), UserShippingCart::getBeforeImageShowUrl, bo.getBeforeImageShowUrl());
        lqw.eq(bo.getBeforeStockQuantity() != null, UserShippingCart::getBeforeStockQuantity, bo.getBeforeStockQuantity());
        lqw.eq(bo.getBeforePickUpPrice() != null, UserShippingCart::getBeforePickUpPrice, bo.getBeforePickUpPrice());
        lqw.eq(bo.getBeforeDropShippingPrice() != null, UserShippingCart::getBeforeDropShippingPrice, bo.getBeforeDropShippingPrice());
        lqw.eq(bo.getBeforeMsrp() != null, UserShippingCart::getBeforeMsrp, bo.getBeforeMsrp());
        return lqw;
    }

    /**
     * 新增用户购物车
     */
    public Boolean insertByBo(UserShippingCartBo bo) {
        UserShippingCart add = MapstructUtils.convert(bo, UserShippingCart.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改用户购物车
     */
    public Boolean updateByBo(UserShippingCartBo bo) {
        UserShippingCart update = MapstructUtils.convert(bo, UserShippingCart.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(UserShippingCart entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除用户购物车
     */
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    @InMethodLog("根据Sku编号获取购物车商品")
    public UserShippingCartVo queryByProductSkuCode(String productSkuCode) {
        LambdaQueryWrapper<UserShippingCart> lqw = Wrappers.lambdaQuery();
        lqw.eq(UserShippingCart::getProductSkuCode, productSkuCode);
        return baseMapper.selectVoOne(lqw);
    }

    @InMethodLog("根据Sku编号删除购物车商品")
    public Boolean deleteByProductSkuCode(String productSkuCode,String supportedLogisticsEnum) {
        LambdaQueryWrapper<UserShippingCart> lqw = Wrappers.lambdaQuery();
        lqw.eq(UserShippingCart::getProductSkuCode, productSkuCode);
        lqw.eq(UserShippingCart::getSupportedLogistics, supportedLogisticsEnum);
        return baseMapper.delete(lqw) > 0;
    }

    @InMethodLog("根据Sku编号数组删除购物车商品")
    public Boolean deleteByProductSkuCode(List<String> productSkuCodeList) {
        LambdaQueryWrapper<UserShippingCart> lqw = Wrappers.lambdaQuery();
        lqw.in(UserShippingCart::getProductSkuCode, productSkuCodeList);
        return baseMapper.delete(lqw) > 0;
    }

    @InMethodLog("查询所有有效的购物车记录")
    public Long queryAllValid() {
        LambdaQueryWrapper<UserShippingCart> lqw = Wrappers.lambdaQuery();
        return baseMapper.selectCount(lqw);
    }


    @InMethodLog("查询所有有效的购物车记录")
    public List<UserShippingCart> getListByIds(List<Long> shippingCartIds) {
        LambdaQueryWrapper<UserShippingCart> lqw = Wrappers.lambdaQuery();
        lqw.in(UserShippingCart::getId, shippingCartIds);
        return baseMapper.selectList(lqw);
    }
}
