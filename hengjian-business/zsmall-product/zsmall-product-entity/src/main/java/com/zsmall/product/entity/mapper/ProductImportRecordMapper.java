package com.zsmall.product.entity.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hengjian.common.mybatis.core.mapper.BaseMapperPlus;
import com.zsmall.product.entity.domain.ProductImportRecord;
import com.zsmall.product.entity.domain.vo.productImport.ProductImportRecordVo;
import org.apache.ibatis.annotations.Param;

/**
 * 商品导入记录Mapper接口
 *
 * <AUTHOR>
 * @date 2023-06-08
 */
public interface ProductImportRecordMapper extends BaseMapperPlus<ProductImportRecord, ProductImportRecordVo> {

    /** 查询导入记录编号是否已存在 */
    @InterceptorIgnore(tenantLine = "true")
    boolean existImportRecordNo(@Param("importRecordNo") String importRecordNo);

    /**
     * 分页查询
     * @param page
     * @param queryValue
     * @return
     */
    Page<ProductImportRecordVo> queryPage(Page<ProductImportRecordVo> page);

}
