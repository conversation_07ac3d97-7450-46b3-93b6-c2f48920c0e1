package com.zsmall.product.entity.domain.dto.product;

import com.zsmall.common.enums.common.AttachmentTypeEnum;
import com.zsmall.common.enums.product.LengthUnitEnum;
import com.zsmall.common.enums.product.ProductVerifyStateEnum;
import com.zsmall.common.enums.product.ShelfStateEnum;
import com.zsmall.common.enums.product.WeightUnitEnum;
import java.math.BigDecimal;
import java.util.List;
import lombok.Data;

/**
 * 商品OpenApi对外信息
 */
@Data
public class OpenApiProductSkuDetails {
    /**
     * SkuID
     */
    private String productSkuCode;
    /**
     * 商品名称
     */
    private String productSkuName;

    /**
     * 支持的物流：All-都支持，PickUpOnly-仅支持自提，DropShippingOnly-仅支持代发不能为空
     */
    private String supportedLogistics;
    /**
     * 规格组成名称，示例：尺寸-大;颜色-白色
     */
    private String specComposeName;

    /**
     * 规格值名称，示例：大/白色
     */
    private String specValName;
    /**
     * Sku销售状态：OnShelf-在售；OffShelf-停售；ForcedOffShelf-强制下架
     */
    private ShelfStateEnum shelfState;
    /**
     * Sku审核状态 Draft:草稿 Pending:审核中 Accepted:已通过 Rejected:已驳回 Abandoned:废弃
     */
    private ProductVerifyStateEnum verifyState;


    /**
     * 商品分类信息
     */
    private List<ProductCategoryOpenApi>  productCategoryOpenApiList;
    @Data
    public static class ProductCategoryOpenApi{
        private String id;
        /**
         * 分类层级
         */
        private String categoryLevel;
        /**
         * 分类名称
         */
        private String categoryName;
        /**
         * 分类英文名称
         */
        private String categoryEnglishName;
    }

    /**
     * 商品附件相关信息
     */
    private List<ProductSkuAttachmentOpenApi> productSkuAttachmentOpenApiList;
    @Data
    public static class ProductSkuAttachmentOpenApi{
        private String id;
        /**
         * 附件类型 File:文件 Zip:压缩包 Image:图片 Video:视频
         */
        private AttachmentTypeEnum attachmentType;
        /**
         * 附件名称
         */
        private String attachmentName;
        /**
         * 附件地址
         */
        private String attachmentShowUrl;
        /**
         * 附件排序  0为主图
         */
        private String attachmentSort;
    }

    /**
     * Sku详情
     */
    private ProductSkuDetailOpenApi productSkuDetailOpenApi;

    /**
     * Sku详情
     */
    @Data
    public static class ProductSkuDetailOpenApi{

        /**
         * 长
         */
        private BigDecimal length;

        /**
         * 宽
         */
        private BigDecimal width;

        /**
         * 高
         */
        private BigDecimal height;

        /**
         * 长度单位  mm:毫米,标准单位:1000.0 / cm:厘米,标准单位:100.0 / inch:英寸,标准单位:39.3700787 / dm:分米,标准单位:10.0 / foot:英尺,标准单位:3.2808399 / m:米,标准单位:1.0
         */
        private LengthUnitEnum lengthUnit;

        /**
         * 重量
         */
        private BigDecimal weight;

        /**
         * 重量单位 t:吨,标准单位:0.001 / mg:毫克,标准单位:1000000.0 / g:克,标准单位:1000.0 / lb:磅,标准单位:2.2046226 / kg:千克,标准单位:1.0
         */
        private WeightUnitEnum weightUnit;

        /**
         * 打包长
         */
        private BigDecimal packLength;

        /**
         * 打包宽
         */
        private BigDecimal packWidth;

        /**
         * 打包高
         */
        private BigDecimal packHeight;

        /**
         * 打包长度单位 mm:毫米,标准单位:1000.0 / cm:厘米,标准单位:100.0 / inch:英寸,标准单位:39.3700787 / dm:分米,标准单位:10.0 / foot:英尺,标准单位:3.2808399 / m:米,标准单位:1.0
         */
        private LengthUnitEnum packLengthUnit;

        /**
         * 打包重量
         */
        private BigDecimal packWeight;

        /**
         * 打包重量单位 mg:毫克,标准单位:1000000.0 / g:克,标准单位:1000.0 / lb:磅,标准单位:2.2046226 / kg:千克,标准单位:1.0 / t:吨,标准单位:0.001
         */
        private WeightUnitEnum packWeightUnit;

        /**
         * 打包尺寸重量与商品尺寸重量相同：0-否，1-是
         */
        private Boolean samePacking;

        /**
         * 处理时效（天）
         */
        private BigDecimal processingTime;

        /**
         * 运输方式
         */
        private String transportMethod;

        /**
         * SKU描述
         */
        private String description;
    }



}
