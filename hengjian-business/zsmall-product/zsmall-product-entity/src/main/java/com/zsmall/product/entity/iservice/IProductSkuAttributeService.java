package com.zsmall.product.entity.iservice;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hengjian.common.core.utils.MapstructUtils;
import com.hengjian.common.core.utils.StringUtils;
import com.hengjian.common.mybatis.core.page.PageQuery;
import com.hengjian.common.mybatis.core.page.TableDataInfo;
import com.zsmall.product.entity.domain.ProductSkuAttribute;
import com.zsmall.product.entity.domain.bo.ProductSkuAttributeBo;
import com.zsmall.product.entity.domain.vo.ProductSkuAttributeVo;
import com.zsmall.product.entity.mapper.ProductSkuAttributeMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 商品SKU属性Service业务层处理
 *
 * <AUTHOR> Li
 * @date 2023-05-26
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class IProductSkuAttributeService extends ServiceImpl<ProductSkuAttributeMapper, ProductSkuAttribute> {

    private final ProductSkuAttributeMapper baseMapper;

    /**
     * 查询商品SKU属性
     */
    public ProductSkuAttributeVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询商品SKU属性列表
     */
    public TableDataInfo<ProductSkuAttributeVo> queryPageList(ProductSkuAttributeBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<ProductSkuAttribute> lqw = buildQueryWrapper(bo);
        Page<ProductSkuAttributeVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询商品SKU属性列表
     */
    public List<ProductSkuAttributeVo> queryList(ProductSkuAttributeBo bo) {
        LambdaQueryWrapper<ProductSkuAttribute> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<ProductSkuAttribute> buildQueryWrapper(ProductSkuAttributeBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<ProductSkuAttribute> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getProductSkuId() != null, ProductSkuAttribute::getProductSkuId, bo.getProductSkuId());
        lqw.eq(bo.getProductAttributeId() != null, ProductSkuAttribute::getProductAttributeId, bo.getProductAttributeId());
        lqw.eq(StringUtils.isNotBlank(bo.getAttributeType()), ProductSkuAttribute::getAttributeType, bo.getAttributeType());
        lqw.like(StringUtils.isNotBlank(bo.getAttributeName()), ProductSkuAttribute::getAttributeName, bo.getAttributeName());
        lqw.eq(StringUtils.isNotBlank(bo.getAttributeValue()), ProductSkuAttribute::getAttributeValue, bo.getAttributeValue());
        lqw.eq(bo.getAttributeSort() != null, ProductSkuAttribute::getAttributeSort, bo.getAttributeSort());
        lqw.eq(bo.getAttributeSourceId() != null, ProductSkuAttribute::getAttributeSourceId, bo.getAttributeSourceId());
        return lqw;
    }

    /**
     * 新增商品SKU属性
     */
    public Boolean insertByBo(ProductSkuAttributeBo bo) {
        ProductSkuAttribute add = MapstructUtils.convert(bo, ProductSkuAttribute.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 批量新增商品SKU属性
     *
     * @param entityList
     * @return
     */
    public Boolean insertBatch(Collection<ProductSkuAttribute> entityList) {
        log.info("进入【批量新增商品SKU属性】 entityList = {}", JSONUtil.toJsonStr(entityList));
        return baseMapper.insertBatch(entityList);
    }

    /**
     * 修改商品SKU属性
     */
    public Boolean updateByBo(ProductSkuAttributeBo bo) {
        ProductSkuAttribute update = MapstructUtils.convert(bo, ProductSkuAttribute.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 根据实体类集合删除
     *
     * @param entityList
     * @return
     */
    public Boolean deleteByEntityList(Collection<ProductSkuAttribute> entityList) {
        log.info("进入【根据实体类集合删除】 entityList = {}", JSONUtil.toJsonStr(entityList));
        return baseMapper.deleteBatchIds(entityList) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(ProductSkuAttribute entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除商品SKU属性
     */
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    /**
     * 根据SKU主键查询
     *
     * @param productSkuId
     * @return
     */
    public List<ProductSkuAttribute> queryByProductSkuId(Long productSkuId) {
        log.info("进入【根据SKU主键查询】 productSkuId = {}", productSkuId);
        LambdaQueryWrapper<ProductSkuAttribute> lqw = Wrappers.lambdaQuery();
        lqw.eq(ProductSkuAttribute::getProductSkuId, productSkuId);
        return baseMapper.selectList(lqw);
    }

    /**
     * 根据商品SKU主键删除
     * @param productSkuIdList
     * @return
     */
    public Boolean deleteByProductSkuIdList(List<Long> productSkuIdList) {
        log.info("进入【根据商品SKU主键删除】 productSkuIdList = {}", JSONUtil.toJsonStr(productSkuIdList));
        LambdaQueryWrapper<ProductSkuAttribute> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(ProductSkuAttribute::getProductSkuId, productSkuIdList);
        return baseMapper.delete(queryWrapper) > 0;
    }

    /**
     * 批量查询商品SKU属性
     * @param productSkuIds SKU主键集合
     * @return SKU属性列表
     */
    public List<ProductSkuAttribute> queryByProductSkuIds(List<Long> productSkuIds) {
        log.info("进入【批量查询商品SKU属性】 productSkuIds = {}", JSONUtil.toJsonStr(productSkuIds));
        if (CollUtil.isEmpty(productSkuIds)) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<ProductSkuAttribute> lqw = Wrappers.lambdaQuery();
        lqw.in(ProductSkuAttribute::getProductSkuId, productSkuIds);
        // 注意：与原始 queryByProductSkuId() 方法保持一致，不使用特殊的租户处理
        return baseMapper.selectList(lqw);
    }
}
