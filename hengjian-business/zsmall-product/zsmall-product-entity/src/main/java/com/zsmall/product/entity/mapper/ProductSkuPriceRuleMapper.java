package com.zsmall.product.entity.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.hengjian.common.mybatis.core.mapper.BaseMapperPlus;
import com.zsmall.product.entity.domain.ProductSkuPriceRule;
import com.zsmall.product.entity.domain.vo.productSkuPrice.ProductSkuPriceRuleVo;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

/**
* <AUTHOR>
* @description 针对表【product_sku_price_rule(商品sku价格计算公式表)】的数据库操作Mapper
* @createDate 2023-05-22 16:56:33
* @Entity generator.domain.ProductSkuPriceRule
*/
public interface ProductSkuPriceRuleMapper extends BaseMapperPlus<ProductSkuPriceRule, ProductSkuPriceRuleVo> {

    /**
     * 查询编号是否已经存在
     * @param ruleCode
     * @return
     */
    @InterceptorIgnore(tenantLine = "true")
    boolean existRuleCode(@Param("ruleCode") String ruleCode);

    /**
     * 根据价格区间查询符合的规则
     * @param value
     * @return
     */
    List<ProductSkuPriceRule> queryByPriceRange(@Param("value") BigDecimal value);

    /**
     * 根据商品sku的id查询公式编码
     * @param productSkuId
     * @return
     */
    String getRuleCodeByProductSkuId(@Param("productSkuId") Long productSkuId);

    List<ProductSkuPriceRuleVo> getListByType(@Param("applicableType") Integer type, @Param("ruleCode") String ruleCode);

    Integer updateDeleteMarkByRuleCode(@Param("ruleCode") String ruleCode);
}




