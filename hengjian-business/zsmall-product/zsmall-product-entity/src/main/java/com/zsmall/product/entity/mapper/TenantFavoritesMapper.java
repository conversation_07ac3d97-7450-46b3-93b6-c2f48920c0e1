package com.zsmall.product.entity.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hengjian.common.mybatis.core.page.PageQuery;
import com.zsmall.product.entity.domain.TenantFavorites;
import com.zsmall.product.entity.domain.bo.tenantFavorites.TenantFavoritesListBo;
import com.zsmall.product.entity.domain.vo.tenantFavorites.TenantFavoritesListVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 租户收藏夹-Mapper层
 * <AUTHOR>
 * @date 2023/8/16
 */
public interface TenantFavoritesMapper extends BaseMapper<TenantFavorites> {

    @InterceptorIgnore(tenantLine = "true")
    Page<TenantFavoritesListVo> queryPage(@Param("tenantId") String tenantId, @Param("bo") TenantFavoritesListBo bo, Page<TenantFavorites> page);

    @InterceptorIgnore(tenantLine = "true")
    Boolean favoriteOrNot(@Param("tenantId") String tenantId, @Param("productCode") String productCode);

    List<String> queryAllProductCode();

    List<Long> queryProductIdsByFavoritesIds(@Param("favoritesIds") List<String> favoritesIds);

    Page<TenantFavorites> selectTenantFavoritesListVoPage(@Param("pageQuery")Page pageQuery,@Param("bo") TenantFavoritesListBo bo);

    void deleteBySpu(@Param("productCode") String productCode, @Param("tenantId") String tenantId);

    void deleteBySku(@Param("productSkuCode")  String productSkuCode, @Param("tenantId")String tenantId);

    Boolean getIsFavoritesIds(@Param("productSkuCode")  String productSkuCode, @Param("tenantId")String tenantId);
}
