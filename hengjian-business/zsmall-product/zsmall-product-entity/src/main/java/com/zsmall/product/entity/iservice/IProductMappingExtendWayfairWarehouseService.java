package com.zsmall.product.entity.iservice;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import com.hengjian.common.log.annotation.InMethodLog;
import org.springframework.stereotype.Service;
import com.zsmall.product.entity.domain.ProductMappingExtendWayfairWarehouse;
import com.zsmall.product.entity.mapper.ProductMappingExtendWayfairWarehouseMapper;

import java.util.List;

/**
 * 商品映射扩展-Wayfair仓库Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-06-21
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class IProductMappingExtendWayfairWarehouseService extends ServiceImpl<ProductMappingExtendWayfairWarehouseMapper, ProductMappingExtendWayfairWarehouse> {

    public ProductMappingExtendWayfairWarehouse queryByWarehouseAndChannel(Long warehouseId, Long channelId) {
        LambdaQueryWrapper<ProductMappingExtendWayfairWarehouse> lqw = Wrappers.lambdaQuery();
        lqw.eq(ProductMappingExtendWayfairWarehouse::getWarehouseId, warehouseId);
        lqw.eq(ProductMappingExtendWayfairWarehouse::getChannelId, channelId);
        return baseMapper.selectOne(lqw);
    }

    public ProductMappingExtendWayfairWarehouse queryByWarehouseAndChannel(String warehouseSystemCode, Long channelId) {
        LambdaQueryWrapper<ProductMappingExtendWayfairWarehouse> lqw = Wrappers.lambdaQuery();
        lqw.eq(ProductMappingExtendWayfairWarehouse::getWarehouseSystemCode, warehouseSystemCode);
        lqw.eq(ProductMappingExtendWayfairWarehouse::getChannelId, channelId);
        return baseMapper.selectOne(lqw);
    }

    @InMethodLog("根据第三方渠道仓库ID查询库存充足的映射仓库")
    public List<ProductMappingExtendWayfairWarehouse> queryByThirdWarehouseId(String thirdWarehouseId, String productSkuCode, Integer quantity, Long channelId) {
        return baseMapper.queryByThirdWarehouseId(thirdWarehouseId, productSkuCode, quantity, channelId);
    }
}
