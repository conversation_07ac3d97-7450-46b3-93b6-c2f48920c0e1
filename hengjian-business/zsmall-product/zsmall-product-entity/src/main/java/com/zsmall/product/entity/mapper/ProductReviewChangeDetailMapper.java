package com.zsmall.product.entity.mapper;

import com.hengjian.common.mybatis.core.mapper.BaseMapperPlus;
import com.zsmall.product.entity.domain.ProductReviewChangeDetail;
import com.zsmall.product.entity.domain.vo.ProductReviewChangeDetailVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 商品审核变更详情Mapper接口
 *
 * <AUTHOR>
 * @date 2023-05-31
 */
public interface ProductReviewChangeDetailMapper extends BaseMapperPlus<ProductReviewChangeDetail, ProductReviewChangeDetailVo> {


    /**
     * 查询还未经过定时器更新的价格变更
     *
     * @param productSkuCode
     * @return
     */
    List<ProductReviewChangeDetail> queryByTaskTodo(@Param("productSkuCode") String productSkuCode);


}
