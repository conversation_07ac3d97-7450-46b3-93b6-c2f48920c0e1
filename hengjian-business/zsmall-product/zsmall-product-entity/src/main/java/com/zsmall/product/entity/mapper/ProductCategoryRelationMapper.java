package com.zsmall.product.entity.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.hengjian.common.mybatis.core.mapper.BaseMapperPlus;
import com.zsmall.product.entity.domain.ProductCategoryRelation;
import com.zsmall.product.entity.domain.vo.category.ProductCategoryRelationVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 商品SPU-商品分类关联Mapper接口
 *
 * <AUTHOR> Li
 * @date 2023-05-22
 */
public interface ProductCategoryRelationMapper extends BaseMapperPlus<ProductCategoryRelation, ProductCategoryRelationVo> {

    @InterceptorIgnore(tenantLine = "true")
    List<String> existProductCodeRelationByCategoryIds(@Param("categoryIds") List<Long> categoryIds);

}
