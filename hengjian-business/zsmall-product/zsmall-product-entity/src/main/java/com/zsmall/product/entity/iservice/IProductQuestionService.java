package com.zsmall.product.entity.iservice;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hengjian.common.core.enums.TenantType;
import com.hengjian.common.core.exception.RStatusCodeException;
import com.hengjian.common.core.utils.MapstructUtils;
import com.hengjian.common.core.utils.StringUtils;
import com.hengjian.common.log.annotation.InMethodLog;
import com.hengjian.common.mybatis.core.domain.NoDeptBaseEntity;
import com.hengjian.common.mybatis.core.page.PageQuery;
import com.hengjian.common.mybatis.core.page.TableDataInfo;
import com.hengjian.common.satoken.utils.LoginHelper;
import com.hengjian.common.tenant.helper.TenantHelper;
import com.zsmall.common.constant.MallConstants;
import com.zsmall.common.enums.BusinessCodeEnum;
import com.zsmall.common.enums.productQuestion.QuestionStatusEnum;
import com.zsmall.common.enums.productQuestion.ReplyTypeEnum;
import com.zsmall.common.enums.statuscode.ZSMallStatusCodeEnum;
import com.zsmall.product.entity.domain.ProductQuestion;
import com.zsmall.product.entity.domain.ProductQuestionAnswer;
import com.zsmall.product.entity.domain.ProductQuestionAnswerLog;
import com.zsmall.product.entity.domain.ProductSku;
import com.zsmall.product.entity.domain.bo.prodcutQuestion.*;
import com.zsmall.product.entity.domain.vo.prodcutQuestion.ProductAnswerLogVo;
import com.zsmall.product.entity.domain.vo.prodcutQuestion.ProductQuestionAnswerVo;
import com.zsmall.product.entity.domain.vo.prodcutQuestion.ProductQuestionVo;
import com.zsmall.product.entity.mapper.ProductQuestionAnswerLogMapper;
import com.zsmall.product.entity.mapper.ProductQuestionAnswerMapper;
import com.zsmall.product.entity.mapper.ProductQuestionMapper;
import com.zsmall.product.entity.util.ProductCodeGenerator;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class IProductQuestionService extends ServiceImpl<ProductQuestionMapper, ProductQuestion> {

    private final IProductSkuService iProductSkuService;
    private final ProductCodeGenerator productCodeGenerator;
    private final ProductQuestionAnswerMapper productQuestionAnswerMapper;
    private final ProductQuestionAnswerLogMapper productQuestionAnswerLogMapper;

    /**
     * 查询商品问答
     */
    public ProductQuestionVo queryByCode(String code) {
        ProductQuestionVo productQuestionVo = null;
        // 暂定不同租户都可以查询其他人的提问
        ProductQuestion productQuestion = TenantHelper.ignore(() -> baseMapper.selectOne(new LambdaQueryWrapper<ProductQuestion>()
            .eq(ProductQuestion::getQuestionCode, code)));

        if (productQuestion == null) {
            throw new RStatusCodeException(ZSMallStatusCodeEnum.NOT_EXIST_QUESTIONS_ERROR);
        }

        Long questionId = productQuestion.getId();
        productQuestionVo = MapstructUtils.convert(productQuestion, ProductQuestionVo.class);

        List<ProductQuestionAnswerVo> questionAnswers = TenantHelper.ignore(() -> productQuestionAnswerMapper
            .selectVoList(new LambdaQueryWrapper<ProductQuestionAnswer>()
                .eq(ProductQuestionAnswer::getQuestionId, questionId)
                .orderByAsc(List.of(ProductQuestionAnswer::getSort, NoDeptBaseEntity::getCreateTime))
            ));

        if (CollUtil.isNotEmpty(questionAnswers)) {
            questionAnswers.forEach(answer -> {
                String answerCode = answer.getAnswerCode();

                Long count = productQuestionAnswerLogMapper.selectCount(new LambdaQueryWrapper<ProductQuestionAnswerLog>()
                    .eq(ProductQuestionAnswerLog::getAnswerCode, answerCode));
                answer.setLogCounts(count);
            });
        }

        productQuestionVo.setQuestionAnswerVos(questionAnswers);

        return productQuestionVo;
    }


    /**
     * 查询商品问答列表
     */
    public TableDataInfo<ProductQuestionVo> queryPageList(ProductQuestionBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<ProductQuestion> lqw = buildQueryWrapper(bo);
        Page<ProductQuestionVo> result = TenantHelper.ignore(() -> baseMapper.selectVoPage(pageQuery.build(), lqw),
            TenantType.Manager, TenantType.Supplier);

        List<ProductQuestionVo> records = result.getRecords();
        if (CollUtil.isNotEmpty(records)) {
            records.forEach(record -> {
                String productSkuCode = record.getProductSkuCode();
                ProductSku productSku = iProductSkuService.queryByProductSkuCode(productSkuCode);
                record.setProductSkuName(productSku.getName());

                // 增加判断是否是管理员，如果是管理员，则获取当前问答是否有举报数据
                if (Objects.equals(TenantType.Manager, LoginHelper.getTenantTypeEnum())) {
                    LambdaQueryWrapper<ProductQuestionAnswer> qaLqw = new LambdaQueryWrapper<ProductQuestionAnswer>()
                        .eq(ProductQuestionAnswer::getQuestionStatus, QuestionStatusEnum.Reported)
                        .eq(ProductQuestionAnswer::getType, ReplyTypeEnum.Question)
                        .exists("select 1 from product_question pq where pq.id = product_question_answer.question_id " +
                            "and pq.question_code = {0}", record.getQuestionCode());

                    long reportedCounts = TenantHelper.ignore(() -> productQuestionAnswerMapper.selectCount(qaLqw));
                    record.setReportedCounts(reportedCounts);
                }
            });
        }

        return TableDataInfo.build(result);
    }

    /**
     * 查询商品问答列表
     */
    public List<ProductQuestionVo> queryList(ProductQuestionBo bo) {
        LambdaQueryWrapper<ProductQuestion> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    /**
     * 查询商品问答列表
     */
    public Page<ProductQuestion> queryPageByProductSkuCode(String productSkuCode, String question, Page<ProductQuestion> page) {
        LambdaQueryWrapper<ProductQuestion> lqw = Wrappers.lambdaQuery();
        lqw.eq(ProductQuestion::getProductSkuCode, productSkuCode);
        lqw.like(StrUtil.isNotBlank(question), ProductQuestion::getQuestion, question);
        lqw.orderByDesc(ProductQuestion::getCreateTime);
        lqw.orderByDesc(ProductQuestion::getId);
        return TenantHelper.ignore(() -> baseMapper.selectPage(page, lqw));
    }

    @InMethodLog("根据问题查询所有回答")
    public List<ProductQuestionAnswer> queryAnswerListByQuestionId(Long questionId) {
        LambdaQueryWrapper<ProductQuestionAnswer> lqw = Wrappers.lambdaQuery();
        lqw.eq(ProductQuestionAnswer::getQuestionId, questionId)
            .orderByAsc(ProductQuestionAnswer::getSort)
            .orderByAsc(ProductQuestionAnswer::getCreateTime)
            .orderByAsc(ProductQuestionAnswer::getId);
        return TenantHelper.ignore(() -> productQuestionAnswerMapper.selectList(lqw));
    }

    private LambdaQueryWrapper<ProductQuestion> buildQueryWrapper(ProductQuestionBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<ProductQuestion> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getQuestionCode()), ProductQuestion::getQuestionCode, bo.getQuestionCode());
        lqw.eq(StringUtils.isNotBlank(bo.getProductCode()), ProductQuestion::getProductCode, bo.getProductCode());
        lqw.eq(StringUtils.isNotBlank(bo.getProductSkuCode()), ProductQuestion::getProductSkuCode, bo.getProductSkuCode());
        lqw.like(StringUtils.isNotBlank(bo.getQuestion()), ProductQuestion::getQuestion, StrUtil.trim(bo.getQuestion()));

        if (StrUtil.equals(bo.getStatus(), QuestionStatusEnum.Reported.name())) {
            lqw.and(q ->
                q.eq(ProductQuestion::getStatus, bo.getStatus())
                    .or()
                    .exists("select 1 from product_question_answer pqa where product_question.id = pqa.question_id " +
                        "and pqa.type = 'question' and pqa.question_status = 'Reported' and pqa.del_flag = '0'"));
        } else {
            lqw.like(StringUtils.isNotBlank(bo.getStatus()), ProductQuestion::getStatus, bo.getStatus());
        }

        List<String> createTimes = bo.getCreateTimes();
        if (CollUtil.isNotEmpty(createTimes)) {
            String start = createTimes.get(0);
            String end = createTimes.get(1);

            lqw.between(NoDeptBaseEntity::getCreateTime, start, end);
        }

        // 如果是供应商，则直接匹配对应的商品
        if (Objects.equals(TenantType.Supplier, LoginHelper.getTenantTypeEnum())) {
            lqw.exists("select 1 from product p where p.tenant_id = {0} and p.product_code = product_question.product_code",
                LoginHelper.getTenantId());
        }

        lqw.orderByDesc(NoDeptBaseEntity::getCreateTime);
        return lqw;
    }

    /**
     * 新增商品问答
     */
    public Boolean insertByBo(ProductQuestionBo bo) {
        ProductQuestion add = MapstructUtils.convert(bo, ProductQuestion.class);
        validEntityBeforeSave(add);
        add.setStatus(QuestionStatusEnum.Pending);
        boolean flag = baseMapper.insert(add) > 0;
//        if (flag) {
//            bo.setId(add.getId());
//        }
        return flag;
    }

    /**
     * 追加提问
     *
     * @param bo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean addAgain(ProductQuestionAgainBo bo) {
        String questionCode = bo.getQuestionCode();
        String question = bo.getQuestion();

        //查询问题是否存在
        LambdaQueryWrapper<ProductQuestion> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProductQuestion::getQuestionCode, questionCode);
        ProductQuestion productQuestion = this.getOne(queryWrapper);
        if (productQuestion == null) {
            throw new RStatusCodeException(ZSMallStatusCodeEnum.NOT_EXIST_QUESTIONS_ERROR);
        }

        QuestionStatusEnum status = productQuestion.getStatus();
        //不允许回复已举报的问题，暂时显示为已关闭
        if (StrUtil.equals(QuestionStatusEnum.Reported.name(), status.name())) {
            throw new RStatusCodeException(ZSMallStatusCodeEnum.CLOSED_QUESTIONS_ERROR);
        }
        //不允许重复提问
        Long questionId = productQuestion.getId();
        ProductQuestionAnswer lastQuestionAnswer = this.getLastAnswer(questionId);
        if (lastQuestionAnswer == null) {
            throw new RStatusCodeException(ZSMallStatusCodeEnum.CONTINUE_QUESTIONS_AGAIN_ERROR);
        }

        String answerCode = productCodeGenerator.codeGenerate(BusinessCodeEnum.ProductQuestionAnswer);
        Integer sort = lastQuestionAnswer.getSort();
        //保存追加提问
        ProductQuestionAnswer entity = new ProductQuestionAnswer();
        entity.setQuestionId(questionId);
        entity.setAnswer(question);
        entity.setType(ReplyTypeEnum.Question);
        entity.setSort(sort + 1);
        entity.setAnswerCode(answerCode);
        productQuestionAnswerMapper.insert(entity);
        //更新问题回复状态为Pending
        productQuestion.setStatus(QuestionStatusEnum.Pending);
        baseMapper.updateById(productQuestion);

        return true;
    }

    /**
     * 获取最后一条回复
     * - 如列表为空或者最新一条如果是问题，则返回空
     *
     * @param questionId
     * @return
     */
    private ProductQuestionAnswer getLastAnswer(Long questionId) {

        List<ProductQuestionAnswer> questionAnswers = TenantHelper.ignore(() -> productQuestionAnswerMapper
            .selectList(new LambdaQueryWrapper<ProductQuestionAnswer>()
                .eq(ProductQuestionAnswer::getQuestionId, questionId)
                .orderByDesc(List.of(ProductQuestionAnswer::getSort, NoDeptBaseEntity::getCreateTime))
            ));

        if (CollUtil.isEmpty(questionAnswers)) {
            return null;
        }

        ProductQuestionAnswer questionAnswer = questionAnswers.get(0);
        // 如果第一条是问题，则也是重复提问
        if (Objects.equals(questionAnswer.getType(), ReplyTypeEnum.Question)) {
            return null;
        }
        return questionAnswer;
    }

    /**
     * 修改商品问答
     */
    public Boolean updateByBo(ProductQuestionBo bo) {
        ProductQuestion update = MapstructUtils.convert(bo, ProductQuestion.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(ProductQuestion entity) {
        String productSkuCode = entity.getProductSkuCode();
        //如果有商品skuCode 查询出对应的商品code
        ProductSku productSku = iProductSkuService.queryByProductSkuCode(productSkuCode);
        if (productSku == null) {
            throw new RStatusCodeException(ZSMallStatusCodeEnum.QUERY_PRODUCT_NOT_FOUND);
        }
        entity.setProductCode(productSku.getProductCode());

        // 判断时候存在问题编码
        String questionCode = entity.getQuestionCode();
        if (StrUtil.isBlank(questionCode)) {
            questionCode = productCodeGenerator.codeGenerate(BusinessCodeEnum.ProductQuestion);
            entity.setQuestionCode(questionCode);
        }
    }

    /**
     * 批量删除商品问答
     */
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    /**
     * 批量删除商品问答
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteWithValidByCodes(List<String> questionCodes) {
        LambdaQueryWrapper<ProductQuestion> queryWrapper = new LambdaQueryWrapper<ProductQuestion>()
            .in(ProductQuestion::getQuestionCode, questionCodes);
        List<ProductQuestion> questions = TenantHelper.ignore(() -> baseMapper.selectList(queryWrapper));
        if (CollUtil.isEmpty(questions)) {
            throw new RStatusCodeException(ZSMallStatusCodeEnum.NOT_EXIST_QUESTIONS_ERROR);
        }

        int deleted = TenantHelper.ignore(() -> baseMapper.delete(queryWrapper));
        log.info("批量删除问题的数量：{}", deleted);
        if (deleted > 0) {
            // 删除所有回复以及追问
            List<Long> questionIds = questions.stream().map(ProductQuestion::getId).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(questionIds)) {
                deleted = TenantHelper.ignore(() -> productQuestionAnswerMapper.delete(new LambdaQueryWrapper<ProductQuestionAnswer>()
                    .in(ProductQuestionAnswer::getQuestionId, questionIds)));
                log.info("批量删除所有问题的回复和追问数量：{}", deleted);
            }
        }

        return true;
    }

    /**
     * 回复
     *
     * @param bo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean addAnswer(ProductAnswerReplyBo bo) {
        String questionCode = bo.getQuestionCode();
        String answer = bo.getAnswer();

        ProductQuestion productQuestion = queryQuestionByCode(questionCode);
        if (productQuestion == null) {
            throw new RStatusCodeException(ZSMallStatusCodeEnum.NOT_EXIST_QUESTIONS_ERROR);
        }

        QuestionStatusEnum questionStatus = productQuestion.getStatus();
        //不允许回复已举报的问题，暂时显示为已关闭
        if (StrUtil.equals(QuestionStatusEnum.Reported.name(), questionStatus.name())) {
            throw new RStatusCodeException(ZSMallStatusCodeEnum.CLOSED_QUESTIONS_ERROR);
        }

        Long questionId = productQuestion.getId();
        Integer maxSort = productQuestionAnswerMapper.getMaxSort(questionId);
        maxSort = maxSort == null ? 1 : maxSort + 1;
//
        String answerCode = productCodeGenerator.codeGenerate(BusinessCodeEnum.ProductQuestionAnswer);

        ProductQuestionAnswer productQuestionAnswer = new ProductQuestionAnswer();
        productQuestionAnswer.setQuestionId(questionId);
        productQuestionAnswer.setAnswer(answer);
        productQuestionAnswer.setType(ReplyTypeEnum.Answer);
        productQuestionAnswer.setSort(maxSort);
        productQuestionAnswer.setAnswerCode(answerCode);
        productQuestionAnswerMapper.insert(productQuestionAnswer);

        productQuestion.setStatus(QuestionStatusEnum.Solved);
        TenantHelper.ignore(() -> baseMapper.updateById(productQuestion));

        return true;
    }

    /**
     * 根据商品代码查询问题
     *
     * @param questionCode
     * @return
     */
    public ProductQuestion queryQuestionByCode(String questionCode) {
        ProductQuestion productQuestion = TenantHelper.ignore(() ->
                baseMapper.selectOne(new LambdaQueryWrapper<ProductQuestion>().eq(ProductQuestion::getQuestionCode, questionCode)),
            TenantType.Manager, TenantType.Supplier);
        return productQuestion;
    }

    /**
     * 编辑回复
     *
     * @param bo
     * @return
     */
    public boolean editAnswer(ProductAnswerEditBo bo) {
        String answerCode = bo.getAnswerCode();
        String answer = bo.getAnswer();

        ReplyTypeEnum replyType = ReplyTypeEnum.Answer;

        ProductQuestionAnswer questionAnswer = getQuestionAnswer(answerCode, replyType);
        if (questionAnswer == null) {
            throw new RStatusCodeException(ZSMallStatusCodeEnum.NOT_EXIST_ANSWERS_ERROR);
        }

        ProductQuestionAnswerLog productQuestionAnswerLog = BeanUtil.toBean(questionAnswer, ProductQuestionAnswerLog.class,
            CopyOptions.create().setIgnoreProperties(MallConstants.BeanIgnore.FAMILIAR_PROPERTIES));
        productQuestionAnswerLogMapper.insert(productQuestionAnswerLog);

        questionAnswer.setAnswer(answer);
        TenantHelper.ignore(() -> productQuestionAnswerMapper.updateById(questionAnswer));

        return true;
    }

    /**
     * 获取问题答复
     *
     * @param answerCode
     * @param replyType
     * @return
     */
    public ProductQuestionAnswer getQuestionAnswer(String answerCode, ReplyTypeEnum replyType) {
        ProductQuestionAnswer questionAnswer = TenantHelper.ignore(() ->
            productQuestionAnswerMapper.selectOne(new LambdaQueryWrapper<ProductQuestionAnswer>()
                .eq(ProductQuestionAnswer::getAnswerCode, answerCode).eq(ProductQuestionAnswer::getType, replyType))
        );
        return questionAnswer;
    }

    /**
     * 删除回复/追问
     *
     * @param bo
     * @return
     */
    public boolean deleteAnswer(ProductAnswerDeleteBo bo) {
        String type = bo.getType();
        String answerCode = bo.getAnswerCode();

        ProductQuestionAnswer questionAnswer = null;
        if (StrUtil.equalsIgnoreCase(type, ReplyTypeEnum.Question.name())) {
            questionAnswer = this.getQuestionAnswer(answerCode, ReplyTypeEnum.Question);
            if (questionAnswer == null) {
                throw new RStatusCodeException(ZSMallStatusCodeEnum.NOT_EXIST_CONTINUE_QUESTIONS_ERROR);
            }

        }

        if (StrUtil.equalsIgnoreCase(type, ReplyTypeEnum.Answer.name())) {
            questionAnswer = this.getQuestionAnswer(answerCode, ReplyTypeEnum.Answer);
            if (questionAnswer == null) {
                throw new RStatusCodeException(ZSMallStatusCodeEnum.NOT_EXIST_ANSWERS_2_ERROR);
            }
        }

        if (questionAnswer != null) {
            ProductQuestionAnswer finalQuestionAnswer = questionAnswer;
            TenantHelper.ignore(() -> productQuestionAnswerMapper.deleteById(finalQuestionAnswer));
        }

        return true;
    }

    /**
     * 查询日志
     *
     * @param answerCode
     * @return
     */
    public List<ProductAnswerLogVo> queryLog(String answerCode) {
        List<ProductAnswerLogVo> logVos = new ArrayList<>();
        List<ProductQuestionAnswerLog> logList = productQuestionAnswerLogMapper.selectList(
            new LambdaQueryWrapper<ProductQuestionAnswerLog>().eq(ProductQuestionAnswerLog::getAnswerCode, answerCode));
        if (CollUtil.isNotEmpty(logList)) {

            logList.forEach(log -> {
                ProductAnswerLogVo logVo = new ProductAnswerLogVo(log.getAnswer(), log.getUpdateBy(), log.getUpdateTime());
                logVos.add(logVo);
            });
        }
        return logVos;
    }

    /**
     * 举报
     *
     * @param bo
     * @return
     */
    public boolean reportQuestion(ProductQuestionsReportBo bo) {
        String questionCode = bo.getQuestionCode();
        String answerCode = bo.getAnswerCode();

        if (StrUtil.isBlank(questionCode) && StrUtil.isBlank(answerCode)) {
            throw new RStatusCodeException(ZSMallStatusCodeEnum.REQUIRED_CANNOT_EMPTY);
        }

        if (StrUtil.isNotBlank(questionCode)) {
            ProductQuestion productQuestion = new ProductQuestion();
            productQuestion.setStatus(QuestionStatusEnum.Reported);
            int update = TenantHelper.ignore(() -> baseMapper.update(productQuestion, new LambdaQueryWrapper<ProductQuestion>()
                .eq(ProductQuestion::getQuestionCode, questionCode)));
            if (update <= 0) {
                throw new RStatusCodeException(ZSMallStatusCodeEnum.NOT_EXIST_QUESTIONS_ERROR);
            }
        }

        if (StrUtil.isNotBlank(answerCode)) {
            ProductQuestionAnswer questionAnswer = new ProductQuestionAnswer();
            questionAnswer.setQuestionStatus(QuestionStatusEnum.Reported);
            int update = TenantHelper.ignore(() -> productQuestionAnswerMapper.update(questionAnswer,
                new LambdaQueryWrapper<ProductQuestionAnswer>().eq(ProductQuestionAnswer::getAnswerCode, answerCode)));
            if (update <= 0) {
                throw new RStatusCodeException(ZSMallStatusCodeEnum.NOT_EXIST_ANSWERS_ERROR);
            }
        }

        return true;
    }
}
