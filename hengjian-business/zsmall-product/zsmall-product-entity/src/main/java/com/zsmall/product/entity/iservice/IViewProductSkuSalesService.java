package com.zsmall.product.entity.iservice;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hengjian.common.log.annotation.InMethodLog;
import com.zsmall.product.entity.domain.viewer.ViewProductSkuSales;
import com.zsmall.product.entity.mapper.ViewProductSkuSalesMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 商品SKU销售情况视图-数据库层接口
 *
 * <AUTHOR>
 * @date 2023/6/9
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class IViewProductSkuSalesService extends ServiceImpl<ViewProductSkuSalesMapper, ViewProductSkuSales> {

    @InMethodLog("根据商品SKU主键查询")
    public ViewProductSkuSales queryByProductSkuId(Long productSkuId) {
        LambdaQueryWrapper<ViewProductSkuSales> lqw = new LambdaQueryWrapper<>();
        lqw.eq(ViewProductSkuSales::getSkuId, productSkuId);
        return baseMapper.selectOne(lqw);
    }



}
