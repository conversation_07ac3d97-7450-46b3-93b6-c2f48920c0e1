package com.zsmall.product.entity.domain.dto.product;

import cn.hutool.json.JSONArray;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 通用参数-商品SKU定价公式
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Schema(name = "通用参数-商品SKU定价公式")
public class ProductSkuPriceRuleBody {
    /**
     * 计算公式名称
     */
    @Schema(title = "计算公式名称")
    private String ruleName;

    /**
     * 计算公式编码
     */
    @Schema(title = "计算公式编码")
    private String ruleCode;

    /**
     * 适用类型（1-时间范围，2-价格区间，3-类目，4-供应商，5-SKU）
     */
    @Schema(title = "适用类型（1-时间范围，2-价格区间，3-类目，4-供应商，5-SKU）")
    private Integer applicableType;

    /**
     * 适用的值
     */
    @Schema(title = "适用的值")
    private JSONArray applicableValue;

}
