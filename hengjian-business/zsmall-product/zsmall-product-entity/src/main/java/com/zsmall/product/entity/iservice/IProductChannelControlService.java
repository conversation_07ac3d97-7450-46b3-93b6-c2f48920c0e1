package com.zsmall.product.entity.iservice;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONArray;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hengjian.common.log.annotation.InMethodLog;
import com.zsmall.product.entity.domain.ProductChannelControl;
import com.zsmall.product.entity.mapper.ProductChannelControlMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * 商品渠道管控Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-07-14
 */
@RequiredArgsConstructor
@Service
public class IProductChannelControlService extends ServiceImpl<ProductChannelControlMapper, ProductChannelControl> {

    @InMethodLog("查询用户时候被允许购买某件商品")
    public boolean checkUserAllow(String tenantId, String productSkuCode, String channelType) {
        ProductChannelControl allChannelControl = this.queryAllChannelControl(productSkuCode);
        // 全渠道管控信息不为空，说明该商品目前是全渠道管控
        JSONArray allowCodes;
        if (allChannelControl != null) {
            allowCodes = allChannelControl.getAllowTenantId();
        } else {
            // 全渠道管控信息不为空，说明该商品目前是全渠道管控
            ProductChannelControl control = queryByProductSkuCodeAndChannel(productSkuCode, channelType);
            if (control == null) {
                return true;
            } else {
                allowCodes = control.getAllowTenantId();
            }
        }

        boolean allow;
        if (CollUtil.isEmpty(allowCodes)) {
            allow = true;
        } else {
            allow = allowCodes.contains(tenantId);
        }
        return allow;
    }

    @InMethodLog("查询是否是全渠道管控")
    public ProductChannelControl queryAllChannelControl(String productSkuCode) {
        LambdaQueryWrapper<ProductChannelControl> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProductChannelControl::getProductSkuCode, productSkuCode);
        queryWrapper.eq(ProductChannelControl::getAllChannelControl, true);
        return getOne(queryWrapper);
    }

    @InMethodLog("根据ItemNo和渠道类型查询商品管控信息")
    public ProductChannelControl queryByProductSkuCodeAndChannel(String productSkuCode, String channelType) {
        LambdaQueryWrapper<ProductChannelControl> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProductChannelControl::getProductSkuCode, productSkuCode);
        queryWrapper.eq(ProductChannelControl::getChannelType, channelType);
        return this.getOne(queryWrapper);
    }

    @InMethodLog("根据ItemNo和渠道类型删除指定的渠道管控")
    public void deleteControlByChannel(String productSkuCode, String channelType) {
        LambdaQueryWrapper<ProductChannelControl> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProductChannelControl::getProductSkuCode, productSkuCode);
        queryWrapper.eq(channelType != null, ProductChannelControl::getChannelType, channelType);
        this.remove(queryWrapper);
    }

}
