package com.zsmall.product.entity.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hengjian.common.mybatis.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;



/**
 * 标签对象 label
 *
 * <AUTHOR> Li
 * @date 2023-05-31
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("product_label")
public class ProductLabel extends BaseEntity {


    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 标签名称
     */
    private String labelName;

    /**
     * 排序
     */
    private Long sort;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;


}
