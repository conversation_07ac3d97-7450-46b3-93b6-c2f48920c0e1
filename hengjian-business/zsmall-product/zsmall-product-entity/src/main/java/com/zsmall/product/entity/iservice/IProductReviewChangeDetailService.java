package com.zsmall.product.entity.iservice;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hengjian.common.core.utils.MapstructUtils;
import com.hengjian.common.core.utils.StringUtils;
import com.hengjian.common.mybatis.core.page.PageQuery;
import com.hengjian.common.mybatis.core.page.TableDataInfo;
import com.zsmall.product.entity.domain.ProductReviewChangeDetail;
import com.zsmall.product.entity.domain.bo.ProductReviewChangeDetailBo;
import com.zsmall.product.entity.domain.vo.ProductReviewChangeDetailVo;
import com.zsmall.product.entity.mapper.ProductReviewChangeDetailMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 商品审核变更详情Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-05-31
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class IProductReviewChangeDetailService extends ServiceImpl<ProductReviewChangeDetailMapper, ProductReviewChangeDetail> {

    private final ProductReviewChangeDetailMapper baseMapper;

    /**
     * 查询商品审核变更详情
     */
    public ProductReviewChangeDetailVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询商品审核变更详情列表
     */
    public TableDataInfo<ProductReviewChangeDetailVo> queryPageList(ProductReviewChangeDetailBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<ProductReviewChangeDetail> lqw = buildQueryWrapper(bo);
        Page<ProductReviewChangeDetailVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询商品审核变更详情列表
     */
    public List<ProductReviewChangeDetailVo> queryList(ProductReviewChangeDetailBo bo) {
        LambdaQueryWrapper<ProductReviewChangeDetail> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<ProductReviewChangeDetail> buildQueryWrapper(ProductReviewChangeDetailBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<ProductReviewChangeDetail> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getReviewRecordId() != null, ProductReviewChangeDetail::getReviewRecordId, bo.getReviewRecordId());
        lqw.eq(StringUtils.isNotBlank(bo.getProductCode()), ProductReviewChangeDetail::getProductCode, bo.getProductCode());
        lqw.eq(StringUtils.isNotBlank(bo.getProductSkuCode()), ProductReviewChangeDetail::getProductSkuCode, bo.getProductSkuCode());
        lqw.like(StringUtils.isNotBlank(bo.getFieldName()), ProductReviewChangeDetail::getFieldName, bo.getFieldName());
        lqw.eq(StringUtils.isNotBlank(bo.getFieldValueBefore()), ProductReviewChangeDetail::getFieldValueBefore, bo.getFieldValueBefore());
        lqw.eq(StringUtils.isNotBlank(bo.getFieldValueAfter()), ProductReviewChangeDetail::getFieldValueAfter, bo.getFieldValueAfter());
        lqw.eq(bo.getAllowUpdate() != null, ProductReviewChangeDetail::getAllowUpdate, bo.getAllowUpdate());
        return lqw;
    }

    /**
     * 新增商品审核变更详情
     */
    public Boolean insertByBo(ProductReviewChangeDetailBo bo) {
        ProductReviewChangeDetail add = MapstructUtils.convert(bo, ProductReviewChangeDetail.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    public Boolean insert(ProductReviewChangeDetail entity) {
        return baseMapper.insert(entity) > 0;
    }

    public Boolean insertBatch(List<ProductReviewChangeDetail> entities) {
        return baseMapper.insertOrUpdateBatch(entities);
    }

    /**
     * 修改商品审核变更详情
     */
    public Boolean updateByBo(ProductReviewChangeDetailBo bo) {
        ProductReviewChangeDetail update = MapstructUtils.convert(bo, ProductReviewChangeDetail.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(ProductReviewChangeDetail entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除商品审核变更详情
     */
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    /**
     * 根据审核主记录查询变更详情
     *
     * @param reviewRecordId
     * @return
     */
    public List<ProductReviewChangeDetail> queryByReviewRecordId(Long reviewRecordId) {
        log.info("进入【根据审核主记录查询变更详情】 reviewRecordId = {}", reviewRecordId);
        return lambdaQuery()
            .eq(ProductReviewChangeDetail::getReviewRecordId, reviewRecordId)
            .eq(ProductReviewChangeDetail::getAllowUpdate, 1)
            .list();
    }

    /**
     * 查询还未经过定时器更新的价格变更
     *
     * @param productSkuCode
     * @return
     */
    public List<ProductReviewChangeDetail> queryByTaskTodo(String productSkuCode) {
        log.info("查询还未经过定时器更新的价格变更 productSkuCode = {}", productSkuCode);
        return baseMapper.queryByTaskTodo(productSkuCode);
    }

    /**
     * 根据审核主记录和商品编号查询变更详情
     *
     * @param reviewRecordId
     * @param productCode
     * @param productSkuCode
     * @return
     */
    public List<ProductReviewChangeDetailVo> queryByReviewRecordIdAndCode(Long reviewRecordId, String productCode,
                                                                          String productSkuCode,Long siteId) {
        log.info("进入【根据审核主记录查询变更详情】 reviewRecordId = {},  productCode = {}, productSkuCode = {}", reviewRecordId, productCode, productSkuCode);
        LambdaQueryWrapper<ProductReviewChangeDetail> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(ProductReviewChangeDetail::getReviewRecordId, reviewRecordId)
            .eq(productCode != null, ProductReviewChangeDetail::getProductCode, productCode);
        if (StrUtil.isBlank(productSkuCode)) {
            lambdaQueryWrapper.isNull(ProductReviewChangeDetail::getProductSkuCode);
        } else {
            lambdaQueryWrapper.eq(ProductReviewChangeDetail::getProductSkuCode, productSkuCode);
        }
        if (ObjectUtil.isNotNull(siteId)) {
            lambdaQueryWrapper.eq(ProductReviewChangeDetail::getSiteId, siteId);
        }
        return baseMapper.selectVoList(lambdaQueryWrapper);
    }

    /**
     * 根据审核主记录和商品编号和字段名查询变更详情
     *
     * @param reviewRecordId
     * @param productCode
     * @param productSkuCode
     * @param fieldName
     * @return
     */
    public List<ProductReviewChangeDetail> queryByReviewRecordIdAndCodeAndFieldName(Long reviewRecordId,
                                                                                    String productCode, String productSkuCode, String fieldName) {
        log.info("进入【根据审核主记录和商品编号和字段名查询变更详情】 reviewRecordId = {},  productCode = {}, productSkuCode = {}", reviewRecordId, productCode, productSkuCode);
        LambdaQueryWrapper<ProductReviewChangeDetail> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(ProductReviewChangeDetail::getReviewRecordId, reviewRecordId)
            .eq(productCode != null, ProductReviewChangeDetail::getProductCode, productCode);

        if (productSkuCode == null) {
            lambdaQueryWrapper.isNull(ProductReviewChangeDetail::getProductSkuCode);
        } else {
            lambdaQueryWrapper.eq(ProductReviewChangeDetail::getProductSkuCode, productSkuCode);
        }

        if (StrUtil.isNotBlank(fieldName)) {
            lambdaQueryWrapper.eq(ProductReviewChangeDetail::getFieldName, fieldName);
        }

        return baseMapper.selectList(lambdaQueryWrapper);
    }

    /**
     * 根据商品编号删除
     *
     * @param productCode
     * @return
     */
    public Boolean deleteByProductCode(String productCode) {
        log.info("进入【根据商品编号删除】 productCode = {}", productCode);
        LambdaQueryWrapper<ProductReviewChangeDetail> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(ProductReviewChangeDetail::getProductCode, productCode);
        return baseMapper.delete(lambdaQueryWrapper) > 0;
    }

    public List<ProductReviewChangeDetail> queryDetail(Long reviewRecordId,
                                                       String productCode, String productSkuCode, String fieldName,Long siteId) {
        log.info("进入【根据审核主记录和商品编号和字段名查询变更详情】 reviewRecordId = {},  productCode = {}, productSkuCode = {}", reviewRecordId, productCode, productSkuCode);
        LambdaQueryWrapper<ProductReviewChangeDetail> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(ProductReviewChangeDetail::getReviewRecordId, reviewRecordId)
                          .eq(productCode != null, ProductReviewChangeDetail::getProductCode, productCode);

        if (productSkuCode == null) {
            lambdaQueryWrapper.isNull(ProductReviewChangeDetail::getProductSkuCode);
        } else {
            lambdaQueryWrapper.eq(ProductReviewChangeDetail::getProductSkuCode, productSkuCode);
        }

        if (StrUtil.isNotBlank(fieldName)) {
            lambdaQueryWrapper.eq(ProductReviewChangeDetail::getFieldName, fieldName);
        }
        return baseMapper.selectList(lambdaQueryWrapper);
    }
}
