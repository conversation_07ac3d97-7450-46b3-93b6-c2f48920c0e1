package com.zsmall.product.entity.iservice;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hengjian.common.core.domain.R;
import com.hengjian.common.core.exception.RStatusCodeException;
import com.hengjian.common.core.utils.MapstructUtils;
import com.hengjian.common.core.utils.StringUtils;
import com.hengjian.common.mybatis.core.page.PageQuery;
import com.hengjian.common.mybatis.core.page.TableDataInfo;
import com.zsmall.common.enums.OperationSymbolEnum;
import com.zsmall.common.enums.productSku.PriceApplicableTypeEnum;
import com.zsmall.common.enums.productSku.PriceItemTypeEnum;
import com.zsmall.common.enums.statuscode.ZSMallStatusCodeEnum;
import com.zsmall.product.entity.domain.ProductSkuPrice;
import com.zsmall.product.entity.domain.ProductSkuPriceRule;
import com.zsmall.product.entity.domain.ProductSkuPriceRuleItem;
import com.zsmall.product.entity.domain.bo.productSkuPrice.ApplicableProductSkuRuleBo;
import com.zsmall.product.entity.domain.bo.productSkuPrice.ProductSkuPriceBo;
import com.zsmall.product.entity.domain.bo.productSkuPrice.ProductSkuPriceRuleBo;
import com.zsmall.product.entity.domain.bo.productSkuPrice.ProductSkuPriceRuleRelationBo;
import com.zsmall.product.entity.domain.dto.productSku.ApplicableProductSkuDTO;
import com.zsmall.product.entity.domain.dto.productSku.ApplicableProductSkuParamDTO;
import com.zsmall.product.entity.domain.vo.product.ApplicableProductSkuVo;
import com.zsmall.product.entity.domain.vo.product.BoundApplicableProductVo;
import com.zsmall.product.entity.domain.vo.productSkuPrice.ProductSkuPriceRuleItemVo;
import com.zsmall.product.entity.domain.vo.productSkuPrice.ProductSkuPriceRuleRelationVo;
import com.zsmall.product.entity.domain.vo.productSkuPrice.ProductSkuPriceRuleVo;
import com.zsmall.product.entity.mapper.ProductSkuPriceRuleMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 商品sku价格计算公式Service业务层处理
 *
 * <AUTHOR> Li
 * @date 2023-05-22
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class IProductSkuPriceRuleService extends ServiceImpl<ProductSkuPriceRuleMapper, ProductSkuPriceRule> {

    private final ProductSkuPriceRuleMapper baseMapper;
    private final IProductSkuPriceService iProductSkuPriceService;
    private final IProductSkuPriceRuleItemService iProductSkuPriceRuleItemService;
    private final IProductSkuPriceRuleRelationService iProductSkuPriceRuleRelationService;
    private final IProductSkuService iProductSkuService;


    /**
     * 查询商品sku价格计算公式
     */
    public ProductSkuPriceRuleVo queryById(Long id) {
        ProductSkuPriceRuleVo ruleVo = baseMapper.selectVoById(id);
        List<ProductSkuPriceRuleItemVo> iTemList = iProductSkuPriceRuleItemService.getItemListByRuleId(ruleVo.getId());
        iTemList.forEach(item -> {
            if (StrUtil.equals(item.getPriceItemType(), PriceItemTypeEnum.UnitPrice.name())) {
                ruleVo.setUnitPrice(item.getPriceCalValue());
                ruleVo.setUnitPriceCal(item.getPriceCal());
            }
            if (StrUtil.equals(item.getPriceItemType(), PriceItemTypeEnum.OperationFee.name())) {
                ruleVo.setOperationFee(item.getPriceCalValue());
                ruleVo.setOperationFeeCal(item.getPriceCal());
            }
            if (StrUtil.equals(item.getPriceItemType(), PriceItemTypeEnum.FinalDeliveryFee.name())) {
                ruleVo.setFinalDeliveryFee(item.getPriceCalValue());
                ruleVo.setFinalDeliveryFeeCal(item.getPriceCal());
            }
        });
        return ruleVo;
    }

    /**
     * 查询商品sku价格计算公式列表
     */
    public TableDataInfo<ProductSkuPriceRuleVo> queryPageList(ProductSkuPriceRuleBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<ProductSkuPriceRule> lqw = buildQueryWrapper(bo);
        Page<ProductSkuPriceRuleVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        List<ProductSkuPriceRuleVo> records = result.getRecords();
        if (CollUtil.isEmpty(records)) {
            return TableDataInfo.build(result);
        }
        List<Long> ruleIds = records.stream().map(ProductSkuPriceRuleVo::getId).collect(Collectors.toList());
        Map<Long, String> priceFormulaMap = iProductSkuPriceRuleItemService.getPriceFormulaByRuleIds(ruleIds);
        records.forEach(vo -> {
            String priceFormula = priceFormulaMap.get(vo.getId());
            vo.setPriceFormula(priceFormula);
        });
        log.info("result = {}", JSONUtil.toJsonStr(result));
        return TableDataInfo.build(result);
    }


    /**
     * 查询商品sku价格计算公式列表
     */
    public List<ProductSkuPriceRuleVo> queryList(ProductSkuPriceRuleBo bo) {
        LambdaQueryWrapper<ProductSkuPriceRule> lqw = buildQueryWrapper(bo);
        List<ProductSkuPriceRuleVo> voList = baseMapper.selectVoList(lqw);
        if (CollUtil.isEmpty(voList)) {
            return voList;
        }
        List<Long> ruleIds = voList.stream().map(ProductSkuPriceRuleVo::getId).collect(Collectors.toList());
        Map<Long, String> priceFormulaMap = iProductSkuPriceRuleItemService.getPriceFormulaByRuleIds(ruleIds);
        voList.forEach(vo -> {
            String priceFormula = priceFormulaMap.get(vo.getId());
            vo.setPriceFormula(priceFormula);
        });
        return voList;
    }

    /**
     * 根据ids查询商品sku价格计算公式列表
     *
     * @param ids
     * @return
     */
    public List<ProductSkuPriceRuleVo> queryListByIds(List<Long> ids) {
        LambdaQueryWrapper<ProductSkuPriceRule> lqw = Wrappers.lambdaQuery();
        lqw.in(ProductSkuPriceRule::getId, ids);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<ProductSkuPriceRule> buildQueryWrapper(ProductSkuPriceRuleBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<ProductSkuPriceRule> lqw = Wrappers.lambdaQuery();
        lqw.like(StringUtils.isNotBlank(bo.getRuleName()), ProductSkuPriceRule::getRuleName, bo.getRuleName());
        lqw.eq(StringUtils.isNotBlank(bo.getRuleCode()), ProductSkuPriceRule::getRuleCode, bo.getRuleCode());
        lqw.eq(bo.getApplicableType() != null, ProductSkuPriceRule::getApplicableType, bo.getApplicableType());
        lqw.eq(ObjectUtil.isNotNull(bo.getApplicableValue()), ProductSkuPriceRule::getApplicableValue, bo.getApplicableValue());
        return lqw;
    }

    /**
     * 新增商品sku价格计算公式
     */
    @Transactional
    public Boolean insertByBo(ProductSkuPriceRuleBo bo) {
        ProductSkuPriceRule add = MapstructUtils.convert(bo, ProductSkuPriceRule.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;

        //添加商品sku价格计算公式表
        List<ProductSkuPriceRuleItemVo> itemVos = iProductSkuPriceRuleItemService.queryListByRuleId(add.getId());
        if (CollUtil.isNotEmpty(itemVos)) {
            List<Long> ids = itemVos.stream().map(ProductSkuPriceRuleItemVo::getId).collect(Collectors.toList());
            Boolean aBoolean = iProductSkuPriceRuleItemService.deleteWithValidByIds(ids, false);
            if (!aBoolean) {
                return aBoolean;
//                throw new RStatusCodeException(ZSMallStatusCodeEnum.PRODUCT_NOT_EXIST)
            }
        }
        List<ProductSkuPriceRuleItem> list = this.parseItem(add.getId(), bo);
        flag = iProductSkuPriceRuleItemService.saveBatch(list);
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改商品sku价格计算公式
     */
    public Boolean updateByBo(ProductSkuPriceRuleBo bo) {
        ProductSkuPriceRule update = MapstructUtils.convert(bo, ProductSkuPriceRule.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(ProductSkuPriceRule entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除商品sku价格计算公式
     */
    public R<Void> deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            if (CollUtil.contains(ids, 1l)) {
                return R.fail(ZSMallStatusCodeEnum.APPLICABLE_BASE_RULE_CANNOT_EDIT);
            }
        }
        return baseMapper.deleteBatchIds(ids) > 0 ? R.ok() : R.fail();
    }

    /**
     * 适用值校验
     *
     * @param bo
     * @return
     */
    public R<Void> checkApplicableValue(ProductSkuPriceRuleBo bo) {
        log.info("调用【适用值校验】方法");
        log.info("接口请求参数：{} ", JSONUtil.toJsonStr(bo));
        try {
            Integer type = bo.getApplicableType();
            JSONArray values = bo.getApplicableValue();
            String ruleCode = bo.getRuleCode();
            Integer unitPriceCal = bo.getUnitPriceCal();
            BigDecimal unitPriceCalValue = bo.getUnitPrice();
            Integer operationFeeCal = bo.getOperationFeeCal();
            BigDecimal operationFeeCalValue = bo.getOperationFee();
            Integer finalDeliveryFeeCal = bo.getFinalDeliveryFeeCal();
            BigDecimal finalDeliveryFeeCalValue = bo.getFinalDeliveryFee();

            //基础公式不能修改校验
            ProductSkuPriceRuleVo vo = this.getBasicRuleByRuleCode(ruleCode);
            if (ObjectUtil.isNotNull(vo)) {
                return R.fail(ZSMallStatusCodeEnum.APPLICABLE_BASE_RULE_CANNOT_EDIT);
            }

            if ((StrUtil.equals("*", OperationSymbolEnum.fromCode(unitPriceCal)) || StrUtil.equals("/", OperationSymbolEnum.fromCode(unitPriceCal))) && NumberUtil.equals(unitPriceCalValue, NumberUtil.toBigDecimal(0))) {
                return R.fail(ZSMallStatusCodeEnum.APPLICABLE_CALCULATION_ERROR);
            }
            if ((StrUtil.equals("*", OperationSymbolEnum.fromCode(operationFeeCal)) || StrUtil.equals("/", OperationSymbolEnum.fromCode(operationFeeCal))) && NumberUtil.equals(operationFeeCalValue, NumberUtil.toBigDecimal(0))) {
                return R.fail(ZSMallStatusCodeEnum.APPLICABLE_CALCULATION_ERROR);
            }
            if ((StrUtil.equals("*", OperationSymbolEnum.fromCode(finalDeliveryFeeCal)) || StrUtil.equals("/", OperationSymbolEnum.fromCode(finalDeliveryFeeCal))) && NumberUtil.equals(finalDeliveryFeeCalValue, NumberUtil.toBigDecimal(0))) {
                return R.fail(ZSMallStatusCodeEnum.APPLICABLE_CALCULATION_ERROR);
            }

            if (type == null || CollUtil.isEmpty(values)) {
                return R.fail(ZSMallStatusCodeEnum.REQUIRED_CANNOT_EMPTY);
            }

            List<ProductSkuPriceRuleVo> rules = this.getListByTypeAndValue(type, values, ruleCode);
            if (CollUtil.isEmpty(rules)) {
                return R.ok();
            }

            switch (type) {
                case 1:
                    throw new RStatusCodeException(ZSMallStatusCodeEnum.APPLICABLE_VALUE_ERROR);
                case 2:
                    throw new RStatusCodeException(ZSMallStatusCodeEnum.APPLICABLE_VALUE_ERROR);
                default:
            }

            return R.ok();
        } catch (RStatusCodeException rStatusCodeException) {
            log.error("调用【适用值校验】方法 转态码异常 rStatusCodeException = {}", rStatusCodeException.getStatusCode());
            return R.fail(rStatusCodeException.getStatusCode());
        } catch (Exception e) {
            log.error("调用【适用值校验】方法异常 e = {}", e.getMessage(), e);
            return R.fail();
        }
    }

    /**
     * 根据适用类型和适用值判断适用值是否变动
     *
     * @param oldType
     * @param newType
     * @param oldValue
     * @param newValue
     * @return true 变动， false 无变动
     */
    public Boolean isChange(Integer oldType, Integer newType, JSONArray oldValue, JSONArray newValue) {
        if (!ObjectUtil.equals(oldType, newType)) {
            return true;
        }
        List<Object> oldList = oldValue.toList(Object.class);
        List<Object> newList = newValue.toList(Object.class);
        if (oldList.size() == newList.size() && !oldList.retainAll(newList)) {
            return false;
        } else {
            return true;
        }
    }

    /**
     * 翻页获取适用产品（适用产品设置）
     *
     * @param bo
     * @param pageQuery
     * @return
     */
    public TableDataInfo<ApplicableProductSkuVo> getApplicableProductSkuPage(ProductSkuPriceRuleBo bo, PageQuery pageQuery) throws RStatusCodeException {

        //筛选参数
        Integer type = bo.getApplicableType();
        JSONArray value = bo.getApplicableValue();
        String ruleCode = bo.getRuleCode();
        List<String> deleteItemNoList = bo.getDeleteItemNoList();
        List<String> replaceItemNoList = bo.getReplaceItemNoList();

        if (type == null || ObjectUtil.isEmpty(value)) {
            throw new RStatusCodeException(ZSMallStatusCodeEnum.REQUIRED_CANNOT_EMPTY);
        }
        ApplicableProductSkuParamDTO dto = new ApplicableProductSkuParamDTO();
        if (CollUtil.isNotEmpty(deleteItemNoList)) {
            dto.setDeleteItemNoList(deleteItemNoList);
        }

        switch (type) {
            case 1:
                List<BigDecimal> priceRange = value.toList(BigDecimal.class);
                dto.setStartPrice(priceRange.get(0));
                dto.setEndPrice(priceRange.get(1));
                break;
            case 2:
                List<String> itemNos = value.toList(String.class);
                dto.setItemNos(itemNos);
                break;
            default:

        }
        if (StrUtil.isBlank(ruleCode)) {
            List<ProductSkuPriceRuleVo> rules = this.getListByTypeAndValue(type, value, ruleCode);
            if (CollUtil.size(rules) > 0) {
                ruleCode = rules.get(0).getRuleCode();
            }
        }


        //公式参数
        Integer pUnitPriceCal = bo.getUnitPriceCal();
        BigDecimal pUnitPriceCalV = bo.getUnitPrice();
        Integer pOperationFeeCal = bo.getOperationFeeCal();
        BigDecimal pOperationFeeCalV = bo.getOperationFee();
        Integer finalDeliveryFeeCal = bo.getFinalDeliveryFeeCal();
        BigDecimal finalDeliveryFeeCalV = bo.getFinalDeliveryFee();

        //设置价格公式明细信息
        List<ProductSkuPriceRuleItemVo> ruleItemList = iProductSkuPriceRuleItemService.parseRuleItemListByRuleInfo(null,
            pUnitPriceCal, pUnitPriceCalV, pOperationFeeCal, pOperationFeeCalV, finalDeliveryFeeCal, finalDeliveryFeeCalV);

        Integer page = pageQuery.getPageNum();
        Integer limit = pageQuery.getPageSize();
        page = page == null || page < 1 ? 1 : page;
        limit = limit == null || limit < 1 ? 10 : limit;
        Page<ApplicableProductSkuDTO> pageParam = new Page<>(page, limit);

        log.info("dto =>{}", dto);

        //获取已绑定的商品数量（全部）
        if (StrUtil.isNotBlank(ruleCode)) {
            dto.setRuleCode(ruleCode);
            List<String> itemNosByRuleCode = iProductSkuPriceRuleRelationService.getItemNosByRuleCode(ruleCode);
            if (CollUtil.isEmpty(deleteItemNoList)) {
                deleteItemNoList = new ArrayList<>();
            }
            if (CollUtil.isNotEmpty(itemNosByRuleCode)) {
                //过滤掉当前公式已绑定的商品
                deleteItemNoList.addAll(itemNosByRuleCode);
            }
            dto.setDeleteItemNoList(deleteItemNoList);
        }
        Integer boundNum = iProductSkuService.getBoundApplicableProductSkuNum(dto);
        log.info("boundNum = {}", boundNum);
//            通过筛选条件获取商品价格
        IPage<ApplicableProductSkuDTO> productSkuPage = iProductSkuService.getApplicableProductSkuPage(dto, pageParam);
        List<ApplicableProductSkuDTO> records = productSkuPage.getRecords();

        List<Long> ruleIds = records.stream().filter(r -> r.getSkuPriceRuleId() != null).map(ApplicableProductSkuDTO::getSkuPriceRuleId).collect(Collectors.toList());
        List<ProductSkuPriceRuleVo> rules = new ArrayList<>();
        if (CollUtil.isNotEmpty(ruleIds)) {
            rules = this.queryListByIds(ruleIds);
        }

        List<ApplicableProductSkuVo> respVos = new ArrayList<>();
        List<Long> ruleIdList = records.stream().map(ApplicableProductSkuDTO::getSkuPriceRuleId).collect(Collectors.toList());
        ruleIdList.add(0l);
        List<ProductSkuPriceRuleItemVo> itemListByRuleIds = iProductSkuPriceRuleItemService.getItemListByRuleIds(ruleIdList);
        for (ApplicableProductSkuDTO r : records) {
            BigDecimal unitPrice = r.getUnitPrice();
            BigDecimal operationFee = r.getOperationFee();
            BigDecimal finalDeliveryFee = r.getFinalDeliveryFee();
            Long ruleId = r.getSkuPriceRuleId();
            //设置响应值
            ApplicableProductSkuVo respBody = new ApplicableProductSkuVo(r.getItemNo(), r.getUnitPrice(),
                r.getOperationFee(), r.getFinalDeliveryFee(), r.getFob(), r.getRetailPrice());
            respBody.setBoundNum(boundNum);
            BoundApplicableProductVo boundVo = new BoundApplicableProductVo();

            //当前计算公式
            ProductSkuPrice priceVo = this.calculate(ruleItemList, unitPrice, operationFee, finalDeliveryFee);
            respBody.setPickUpPrice(NumberUtil.toBigDecimal(priceVo.getPlatformPickUpPrice()));
            respBody.setDropShippingPrice(NumberUtil.toBigDecimal(priceVo.getPlatformDropShippingPrice()));
            //计算自提价差和代发价差
            BigDecimal pIncrease = this.calculatePriceIncrease(NumberUtil.add(unitPrice, operationFee), respBody.getPickUpPrice());
            BigDecimal oIncrease = this.calculatePriceIncrease(NumberUtil.add(unitPrice, operationFee, finalDeliveryFee), respBody.getDropShippingPrice());
            respBody.setPickUpPriceDifference(pIncrease + "%");
            respBody.setDropShippingPriceDifference(oIncrease + "%");
            //如果未绑定公式，则只用当前计算公式计算商品价格
            if (ruleId != null) {
                //过滤已点击替换的数据
                if (CollUtil.isEmpty(replaceItemNoList) || !replaceItemNoList.contains(r.getItemNo())) {
                    List<ProductSkuPriceRuleVo> collect = rules.stream().filter(ru -> ruleId == ru.getId()).collect(Collectors.toList());
                    if (CollUtil.isNotEmpty(collect)) {
                        ProductSkuPriceRuleVo entity = collect.get(0);
                        boundVo.setRuleName(entity.getRuleName());
                        //原计算公式
                        List<ProductSkuPriceRuleItemVo> collect1 = itemListByRuleIds.stream().filter(i -> i.getProductSkuPriceRuleId() == entity.getId()).collect(Collectors.toList());
                        ProductSkuPrice boundPriceVo = this.calculate(collect1, unitPrice, operationFee, finalDeliveryFee);
                        boundVo.setDropShippingPrice(boundPriceVo.getPlatformDropShippingPrice());
                        boundVo.setPickUpPrice(boundPriceVo.getPlatformPickUpPrice());
                        //设置价格涨幅
                        BigDecimal boundPIncrease = this.calculatePriceIncrease(boundVo.getPickUpPrice(), NumberUtil.toBigDecimal(respBody.getPickUpPrice()));
                        BigDecimal boundOIncrease = this.calculatePriceIncrease(boundVo.getDropShippingPrice(), NumberUtil.toBigDecimal(respBody.getDropShippingPrice()));
                        boundVo.setDropShippingPriceIncreaseType(NumberUtil.isGreater(boundOIncrease, BigDecimal.ZERO) ? "up" : "down");
                        boundVo.setPickUpPriceIncreaseType(NumberUtil.isGreater(boundPIncrease, BigDecimal.ZERO) ? "up" : "down");
                        boundVo.setDropShippingPriceIncrease(boundOIncrease + "%");
                        boundVo.setPickUpPriceIncrease(boundPIncrease + "%");
                        respBody.setRespBoundBody(boundVo);
                    }
                }
            }
            respVos.add(respBody);
        }

        return TableDataInfo.build(respVos, productSkuPage.getTotal());

    }

    /**
     * 通过公式ruleCode获取适用产品（翻页）
     *
     * @param bo
     * @param pageQuery
     * @return
     */
    public TableDataInfo<ApplicableProductSkuVo> getApplicableProductSkuByRuleCodePage(ApplicableProductSkuRuleBo bo, PageQuery pageQuery) throws RStatusCodeException {

        //筛选参数
        String ruleCode = bo.getRuleCode();
        String itemNo = bo.getItemNo();
        String queryValue = StrUtil.trim(bo.getQueryValue());
        String queryType = bo.getQueryType();
        if (StrUtil.isBlank(queryValue)) {
            queryType = null;
        }
        if (StrUtil.isBlank(ruleCode)) {
            throw new RStatusCodeException(ZSMallStatusCodeEnum.REQUIRED_CANNOT_EMPTY);
        }

        //根据ruleCode获取itemNos
        List<String> itemNos = iProductSkuPriceRuleRelationService.getItemNosByRuleCode(ruleCode);
        if (CollUtil.isEmpty(itemNos)) {
            return TableDataInfo.build(new ArrayList<>(), 0l);
        }
        //设置参数
        ApplicableProductSkuParamDTO dto = new ApplicableProductSkuParamDTO();
        List<String> itemNoList = new ArrayList<>();
        itemNoList.addAll(itemNos);
        if (StrUtil.isNotBlank(itemNo)) {
            itemNoList.add(itemNo);
        }
        if (StrUtil.isNotBlank(queryType)) {
            dto.setQueryValue(queryValue);
            dto.setType(queryType);
        }
        dto.setItemNos(itemNoList);

        Integer page = pageQuery.getPageNum();
        Integer limit = pageQuery.getPageSize();
        page = page == null || page < 1 ? 1 : page;
        limit = limit == null || limit < 1 ? 10 : limit;

        Page<ApplicableProductSkuDTO> pageParam = new Page<>(page, limit);
        //通过筛选条件获取商品价格
        IPage<ApplicableProductSkuDTO> productSkuPage = iProductSkuService.getApplicableProductSkuPage(dto, pageParam);

        List<ApplicableProductSkuDTO> records = productSkuPage.getRecords();
        ProductSkuPriceRule ruleEntity = this.getByRuleCode(ruleCode);

        List<ApplicableProductSkuVo> respBodys = new ArrayList<>();
        records.stream().forEach(r -> {
            BigDecimal unitPrice = r.getUnitPrice();
            BigDecimal operationFee = r.getOperationFee();
            BigDecimal finalDeliveryFee = r.getFinalDeliveryFee();
            //设置响应值
            ApplicableProductSkuVo respBody = new ApplicableProductSkuVo(r.getItemNo(), r.getUnitPrice(),
                r.getOperationFee(), r.getFinalDeliveryFee(), r.getFob(), r.getRetailPrice());
            //计算自提价和代发价
            ProductSkuPrice priceDTO = this.calculateByRule(ruleEntity.getRuleCode(), unitPrice, operationFee, finalDeliveryFee);
            respBody.setPickUpPrice(NumberUtil.toBigDecimal(priceDTO.getPlatformPickUpPrice()));
            respBody.setDropShippingPrice(NumberUtil.toBigDecimal(priceDTO.getPlatformDropShippingPrice()));
            //计算自提价差和代发价差
            BigDecimal pIncrease = this.calculatePriceIncrease(NumberUtil.add(unitPrice, operationFee), respBody.getPickUpPrice());
            BigDecimal oIncrease = this.calculatePriceIncrease(NumberUtil.add(unitPrice, operationFee, finalDeliveryFee), respBody.getDropShippingPrice());
            respBody.setPickUpPriceDifference(pIncrease + "%");
            respBody.setDropShippingPriceDifference(oIncrease + "%");
            respBodys.add(respBody);
        });
        TableDataInfo<ApplicableProductSkuVo> resultPage = new TableDataInfo<>();
        resultPage.setTotal(productSkuPage.getTotal());
        resultPage.setRows(respBodys);

        return TableDataInfo.build(respBodys, productSkuPage.getTotal());

    }

    public ProductSkuPriceRule getByRuleCode(String ruleCode) {
        ProductSkuPriceRuleBo bo = new ProductSkuPriceRuleBo();
        bo.setRuleCode(ruleCode);
        LambdaQueryWrapper<ProductSkuPriceRule> lqw = Wrappers.lambdaQuery();
        lqw.eq(ProductSkuPriceRule::getRuleCode, ruleCode);
        return baseMapper.selectOne(lqw);
    }

    public ProductSkuPrice calculatePricesByPriceEntity(ProductSkuPriceRuleBo priceEntity) {
        //价格公式具体明细
        ProductSkuPrice productSkuPrice = null;
        try {
            log.info("根据商品价格计算提价后的价格（单个商品价格实体） = {}", JSONUtil.toJsonStr(priceEntity));
            Long priceId = priceEntity.getId();
            Long productSkuId = priceEntity.getProductSkuId();
            BigDecimal unitPrice = priceEntity.getUnitPrice();
            BigDecimal operationFee = priceEntity.getOperationFee();
            BigDecimal finalDeliveryFee = priceEntity.getFinalDeliveryFee();
            ProductSkuPriceRuleRelationVo relation = iProductSkuPriceRuleRelationService.getByProductSkuId(productSkuId);
            if (ObjectUtil.isNull(relation)) {
                productSkuPrice = this.matchRule(null, unitPrice, operationFee, finalDeliveryFee, false);
                return productSkuPrice;
            }
            List<ProductSkuPriceRuleItemVo> itemList = iProductSkuPriceRuleItemService.queryListByRuleId(relation.getProductSkuPriceRuleId());
            productSkuPrice = this.calculatePriceItem(itemList, unitPrice, operationFee, finalDeliveryFee);
            productSkuPrice.setProductSkuPriceRuleId(relation.getProductSkuPriceRuleId());
        } catch (Exception e) {
            log.info("根据商品价格计算提价后的价格（单个商品价格实体）发生错误 {}", e.getMessage(), e);
        }
        return productSkuPrice;
    }

    public List<ProductSkuPrice> calculateProductSkuPricesByRuleCode(String ruleCode, List<ProductSkuPrice> oldPrices) {

        List<ProductSkuPrice> voList = new ArrayList<>();
        //通过价格公式编码获取价格公式信息
        ProductSkuPriceRule rule = this.getByRuleCode(ruleCode);
        if (rule == null) {
            return voList;
        }
        //通过价格公式id获取公式 明细数据
        List<ProductSkuPriceRuleItemVo> itemList = iProductSkuPriceRuleItemService.queryListByRuleId(rule.getId());
        if (CollUtil.isEmpty(itemList)) {
            return voList;
        }

        oldPrices.stream().forEach(p -> {
            Long id = p.getId();
            BigDecimal unitPrice = p.getOriginalUnitPrice();
            BigDecimal handlingFee = p.getOriginalOperationFee();
            BigDecimal tailDeliveryFee = p.getOriginalFinalDeliveryFee();

            //根据公司计算自提价和代发价
            ProductSkuPrice vo = this.calculate(itemList, unitPrice, handlingFee, tailDeliveryFee);
            vo.setId(rule.getId());
            vo.setId(id);
            vo.setProductSkuId(p.getProductSkuId());
            vo.setProductSkuCode(p.getProductSkuCode());
            voList.add(vo);
        });
        return voList;
    }

    public ProductSkuPrice calculateByRule(String ruleCode, BigDecimal unitPrice, BigDecimal
        operationFee, BigDecimal finalDeliveryFee) {
        //通过价格公式编码获取价格公式信息
        ProductSkuPriceRule rule = this.getByRuleCode(ruleCode);
        //通过价格公式id获取公式 明细数据
        List<ProductSkuPriceRuleItemVo> itemList = iProductSkuPriceRuleItemService.queryListByRuleId(rule.getId());
        //计算自提价和代发价
        ProductSkuPrice vo = this.calculate(itemList, unitPrice, operationFee, finalDeliveryFee);
        return vo;
    }

    public ProductSkuPrice calculate(List<ProductSkuPriceRuleItemVo> itemList, BigDecimal
        unitPrice, BigDecimal operationFee, BigDecimal finalDeliveryFee) {
        //原始自提价（供货商，产品单价+操作费）
        BigDecimal originalPickUpPrice = NumberUtil.add(unitPrice, operationFee);
        //原始代发价（供货商，产品单价+操作费+尾程派送费）
        BigDecimal originalDropShippingPrice = NumberUtil.add(unitPrice, operationFee, finalDeliveryFee);
        //平台产品单价（平台+分销商）
        BigDecimal platformUnitPrice = new BigDecimal(0);
        //平台操作费（平台+分销商）
        BigDecimal platformOperationFee = new BigDecimal(0);
        //平台尾程派送费（平台+分销商）
        BigDecimal platformFinalDeliveryFee = new BigDecimal(0);
        //平台自提价（平台+分销商，产品单价+操作费）
        BigDecimal platformPickUpPrice = new BigDecimal(0);
        //平台代发价（平台+分销商，产品单价+操作费+尾程派送费）
        BigDecimal platformDropShippingPrice = new BigDecimal(0);

        for (ProductSkuPriceRuleItemVo i : itemList) {
            //计算提价后的原价、操作费、运费
            if (StrUtil.equals(PriceItemTypeEnum.UnitPrice.name(), i.getPriceItemType())) {
                platformUnitPrice =
                    NumberUtil.round(OperationSymbolEnum.calculate(i.getPriceCal(), unitPrice, i.getPriceCalValue()), 2);
            }
            if (StrUtil.equals(PriceItemTypeEnum.OperationFee.name(), i.getPriceItemType())) {
                platformOperationFee =
                    NumberUtil.round(OperationSymbolEnum.calculate(i.getPriceCal(), operationFee, i.getPriceCalValue()), 2);
            }
            if (StrUtil.equals(PriceItemTypeEnum.FinalDeliveryFee.name(), i.getPriceItemType())) {
                platformFinalDeliveryFee =
                    NumberUtil.round(OperationSymbolEnum.calculate(i.getPriceCal(), finalDeliveryFee, i.getPriceCalValue()), 2);
            }
        }
        platformPickUpPrice = NumberUtil.add(platformUnitPrice, platformOperationFee);
        platformDropShippingPrice = NumberUtil.add(platformUnitPrice, platformOperationFee, platformFinalDeliveryFee);

        ProductSkuPrice vo = new ProductSkuPrice(
            unitPrice,
            operationFee,
            finalDeliveryFee,
            originalPickUpPrice,
            originalDropShippingPrice,
            platformUnitPrice,
            platformOperationFee,
            platformFinalDeliveryFee,
            platformPickUpPrice,
            platformDropShippingPrice
        );
        return vo;
    }

    public ProductSkuPrice queryByPriceRange(ProductSkuPriceBo priceEntity) {
        log.info("根据价格查询查询并计算平台价格 priceEntity = {}", JSONUtil.toJsonStr(priceEntity));
        List<ProductSkuPriceRule> ruleList = baseMapper.queryByPriceRange(priceEntity.getOriginalUnitPrice());
        if (CollUtil.isNotEmpty(ruleList)) {
            ProductSkuPriceRule productSkuPriceRule = ruleList.get(0);

            //价格公式具体明细
            List<ProductSkuPriceRuleItemVo> itemList =
                iProductSkuPriceRuleItemService.queryListByRuleId(productSkuPriceRule.getId());
            BigDecimal unitPrice = priceEntity.getOriginalUnitPrice();
            BigDecimal operationFee = priceEntity.getOriginalOperationFee();
            BigDecimal finalDeliveryFee = priceEntity.getOriginalFinalDeliveryFee();
            //计算提价后的具体明细
            ProductSkuPrice dto = this.calculatePriceItem(itemList, unitPrice, operationFee, finalDeliveryFee);
            dto.setId(priceEntity.getId());
            dto.setProductSkuPriceRuleId(productSkuPriceRule.getId());
            return dto;
        } else {
            return null;
        }
    }

    public List<ProductSkuPriceRuleVo> getListByTypeAndValue(Integer type, JSONArray applicableValues, String ruleCode) {
        log.info("根据公式类型和适用值获取符合的公式数据 type = {}, applicableValues = {}, ruleCode = {}", type, JSONUtil.toJsonStr(applicableValues), ruleCode);
        LambdaQueryWrapper<ProductSkuPriceRule> wrapper = new LambdaQueryWrapper<>();
        wrapper.ne(ProductSkuPriceRule::getId, 1);
        //排除该ruleCode
        if (StrUtil.isNotBlank(ruleCode)) {
            wrapper.ne(ProductSkuPriceRule::getRuleCode, ruleCode);
        }
        List<ProductSkuPriceRuleVo> voList = baseMapper.selectVoList(wrapper);
        if (CollUtil.isEmpty(voList)) {
            return voList;
        }
        List<ProductSkuPriceRuleVo> typeList = new ArrayList<>();
        for (ProductSkuPriceRuleVo vo : voList) {
            if (ObjectUtil.equals(type, 1) && ObjectUtil.equals(vo.getApplicableType(), 1)) {
                List<BigDecimal> appValues = applicableValues.toList(BigDecimal.class);
                BigDecimal start = appValues.get(0);
                BigDecimal end = appValues.get(1);

                JSONArray applicableValue = vo.getApplicableValue();
                List<BigDecimal> values = applicableValue.toList(BigDecimal.class);
                BigDecimal startPrice = values.get(0);
                BigDecimal endPrice = values.get(1);

                if ((start.compareTo(startPrice) >= 0 && start.compareTo(endPrice) <= 0)
                || (end.compareTo(startPrice) >= 0 && end.compareTo(endPrice) <= 0)
                    || (startPrice.compareTo(start) >= 0 && startPrice.compareTo(end) <= 0
                    && endPrice.compareTo(start) >= 0 && endPrice.compareTo(end) <= 0)) {
                    typeList.add(vo);
                }

            }
            if (ObjectUtil.equals(type, 2) && ObjectUtil.equals(vo.getApplicableType(), 2)) {
                List<String> appValues = applicableValues.toList(String.class);

                JSONArray applicableValue = vo.getApplicableValue();
                List<String> values = applicableValue.toList(String.class);
                log.info("appValues = {}, values = {}", JSONUtil.toJsonStr(appValues), JSONUtil.toJsonStr(values));
                appValues.retainAll(values);
                if (CollUtil.isNotEmpty(appValues)) {
                    typeList.add(vo);
                }
            }

            //TODO 其它类型待处理
        }
        return typeList;
    }

    public List<ProductSkuPriceRuleVo> getListByType(Integer type, String ruleCode) {
        log.info("根据公式类型和适用值获取符合的公式数据 type = {}, ruleCode = {}", type, ruleCode);
        return baseMapper.getListByType(type, ruleCode);
    }

    public ProductSkuPriceRuleVo getBasicRuleByRuleCode(String ruleCode) {
        log.info("根据ruleCode判断基础公式是否存在  ruleCode = {}", ruleCode);
        LambdaQueryWrapper<ProductSkuPriceRule> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProductSkuPriceRule::getId, 1)
            .eq(ProductSkuPriceRule::getRuleCode, ruleCode);
        return baseMapper.selectVoOne(queryWrapper);
    }

    public ProductSkuPrice matchRule(String itemNo, BigDecimal unitPrice, BigDecimal
        operationFee, BigDecimal finalDeliveryFee, Boolean isMatchBase) throws RStatusCodeException {
        ProductSkuPriceRuleVo baseRule = this.queryById(1l);
        String ruleCode = null;
        Long ruleId = null;
        List<ProductSkuPriceRuleVo> ruleList = baseMapper.selectVoList();
        List<ProductSkuPriceRuleVo> rules = CollUtil.newCopyOnWriteArrayList(ruleList);
        rules.remove(baseRule);
        if (!isMatchBase) {
            //优先匹配itemNo规则（第三级）
            List<ProductSkuPriceRuleVo> collect_3 = rules.stream().filter(rule -> rule.getApplicableType() == PriceApplicableTypeEnum.ItemNo.getCode()).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(collect_3)) {
                for (ProductSkuPriceRuleVo rule : collect_3) {
                    JSONArray applicableValue = rule.getApplicableValue();
                    List<String> itemNos = applicableValue.toList(String.class);
                    if (itemNos.contains(itemNo)) {
                        ruleId = rule.getId();
                        ruleCode = rule.getRuleCode();
                        break;
                    }
                }
                rules.removeAll(collect_3);
            }
            //第三级匹配 不到公式后进入第二级
            if (ruleId == null) {
                //匹配价格规则（第二级(过滤掉基础公式)）
                List<ProductSkuPriceRuleVo> collect_2 = rules.stream().filter(rule ->
                    !ObjectUtil.equals(rule.getId(), baseRule.getId()) &&
                        rule.getApplicableType() == PriceApplicableTypeEnum.PriceRange.getCode()
                ).collect(Collectors.toList());
                if (CollUtil.isNotEmpty(collect_2)) {
                    List<ProductSkuPriceRuleVo> priceRules = collect_2.stream()
                        .filter(rule -> Objects.equals(rule.getApplicableType(), PriceApplicableTypeEnum.PriceRange.getCode()))
                        .collect(Collectors.toList());
                    for (ProductSkuPriceRuleVo rule : priceRules) {
                        JSONArray applicableValue = rule.getApplicableValue();
                        List<BigDecimal> prices = applicableValue.toList(BigDecimal.class);
                        if (CollUtil.isEmpty(prices)) {
                            continue;
                        }
                        BigDecimal startPrice = prices.get(0);
                        BigDecimal endPrice = prices.get(1);

                        if (NumberUtil.isGreaterOrEqual(unitPrice, startPrice) && NumberUtil.isLessOrEqual(unitPrice, endPrice)) {
                            ruleId = rule.getId();
                            ruleCode = rule.getRuleCode();
                            break;
                        }
                    }
                    rules.removeAll(collect_2);
                }
            }
        }
        //匹配基础价格规则（第一级）
        if (ruleId == null) {
            JSONArray applicableValue = baseRule.getApplicableValue();
            List<BigDecimal> prices = applicableValue.toList(BigDecimal.class);
            if (CollUtil.isEmpty(prices)) {
                return null;
            }
            BigDecimal startPrice = prices.get(0);
            BigDecimal endPrice = prices.get(1);

            if (NumberUtil.isGreaterOrEqual(unitPrice, startPrice) && NumberUtil.isLessOrEqual(unitPrice, endPrice)) {
                ruleId = baseRule.getId();
                ruleCode = baseRule.getRuleCode();
            }

        }
        //无匹配公式则返回null
        if (ruleId == null) {
            throw new RStatusCodeException(ZSMallStatusCodeEnum.SYSTEM_ERROR_E10028);
        }
        //价格公式具体明细
        List<ProductSkuPriceRuleItemVo> itemList =
            iProductSkuPriceRuleItemService.queryListByRuleId(ruleId);
        //计算提价后的具体明细
        ProductSkuPrice dto =
            this.calculatePriceItem(itemList, unitPrice, operationFee, finalDeliveryFee);
        dto.setProductSkuPriceRuleId(ruleId);
        dto.setProductSkuPriceRuleCode(ruleCode);
        return dto;
    }

    /**
     * 匹配公式
     *
     * @param productSkuPrice
     * @param isMatchBase
     * @return
     * @throws RStatusCodeException
     */
    public Long matchRule(ProductSkuPrice productSkuPrice, Boolean isMatchBase) throws RStatusCodeException {
        String productSkuCode = productSkuPrice.getProductSkuCode();
        Long productSkuId = productSkuPrice.getProductSkuId();
        Long id = productSkuPrice.getId();
        BigDecimal msrp = productSkuPrice.getMsrp();
        BigDecimal originalUnitPrice = productSkuPrice.getOriginalUnitPrice();
        BigDecimal originalOperationFee = productSkuPrice.getOriginalOperationFee();
        BigDecimal originalFinalDeliveryFee = productSkuPrice.getOriginalFinalDeliveryFee();

        ProductSkuPrice newProductSkuPrice = matchRule(productSkuCode, originalUnitPrice, originalOperationFee, originalFinalDeliveryFee, isMatchBase);
        if (newProductSkuPrice == null) {
            return null;
        }
        BeanUtil.copyProperties(newProductSkuPrice, productSkuPrice);
        productSkuPrice.setId(id);
        productSkuPrice.setProductSkuId(productSkuId);
        productSkuPrice.setProductSkuCode(productSkuCode);
        productSkuPrice.setMsrp(msrp);
        log.info("matchRule - productSkuPrice = {}", JSONUtil.toJsonStr(productSkuPrice));
        Long productSkuPriceRuleId = productSkuPrice.getProductSkuPriceRuleId();
        return productSkuPriceRuleId;
    }

    public Boolean bindRule(Long productSkuId, String productSkuCode, Long productSkuPriceId, Long ruleId) {
        if (productSkuId == null || ruleId == null || productSkuPriceId == null) {
            return false;
        }
        //删除原有绑定
        iProductSkuPriceRuleRelationService.deleteRelationByProductSkuId(productSkuId);
        ProductSkuPriceRuleRelationBo entity =
            new ProductSkuPriceRuleRelationBo(productSkuPriceId, productSkuId, productSkuCode, ruleId);
        return iProductSkuPriceRuleRelationService.insertByBo(entity);
    }

    /**
     * 绑定公式
     *
     * @param productSkuPrice
     * @param ruleId
     * @return
     */
    public Boolean bindRule(ProductSkuPrice productSkuPrice, Long ruleId) {
        Long productSkuPriceId = productSkuPrice.getId();
        Long productSkuId = productSkuPrice.getProductSkuId();
        String productSkuCode = productSkuPrice.getProductSkuCode();

        if (productSkuId == null || ruleId == null || productSkuPriceId == null) {
            return false;
        }
        //删除原有绑定
        iProductSkuPriceRuleRelationService.deleteRelationByProductSkuId(productSkuId);
        ProductSkuPriceRuleRelationBo entity =
            new ProductSkuPriceRuleRelationBo(productSkuPriceId, productSkuId, productSkuCode, ruleId);
        return iProductSkuPriceRuleRelationService.insertByBo(entity);
    }

    private List<ProductSkuPriceRuleItem> parseItem(Long ruleId, ProductSkuPriceRuleBo bo) {

        Integer unitPriceCal = bo.getUnitPriceCal();
        BigDecimal unitPrice = bo.getUnitPrice();
        Integer operationFeeCal = bo.getOperationFeeCal();
        BigDecimal operationFee = bo.getOperationFee();
        Integer finalDeliveryFeeCal = bo.getFinalDeliveryFeeCal();
        BigDecimal finalDeliveryFee = bo.getFinalDeliveryFee();
        List<ProductSkuPriceRuleItem> list = new ArrayList<>();
        ProductSkuPriceRuleItem item1 = new ProductSkuPriceRuleItem(ruleId, "", unitPriceCal, unitPrice);
        ProductSkuPriceRuleItem item2 = new ProductSkuPriceRuleItem(ruleId, "", operationFeeCal, operationFee);
        ProductSkuPriceRuleItem item3 = new ProductSkuPriceRuleItem(ruleId, "", finalDeliveryFeeCal, finalDeliveryFee);
        list.add(item1);
        list.add(item2);
        list.add(item3);
        return list;

    }


    /**
     * 计算商品价格提价信息
     *
     * @param itemList
     * @param unitPrice
     * @param operationFee
     * @param finalDeliveryFee
     * @return 返回具体的提价明细
     */
    private ProductSkuPrice calculatePriceItem(List<ProductSkuPriceRuleItemVo> itemList,
                                               BigDecimal unitPrice, BigDecimal operationFee, BigDecimal finalDeliveryFee) {
        ProductSkuPrice price = new ProductSkuPrice();
        BigDecimal unitPriceMd = BigDecimal.ZERO;
        BigDecimal operationFeeMd = BigDecimal.ZERO;
        BigDecimal finalDeliveryFeeMd = BigDecimal.ZERO;

        for (ProductSkuPriceRuleItemVo item : itemList) {
            //计算原价
            if (StrUtil.equals(PriceItemTypeEnum.UnitPrice.name(), item.getPriceItemType())) {
                unitPriceMd =
                    NumberUtil.round(OperationSymbolEnum.calculate(item.getPriceCal(), unitPrice, item.getPriceCalValue()), 2);
                price.setOriginalUnitPrice(unitPrice);
                price.setPlatformUnitPrice(unitPriceMd);
            }
            //计算操作费
            if (StrUtil.equals(PriceItemTypeEnum.OperationFee.name(), item.getPriceItemType())) {
                operationFeeMd =
                    NumberUtil.round(OperationSymbolEnum.calculate(item.getPriceCal(), operationFee, item.getPriceCalValue()), 2);
                price.setOriginalOperationFee(operationFee);
                price.setPlatformOperationFee(operationFeeMd);
            }
            //计算远程派送费
            if (StrUtil.equals(PriceItemTypeEnum.FinalDeliveryFee.name(), item.getPriceItemType())) {
                finalDeliveryFeeMd =
                    NumberUtil.round(OperationSymbolEnum.calculate(item.getPriceCal(), finalDeliveryFee, item.getPriceCalValue()),
                        2);
                price.setOriginalFinalDeliveryFee(finalDeliveryFee);
                price.setPlatformFinalDeliveryFee(finalDeliveryFeeMd);
            }
        }

        BigDecimal originalPickUpPrice = NumberUtil.add(unitPrice, operationFee);
        BigDecimal originalDropShippingPrice = NumberUtil.add(originalPickUpPrice, finalDeliveryFee);
        BigDecimal pickUpPrice = NumberUtil.add(unitPriceMd, operationFeeMd);
        BigDecimal dropShippingPrice = NumberUtil.add(pickUpPrice, finalDeliveryFeeMd);
        price.setOriginalPickUpPrice(originalPickUpPrice);
        price.setOriginalDropShippingPrice(originalDropShippingPrice);
        price.setPlatformPickUpPrice(pickUpPrice);
        price.setPlatformDropShippingPrice(dropShippingPrice);
        return price;
    }

    /**
     * 计算价格涨幅
     *
     * @param value1 原价格
     * @param value2 涨幅后价格
     * @return
     */
    private BigDecimal calculatePriceIncrease(BigDecimal value1, BigDecimal value2) {

        BigDecimal sub = NumberUtil.sub(value2, value1);
        if (NumberUtil.equals(value1, NumberUtil.toBigDecimal(0))) {
            return NumberUtil.toBigDecimal(100);
        } else {
            return NumberUtil.round(NumberUtil.mul(NumberUtil.div(sub, value1), 100), 2);
        }
    }


}
