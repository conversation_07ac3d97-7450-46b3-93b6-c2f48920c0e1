package com.zsmall.product.entity.iservice;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zsmall.common.enums.MyBatisTaskStateType;
import com.zsmall.product.entity.domain.TaskSkuPriceChange;
import com.zsmall.product.entity.mapper.TaskSkuPriceChangeMapper;
import org.springframework.stereotype.Service;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【task_sku_price_change(定时任务-sku价格变更)】的数据库操作Service
* @createDate 2023-06-28 12:31:09
*/
@Service
public class ITaskSkuPriceChangeService extends ServiceImpl<TaskSkuPriceChangeMapper, TaskSkuPriceChange> {


    /**
     * 根据执行之间查询待处理的任务
     * @param executeDate
     * @return
     */
    public List<TaskSkuPriceChange> queryTodoByDate(String executeDate) {
        return lambdaQuery().eq(TaskSkuPriceChange::getExecuteDate, executeDate)
            .eq(TaskSkuPriceChange::getTaskState, MyBatisTaskStateType.Todo.name())
            .orderByAsc(TaskSkuPriceChange::getCreateTime).list();
    }

    /**
     * 根据商品编号和执行日期查询定时任务
     * @param productCode
     * @param executeDate
     * @return
     */
    public TaskSkuPriceChange queryByProductCodeAndDate(String productCode, String executeDate) {
        return lambdaQuery().eq(TaskSkuPriceChange::getProductCode, productCode)
            .eq(TaskSkuPriceChange::getExecuteDate, executeDate)
            .eq(TaskSkuPriceChange::getTaskState, MyBatisTaskStateType.Todo.name()).one();
    }


    /**
     * 根据审核记录id查询价格变更定时任务
     * @param reviewRecordId
     * @return
     */
    public TaskSkuPriceChange queryByReviewRecordId(Long reviewRecordId) {
        List<TaskSkuPriceChange> list = lambdaQuery().eq(TaskSkuPriceChange::getReviewRecordId, reviewRecordId).list();
        if (CollUtil.isNotEmpty(list)) {
            return list.get(0);
        } else {
            return null;
        }
    }
}
