package com.zsmall.product.entity.iservice;

import com.hengjian.common.log.annotation.InMethodLog;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zsmall.product.entity.domain.ProductMappingExtendRakutenInfo;
import com.zsmall.product.entity.mapper.ProductMappingExtendRakutenInfoMapper;
/**
 * 商品映射扩展-Rakuten信息表-数据库接口层
 * <AUTHOR>
 * @date 2023/10/27
 */
@Service
public class IProductMappingExtendRakutenInfoService extends ServiceImpl<ProductMappingExtendRakutenInfoMapper, ProductMappingExtendRakutenInfo> {

    @InMethodLog("根据商品映射表主键查询Rakuten信息表")
    public ProductMappingExtendRakutenInfo queryByProductMappingId(Long productMappingId) {
        return lambdaQuery().eq(ProductMappingExtendRakutenInfo::getProductMappingId, productMappingId).one();
    }

}
