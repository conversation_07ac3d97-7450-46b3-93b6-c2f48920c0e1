package com.zsmall.product.entity.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zsmall.product.entity.domain.ProductSku;
import com.zsmall.product.entity.domain.dto.productSku.*;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 商品SKU统计-Mapper层
 *
 * <AUTHOR>
 * @date 2023/8/10
 */
public interface ProductSkuStatisticsMapper extends BaseMapper<ProductSku> {

    @InterceptorIgnore(tenantLine = "true")
    IPage<ProductSkuOrderManagerDTO> queryProductSkuPage(IPage<ProductSkuOrderManagerDTO> page, @Param("dto") ProductSkuDTO dto);

    @InterceptorIgnore(tenantLine = "true")
    IPage<ProductSkuOrderSupplierDTO> queryProductSkuSupplierPage(Page<ProductSkuOrderSupplierDTO> page, @Param("dto") ProductSkuDTO dto);

    @InterceptorIgnore(tenantLine = "true")
    IPage<ProductSkuOrderDistributorDTO> queryProductSkuDistributorPage(IPage<ProductSkuOrderDistributorDTO> page, @Param("dto") ProductSkuDTO dto);

    @InterceptorIgnore(tenantLine = "true")
    List<ProductSkuByDayDto> getDayProductSkuByProductSkuCode(@Param("productSkuCode") String productSkuCode, @Param("startTime") String startTime, @Param("endTime") String endTime);

    @InterceptorIgnore(tenantLine = "true")
    List<AnalysisProductSkuByDayDto> getProductSkuOrdersByDay(@Param("productSkuCode") String productSkuCode, @Param("startTime") String startTime, @Param("endTime") String endTime);

    @InterceptorIgnore(tenantLine = "true")
    List<AnalysisProductSkuByDayDto> getDayProductDropByProductSkuCode(@Param("productSkuCode") String productSkuCode, @Param("startTime") String startTime, @Param("endTime") String endTime);

    @InterceptorIgnore(tenantLine = "true")
    List<AnalysisProductSkuByDayDto> getProductSkuOrdersByChannel(@Param("productSkuCode") String productSkuCode, @Param("startTime") String startTime, @Param("endTime") String endTime);

    @InterceptorIgnore(tenantLine = "true")
    List<DistributorDataDto> getDisListByProductSkuCode(@Param("productSkuCode") String productSkuCode, @Param("startTime") String startTime, @Param("endTime") String endTime);

    @InterceptorIgnore(tenantLine = "true")
    ProductSkuByDayDto getDayProductSkuByProductSkuCodeAndEndTime(@Param("productSkuCode") String productSkuCode, @Param("endTime") String endTime);

    @InterceptorIgnore(tenantLine = "true")
    List<AnalysisProductSkuByDayDto> getProductSkuOrderDisByDay(@Param("tenantId") String tenantId, @Param("startTime") String startTime, @Param("endTime") String endTime);

    @InterceptorIgnore(tenantLine = "true")
    List<AnalysisProductSkuByDayDto> getProductSkuOrderDistributionByDay(@Param("tenantId") String tenantId, @Param("startTime") String startTime, @Param("endTime") String endTime);

    @InterceptorIgnore(tenantLine = "true")
    List<AnalysisProductSkuByDayDto> getProductSkuOrdersDisByChannel(@Param("tenantId") String tenantId, @Param("startTime") String startTime, @Param("endTime") String endTime);

    @InterceptorIgnore(tenantLine = "true")
    List<DistributorOrdersDataDto> getSkuListByTenantId(@Param("tenantId") String tenantId, @Param("startTime") String startTime, @Param("endTime") String endTime);

    @InterceptorIgnore(tenantLine = "true")
    List<DistributorOrdersDataDto> getSupListByTenantId(@Param("tenantId") String tenantId, @Param("startTime") String startTime, @Param("endTime") String endTime);

    @InterceptorIgnore(tenantLine = "true")
    List<AnalysisProductSkuByDayDto> getProductSkuOrderSupByDay(@Param("tenantId") String tenantId, @Param("startTime") String startTime, @Param("endTime") String endTime);

    @InterceptorIgnore(tenantLine = "true")
    List<ProductSkuByDayDto> getDayProductSkuByTenantId(@Param("tenantId") String tenantId, @Param("startTime") String startTime, @Param("endTime") String endTime);

    @InterceptorIgnore(tenantLine = "true")
    ProductSkuByDayDto getDayProductSkuByTenantIdAndEndTime(@Param("tenantId") String tenantId, @Param("endTime") String endTime);

    @InterceptorIgnore(tenantLine = "true")
    List<AnalysisProductSkuByDayDto> getProductSkuOrdersSupByChannel(@Param("tenantId") String tenantId, @Param("startTime") String startTime, @Param("endTime") String endTime);

    @InterceptorIgnore(tenantLine = "true")
    List<DistributorDataDto> getDisListByTenantId(@Param("tenantId") String tenantId, @Param("startTime") String startTime, @Param("endTime") String endTime);

    @InterceptorIgnore(tenantLine = "true")
    List<AnalysisProductSkuByDayDto> getDayProductDropByTenantId(@Param("tenantId") String tenantId, @Param("startTime") String startTime, @Param("endTime") String endTime);



}
