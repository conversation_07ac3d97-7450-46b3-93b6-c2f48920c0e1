package com.zsmall.product.entity.mapper;

import com.hengjian.common.mybatis.core.mapper.BaseMapperPlus;
import com.zsmall.product.entity.domain.ProductSkuPriceLog;
import com.zsmall.product.entity.domain.vo.productSkuPrice.ProductSkuPriceLogVo;

/**
* <AUTHOR>
* @description 针对表【product_sku_price_log(商品sku价格变动日志表)】的数据库操作Mapper
* @createDate 2023-05-12 10:16:57
* @Entity com.zsmall.product.entity.domain.ProductSkuPriceLog
*/
public interface ProductSkuPriceLogMapper extends BaseMapperPlus<ProductSkuPriceLog, ProductSkuPriceLogVo> {

}




