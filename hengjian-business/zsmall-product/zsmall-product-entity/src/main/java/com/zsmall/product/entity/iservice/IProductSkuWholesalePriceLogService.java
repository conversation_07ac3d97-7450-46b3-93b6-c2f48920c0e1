package com.zsmall.product.entity.iservice;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hengjian.common.core.utils.MapstructUtils;
import com.hengjian.common.log.annotation.InMethodLog;
import com.hengjian.common.mybatis.core.page.PageQuery;
import com.hengjian.common.mybatis.core.page.TableDataInfo;
import com.zsmall.product.entity.domain.ProductSkuWholesalePrice;
import com.zsmall.product.entity.domain.ProductSkuWholesalePriceLog;
import com.zsmall.product.entity.domain.bo.wholesale.ProductSkuWholesalePriceLogBo;
import com.zsmall.product.entity.domain.vo.wholesale.ProductSkuWholesalePriceLogVo;
import com.zsmall.product.entity.mapper.ProductSkuWholesalePriceLogMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 国外现货批发商品SKU价格日志Service业务层处理
 *
 * <AUTHOR> Li
 * @date 2023-05-29
 */
@RequiredArgsConstructor
@Service
public class IProductSkuWholesalePriceLogService extends ServiceImpl<ProductSkuWholesalePriceLogMapper, ProductSkuWholesalePriceLog> {

    private final ProductSkuWholesalePriceLogMapper baseMapper;

    /**
     * 查询国外现货批发商品SKU价格日志
     */
    public ProductSkuWholesalePriceLogVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询国外现货批发商品SKU价格日志列表
     */
    public TableDataInfo<ProductSkuWholesalePriceLogVo> queryPageList(ProductSkuWholesalePriceLogBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<ProductSkuWholesalePriceLog> lqw = buildQueryWrapper(bo);
        Page<ProductSkuWholesalePriceLogVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询国外现货批发商品SKU价格日志列表
     */
    public List<ProductSkuWholesalePriceLogVo> queryList(ProductSkuWholesalePriceLogBo bo) {
        LambdaQueryWrapper<ProductSkuWholesalePriceLog> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<ProductSkuWholesalePriceLog> buildQueryWrapper(ProductSkuWholesalePriceLogBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<ProductSkuWholesalePriceLog> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getProductId() != null, ProductSkuWholesalePriceLog::getProductId, bo.getProductId());
        lqw.eq(bo.getProductSkuId() != null, ProductSkuWholesalePriceLog::getProductSkuId, bo.getProductSkuId());
        lqw.eq(bo.getProductWholesaleTieredPriceLogId() != null, ProductSkuWholesalePriceLog::getProductWholesaleTieredPriceLogId, bo.getProductWholesaleTieredPriceLogId());
        lqw.eq(bo.getProductSkuWholesalePriceId() != null, ProductSkuWholesalePriceLog::getProductSkuWholesalePriceId, bo.getProductSkuWholesalePriceId());
        lqw.eq(bo.getTieredPriceId() != null, ProductSkuWholesalePriceLog::getTieredPriceId, bo.getTieredPriceId());
        lqw.eq(bo.getOriginUnitPrice() != null, ProductSkuWholesalePriceLog::getOriginUnitPrice, bo.getOriginUnitPrice());
        lqw.eq(bo.getPlatformUnitPrice() != null, ProductSkuWholesalePriceLog::getPlatformUnitPrice, bo.getPlatformUnitPrice());
        return lqw;
    }

    /**
     * 新增国外现货批发商品SKU价格日志
     */
    public Boolean insertByBo(ProductSkuWholesalePriceLogBo bo) {
        ProductSkuWholesalePriceLog add = MapstructUtils.convert(bo, ProductSkuWholesalePriceLog.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改国外现货批发商品SKU价格日志
     */
    public Boolean updateByBo(ProductSkuWholesalePriceLogBo bo) {
        ProductSkuWholesalePriceLog update = MapstructUtils.convert(bo, ProductSkuWholesalePriceLog.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(ProductSkuWholesalePriceLog entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除国外现货批发商品SKU价格日志
     */
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    @InMethodLog(value = "批量添加批发商品SKU价格日数据")
    public void batchSavePriceLog(List<ProductSkuWholesalePrice> priceList) {
        if (CollUtil.isEmpty(priceList)) {
            return;
        }
        List<ProductSkuWholesalePriceLog> logList = new ArrayList<>();
        for (ProductSkuWholesalePrice price: priceList) {
            ProductSkuWholesalePriceLog priceLog = BeanUtil.copyProperties(price, ProductSkuWholesalePriceLog.class);
            priceLog.setId(null);
            priceLog.setTieredPriceId(price.getId());
            logList.add(priceLog);
        }
        this.saveBatch(logList);
    }
}
