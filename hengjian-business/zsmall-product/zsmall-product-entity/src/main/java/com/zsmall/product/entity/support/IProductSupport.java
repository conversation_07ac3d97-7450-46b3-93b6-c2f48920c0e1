package com.zsmall.product.entity.support;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.hengjian.common.core.enums.TenantType;
import com.hengjian.common.core.utils.MapstructUtils;
import com.hengjian.common.log.annotation.InMethodLog;
import com.hengjian.extend.utils.SystemEventUtils;
import com.hengjian.system.domain.dto.NoticePublishDto;
import com.zsmall.common.enums.BusinessParameterType;
import com.zsmall.common.enums.MyBatisTaskStateType;
import com.zsmall.common.enums.common.ChannelTypeEnum;
import com.zsmall.common.enums.order.OrderExceptionEnum;
import com.zsmall.common.enums.product.AttributeTypeEnum;
import com.zsmall.common.enums.product.ProductReviewTypeEnum;
import com.zsmall.common.enums.product.ProductVerifyStateEnum;
import com.zsmall.common.enums.product.StockManagerEnum;
import com.zsmall.common.service.BusinessParameterService;
import com.zsmall.product.entity.domain.*;
import com.zsmall.product.entity.domain.bo.product.ProductImportBo;
import com.zsmall.product.entity.domain.bo.productSku.ProductPriceImportBo;
import com.zsmall.product.entity.domain.bo.productSku.ProductSkuImportBo;
import com.zsmall.product.entity.domain.dto.productSkuPrice.ProductPriceChangeDto;
import com.zsmall.product.entity.iservice.*;
import com.zsmall.warehouse.entity.domain.event.ThirdWarehouseEvent;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2024/9/27 16:05
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class IProductSupport {
    private final BusinessParameterService businessParameterService;
    private final ITaskSkuPriceChangeService iTaskSkuPriceChangeService;
    private final ITaskStockSyncService iTaskStockSyncService;
    private final IProductMappingService iProductMappingService;
    private final IProductService iProductService;
    private final IProductSkuService iProductSkuService;
    private final IProductAttributeService iProductAttributeService;
    private final IProductSkuDetailService iProductSkuDetailService;
    private final IProductSkuPriceService iProductSkuPriceService;
    private final IProductSkuStockService iProductSkuStockService;
    private final IProductSkuAttributeService iProductSkuAttributeService;
    private final IProductSkuAttachmentService iProductSkuAttachmentService;
    private final IProductCategoryRelationCall iProductCategoryRelationService;

    private final IRuleLevelProductPriceService iRuleLevelProductPriceService;

    /**
     * 保存商品导入数据
     * @param tenantId
     * @param productImportBoList
     */
    @Transactional(rollbackFor = { Exception.class, RuntimeException.class })
    public void saveProductImport(String tenantId, Collection<ProductImportBo> productImportBoList) {
        if (CollUtil.isNotEmpty(productImportBoList)) {
            for (ProductImportBo productImportBo : productImportBoList) {
                Product product = MapstructUtils.convert(productImportBo, Product.class);
                product.setTenantId(tenantId);
                iProductService.save(product);
                Long productId = product.getId();

                List<ProductCategoryRelation> categoryRelationList = productImportBo.getCategoryRelationList();
                if (CollUtil.isNotEmpty(categoryRelationList)) {
                    categoryRelationList.forEach(item -> item.setProductId(productId));
                    iProductCategoryRelationService.insertBatch(categoryRelationList);
                }

                List<ProductAttribute> productAttributeList = productImportBo.getProductAttributeList();
                if (CollUtil.isNotEmpty(productAttributeList)) {
                    productAttributeList.forEach(item -> item.setProductId(productId));
                    iProductAttributeService.saveBatch(productAttributeList);
                }

                List<ProductAttribute> optionalSpecList = productImportBo.getOptionalSpecList();
                if (CollUtil.isNotEmpty(optionalSpecList)) {
                    optionalSpecList.forEach(item -> item.setProductId(productId));
                    iProductAttributeService.saveBatch(optionalSpecList);
                }

                List<ProductSkuImportBo> productSkuVoList = productImportBo.getProductSkuVoList();
                for (ProductSkuImportBo productSkuImportBo : productSkuVoList) {
                    ProductSku productSku = MapstructUtils.convert(productSkuImportBo, ProductSku.class);
                    productSku.setProductId(productId);
                    productSku.setTenantId(tenantId);
                    iProductSkuService.save(productSku);
                    Long productSkuId = productSku.getId();
                    StockManagerEnum stockManager = productSku.getStockManager();
                    String productSkuCode = productSku.getProductSkuCode();

                    ProductSkuDetail skuDetail = productSkuImportBo.getSkuDetail();
                    ProductSkuPrice skuPrice = productSkuImportBo.getSkuPrice();
                    skuDetail.setProductSkuId(productSkuId);
                    skuPrice.setProductSkuId(productSkuId);

                    List<ProductSkuStock> skuStockList = productSkuImportBo.getSkuStockList();
                    skuStockList.forEach(item -> item.setTenantId(tenantId));
                    List<ProductSkuAttribute> skuAttributeList = productSkuImportBo.getSkuAttributeList();
                    skuAttributeList.forEach(item -> item.setProductSkuId(productSkuId));
                    List<ProductSkuAttachment> skuAttachmentList = productSkuImportBo.getSkuAttachmentList();
                    skuAttachmentList.forEach(item -> item.setProductSkuId(productSkuId));

                    iProductSkuDetailService.save(skuDetail);
                    iProductSkuPriceService.save(skuPrice);
                    iProductSkuStockService.saveBatch(skuStockList);
                    iProductSkuAttributeService.saveBatch(skuAttributeList);
                    iProductSkuAttachmentService.saveBatch(skuAttachmentList);



                    // 非自有仓库需要查询第三方库存
                    if (!StockManagerEnum.OwnWarehouse.equals(stockManager)) {
                        ThirdWarehouseEvent.queryStock(stockManager, productSkuCode);
                    }
                }
            }
        }
    }

    @Transactional(rollbackFor = { Exception.class, RuntimeException.class })
    public void saveProductImportForPriceV2(String tenantId, Collection<ProductPriceImportBo> productImportBoList) {
        if (CollUtil.isNotEmpty(productImportBoList)) {
            // 盘查是否缺少了 item.setProductId(productId); item.setProductSkuId(productSkuId);
            List<RuleLevelProductPrice> allRuleLevelProductPrices = productImportBoList.stream()
                                                                                       .flatMap(bo -> Optional.ofNullable(bo.getRuleLevelProductPrices()).orElse(Collections.emptyList()).stream())
                                                                                       .collect(Collectors.toList());

            List<RuleLevelProductPrice> allRuleLevelProductPricesForUpdate = productImportBoList.stream()
                                                                                                .flatMap(bo -> Optional.ofNullable(bo.getRuleLevelProductPricesForUpdate()).orElse(Collections.emptyList()).stream())
                                                                                                .collect(Collectors.toList());
            if (CollUtil.isNotEmpty(allRuleLevelProductPrices)){
                iRuleLevelProductPriceService.saveBatch(allRuleLevelProductPrices);
            }

            if(CollUtil.isNotEmpty(allRuleLevelProductPricesForUpdate)){
                iRuleLevelProductPriceService.updateBatchById(allRuleLevelProductPricesForUpdate);
            }


        }
    }
    /**
     * 根据条件判断，生成价格变更，定时任务
     * @param effectiveType
     * @param effectiveDate
     * @param priceRecords
     */
    @InMethodLog(value = "根据条件判断生成价格变更，定时任务")
    public void generateTaskSkuPriceChange(String effectiveType, Date effectiveDate, List<ProductReviewRecord> priceRecords) {
        if (CollUtil.isNotEmpty(priceRecords)) {
            String effectiveDateString = "";
            String taskState = MyBatisTaskStateType.Todo.name();

            if (StrUtil.equals(effectiveType, "NOW")) {
                effectiveDateString = DateUtil.format(new Date(), "yyyy-MM-dd");
                taskState = MyBatisTaskStateType.Finished.name();
            } else {
                if (effectiveDate != null) {
                    effectiveDateString = DateUtil.format(effectiveDate, "yyyy-MM-dd");
                } else {
                    Integer days = businessParameterService.getValueFromInteger(BusinessParameterType.PRICE_CHANGE_EFFECTIVE_DAYS);
                    Date newExecuteDate = DateUtil.offsetDay(new Date(), days);
                    effectiveDateString = DateUtil.format(newExecuteDate, "yyyy-MM-dd");
                }
            }
            List<TaskSkuPriceChange> taskSkuPriceChanges = new ArrayList<>();
            // 指定生效日期的价格变更，需要生成定时任务
            for (ProductReviewRecord reviewRecord : priceRecords) {
                ProductReviewTypeEnum reviewType = reviewRecord.getReviewType();
                ProductVerifyStateEnum reviewState = reviewRecord.getReviewState();

                if (ProductVerifyStateEnum.Accepted.equals(reviewState) && !ProductReviewTypeEnum.NewProduct.equals(reviewType)) {
                    TaskSkuPriceChange newChangeEntity = new TaskSkuPriceChange();
                    newChangeEntity.setProductCode(reviewRecord.getProductCode());
                    newChangeEntity.setReviewRecordId(reviewRecord.getId());
                    newChangeEntity.setTaskState(taskState);
                    newChangeEntity.setExecuteDate(effectiveDateString);
                    taskSkuPriceChanges.add(newChangeEntity);
                }
            }
            if(CollUtil.isNotEmpty(taskSkuPriceChanges)) {
                iTaskSkuPriceChangeService.saveBatch(taskSkuPriceChanges);
            }
        }
    }

    /**
     * 根据条件判断，生成价格变更，定时任务
     * @param effectiveType
     * @param executeDate
     * @param reviewRecord
     */
    @InMethodLog(value = "根据条件判断生成价格变更，定时任务")
    public void generateTaskSkuPriceChange(String effectiveType, String executeDate, ProductReviewRecord reviewRecord) {
        ProductReviewTypeEnum reviewType = reviewRecord.getReviewType();
        ProductVerifyStateEnum reviewState = reviewRecord.getReviewState();
        if (ProductVerifyStateEnum.Accepted.equals(reviewState) && !ProductReviewTypeEnum.NewProduct.equals(reviewType)) {
            TaskSkuPriceChange newChangeEntity = new TaskSkuPriceChange();

            newChangeEntity.setProductCode(reviewRecord.getProductCode());
            newChangeEntity.setReviewRecordId(reviewRecord.getId());
            newChangeEntity.setTaskState(MyBatisTaskStateType.Todo.name());

            if (StrUtil.equals(effectiveType, "NOW")) {
                String format = DateUtil.format(new Date(), "yyyy-MM-dd");
                newChangeEntity.setExecuteDate(format);
                newChangeEntity.setTaskState(MyBatisTaskStateType.Finished.name());
            } else {
                newChangeEntity.setTaskState(MyBatisTaskStateType.Todo.name());
                // 传入执行时间时直接去用，不传时取出参数表中的天数，重新计算一个新的生效时间
                if (executeDate != null) {
                    newChangeEntity.setExecuteDate(executeDate);
                } else {
                    Integer days = businessParameterService.getValueFromInteger(BusinessParameterType.PRICE_CHANGE_EFFECTIVE_DAYS);
                    Date newExecuteDate = DateUtil.offsetDay(new Date(), days);
                    newChangeEntity.setExecuteDate(DateUtil.format(newExecuteDate, "yyyy-MM-dd"));
                }
            }
            iTaskSkuPriceChangeService.save(newChangeEntity);
        }
    }

    /**
     * 获取不同渠道价格
     * 注：Wayfair都是自提价，其他平台都是代发价
     * @param channelType
     * @param productSkuPrice
     * @return
     */
    @InMethodLog(value = "获取不同渠道价格")
    public BigDecimal getProductSkuPrice(ChannelTypeEnum channelType, ProductSkuPrice productSkuPrice) {
        return ChannelTypeEnum.Wayfair.equals(channelType) ? productSkuPrice.getPlatformPickUpPrice() : productSkuPrice.getPlatformDropShippingPrice();
    }

    @InMethodLog(value = "设置商品属性")
    public ProductAttribute setProductAttribute(Long id, Long productId, String key, Object value, AttributeTypeEnum attributeType, Long sourceId, Integer sort) {
        ProductAttribute productAttribute = new ProductAttribute();
        if (id != null) {
            productAttribute.setId(id);
        }

        productAttribute.setProductId(productId);
        productAttribute.setAttributeName(key);
        productAttribute.addAttributeValue(value);
        productAttribute.setAttributeType(attributeType);
        productAttribute.setAttributeSourceId(sourceId);
        productAttribute.setAttributeSort(sort);
        return productAttribute;
    }

    /**
     * 封装数据，准备推送商品价格变动
     * @param productPriceChangeList
     */
    public void toPushProductPriceChangeEvent(List<ProductPriceChangeDto> productPriceChangeList) {
        log.info("价格变更List，搜集后统一发送通知 productPriceChangeList = {}", JSONUtil.toJsonStr(productPriceChangeList));

        if (CollUtil.isNotEmpty(productPriceChangeList)) {
            DateTime nowDateTime = DateTime.now();
            String nowDateStr = DateUtil.format(nowDateTime, "yyyy-MM-dd");
            String now = DateUtil.format(nowDateTime, "yyyy-MM-dd HH:mm:ss");
            String template = businessParameterService.getValueFromString(BusinessParameterType.PRICE_CHANGE_TEMPLATE);

            List<NoticePublishDto> noticePublishDtos = new ArrayList<>();
            productPriceChangeList.forEach(changeDto -> {
                //1、获取商品映射数据 2、修改映射数据的最总价格
                List<ProductMapping> productMappings = iProductMappingService.queryListByProductSkuCode(changeDto.getProductSkuCode());
                List<String> collect = productMappings.stream().map(ProductMapping::getTenantId).collect(Collectors.toList());
                changeDto.setTenantIdList(collect);

                String effectiveTime = changeDto.getEffectiveTime();
                if (StrUtil.isBlank(effectiveTime)) {
                    effectiveTime = nowDateStr;
                }

                String content = StrUtil.format(template, now,
                    changeDto.getProductSkuCode(), changeDto.getBefore_pickUpPrice(), changeDto.getBefore_dropShippingPrice(),
                    changeDto.getAfter_pickUpPrice(), changeDto.getAfter_dropShippingPrice(), effectiveTime);

                List<String> tenantTypes = new ArrayList<>();
                tenantTypes.add(TenantType.Distributor.name());
                tenantTypes.add(TenantType.Supplier.name());

                NoticePublishDto noticePublishDto = new NoticePublishDto("Price Change Notice", content, collect, tenantTypes);
                noticePublishDtos.add(noticePublishDto);
            });

            if (CollUtil.isNotEmpty(noticePublishDtos)) {
                // 创建消息公告
                SystemEventUtils.publishNotices(noticePublishDtos);
            }
        }
    }

    /**
     * 根据productSkuCode 更新商品SPU表 库存推送时间
     * @param productSkuCode  Sku唯一编号
     */
    public void updateProductInventoryPushTime(String productSkuCode) {
        iProductService.updateProductInventoryPushTime(productSkuCode);
    }







    @InMethodLog("发货方式变动修改订单的异常状态")
    public void updateOrderExceptionStatusByShippingWay(Long channelId,OrderExceptionEnum orderExceptionEnum,String productSkuCode) {

    }
}
