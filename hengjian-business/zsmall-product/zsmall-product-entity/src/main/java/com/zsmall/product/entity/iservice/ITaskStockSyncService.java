package com.zsmall.product.entity.iservice;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hengjian.common.core.utils.MapstructUtils;
import com.hengjian.common.core.utils.StringUtils;
import com.hengjian.common.log.annotation.InMethodLog;
import com.hengjian.common.mybatis.core.page.PageQuery;
import com.hengjian.common.mybatis.core.page.TableDataInfo;
import com.zsmall.common.enums.TaskStateEnum;
import com.zsmall.product.entity.domain.TaskStockSync;
import com.zsmall.product.entity.domain.bo.TaskStockSyncBo;
import com.zsmall.product.entity.domain.vo.TaskStockSyncVo;
import com.zsmall.product.entity.mapper.TaskStockSyncMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 任务-库存同步Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-07-10
 */
@RequiredArgsConstructor
@Service
public class ITaskStockSyncService extends ServiceImpl<TaskStockSyncMapper, TaskStockSync> {

    private final TaskStockSyncMapper baseMapper;

    /**
     * 查询任务-库存同步
     */
    public TaskStockSyncVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询任务-库存同步列表
     */
    public TableDataInfo<TaskStockSyncVo> queryPageList(TaskStockSyncBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<TaskStockSync> lqw = buildQueryWrapper(bo);
        Page<TaskStockSyncVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询任务-库存同步列表
     */
    public List<TaskStockSyncVo> queryList(TaskStockSyncBo bo) {
        LambdaQueryWrapper<TaskStockSync> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<TaskStockSync> buildQueryWrapper(TaskStockSyncBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<TaskStockSync> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getProductSkuId() != null, TaskStockSync::getProductSkuId, bo.getProductSkuId());
        lqw.eq(StringUtils.isNotBlank(bo.getTaskState()), TaskStockSync::getTaskState, bo.getTaskState());
        lqw.eq(StringUtils.isNotBlank(bo.getTaskExecuteMessage()), TaskStockSync::getTaskExecuteMessage, bo.getTaskExecuteMessage());
        return lqw;
    }

    /**
     * 新增任务-库存同步
     */
    public Boolean insertByBo(TaskStockSyncBo bo) {
        TaskStockSync add = MapstructUtils.convert(bo, TaskStockSync.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改任务-库存同步
     */
    public Boolean updateByBo(TaskStockSyncBo bo) {
        TaskStockSync update = MapstructUtils.convert(bo, TaskStockSync.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(TaskStockSync entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除任务-库存同步
     */
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    @InMethodLog("根据productSkuId获取库存同步数据")
    public Long countByStateProductSkuId(TaskStateEnum taskStateEnum, Long productSkuId) {
        LambdaQueryWrapper<TaskStockSync> lqw = Wrappers.lambdaQuery();
        lqw.eq(TaskStockSync::getProductSkuId, productSkuId);
        lqw.eq(TaskStockSync::getTaskState, taskStateEnum);
        return baseMapper.selectCount(lqw);
    }

    @InMethodLog("是否存在等待同步的数据")
    public Boolean existByStateProductSkuId(TaskStateEnum taskStateEnum, Long productSkuId) {
        LambdaQueryWrapper<TaskStockSync> lqw = Wrappers.lambdaQuery();
        lqw.eq(TaskStockSync::getProductSkuId, productSkuId);
        lqw.eq(TaskStockSync::getTaskState, taskStateEnum);
        return baseMapper.exists(lqw);
    }

    @InMethodLog("根据任务状态查询")
    public List<TaskStockSync> queryByTaskState(TaskStateEnum taskState) {
        LambdaQueryWrapper<TaskStockSync> lqw = Wrappers.lambdaQuery();
        lqw.eq(TaskStockSync::getTaskState, taskState);
        lqw.orderByAsc(TaskStockSync::getCreateTime);
        return baseMapper.selectList(lqw);
    }
}
