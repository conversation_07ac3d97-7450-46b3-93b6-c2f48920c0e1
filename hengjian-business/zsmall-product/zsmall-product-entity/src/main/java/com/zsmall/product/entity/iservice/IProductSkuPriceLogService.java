package com.zsmall.product.entity.iservice;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zsmall.common.enums.priceLog.PriceOperateLog;
import com.zsmall.product.entity.domain.ProductSkuPrice;
import com.zsmall.product.entity.domain.ProductSkuPriceLog;
import com.zsmall.product.entity.mapper.ProductSkuPriceLogMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 商品sku价格变动日志Service业务层处理
 *
 * <AUTHOR> Li
 * @date 2023-05-22
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class IProductSkuPriceLogService extends ServiceImpl<ProductSkuPriceLogMapper, ProductSkuPriceLog> {

    private final ProductSkuPriceLogMapper baseMapper;

    /**
     * 记录商品价格日志
     *
     * @param priceEntity
     * @param logType
     */
    public void recordPriceChanges(ProductSkuPrice priceEntity, String logType) {

        if(StrUtil.isBlank(logType)){
            logType = PriceOperateLog.Update.name();
        }
        ProductSkuPriceLog productSkuPriceLogEntity = BeanUtil.toBean(priceEntity, ProductSkuPriceLog.class);
        log.info("productSkuPriceLogEntity:{}", JSONUtil.toJsonStr(productSkuPriceLogEntity));
        String finalLogType = logType;
        ThreadUtil.execute(() -> {
            // 新增逻辑没有id
            if(PriceOperateLog.Update.name().equals(finalLogType)||PriceOperateLog.Delete.name().equals(finalLogType)){
                Long productSkuPriceId = productSkuPriceLogEntity.getId();
                productSkuPriceLogEntity.setProductSkuPriceId(productSkuPriceId);
            }
            productSkuPriceLogEntity.setId(null);
            productSkuPriceLogEntity.setLogType(finalLogType);
            this.save(productSkuPriceLogEntity);
        });
    }

}
