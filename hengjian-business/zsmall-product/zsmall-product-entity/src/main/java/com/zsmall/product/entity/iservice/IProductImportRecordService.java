package com.zsmall.product.entity.iservice;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hengjian.common.log.annotation.InMethodLog;
import com.hengjian.common.mybatis.core.page.PageQuery;
import com.hengjian.common.mybatis.core.page.TableDataInfo;
import com.zsmall.common.enums.orderImportRecord.ImportStateEnum;
import com.zsmall.product.entity.domain.ProductImportRecord;
import com.zsmall.product.entity.domain.vo.productImport.ProductImportRecordVo;
import com.zsmall.product.entity.mapper.ProductImportRecordMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 商品导入记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-06-08
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class IProductImportRecordService extends ServiceImpl<ProductImportRecordMapper, ProductImportRecord> {

    public boolean existImportRecordNo(String importRecordNo) {
        return baseMapper.existImportRecordNo(importRecordNo);
    }

    /**
     * 查询订单导入记录列表
     */
    public TableDataInfo<ProductImportRecordVo> queryPageList(PageQuery pageQuery) {
        Page<ProductImportRecordVo> result = baseMapper.queryPage(pageQuery.build());
        return TableDataInfo.build(result);
    }

    @InMethodLog("根据记录编号查询")
    public ProductImportRecord queryByRecordNo(String recordNo) {
        return lambdaQuery().eq(ProductImportRecord::getImportRecordNo, recordNo).one();
    }

    @InMethodLog("根据记录编号和状态查询")
    public ProductImportRecord queryByRecordNoAndState(String recordNo, ImportStateEnum importState) {
        return lambdaQuery().eq(ProductImportRecord::getImportRecordNo, recordNo)
            .eq(ProductImportRecord::getImportState, importState).one();
    }

    @InMethodLog("是否存在导入中的记录")
    public Boolean existImportingRecord() {
        return lambdaQuery().eq(ProductImportRecord::getImportState, ImportStateEnum.Importing).count() > 0;
    }


}
