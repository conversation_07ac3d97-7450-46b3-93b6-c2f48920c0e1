package com.zsmall.product.entity.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.hengjian.common.mybatis.core.mapper.BaseMapperPlus;
import com.zsmall.product.entity.domain.ProductGlobalAttribute;
import com.zsmall.product.entity.domain.vo.ProductGlobalAttributeVo;
import com.zsmall.product.entity.domain.vo.productGlobalAttribute.ProductGlobalAttributeSimpleVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 商品全局属性Mapper接口
 *
 * <AUTHOR>
 * @date 2023-05-19
 */
public interface ProductGlobalAttributeMapper extends BaseMapperPlus<ProductGlobalAttribute, ProductGlobalAttributeVo> {


    /**
     * 根据商品分类主键查询关联的属性
     *
     * @param productCategoryId
     * @return
     */
    List<ProductGlobalAttributeSimpleVo> queryByProductCategoryId(@Param("productCategoryId") Long productCategoryId,
                                                                  @Param("attributeBelong") String attributeBelong,
                                                                  @Param("attributeScope") String attributeScope);

    /**
     * 根据商品分类主键和是否必填查询关联的属性
     *
     * @param productCategoryId
     * @return
     */
    List<ProductGlobalAttributeSimpleVo> queryByProductCategoryIdAndRequired(@Param("productCategoryId") Long productCategoryId,
                                                                             @Param("attributeBelong") String attributeBelong,
                                                                             @Param("attributeScope") String attributeScope,
                                                                             @Param("isRequired") Boolean isRequired);

    /**
     * 查询商品全局属性列表（提供给分类绑定属性使用）
     *
     * @param attributeBelong
     * @param attributeScope
     * @return
     */
    @InterceptorIgnore(tenantLine = "true")
    List<ProductGlobalAttributeSimpleVo> queryForCategory(@Param("attributeBelong") String attributeBelong,
                                                          @Param("attributeScope") String attributeScope);

    ProductGlobalAttribute queryByAttributeNameAndScopeAndCategory(@Param("tenantId") String tenantId,
        @Param("attributeName") String attributeName,
        @Param("attributeScope") String attributeScope,
        @Param("belongCategoryId") Long belongCategoryId);

}
