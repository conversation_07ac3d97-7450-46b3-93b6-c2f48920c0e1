package com.zsmall.product.entity.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.hengjian.common.mybatis.core.mapper.BaseMapperPlus;
import com.zsmall.product.entity.domain.ProductSkuPriceRuleRelation;
import com.zsmall.product.entity.domain.vo.productSkuPrice.ProductSkuPriceRuleRelationVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【product_sku_price_rule_relation(商品sku价格计算公式关联表)】的数据库操作Mapper
* @createDate 2023-05-22 16:56:33
* @Entity generator.domain.ProductSkuPriceRuleRelation
*/
public interface ProductSkuPriceRuleRelationMapper extends BaseMapperPlus<ProductSkuPriceRuleRelation, ProductSkuPriceRuleRelationVo> {

    /**
     * 通过RuleCode获取itemNos
     * @param ruleCode
     * @return
     */
    List<String> getItemNosByRuleCode(@Param("ruleCode") String ruleCode);

    /**
     * 根据ItemNo查询定价关联
     * @param productSkuCode
     * @return
     */
    @InterceptorIgnore(tenantLine = "true")
    ProductSkuPriceRuleRelation getByProductSkuCode(@Param("productSkuCode") String productSkuCode);

}




