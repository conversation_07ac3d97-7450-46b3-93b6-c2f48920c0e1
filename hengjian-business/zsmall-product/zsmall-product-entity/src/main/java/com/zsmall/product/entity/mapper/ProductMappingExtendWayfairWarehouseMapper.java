package com.zsmall.product.entity.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.hengjian.common.mybatis.core.mapper.BaseMapperPlus;
import com.zsmall.product.entity.domain.ProductMappingExtendWayfairWarehouse;
import com.zsmall.product.entity.domain.vo.productMapping.ProductMappingExtendWayfairWarehouseVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 商品映射扩展-Wayfair仓库Mapper接口
 *
 * <AUTHOR>
 * @date 2023-06-21
 */
public interface ProductMappingExtendWayfairWarehouseMapper extends BaseMapperPlus<ProductMappingExtendWayfairWarehouse, ProductMappingExtendWayfairWarehouseVo> {

    @InterceptorIgnore(tenantLine = "true")
    List<ProductMappingExtendWayfairWarehouse> queryByThirdWarehouseId(@Param("thirdWarehouseId") String thirdWarehouseId,
                                                                       @Param("productSkuCode") String productSkuCode,
                                                                       @Param("quantity") Integer quantity,
                                                                       @Param("channelId") Long channelId);

}
