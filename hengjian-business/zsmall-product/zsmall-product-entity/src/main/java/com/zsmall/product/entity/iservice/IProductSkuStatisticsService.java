package com.zsmall.product.entity.iservice;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.TimeInterval;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hengjian.common.log.annotation.InMethodLog;
import com.zsmall.product.entity.domain.ProductSku;
import com.zsmall.product.entity.domain.dto.productSku.*;
import com.zsmall.product.entity.mapper.ProductSkuStatisticsMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 商品SKU统计-数据库层
 *
 * <AUTHOR>
 * @date 2023/8/10
 */
@Slf4j
@Service
public class IProductSkuStatisticsService extends ServiceImpl<ProductSkuStatisticsMapper, ProductSku>  {

    @InMethodLog(value = "查询productShu列表")
    public IPage<ProductSkuOrderManagerDTO> getProductSkuPageByDto(Page<ProductSkuOrderManagerDTO> page, ProductSkuDTO productSkuDTO) {
        TimeInterval timer = DateUtil.timer();
        IPage<ProductSkuOrderManagerDTO> pageResult = baseMapper.queryProductSkuPage(page, productSkuDTO);
        log.info("进入【查询商品shu列表分页】方法, endTime = {}", timer.interval());
        return pageResult;
    }

    @InMethodLog(value = "查询供应商商品shu列表")
    public IPage<ProductSkuOrderSupplierDTO> getProductSkuSupplierPageByDto(Page<ProductSkuOrderSupplierDTO> page, ProductSkuDTO productSkuDTO) {
        TimeInterval timer = DateUtil.timer();
        IPage<ProductSkuOrderSupplierDTO> pageResult = baseMapper.queryProductSkuSupplierPage(page, productSkuDTO);
        log.info("进入【查询供应商商品shu列表分页】方法, endTime = {}", timer.interval());
        return pageResult;
    }

    @InMethodLog(value = "查询分销商商品shu列表分页")
    public IPage<ProductSkuOrderDistributorDTO> getProductSkuDistributorPageByDto(Page<ProductSkuOrderDistributorDTO> page, ProductSkuDTO productSkuDTO) {
        TimeInterval timer = DateUtil.timer();
        IPage<ProductSkuOrderDistributorDTO> pageResult = baseMapper.queryProductSkuDistributorPage(page, productSkuDTO);
        log.info("进入【查询分销商商品shu列表分页】方法, endTime = {}", timer.interval());
        return pageResult;
    }

    @InMethodLog(value = "数据统计分销商详情 - 获取sku价格、库存历史数据")
    public List<ProductSkuByDayDto> getDayProductSkuByProductSkuCode(String productSkuCode, String startTime, String endTime) {
        return baseMapper.getDayProductSkuByProductSkuCode(productSkuCode, startTime, endTime);
    }

    @InMethodLog(value = "获取商品每日销量数据 (每日)")
    public List<AnalysisProductSkuByDayDto> getProductSkuOrdersByDay(String productSkuCode, String startTime, String endTime){
        return baseMapper.getProductSkuOrdersByDay(productSkuCode, startTime, endTime);
    }

    @InMethodLog(value = "获取收藏该商品的分销商数量 (每日)")
    public List<AnalysisProductSkuByDayDto> getDayProductDropByProductSkuCode(String productSkuCode, String startTime, String endTime) {
        return baseMapper.getDayProductDropByProductSkuCode(productSkuCode, startTime, endTime);
    }


    @InMethodLog(value = "按渠道获取订单数据 (每日)")
    public List<AnalysisProductSkuByDayDto> getProductSkuOrdersByChannel(String productSkuCode, String startTime, String endTime) {
        return baseMapper.getProductSkuOrdersByChannel(productSkuCode, startTime, endTime);
    }

    @InMethodLog(value = "根据itemNo获取分销商列表")
    public List<DistributorDataDto> getDisListByProductSkuCode(String productSkuCode, String startTime, String endTime) {
        return baseMapper.getDisListByProductSkuCode(productSkuCode, startTime, endTime);
    }

    @InMethodLog(value = "数据统计分销商详情 - 获取分销商每日订单数据")
    public List<AnalysisProductSkuByDayDto> getProductSkuOrderDisByDay(String tenantId, String startTime, String endTime) {
        return baseMapper.getProductSkuOrderDisByDay(tenantId, startTime, endTime);
    }

    @InMethodLog(value = "数据统计分销商详情 - 获取分销商每日铺货sku数")
    public List<AnalysisProductSkuByDayDto> getProductSkuOrderDistributionByDay(String tenantId, String startTime, String endTime) {
        return baseMapper.getProductSkuOrderDistributionByDay(tenantId, startTime, endTime);
    }

    @InMethodLog(value = "数据统计分销商详情 - 获取不同渠道的订单数据")
    public List<AnalysisProductSkuByDayDto> getProductSkuOrdersDisByChannel(String tenantId, String startTime, String endTime) {
        return baseMapper.getProductSkuOrdersDisByChannel(tenantId, startTime, endTime);
    }

    @InMethodLog(value = "数据统计分销商详情 - 获取SKU列表")
    public List<DistributorOrdersDataDto> getSkuListByTenantId(String tenantId, String startTime, String endTime) {
        return baseMapper.getSkuListByTenantId(tenantId, startTime, endTime);
    }

    @InMethodLog(value = "数据统计分销商详情 - 获取上架列表")
    public List<DistributorOrdersDataDto> getSupListByTenantId(String tenantId, String startTime, String endTime) {
        return baseMapper.getSupListByTenantId(tenantId, startTime, endTime);
    }

    @InMethodLog(value = "数据统计供应商详情 - 获取供应商每日订单数据")
    public List<AnalysisProductSkuByDayDto> getProductSkuOrderSupByDay(String tenantId, String startTime, String endTime) {
        return baseMapper.getProductSkuOrderSupByDay(tenantId, startTime, endTime);
    }

    @InMethodLog(value = "数据统计供应商详情 - 获取供应商每日收藏的分销商数量")
    public List<AnalysisProductSkuByDayDto> getDayProductDropByTenantId(String tenantId, String startTime, String endTime) {
        return baseMapper.getDayProductDropByTenantId(tenantId, startTime, endTime);
    }

    @InMethodLog(value = "数据统计供应商详情 - 获取不同渠道的订单数据")
    public List<AnalysisProductSkuByDayDto> getProductSkuOrdersSupByChannel(String tenantId, String startTime, String endTime) {
        return baseMapper.getProductSkuOrdersSupByChannel(tenantId, startTime, endTime);
    }

    @InMethodLog(value = "数据统计供应商详情 - 根据供应商id获取分销商列表数据")
    public List<DistributorDataDto> getDisListByTenantId(String tenantId, String startTime, String endTime) {
        return baseMapper.getDisListByTenantId(tenantId, startTime, endTime);
    }

    @InMethodLog(value = "数据统计供应商详情 - 获取供应商库存历史数据")
    public List<ProductSkuByDayDto> getDayProductSkuByTenantId(String tenantId, String startTime, String endTime) {
        return baseMapper.getDayProductSkuByTenantId(tenantId, startTime, endTime);
    }

    @InMethodLog(value = "获取SKU在endTime时间之前的最新的sku历史数据")
    public ProductSkuByDayDto getDayProductSkuByProductSkuCodeAndEndTime(String productSkuCode, String endTime) {
        return baseMapper.getDayProductSkuByProductSkuCodeAndEndTime(productSkuCode, endTime);
    }

    @InMethodLog(value = "获取供应商在endTime时间之前的最新的sku库存历史数据")
    public ProductSkuByDayDto getDayProductSkuBytenantIdAndEndTime(String tenantId, String endTime) {
        return baseMapper.getDayProductSkuByTenantIdAndEndTime(tenantId, endTime);
    }

}
