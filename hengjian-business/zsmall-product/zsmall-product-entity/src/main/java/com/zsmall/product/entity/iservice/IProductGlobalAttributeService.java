package com.zsmall.product.entity.iservice;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hengjian.common.core.exception.RStatusCodeException;
import com.hengjian.common.core.utils.StringUtils;
import com.hengjian.common.log.annotation.InMethodLog;
import com.hengjian.common.mybatis.core.page.PageQuery;
import com.hengjian.common.mybatis.core.page.TableDataInfo;
import com.hengjian.common.tenant.helper.TenantHelper;
import com.zsmall.common.enums.product.AttributeScopeEnum;
import com.zsmall.common.enums.statuscode.ZSMallStatusCodeEnum;
import com.zsmall.product.entity.domain.ProductGlobalAttribute;
import com.zsmall.product.entity.domain.bo.ProductGlobalAttributeBo;
import com.zsmall.product.entity.domain.vo.ProductGlobalAttributeVo;
import com.zsmall.product.entity.domain.vo.productGlobalAttribute.ProductGlobalAttributeSimpleVo;
import com.zsmall.product.entity.mapper.ProductGlobalAttributeMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 商品全局属性Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-05-19
 */
@RequiredArgsConstructor
@Service
public class IProductGlobalAttributeService extends ServiceImpl<ProductGlobalAttributeMapper, ProductGlobalAttribute> {

    /**
     * 查询商品全局属性（无视租户）
     */
    public ProductGlobalAttribute queryByIdNotTenant(Long id) {
        return TenantHelper.ignore(() -> baseMapper.selectById(id));
    }

    /**
     * 查询商品全局属性列表
     */
    public TableDataInfo<ProductGlobalAttributeVo> queryPageList(ProductGlobalAttributeBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<ProductGlobalAttribute> lqw = buildQueryWrapper(bo);
        Page<ProductGlobalAttributeVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询商品全局属性列表
     */
    public List<ProductGlobalAttributeVo> queryList(ProductGlobalAttributeBo bo) {
        LambdaQueryWrapper<ProductGlobalAttribute> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<ProductGlobalAttribute> buildQueryWrapper(ProductGlobalAttributeBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<ProductGlobalAttribute> lqw = Wrappers.lambdaQuery();
        lqw.like(StringUtils.isNotBlank(bo.getAttributeName()), ProductGlobalAttribute::getAttributeName, bo.getAttributeName());
        lqw.eq(CollUtil.isNotEmpty(bo.getAttributeValues()), ProductGlobalAttribute::getAttributeValues, bo.getAttributeValues());
        lqw.eq(StringUtils.isNotBlank(bo.getAttributeNotes()), ProductGlobalAttribute::getAttributeNotes, bo.getAttributeNotes());
        lqw.eq(StringUtils.isNotBlank(bo.getAttributeScope()), ProductGlobalAttribute::getAttributeScope, bo.getAttributeScope());
        lqw.eq(bo.getAttributeState() != null, ProductGlobalAttribute::getAttributeState, bo.getAttributeState());
        lqw.eq(bo.getIsSupportCustom() != null, ProductGlobalAttribute::getIsSupportCustom, bo.getIsSupportCustom());
        lqw.eq(bo.getIsBasicAttribute() != null, ProductGlobalAttribute::getIsBasicAttribute, bo.getIsBasicAttribute());
        return lqw;
    }

    /**
     * 保存前的数据校验
     */
    public void validEntityBeforeSave(ProductGlobalAttribute entity, List<Long> specifyCategoryIds) throws RStatusCodeException {
        Long id = entity.getId();
        String attributeName = entity.getAttributeName();
        AttributeScopeEnum attributeScope = entity.getAttributeScope();
        LambdaQueryWrapper<ProductGlobalAttribute> lqw = Wrappers.lambdaQuery();
        lqw.eq(ProductGlobalAttribute::getAttributeName, attributeName);
        lqw.eq(ProductGlobalAttribute::getAttributeScope, attributeScope);

        if (id != null) {
            lqw.ne(ProductGlobalAttribute::getId, id);
        }

        if (baseMapper.exists(lqw)) {
            throw new RStatusCodeException(ZSMallStatusCodeEnum.PRODUCT_GLOBAL_ATTRIBUTE_NAME_REPEAT);
        }
    }

    /**
     * 批量删除商品全局属性
     */
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    @InMethodLog("根据商品分类主键查询关联的属性")
    public List<ProductGlobalAttributeSimpleVo> queryByProductCategoryId(Long productCategoryId, String attributeBelong, String attributeScope) {
        return baseMapper.queryByProductCategoryId(productCategoryId, attributeBelong, attributeScope);
    }

    @InMethodLog("根据商品分类主键和是否必填查询关联的属性")
    public List<ProductGlobalAttributeSimpleVo> queryByProductCategoryIdAndRequired(Long productCategoryId, String attributeBelong, String attributeScope, Boolean isRequired) {
        return baseMapper.queryByProductCategoryIdAndRequired(productCategoryId, attributeBelong, attributeScope, isRequired);
    }

    @InMethodLog("查询商品全局属性列表（提供给分类绑定属性使用）")
    public List<ProductGlobalAttributeSimpleVo> queryForCategory(String attributeBelong, String attributeScope) {
        return baseMapper.queryForCategory(attributeBelong, attributeScope);
    }

    @InMethodLog("根据租户、属性名、属性作用域和归属分类查询")
    public ProductGlobalAttribute queryByAttributeNameAndScopeAndCategory(String tenantId, String attributeName, AttributeScopeEnum attributeScope, Long belongCategoryId) {
        return baseMapper.queryByAttributeNameAndScopeAndCategory(tenantId, attributeName, attributeScope.name(), belongCategoryId);
    }


}
