package com.zsmall.product.entity.iservice;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hengjian.common.core.utils.MapstructUtils;
import com.hengjian.common.mybatis.core.page.PageQuery;
import com.hengjian.common.mybatis.core.page.TableDataInfo;
import com.zsmall.product.entity.domain.ProductWholesaleTieredPrice;
import com.zsmall.product.entity.domain.bo.wholesale.ProductWholesaleTieredPriceBo;
import com.zsmall.product.entity.domain.vo.wholesale.ProductWholesaleTieredPriceVo;
import com.zsmall.product.entity.mapper.ProductWholesaleTieredPriceMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 国外现货批发商品阶梯价主Service业务层处理
 *
 * <AUTHOR> Li
 * @date 2023-05-29
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class IProductWholesaleTieredPriceService extends ServiceImpl<ProductWholesaleTieredPriceMapper, ProductWholesaleTieredPrice> {

    private final ProductWholesaleTieredPriceMapper baseMapper;

    /**
     * 查询国外现货批发商品阶梯价主
     */
    public ProductWholesaleTieredPriceVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询国外现货批发商品阶梯价主列表
     */
    public TableDataInfo<ProductWholesaleTieredPriceVo> queryPageList(ProductWholesaleTieredPriceBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<ProductWholesaleTieredPrice> lqw = buildQueryWrapper(bo);
        Page<ProductWholesaleTieredPriceVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询国外现货批发商品阶梯价主列表
     */
    public List<ProductWholesaleTieredPriceVo> queryList(ProductWholesaleTieredPriceBo bo) {
        LambdaQueryWrapper<ProductWholesaleTieredPrice> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    public List<ProductWholesaleTieredPriceVo> queryListByIds(List<Long> ids) {
        LambdaQueryWrapper<ProductWholesaleTieredPrice> lqw = Wrappers.lambdaQuery();
        lqw.in(ProductWholesaleTieredPrice::getId, ids);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<ProductWholesaleTieredPrice> buildQueryWrapper(ProductWholesaleTieredPriceBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<ProductWholesaleTieredPrice> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getProductId() != null, ProductWholesaleTieredPrice::getProductId, bo.getProductId());
        lqw.eq(bo.getMinimumQuantity() != null, ProductWholesaleTieredPrice::getMinimumQuantity, bo.getMinimumQuantity());
        lqw.eq(bo.getEstimatedOperationFee() != null, ProductWholesaleTieredPrice::getEstimatedOperationFee, bo.getEstimatedOperationFee());
        lqw.eq(bo.getEstimatedShippingFee() != null, ProductWholesaleTieredPrice::getEstimatedShippingFee, bo.getEstimatedShippingFee());
        lqw.eq(bo.getEstimatedHandleTime() != null, ProductWholesaleTieredPrice::getEstimatedHandleTime, bo.getEstimatedHandleTime());
        return lqw;
    }

    /**
     * 新增国外现货批发商品阶梯价主
     */
    public Boolean insertByBo(ProductWholesaleTieredPriceBo bo) {
        ProductWholesaleTieredPrice add = MapstructUtils.convert(bo, ProductWholesaleTieredPrice.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 批量保存批发商品阶梯价主
     *
     * @param wholesaleTieredPriceList
     * @return
     */
    public Boolean insertOrUpdateBatch(List<ProductWholesaleTieredPrice> wholesaleTieredPriceList) {
        log.info("【批量保存批发商品阶梯价主】 wholesaleTieredPriceList = {}", JSONUtil.toJsonStr(wholesaleTieredPriceList));
        return baseMapper.insertOrUpdateBatch(wholesaleTieredPriceList);
    }

    /**
     * 修改国外现货批发商品阶梯价主
     */
    public Boolean updateByBo(ProductWholesaleTieredPriceBo bo) {
        ProductWholesaleTieredPrice update = MapstructUtils.convert(bo, ProductWholesaleTieredPrice.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(ProductWholesaleTieredPrice entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除国外现货批发商品阶梯价主
     */
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }


    public List<ProductWholesaleTieredPrice> queryListByProductId(Long productId) {
        log.info("【根据商品id获取国外现货商品阶梯价集合】 productId = {}", productId);
        LambdaQueryWrapper<ProductWholesaleTieredPrice> lqw = Wrappers.lambdaQuery();
        lqw.eq(ProductWholesaleTieredPrice::getProductId, productId)
            .orderByAsc(ProductWholesaleTieredPrice::getMinimumQuantity);
        return baseMapper.selectList(lqw);

    }

    public ProductWholesaleTieredPrice queryListByProductIdAndQuantity(Long productId, Integer minQuantity) {
        log.info("【根据商品id和数量获取国外现货商品阶梯价】 productId = {}, minQuantity = {}", productId, minQuantity);
        LambdaQueryWrapper<ProductWholesaleTieredPrice> lqw = Wrappers.lambdaQuery();
        lqw.eq(ProductWholesaleTieredPrice::getProductId, productId)
            .le(true, ProductWholesaleTieredPrice::getMinimumQuantity, minQuantity)
            .orderByAsc(ProductWholesaleTieredPrice::getMinimumQuantity);
        List<ProductWholesaleTieredPrice> productWholesaleTieredPrices = baseMapper.selectList(lqw);
        if (CollUtil.isNotEmpty(productWholesaleTieredPrices)) {
            return productWholesaleTieredPrices.get(productWholesaleTieredPrices.size() - 1);
        }
        return null;
    }
}
