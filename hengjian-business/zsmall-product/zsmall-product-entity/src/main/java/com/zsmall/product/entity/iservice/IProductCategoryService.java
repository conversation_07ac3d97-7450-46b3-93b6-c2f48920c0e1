package com.zsmall.product.entity.iservice;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hengjian.common.core.utils.MapstructUtils;
import com.hengjian.common.core.utils.StringUtils;
import com.hengjian.common.log.annotation.InMethodLog;
import com.zsmall.product.entity.domain.ProductCategory;
import com.zsmall.product.entity.domain.bo.category.ProductCategoryBo;
import com.zsmall.product.entity.domain.bo.category.ProductCategoryParamBo;
import com.zsmall.product.entity.domain.vo.category.ProductCategoryVo;
import com.zsmall.product.entity.mapper.ProductCategoryMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * 商品分类Service业务层处理
 *
 * <AUTHOR> Li
 * @date 2023-05-18
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class IProductCategoryService extends ServiceImpl<ProductCategoryMapper, ProductCategory> {

    /**
     * 查询商品分类
     */
    public ProductCategoryVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    public List<ProductCategoryVo> selectVoList() {
        return baseMapper.selectVoList();
    }


    /**
     * 查询商品分类列表
     */
    public List<ProductCategoryVo> queryList(ProductCategoryParamBo bo) {
        LambdaQueryWrapper<ProductCategory> lqw = Wrappers.lambdaQuery();
        lqw.like(StrUtil.isNotBlank(bo.getCategoryName()), ProductCategory::getCategoryName, bo.getCategoryName());
        lqw.eq(bo.getCategoryState() != null, ProductCategory::getCategoryState, bo.getCategoryState());
        List<ProductCategoryVo> productCategoryVos = baseMapper.selectVoList(lqw);
        log.info("productCategoryVos = {}", JSONUtil.toJsonStr(productCategoryVos));
        return baseMapper.selectVoList(lqw);
    }

    /**
     * 是否存在子节点
     * @param parentId
     * @return
     */
    public Boolean existsByParentId(Long parentId) {
        LambdaQueryWrapper<ProductCategory> childNode = Wrappers.lambdaQuery();
        childNode.eq(ProductCategory::getParentId, parentId);
        return baseMapper.exists(childNode);
    }

    public List<ProductCategoryVo> queryListByIds(List<Long> ids) {
        LambdaQueryWrapper<ProductCategory> lqw = Wrappers.lambdaQuery();
        lqw.in(ProductCategory::getId, ids);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<ProductCategory> buildQueryWrapper(ProductCategoryBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<ProductCategory> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getParentId() != null, ProductCategory::getParentId, bo.getParentId());
        lqw.eq(bo.getCategoryLevel() != null, ProductCategory::getCategoryLevel, bo.getCategoryLevel());
        lqw.like(StringUtils.isNotBlank(bo.getCategoryName()), ProductCategory::getCategoryName, bo.getCategoryName());
        lqw.like(ObjectUtil.isNotNull(bo.getCategoryOtherName()), ProductCategory::getCategoryOtherName, bo.getCategoryOtherName());
        lqw.eq(bo.getCategorySort() != null, ProductCategory::getCategorySort, bo.getCategorySort());
        lqw.eq(StringUtils.isNotBlank(bo.getCategoryImageSavePath()), ProductCategory::getCategoryImageSavePath, bo.getCategoryImageSavePath());
        lqw.eq(StringUtils.isNotBlank(bo.getCategoryImageShowUrl()), ProductCategory::getCategoryImageShowUrl, bo.getCategoryImageShowUrl());
        lqw.eq(StringUtils.isNotBlank(bo.getCategoryIconSavePath()), ProductCategory::getCategoryIconSavePath, bo.getCategoryIconSavePath());
        lqw.eq(StringUtils.isNotBlank(bo.getCategoryIconShowUrl()), ProductCategory::getCategoryIconShowUrl, bo.getCategoryIconShowUrl());
        lqw.eq(bo.getCategoryState() != null, ProductCategory::getCategoryState, bo.getCategoryState());
        return lqw;
    }

    /**
     * 新增商品分类
     */
    public Boolean insertByBo(ProductCategoryBo bo) {
        ProductCategory add = MapstructUtils.convert(bo, ProductCategory.class);
        validEntityBeforeSave(add);
        log.info("ProductCategory = {}", JSONUtil.toJsonStr(add));
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改商品分类
     */
    public Boolean updateByBo(ProductCategoryBo bo) {
        ProductCategory update = MapstructUtils.convert(bo, ProductCategory.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(ProductCategory entity){
        Long parentId = entity.getParentId();
        if (ObjectUtil.equals(parentId, 0l)) {
            entity.setCategoryLevel(1);
        } else {
            ProductCategoryVo productCategoryVo = this.queryById(parentId);
            Integer categoryLevel = productCategoryVo.getCategoryLevel();
            entity.setCategoryLevel(categoryLevel + 1);
        }
    }

    @InMethodLog("根据给定的分类id，查询分类链文本，示例：分类A/分类B/分类C（左至右为从父级到子级）")
    public String queryCategoryNameChainByIdOrderByLevelASC(Long belongCategoryId) {
        return baseMapper.queryCategoryNameChainByIdOrderByLevelASC(belongCategoryId);
    }

    /**
     * 根据给定的分类id，倒序查（由子级查父级）整个分类关系链
     *
     * @param belongCategoryId
     * @return
     */
    public List<ProductCategory> queryCategoryChainById(Long belongCategoryId) {
        log.info("进入【根据给定的分类id，倒查（由子级查父级）整个分类关系链】 belongCategoryId = {}", belongCategoryId);
        return baseMapper.queryCategoryChainById(belongCategoryId);
    }

    @InMethodLog("根据指定节点查询全部的产品分类信息")
    public List<ProductCategory> queryAllCategoryChainById(Long belongCategoryId) {
        log.info("进入【根据给定的分类id，查询整个分类关系链】 belongCategoryId = {}", belongCategoryId);
        return baseMapper.queryAllCategoryChainById(belongCategoryId);
    }

    /**
     * 根据给定的分类id，正序查（由父级查子级）整个分类关系链
     *
     * @param belongCategoryId
     * @return
     */
    public List<ProductCategory> queryCategoryChainByIdDesc(Long belongCategoryId) {
        log.info("进入【根据给定的分类id，倒查（由父级查子级）整个分类关系链】 belongCategoryId = {}", belongCategoryId);
        return baseMapper.queryCategoryChainByIdDesc(belongCategoryId);
    }

    @InMethodLog("同一父级下是否存在相同的分类名")
    public Boolean existsCategoryName(Long parendId, String categoryName, Long selfId) {
        LambdaQueryWrapper<ProductCategory> lqw = Wrappers.lambdaQuery();
        lqw.eq(ProductCategory::getParentId, parendId);
        lqw.eq(ProductCategory::getCategoryName, categoryName);
        lqw.ne(selfId != null, ProductCategory::getId, selfId);
        return baseMapper.exists(lqw);
    }

    @InMethodLog("根据分类名查询")
    public List<ProductCategory> queryByCategoryName(String categoryName) {
        LambdaQueryWrapper<ProductCategory> lqw = Wrappers.lambdaQuery();
        lqw.eq(ProductCategory::getCategoryName, categoryName);
        return baseMapper.selectList(lqw);
    }

    @InMethodLog("根据分类名模糊查询")
    public List<ProductCategory> queryLikeByCategoryName(String categoryName) {
        LambdaQueryWrapper<ProductCategory> lqw = Wrappers.lambdaQuery();
        lqw.like(ProductCategory::getCategoryName, categoryName);
        return baseMapper.selectList(lqw);
    }
}
