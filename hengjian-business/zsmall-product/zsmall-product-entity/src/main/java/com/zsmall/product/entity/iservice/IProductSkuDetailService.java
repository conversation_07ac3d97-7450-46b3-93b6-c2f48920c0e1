package com.zsmall.product.entity.iservice;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hengjian.common.core.enums.TenantType;
import com.hengjian.common.core.utils.MapstructUtils;
import com.hengjian.common.core.utils.StringUtils;
import com.hengjian.common.mybatis.core.page.PageQuery;
import com.hengjian.common.mybatis.core.page.TableDataInfo;
import com.hengjian.common.satoken.utils.LoginHelper;
import com.hengjian.common.tenant.helper.TenantHelper;
import com.zsmall.product.entity.domain.ProductSkuDetail;
import com.zsmall.product.entity.domain.bo.ProductSkuDetailBo;
import com.zsmall.product.entity.domain.vo.ProductSkuDetailVo;
import com.zsmall.product.entity.mapper.ProductSkuDetailMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 商品SKU详情Service业务层处理
 *
 * <AUTHOR> Li
 * @date 2023-05-26
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class IProductSkuDetailService extends ServiceImpl<ProductSkuDetailMapper, ProductSkuDetail> {

    private final ProductSkuDetailMapper baseMapper;

    /**
     * 查询商品SKU详情
     */
    public ProductSkuDetailVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询商品SKU详情列表
     */
    public TableDataInfo<ProductSkuDetailVo> queryPageList(ProductSkuDetailBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<ProductSkuDetail> lqw = buildQueryWrapper(bo);
        Page<ProductSkuDetailVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询商品SKU详情列表
     */
    public List<ProductSkuDetailVo> queryList(ProductSkuDetailBo bo) {
        LambdaQueryWrapper<ProductSkuDetail> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<ProductSkuDetail> buildQueryWrapper(ProductSkuDetailBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<ProductSkuDetail> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getProductSkuId() != null, ProductSkuDetail::getProductSkuId, bo.getProductSkuId());
        lqw.eq(bo.getLength() != null, ProductSkuDetail::getLength, bo.getLength());
        lqw.eq(bo.getWidth() != null, ProductSkuDetail::getWidth, bo.getWidth());
        lqw.eq(bo.getHeight() != null, ProductSkuDetail::getHeight, bo.getHeight());
        lqw.eq(StringUtils.isNotBlank(bo.getLengthUnit()), ProductSkuDetail::getLengthUnit, bo.getLengthUnit());
        lqw.eq(bo.getWeight() != null, ProductSkuDetail::getWeight, bo.getWeight());
        lqw.eq(StringUtils.isNotBlank(bo.getWeightUnit()), ProductSkuDetail::getWeightUnit, bo.getWeightUnit());
        lqw.eq(bo.getPackLength() != null, ProductSkuDetail::getPackLength, bo.getPackLength());
        lqw.eq(bo.getPackWidth() != null, ProductSkuDetail::getPackWidth, bo.getPackWidth());
        lqw.eq(bo.getPackHeight() != null, ProductSkuDetail::getPackHeight, bo.getPackHeight());
        lqw.eq(StringUtils.isNotBlank(bo.getPackLengthUnit()), ProductSkuDetail::getPackLengthUnit, bo.getPackLengthUnit());
        lqw.eq(bo.getPackWeight() != null, ProductSkuDetail::getPackWeight, bo.getPackWeight());
        lqw.eq(StringUtils.isNotBlank(bo.getPackWeightUnit()), ProductSkuDetail::getPackWeightUnit, bo.getPackWeightUnit());
        lqw.eq(bo.getSamePacking() != null, ProductSkuDetail::getSamePacking, bo.getSamePacking());
        lqw.eq(bo.getProcessingTime() != null, ProductSkuDetail::getProcessingTime, bo.getProcessingTime());
        lqw.eq(StringUtils.isNotBlank(bo.getTransportMethod()), ProductSkuDetail::getTransportMethod, bo.getTransportMethod());
        lqw.eq(StringUtils.isNotBlank(bo.getDescription()), ProductSkuDetail::getDescription, bo.getDescription());
        return lqw;
    }

    /**
     * 新增商品SKU详情
     */
    public Boolean insertByBo(ProductSkuDetailBo bo) {
        ProductSkuDetail add = MapstructUtils.convert(bo, ProductSkuDetail.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 新增商品SKU详情
     *
     * @param entity
     * @return
     */
    public Boolean insert(ProductSkuDetail entity) {
        log.info("进入【新增商品SKU详情】 entity = {}", JSONUtil.toJsonStr(entity));
        return baseMapper.insert(entity) > 0;
    }

    /**
     * 新增/更新商品SKU详情
     * @param entity
     * @return
     */
    public Boolean insertOrUpdate(ProductSkuDetail entity) {
        log.info("进入【新增/更新商品SKU详情】 entity = {}", JSONUtil.toJsonStr(entity));
        return baseMapper.insertOrUpdate(entity);
    }

    /**
     * 修改商品SKU详情
     */
    public Boolean updateByBo(ProductSkuDetailBo bo) {
        ProductSkuDetail update = MapstructUtils.convert(bo, ProductSkuDetail.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(ProductSkuDetail entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除商品SKU详情
     */
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    /**
     * 根据skuId获取sku详情
     *
     * @param productSkuId
     * @return
     */
    public ProductSkuDetail queryByProductSkuId(Long productSkuId) {
        LambdaQueryWrapper<ProductSkuDetail> lqw = Wrappers.lambdaQuery();
        lqw.eq(ProductSkuDetail::getProductSkuId, productSkuId);
        if (!ObjectUtil.equals(LoginHelper.getTenantTypeEnum(), TenantType.Supplier)) {
            return TenantHelper.ignore(() -> baseMapper.selectOne(lqw));
        }
        return baseMapper.selectOne(lqw);
    }

    public List<ProductSkuDetail> getListByProductId(Long productId) {
        log.info("进入【根据商品Id查询sku详情】方法, productId = {}", productId);
        if (ObjectUtil.isNull(productId)){
            return new ArrayList<>();
        }
        return baseMapper.getListByProductId(productId);
    }

    public Boolean deleteByProductSkuIdList(List<Long> productSkuIdList) {
        log.info("进入【根据Sku主键数组删除】 productSkuIdList = {}", JSONUtil.toJsonStr(productSkuIdList));
        LambdaQueryWrapper<ProductSkuDetail> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(ProductSkuDetail::getProductSkuId, productSkuIdList);
        return baseMapper.delete(queryWrapper) > 0;
    }

    /**
     * 根据商品SKU唯一编号查询商品SKU详情
     * @param productSkuCode
     * @return
     */
    public ProductSkuDetail queryByProductSkuCode(String productSkuCode) {
        log.info("进入【根据商品SKU唯一编号查询商品SKU详情】 productSkuCode = {}", productSkuCode);
        return baseMapper.queryByProductSkuCode(productSkuCode);
    }

    /**
     * 批量查询商品SKU详情
     * @param productSkuIds SKU主键集合
     * @return SKU详情列表
     */
    public List<ProductSkuDetail> queryByProductSkuIds(List<Long> productSkuIds) {
        log.info("进入【批量查询商品SKU详情】 productSkuIds = {}", JSONUtil.toJsonStr(productSkuIds));
        if (CollUtil.isEmpty(productSkuIds)) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<ProductSkuDetail> lqw = Wrappers.lambdaQuery();
        lqw.in(ProductSkuDetail::getProductSkuId, productSkuIds);
        // 保持与原始 queryByProductSkuId() 方法相同的租户处理逻辑
        if (!ObjectUtil.equals(LoginHelper.getTenantTypeEnum(), TenantType.Supplier)) {
            return TenantHelper.ignore(() -> baseMapper.selectList(lqw));
        }
        return baseMapper.selectList(lqw);
    }

}
