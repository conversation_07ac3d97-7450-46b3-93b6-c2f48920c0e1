package com.zsmall.product.entity.iservice;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zsmall.product.entity.domain.ProductWholesaleTieredPrice;
import com.zsmall.product.entity.domain.ProductWholesaleTieredPriceLog;
import com.zsmall.product.entity.domain.bo.wholesale.ProductWholesaleTieredPriceLogBo;
import com.zsmall.product.entity.domain.vo.wholesale.ProductWholesaleTieredPriceLogVo;
import com.zsmall.product.entity.mapper.ProductWholesaleTieredPriceLogMapper;
import lombok.RequiredArgsConstructor;
import com.hengjian.common.core.utils.MapstructUtils;
import com.hengjian.common.core.utils.StringUtils;
import com.hengjian.common.log.annotation.InMethodLog;
import com.hengjian.common.mybatis.core.page.PageQuery;
import com.hengjian.common.mybatis.core.page.TableDataInfo;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 国外现货批发商品阶梯价日志Service业务层处理
 *
 * <AUTHOR> Li
 * @date 2023-05-29
 */
@RequiredArgsConstructor
@Service
public class IProductWholesaleTieredPriceLogService extends ServiceImpl<ProductWholesaleTieredPriceLogMapper, ProductWholesaleTieredPriceLog> {

    private final ProductWholesaleTieredPriceLogMapper baseMapper;

    /**
     * 查询国外现货批发商品阶梯价日志
     */
    public ProductWholesaleTieredPriceLogVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询国外现货批发商品阶梯价日志列表
     */
    public TableDataInfo<ProductWholesaleTieredPriceLogVo> queryPageList(ProductWholesaleTieredPriceLogBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<ProductWholesaleTieredPriceLog> lqw = buildQueryWrapper(bo);
        Page<ProductWholesaleTieredPriceLogVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询国外现货批发商品阶梯价日志列表
     */
    public List<ProductWholesaleTieredPriceLogVo> queryList(ProductWholesaleTieredPriceLogBo bo) {
        LambdaQueryWrapper<ProductWholesaleTieredPriceLog> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<ProductWholesaleTieredPriceLog> buildQueryWrapper(ProductWholesaleTieredPriceLogBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<ProductWholesaleTieredPriceLog> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getProductId() != null, ProductWholesaleTieredPriceLog::getProductId, bo.getProductId());
        lqw.eq(bo.getProductWholesaleTieredPriceId() != null, ProductWholesaleTieredPriceLog::getProductWholesaleTieredPriceId, bo.getProductWholesaleTieredPriceId());
        lqw.eq(StringUtils.isNotBlank(bo.getHandleUser()), ProductWholesaleTieredPriceLog::getHandleUser, bo.getHandleUser());
        lqw.eq(bo.getMinimumQuantity() != null, ProductWholesaleTieredPriceLog::getMinimumQuantity, bo.getMinimumQuantity());
        lqw.eq(bo.getEstimatedOperationFee() != null, ProductWholesaleTieredPriceLog::getEstimatedOperationFee, bo.getEstimatedOperationFee());
        lqw.eq(bo.getEstimatedShippingFee() != null, ProductWholesaleTieredPriceLog::getEstimatedShippingFee, bo.getEstimatedShippingFee());
        lqw.eq(bo.getEstimatedHandleTime() != null, ProductWholesaleTieredPriceLog::getEstimatedHandleTime, bo.getEstimatedHandleTime());
        return lqw;
    }

    /**
     * 新增国外现货批发商品阶梯价日志
     */
    public Boolean insertByBo(ProductWholesaleTieredPriceLogBo bo) {
        ProductWholesaleTieredPriceLog add = MapstructUtils.convert(bo, ProductWholesaleTieredPriceLog.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改国外现货批发商品阶梯价日志
     */
    public Boolean updateByBo(ProductWholesaleTieredPriceLogBo bo) {
        ProductWholesaleTieredPriceLog update = MapstructUtils.convert(bo, ProductWholesaleTieredPriceLog.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(ProductWholesaleTieredPriceLog entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除国外现货批发商品阶梯价日志
     */
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    @InMethodLog(value = "批量添加批发商品阶梯价日志数据")
    public void batchSaveTieredPriceLog(List<ProductWholesaleTieredPrice> tieredPriceList) {
        if (CollUtil.isEmpty(tieredPriceList)) {
            return;
        }
        List<ProductWholesaleTieredPriceLog> logList = new ArrayList<>();
        for (ProductWholesaleTieredPrice tieredPrice: tieredPriceList) {
            ProductWholesaleTieredPriceLog tieredPriceLog = BeanUtil.copyProperties(tieredPrice, ProductWholesaleTieredPriceLog.class);
            tieredPriceLog.setId(null);
            tieredPriceLog.setProductWholesaleTieredPriceId(tieredPrice.getId());
            logList.add(tieredPriceLog);
        }
        this.saveBatch(logList);
    }
}
