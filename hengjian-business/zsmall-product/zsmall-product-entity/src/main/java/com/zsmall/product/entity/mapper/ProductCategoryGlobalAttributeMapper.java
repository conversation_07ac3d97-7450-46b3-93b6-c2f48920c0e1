package com.zsmall.product.entity.mapper;

import com.hengjian.common.mybatis.core.mapper.BaseMapperPlus;
import com.zsmall.product.entity.domain.ProductCategoryGlobalAttribute;
import com.zsmall.product.entity.domain.vo.category.ProductCategoryGlobalAttributeVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 商品分类-商品全局属性关联Mapper接口
 *
 * <AUTHOR> Li
 * @date 2023-05-22
 */
public interface ProductCategoryGlobalAttributeMapper extends BaseMapperPlus<ProductCategoryGlobalAttribute, ProductCategoryGlobalAttributeVo> {

    ProductCategoryGlobalAttribute queryByCategoryIdAndGlobalAttributeId(@Param("categoryId") Long categoryId, @Param("globalAttributeId") Long globalAttributeId);

    List<Long> queryCategoryIdsByGlobalAttributeId(@Param("globalAttributeId") Long globalAttributeId);

}
