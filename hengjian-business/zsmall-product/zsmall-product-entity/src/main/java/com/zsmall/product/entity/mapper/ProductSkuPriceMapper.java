package com.zsmall.product.entity.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.hengjian.common.mybatis.core.mapper.BaseMapperPlus;
import com.zsmall.product.entity.domain.ProductSkuPrice;
import com.zsmall.product.entity.domain.RuleLevelProductPrice;
import com.zsmall.product.entity.domain.dto.productSku.ProductSkuWarehouseByCountryCode;
import com.zsmall.product.entity.domain.vo.productSkuPrice.ProductSkuPriceVo;
import java.util.HashMap;
import java.util.List;
import java.util.Set;
import org.apache.ibatis.annotations.Param;

/**
* <AUTHOR>
* @description 针对表【product_sku_price(商品SKU定价表)】的数据库操作Mapper
* @createDate 2023-05-22 16:56:33
* @Entity generator.domain.ProductSkuPrice
*/
public interface ProductSkuPriceMapper extends BaseMapperPlus<ProductSkuPrice, ProductSkuPriceVo> {

    List<ProductSkuPrice> getListByProductId(@Param("productId") Long productId);

    /**
     * 分仓查询库存
     * @param productSkuCode
     * @return
     */
    List<HashMap<String, Object>> getStockWithWarehouseSystemCode(@Param("productSkuCode") String productSkuCode,@Param("tenantId") String tenantId);


    /**
     *  根据站点查询库存
     * @param productSkuCode 商品SKU编码
     * @param tenantId 租户ID
     * @param site  站点
     * @return 仓库编码
     */
    @InterceptorIgnore(tenantLine = "true")
    List<HashMap<String, Object>> getStockWithWarehouseSystemCodeBySite(@Param("productSkuCode") String productSkuCode,@Param("tenantId") String tenantId,@Param("site") String site);

    /**
     * 根据站点和收货地址国家查询仓库
     * @param productSkuCode  商品SKU编码
     * @param countryCode 收货地址国家
     * @return  仓库编码
     */
    @InterceptorIgnore(tenantLine = "true")
    List<ProductSkuWarehouseByCountryCode> getWarehouseCodeBySite(@Param("productSkuCode") String productSkuCode,  @Param("countryCode") String countryCode);


    /**
     * 分仓查询库存使用resultMap返回
     * @param productSkuCode
     * @param tenantId
     * @return
     */
    List<HashMap<String, Object>> getStockWithWarehouseSystemCodeNew(@Param("productSkuCode") String productSkuCode,@Param("tenantId") String tenantId);

    Integer getSoldOutByProductSkuId(@Param("productSkuId")Long productSkuId);

    List<RuleLevelProductPrice> getRuleLevelProductPriceSitePriceMap(@Param("productSkuCodeSet") Set<String> productSkuCodeSet,@Param("tenantId") String tenantId);

    /**
     * 是否测算
     * @param tenantId
     * @return
     */
    Integer getIsCalculation(@Param("tenantId") String tenantId);
}




