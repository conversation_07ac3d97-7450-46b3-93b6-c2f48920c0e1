package com.zsmall.product.entity.mapper;

import com.hengjian.common.mybatis.core.mapper.BaseMapperPlus;
import com.zsmall.product.entity.domain.ProductSkuPriceRuleItem;
import com.zsmall.product.entity.domain.vo.productSkuPrice.ProductSkuPriceRuleItemVo;

/**
* <AUTHOR>
* @description 针对表【product_sku_price_rule_item(商品sku价格计算公式表)】的数据库操作Mapper
* @createDate 2023-05-22 16:56:33
* @Entity generator.domain.ProductSkuPriceRuleItem
*/
public interface ProductSkuPriceRuleItemMapper extends BaseMapperPlus<ProductSkuPriceRuleItem, ProductSkuPriceRuleItemVo> {

}




