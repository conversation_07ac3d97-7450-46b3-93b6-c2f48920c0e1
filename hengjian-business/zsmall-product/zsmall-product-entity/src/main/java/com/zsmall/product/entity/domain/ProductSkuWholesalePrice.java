package com.zsmall.product.entity.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.zsmall.common.domain.SortEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;



/**
 * 国外现货批发商品SKU价格对象 product_sku_wholesale_price
 *
 * <AUTHOR>
 * @date 2023-05-29
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("product_sku_wholesale_price")
public class ProductSkuWholesalePrice extends SortEntity {


    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 商品主键
     */
    private Long productId;

    /**
     * 商品SKU主键
     */
    private Long productSkuId;

    /**
     * 阶梯定价表主键
     */
    private Long tieredPriceId;

    /**
     * 原始单价（供货商）
     */
    private BigDecimal originUnitPrice;

    /**
     * 平台单价（员工、分销商）
     */
    private BigDecimal platformUnitPrice;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;


}
