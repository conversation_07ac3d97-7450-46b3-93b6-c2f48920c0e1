package com.zsmall.product.entity.iservice;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hengjian.common.core.utils.MapstructUtils;
import com.hengjian.common.core.utils.StringUtils;
import com.hengjian.common.mybatis.core.page.PageQuery;
import com.hengjian.common.mybatis.core.page.TableDataInfo;
import com.zsmall.product.entity.domain.ProductWholesaleDetail;
import com.zsmall.product.entity.domain.bo.wholesale.ProductWholesaleDetailBo;
import com.zsmall.product.entity.domain.vo.wholesale.ProductWholesaleDetailVo;
import com.zsmall.product.entity.mapper.ProductWholesaleDetailMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 国外现货批发商品详情Service业务层处理
 *
 * <AUTHOR> Li
 * @date 2023-05-29
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class IProductWholesaleDetailService extends ServiceImpl<ProductWholesaleDetailMapper, ProductWholesaleDetail> {

    private final ProductWholesaleDetailMapper baseMapper;

    /**
     * 查询国外现货批发商品详情
     */
    public ProductWholesaleDetailVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询国外现货批发商品详情列表
     */
    public TableDataInfo<ProductWholesaleDetailVo> queryPageList(ProductWholesaleDetailBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<ProductWholesaleDetail> lqw = buildQueryWrapper(bo);
        Page<ProductWholesaleDetailVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询国外现货批发商品详情列表
     */
    public List<ProductWholesaleDetailVo> queryList(ProductWholesaleDetailBo bo) {
        LambdaQueryWrapper<ProductWholesaleDetail> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<ProductWholesaleDetail> buildQueryWrapper(ProductWholesaleDetailBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<ProductWholesaleDetail> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getProductId() != null, ProductWholesaleDetail::getProductId, bo.getProductId());
        lqw.eq(bo.getMinimumQuantity() != null, ProductWholesaleDetail::getMinimumQuantity, bo.getMinimumQuantity());
        lqw.eq(bo.getDepositRatio() != null, ProductWholesaleDetail::getDepositRatio, bo.getDepositRatio());
        lqw.eq(bo.getReservedTime() != null, ProductWholesaleDetail::getReservedTime, bo.getReservedTime());
        lqw.eq(ObjectUtil.isNotNull(bo.getDeliveryType()), ProductWholesaleDetail::getDeliveryType, bo.getDeliveryType());
        lqw.eq(StringUtils.isNotBlank(bo.getWarehouseSystemCode()), ProductWholesaleDetail::getWarehouseSystemCode, bo.getWarehouseSystemCode());
        lqw.eq(StringUtils.isNotBlank(bo.getLogisticsTemplateNo()), ProductWholesaleDetail::getLogisticsTemplateNo, bo.getLogisticsTemplateNo());
        return lqw;
    }

    /**
     * 新增国外现货批发商品详情
     */
    public Boolean insertByBo(ProductWholesaleDetailBo bo) {
        ProductWholesaleDetail add = MapstructUtils.convert(bo, ProductWholesaleDetail.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改国外现货批发商品详情
     */
    public Boolean updateByBo(ProductWholesaleDetailBo bo) {
        ProductWholesaleDetail update = MapstructUtils.convert(bo, ProductWholesaleDetail.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(ProductWholesaleDetail entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除国外现货批发商品详情
     */
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }


    public ProductWholesaleDetail queryByProductId(Long productId) {
        log.info("【根据商品ID查询国外现货批发商品详情】 productId = {}", productId);
        LambdaQueryWrapper<ProductWholesaleDetail> lqw = Wrappers.lambdaQuery();
        lqw.eq(ProductWholesaleDetail::getProductId, productId);
        return baseMapper.selectOne(lqw);
    }
}
