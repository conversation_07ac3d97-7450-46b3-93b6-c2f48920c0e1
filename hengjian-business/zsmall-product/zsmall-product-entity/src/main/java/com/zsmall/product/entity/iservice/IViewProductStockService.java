package com.zsmall.product.entity.iservice;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zsmall.product.entity.domain.viewer.ViewProductStock;
import com.zsmall.product.entity.mapper.ViewProductStockMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 商品SPU总库存视图-数据库层接口
 *
 * <AUTHOR>
 * @date 2023/6/9
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class IViewProductStockService extends ServiceImpl<ViewProductStockMapper, ViewProductStock> {

    public ViewProductStock queryByProductId(Long productId) {
        LambdaQueryWrapper<ViewProductStock> lqw = new LambdaQueryWrapper<>();
        return lambdaQuery().eq(ViewProductStock::getProductId, productId).one();
    }

}
