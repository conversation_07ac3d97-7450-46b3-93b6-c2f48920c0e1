package com.zsmall.product.entity.iservice;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zsmall.product.entity.domain.ProductCategoryGlobalAttribute;
import com.zsmall.product.entity.domain.bo.category.ProductCategoryGlobalAttributeBo;
import com.zsmall.product.entity.domain.vo.category.ProductCategoryGlobalAttributeVo;
import com.zsmall.product.entity.mapper.ProductCategoryGlobalAttributeMapper;
import lombok.RequiredArgsConstructor;
import com.hengjian.common.core.utils.MapstructUtils;
import com.hengjian.common.core.utils.StringUtils;
import com.hengjian.common.mybatis.core.page.PageQuery;
import com.hengjian.common.mybatis.core.page.TableDataInfo;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 商品分类-商品全局属性关联Service数据库层处理
 *
 * <AUTHOR> Li
 * @date 2023-05-22
 */
@RequiredArgsConstructor
@Service
public class IProductCategoryGlobalAttributeService extends ServiceImpl<ProductCategoryGlobalAttributeMapper, ProductCategoryGlobalAttribute> {

    private final ProductCategoryGlobalAttributeMapper baseMapper;

    /**
     * 查询商品分类-商品全局属性关联
     */
    public ProductCategoryGlobalAttributeVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询商品分类-商品全局属性关联列表
     */
    public TableDataInfo<ProductCategoryGlobalAttributeVo> queryPageList(ProductCategoryGlobalAttributeBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<ProductCategoryGlobalAttribute> lqw = buildQueryWrapper(bo);
        Page<ProductCategoryGlobalAttributeVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询商品分类-商品全局属性关联列表
     */
    public List<ProductCategoryGlobalAttributeVo> queryList(ProductCategoryGlobalAttributeBo bo) {
        LambdaQueryWrapper<ProductCategoryGlobalAttribute> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<ProductCategoryGlobalAttribute> buildQueryWrapper(ProductCategoryGlobalAttributeBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<ProductCategoryGlobalAttribute> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getGlobalAttributeId() != null, ProductCategoryGlobalAttribute::getGlobalAttributeId, bo.getGlobalAttributeId());
        lqw.eq(bo.getProductCategoryId() != null, ProductCategoryGlobalAttribute::getProductCategoryId, bo.getProductCategoryId());
        lqw.eq(bo.getIsRequired() != null, ProductCategoryGlobalAttribute::getIsRequired, bo.getIsRequired());
        lqw.eq(StringUtils.isNotBlank(bo.getCustomValues()), ProductCategoryGlobalAttribute::getCustomValues, bo.getCustomValues());
        return lqw;
    }

    /**
     * 新增商品分类-商品全局属性关联
     */
    public Boolean insertByBo(ProductCategoryGlobalAttributeBo bo) {
        ProductCategoryGlobalAttribute add = MapstructUtils.convert(bo, ProductCategoryGlobalAttribute.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改商品分类-商品全局属性关联
     */
    public Boolean updateByBo(ProductCategoryGlobalAttributeBo bo) {
        ProductCategoryGlobalAttribute update = MapstructUtils.convert(bo, ProductCategoryGlobalAttribute.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(ProductCategoryGlobalAttribute entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除商品分类-商品全局属性关联
     */
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    public Boolean deleteByGlobalAttributeId(Long globalAttributeId) {
        LambdaQueryWrapper<ProductCategoryGlobalAttribute> lqw = Wrappers.lambdaQuery();
        lqw.eq(ProductCategoryGlobalAttribute::getGlobalAttributeId, globalAttributeId);
        return baseMapper.delete(lqw) > 0;
    }

    public ProductCategoryGlobalAttribute queryByCategoryIdAndGlobalAttributeId(Long categoryId, Long globalAttributeId) {
        return baseMapper.queryByCategoryIdAndGlobalAttributeId(categoryId, globalAttributeId);
    }

    public List<Long> queryCategoryIdsByGlobalAttributeId(Long globalAttributeId) {
        return baseMapper.queryCategoryIdsByGlobalAttributeId(globalAttributeId);
    }
}
