package com.zsmall.product.entity.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.hengjian.common.mybatis.core.mapper.BaseMapperPlus;
import com.zsmall.product.entity.domain.ProductQuestionAnswer;
import com.zsmall.product.entity.domain.vo.prodcutQuestion.ProductQuestionAnswerVo;
import org.apache.ibatis.annotations.Param;

/**
 * 商品问答Mapper接口
 *
 * <AUTHOR> Li
 * @date 2023-07-26
 */
public interface ProductQuestionAnswerMapper extends BaseMapperPlus<ProductQuestionAnswer, ProductQuestionAnswerVo> {

    /**
     * 判断问答编码是否存在
     * @param code
     * @return
     */
    @InterceptorIgnore(tenantLine = "true")
    boolean existAnswerCode(@Param("answerCode") String code);

    @InterceptorIgnore(tenantLine = "true")
    Integer getMaxSort(@Param("questionId") Long questionId);

}
