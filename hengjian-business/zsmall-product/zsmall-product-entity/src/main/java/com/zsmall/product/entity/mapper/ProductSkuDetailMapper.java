package com.zsmall.product.entity.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.hengjian.common.mybatis.core.mapper.BaseMapperPlus;
import com.zsmall.product.entity.domain.ProductSkuDetail;
import com.zsmall.product.entity.domain.vo.ProductSkuDetailVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 商品SKU详情Mapper接口
 *
 * <AUTHOR> Li
 * @date 2023-05-26
 */
public interface ProductSkuDetailMapper extends BaseMapperPlus<ProductSkuDetail, ProductSkuDetailVo> {

    @InterceptorIgnore(tenantLine = "true")
    List<ProductSkuDetail> getListByProductId(@Param("productId") Long productId);

    @InterceptorIgnore(tenantLine = "true")
    ProductSkuDetail queryByProductSkuCode(@Param("productSkuCode") String productSkuCode);

}
