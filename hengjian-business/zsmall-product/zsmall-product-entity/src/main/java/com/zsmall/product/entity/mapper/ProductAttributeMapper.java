package com.zsmall.product.entity.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.hengjian.common.mybatis.core.mapper.BaseMapperPlus;
import com.zsmall.product.entity.domain.ProductAttribute;
import com.zsmall.product.entity.domain.vo.ProductAttributeVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 商品SPU属性Mapper接口
 *
 * <AUTHOR>
 * @date 2023-05-29
 */
public interface ProductAttributeMapper extends BaseMapperPlus<ProductAttribute, ProductAttributeVo> {

    /**
     * 根据商品主键查询所有已存在的属性主键
     * @param productId
     * @return
     */
    List<Long> queryIdsByProductId(@Param("productId") Long productId);

    @InterceptorIgnore(tenantLine = "true")
    Boolean existRequiredAttribute(@Param("productId") Long productId);

}
