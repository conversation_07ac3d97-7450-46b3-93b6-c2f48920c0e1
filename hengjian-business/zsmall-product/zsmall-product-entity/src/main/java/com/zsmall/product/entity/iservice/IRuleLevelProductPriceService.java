package com.zsmall.product.entity.iservice;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zsmall.product.entity.domain.RuleLevelProductPrice;
import com.zsmall.product.entity.mapper.RuleLevelProductPriceMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2024/9/27 16:01
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class IRuleLevelProductPriceService extends ServiceImpl<RuleLevelProductPriceMapper, RuleLevelProductPrice> {

    /**
     * 功能描述：按级别id和站点id获取一个
     *
     * @param levelId      级别id
     * @param siteId       站点id
     * @param productSkuId 产品sku id
     * @return {@link RuleLevelProductPrice }
     * <AUTHOR>
     * @date 2025/02/07
     */
    public RuleLevelProductPrice getOneByCombinedIndex(Long levelId, Long siteId, Long productSkuId){
        LambdaQueryWrapper<RuleLevelProductPrice> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(RuleLevelProductPrice::getLevelId, levelId)
               .eq(RuleLevelProductPrice::getSiteId, siteId)
               .eq(RuleLevelProductPrice::getProductSkuId, productSkuId);
        RuleLevelProductPrice one = getOne(wrapper);
        return one;
    }
}
