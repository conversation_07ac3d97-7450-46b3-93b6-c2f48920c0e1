package com.zsmall.product.entity.iservice;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hengjian.common.core.utils.MapstructUtils;
import com.hengjian.common.mybatis.core.page.PageQuery;
import com.hengjian.common.mybatis.core.page.TableDataInfo;
import com.zsmall.product.entity.domain.ProductSkuWholesalePrice;
import com.zsmall.product.entity.domain.ProductWholesaleTieredPrice;
import com.zsmall.product.entity.domain.bo.wholesale.ProductSkuWholesalePriceBo;
import com.zsmall.product.entity.domain.vo.wholesale.ProductSkuWholesalePriceVo;
import com.zsmall.product.entity.mapper.ProductSkuWholesalePriceMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 国外现货批发商品SKU价格Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-05-29
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class IProductSkuWholesalePriceService extends ServiceImpl<ProductSkuWholesalePriceMapper, ProductSkuWholesalePrice> {

    private final ProductSkuWholesalePriceMapper baseMapper;
    private final IProductWholesaleTieredPriceService iProductWholesaleTieredPriceService;

    /**
     * 查询国外现货批发商品SKU价格
     */
    public ProductSkuWholesalePriceVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询国外现货批发商品SKU价格列表
     */
    public TableDataInfo<ProductSkuWholesalePriceVo> queryPageList(ProductSkuWholesalePriceBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<ProductSkuWholesalePrice> lqw = buildQueryWrapper(bo);
        Page<ProductSkuWholesalePriceVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询国外现货批发商品SKU价格列表
     */
    public List<ProductSkuWholesalePriceVo> queryList(ProductSkuWholesalePriceBo bo) {
        LambdaQueryWrapper<ProductSkuWholesalePrice> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    public List<ProductSkuWholesalePriceVo> queryListByIds(List<Long> ids) {
        LambdaQueryWrapper<ProductSkuWholesalePrice> lqw = Wrappers.lambdaQuery();
        lqw.in(ProductSkuWholesalePrice::getId, ids);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<ProductSkuWholesalePrice> buildQueryWrapper(ProductSkuWholesalePriceBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<ProductSkuWholesalePrice> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getProductId() != null, ProductSkuWholesalePrice::getProductId, bo.getProductId());
        lqw.eq(bo.getProductSkuId() != null, ProductSkuWholesalePrice::getProductSkuId, bo.getProductSkuId());
        lqw.eq(bo.getTieredPriceId() != null, ProductSkuWholesalePrice::getTieredPriceId, bo.getTieredPriceId());
        lqw.eq(bo.getOriginUnitPrice() != null, ProductSkuWholesalePrice::getOriginUnitPrice, bo.getOriginUnitPrice());
        lqw.eq(bo.getPlatformUnitPrice() != null, ProductSkuWholesalePrice::getPlatformUnitPrice, bo.getPlatformUnitPrice());
        return lqw;
    }

    /**
     * 新增国外现货批发商品SKU价格
     */
    public Boolean insertByBo(ProductSkuWholesalePriceBo bo) {
        ProductSkuWholesalePrice add = MapstructUtils.convert(bo, ProductSkuWholesalePrice.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 批量保存批发商品SKU价格
     *
     * @param wholesalePricesList
     * @return
     */
    public Boolean insertOrUpdateBatch(List<ProductSkuWholesalePrice> wholesalePricesList) {
        log.info("【批量保存批发商品SKU价格】 wholesalePricesList = {}", JSONUtil.toJsonStr(wholesalePricesList));
        return baseMapper.insertOrUpdateBatch(wholesalePricesList);
    }

    /**
     * 修改国外现货批发商品SKU价格
     */
    public Boolean updateByBo(ProductSkuWholesalePriceBo bo) {
        ProductSkuWholesalePrice update = MapstructUtils.convert(bo, ProductSkuWholesalePrice.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(ProductSkuWholesalePrice entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除国外现货批发商品SKU价格
     */
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }


    public List<ProductSkuWholesalePrice> queryListByProductId(Long productId) {
        log.info("【根据商品id获取国外现货商品SKU价格数据集】 productId = {}", productId);
        LambdaQueryWrapper<ProductSkuWholesalePrice> lqw = Wrappers.lambdaQuery();
        lqw.eq(ProductSkuWholesalePrice::getProductId, productId);
        return baseMapper.selectList(lqw);
    }

    public List<ProductSkuWholesalePrice> queryListByTieredPriceId(Long tieredPriceId) {
        log.info("【根据阶梯价id获取国外现货批发价格集合】 tieredPriceId = {}", tieredPriceId);
        LambdaQueryWrapper<ProductSkuWholesalePrice> lqw = Wrappers.lambdaQuery();
        lqw.eq(ProductSkuWholesalePrice::getTieredPriceId, tieredPriceId);
        return baseMapper.selectList(lqw);
    }

    public List<ProductSkuWholesalePrice> queryListByProductSkuId(Long productSkuId) {
        log.info("【根据skuId获取国外现货批发价格集合】 productSkuId = {}", productSkuId);
        LambdaQueryWrapper<ProductSkuWholesalePrice> lqw = Wrappers.lambdaQuery();
        lqw.eq(ProductSkuWholesalePrice::getProductSkuId, productSkuId);
        return baseMapper.selectList(lqw);
    }

    public ProductSkuWholesalePrice getOneByProductSkuIdAndTieredPriceId(Long productSkuId, Long tieredPriceId) {
        log.info("【根据skuId阶梯价id和获取国外现货批发价格（唯一值）】 productSkuId = {}, tieredPriceId = {}", productSkuId, tieredPriceId);
        LambdaQueryWrapper<ProductSkuWholesalePrice> lqw = Wrappers.lambdaQuery();
        lqw.eq(ProductSkuWholesalePrice::getProductSkuId, productSkuId)
            .eq(ProductSkuWholesalePrice::getTieredPriceId, tieredPriceId);
        return baseMapper.selectOne(lqw);
    }

    public ProductSkuWholesalePrice getOneByProductIdAndProductSkuId(Long productId, Long productSkuId) {
        log.info("【根据商品Id和skuId最低阶梯数量的价格实体】 productId = {}, productSkuId = {}", productId, productSkuId);
        List<ProductWholesaleTieredPrice> pwts = iProductWholesaleTieredPriceService.queryListByProductId(productId);
        if (CollUtil.size(pwts) > 0) {
            Long tieredPriceId = pwts.get(0).getId();
            return this.getOneByProductSkuIdAndTieredPriceId(productSkuId, tieredPriceId);
        }
        return null;
    }
}
