package com.zsmall.product.entity.iservice;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hengjian.common.log.annotation.InMethodLog;
import com.hengjian.common.satoken.utils.LoginHelper;
import com.zsmall.product.entity.domain.ProductSku;
import com.zsmall.product.entity.domain.ProductSkuPrice;
import com.zsmall.product.entity.domain.TenantFavorites;
import com.zsmall.product.entity.domain.bo.tenantFavorites.TenantFavoritesListBo;
import com.zsmall.product.entity.domain.vo.tenantFavorites.TenantFavoritesListVo;
import com.zsmall.product.entity.mapper.TenantFavoritesMapper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 租户收藏夹-数据库层
 * <AUTHOR>
 * @date 2023/8/16
 */
@Service
public class ITenantFavoritesService extends ServiceImpl<TenantFavoritesMapper, TenantFavorites> {
    @Resource
    private IProductSkuService iProductSkuService;
    @Resource
    private IProductSkuPriceService iProductSkuPriceService;

    @InMethodLog("分页查询租户收藏夹")
    public Page<TenantFavoritesListVo> queryPage(TenantFavoritesListBo bo, Page<TenantFavorites> page) {
        return baseMapper.queryPage(LoginHelper.getTenantId(), bo, page);
    }

    @InMethodLog("查询所有收藏的商品编号")
    public List<String> queryAllProductCode() {
        return baseMapper.queryAllProductCode();
    }

    @InMethodLog("根据商品编号查询收藏夹")
    public TenantFavorites queryByProductCode(String productCode) {
        LambdaQueryWrapper<TenantFavorites> lqw = Wrappers.lambdaQuery();
        lqw.eq(TenantFavorites::getProductCode, productCode);
        return baseMapper.selectOne(lqw);
    }

    @InMethodLog("判断商品是否在收藏夹中")
    public Boolean inFavorites(String productCode) {
        LambdaQueryWrapper<TenantFavorites> lqw = Wrappers.lambdaQuery();
        lqw.eq(TenantFavorites::getProductCode, productCode);
        return baseMapper.exists(lqw);
    }

    @InMethodLog("根据主键删除收藏夹")
    public Boolean deleteByIds(List<String> ids) {
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    @InMethodLog("根据收藏夹主键查询商品主键")
    public List<Long> queryProductIdsByFavoritesIds(List<String> favoritesIds) {
        return baseMapper.queryProductIdsByFavoritesIds(favoritesIds);
    }
    @InMethodLog("根据商品编号创建收藏夹")
    public List<TenantFavorites> createByProductCode(String productCode, String tenantId) {
        LambdaQueryWrapper<ProductSku> lqw = new LambdaQueryWrapper<ProductSku>().eq(ProductSku::getProductCode, productCode)
                                                                                .eq(ProductSku::getDelFlag, 0);
        List<TenantFavorites> favoritesList = new ArrayList<>();
        List<ProductSku> productSkus = iProductSkuService.list(lqw);
        for (ProductSku skus : productSkus) {
            TenantFavorites tenantFavorites = new TenantFavorites();

            tenantFavorites.setProductId(skus.getProductId());
            tenantFavorites.setProductCode(productCode);
            tenantFavorites.setProductSkuId(skus.getId());
            tenantFavorites.setProductSkuCode(skus.getProductSkuCode());
            // 所有仓库的库存总数 stock_total
//            tenantFavorites.setStockTotal();
            // sku_price
            LambdaQueryWrapper<ProductSkuPrice> skuLqw = new LambdaQueryWrapper<ProductSkuPrice>().eq(ProductSkuPrice::getProductSkuCode, skus.getProductSkuCode())
                                                                                              .eq(ProductSkuPrice::getDelFlag, 0);
            ProductSkuPrice productSkuPrice = iProductSkuPriceService.getOne(skuLqw);
            tenantFavorites.setPlatformPickUpPrice(productSkuPrice.getPlatformPickUpPrice());
            tenantFavorites.setPlatformDropShippingPrice(productSkuPrice.getPlatformDropShippingPrice());
            tenantFavorites.setTenantId(tenantId);
            favoritesList.add(tenantFavorites);
        }
        return favoritesList;
    }

    public void removeByProductCodeAndTenantId(String productCode, String tenantId) {
        baseMapper.delete(Wrappers.<TenantFavorites>lambdaQuery().eq(TenantFavorites::getProductCode, productCode)
                                                                .eq(TenantFavorites::getTenantId, tenantId));
    }
}
