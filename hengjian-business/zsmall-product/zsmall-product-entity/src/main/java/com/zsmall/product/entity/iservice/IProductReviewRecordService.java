package com.zsmall.product.entity.iservice;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hengjian.common.core.enums.TenantType;
import com.hengjian.common.core.utils.MapstructUtils;
import com.hengjian.common.core.utils.StringUtils;
import com.hengjian.common.log.annotation.InMethodLog;
import com.hengjian.common.satoken.utils.LoginHelper;
import com.hengjian.common.tenant.helper.TenantHelper;
import com.zsmall.common.enums.product.ProductReviewTypeEnum;
import com.zsmall.common.enums.product.ProductVerifyStateEnum;
import com.zsmall.product.entity.domain.ProductReviewRecord;
import com.zsmall.product.entity.domain.bo.ProductReviewRecordBo;
import com.zsmall.product.entity.domain.dto.product.ProductReviewPageDTO;
import com.zsmall.product.entity.domain.vo.ProductReviewRecordVo;
import com.zsmall.product.entity.mapper.ProductReviewRecordMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 商品审核记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-05-31
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class IProductReviewRecordService extends ServiceImpl<ProductReviewRecordMapper, ProductReviewRecord> {

    private final ProductReviewRecordMapper baseMapper;

    /**
     * 查询商品审核记录
     */
    public ProductReviewRecordVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    public Page<ProductReviewRecordVo> queryPage(Page<ProductReviewPageDTO> page, ProductReviewPageDTO query) {
        TenantType tenantType = LoginHelper.getTenantTypeEnum();
        if (TenantType.Manager.equals(tenantType)) {
            return TenantHelper.ignore(() -> baseMapper.queryPage(page, query));
        } else {
            return baseMapper.queryPage(page, query);
        }
    }

    public List<ProductReviewRecord> selectList(String productCode) {
        LambdaQueryWrapper<ProductReviewRecord> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(ProductReviewRecord::getProductCode, productCode)
            .eq(ProductReviewRecord::getReviewState, ProductVerifyStateEnum.Draft)
            .orderByDesc(ProductReviewRecord::getCreateTime);
        return baseMapper.selectList(lambdaQueryWrapper);
    }

    /**
     * 查询商品审核记录列表
     */

    /**
     * 查询商品审核记录列表
     */
    public List<ProductReviewRecordVo> queryList(ProductReviewRecordBo bo) {
        LambdaQueryWrapper<ProductReviewRecord> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    public List<ProductReviewRecordVo> queryListByIds(List<Long> ids) {
        LambdaQueryWrapper<ProductReviewRecord> lqw = Wrappers.lambdaQuery();
        lqw.in(ProductReviewRecord::getId, ids);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<ProductReviewRecord> buildQueryWrapper(ProductReviewRecordBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<ProductReviewRecord> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getProductCode()), ProductReviewRecord::getProductCode, bo.getProductCode());
        lqw.eq(StringUtils.isNotBlank(bo.getReviewOpinion()), ProductReviewRecord::getReviewOpinion, bo.getReviewOpinion());
        lqw.eq(bo.getSubmitDateTime() != null, ProductReviewRecord::getSubmitDateTime, bo.getSubmitDateTime());
        lqw.eq(bo.getSubmitUserId() != null, ProductReviewRecord::getSubmitUserId, bo.getSubmitUserId());
        lqw.eq(bo.getReviewUserId() != null, ProductReviewRecord::getReviewUserId, bo.getReviewUserId());
        lqw.eq(bo.getReviewDateTime() != null, ProductReviewRecord::getReviewDateTime, bo.getReviewDateTime());
        lqw.eq(StringUtils.isNotBlank(bo.getReviewType()), ProductReviewRecord::getReviewType, bo.getReviewType());
        lqw.eq(StringUtils.isNotBlank(bo.getReviewStatus()), ProductReviewRecord::getReviewState, bo.getReviewStatus());
        return lqw;
    }

    /**
     * 新增商品审核记录
     */
    public Boolean insertByBo(ProductReviewRecordBo bo) {
        ProductReviewRecord add = MapstructUtils.convert(bo, ProductReviewRecord.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        return flag;
    }

    public Boolean insert(ProductReviewRecord entity) {
        return baseMapper.insert(entity) > 0;
    }

    /**
     * 修改商品审核记录
     */
    public Boolean updateByBo(ProductReviewRecordBo bo) {
        ProductReviewRecord update = MapstructUtils.convert(bo, ProductReviewRecord.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(ProductReviewRecord entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 商品某个商品审核中的记录
     *
     * @param productCode
     * @return
     */
    public ProductReviewRecord queryRecordByProductCode(String productCode) {
        log.info("进入【商品某个商品审核中的记录】 productCode = {}", productCode);
        LambdaQueryWrapper<ProductReviewRecord> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(ProductReviewRecord::getProductCode, productCode)
            .eq(ProductReviewRecord::getReviewState, ProductVerifyStateEnum.Pending.name());
        return baseMapper.selectOne(lambdaQueryWrapper);
    }


    /**
     * 商品某个商品审核中的记录
     *
     * @param productCode
     * @return
     */
    public ProductReviewRecord queryNotNewProductRecordByProductCode(String productCode) {
        log.info("进入【商品某个商品审核中的记录】 productCode = {}", productCode);
        List<String> verifyStatusList = new ArrayList<>();
        verifyStatusList.add(ProductVerifyStateEnum.Pending.name());
        verifyStatusList.add(ProductVerifyStateEnum.Accepted.name());
        LambdaQueryWrapper<ProductReviewRecord> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(ProductReviewRecord::getProductCode, productCode)
            .in(ProductReviewRecord::getReviewState, verifyStatusList)
            .ne(ProductReviewRecord::getReviewType, ProductReviewTypeEnum.NewProduct)
            .orderByDesc(ProductReviewRecord::getCreateTime);
        List<ProductReviewRecord> list = baseMapper.selectList(lambdaQueryWrapper);
        return CollUtil.isEmpty(list) ? null : list.get(0);
    }

    /**
     * 商品某个商品指定审核状态的记录，若有多条取最新一条
     *
     * @param productCode
     * @param reviewType
     * @return
     */
    public ProductReviewRecord queryRecordByProductCodeAndReviewType(String productCode, ProductVerifyStateEnum reviewType) {
        log.info("进入【商品某个商品审核中的记录】 productCode = {}", productCode);
        LambdaQueryWrapper<ProductReviewRecord> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(ProductReviewRecord::getProductCode, productCode)
            .eq(ProductReviewRecord::getReviewState, reviewType)
            .orderByDesc(ProductReviewRecord::getReviewDateTime);
        List<ProductReviewRecord> recordEntities = baseMapper.selectList(lambdaQueryWrapper);
        return CollUtil.isNotEmpty(recordEntities) ? recordEntities.get(0) : null;
    }
    /**
     * 商品某个商品指定审核状态的记录，若有多条取最新一条
     *
     * @param productCodes
     * @param reviewType
     * @return
     */
    public List<ProductReviewRecord> queryRecordByProductCodesAndReviewType(List<String> productCodes, ProductVerifyStateEnum reviewType) {
        log.info("进入【商品某个商品审核中的记录】 productCode = {}", productCodes);
        return baseMapper.queryRecordByProductCodesAndReviewType(productCodes,reviewType.name());
    }

    /**
     * 将所有Pending记录改成遗弃，除了当前记录id
     *
     * @param productCode
     * @param nowRecordId
     */
    public void setAllPendingToAbandoned(String productCode, Long nowRecordId) {
        baseMapper.setAllPendingToAbandoned(productCode, nowRecordId);
    }

    /**
     * 查询审核记录关联的Item No
     *
     * @param recordId
     * @return
     */
    public List<String> queryProductSkuCodeByRecord(Long recordId) {
        return this.baseMapper.queryProductSkuCodeByRecord(recordId);
    }

    public List<ProductReviewRecordVo> getListByProductCodePending(String productCode, String reviewType) {
        log.info("进入【商品某个商品审核中的记录】 productCode = {}, reviewType = {}", productCode, reviewType);
        LambdaQueryWrapper<ProductReviewRecord> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(ProductReviewRecord::getProductCode, productCode)
            .eq(ProductReviewRecord::getReviewState, reviewType)
            .orderByDesc(ProductReviewRecord::getReviewDateTime);
        return baseMapper.selectVoList(lambdaQueryWrapper);

    }

    /**
     * 获取某个商品最后一条审核数据
     *
     * @param productCode
     * @return
     */
    public ProductReviewRecord getLastByProductCode(String productCode) {
        log.info("进入【获取某个商品最后一条审核数据】 productCode = {}", productCode);
        LambdaQueryWrapper<ProductReviewRecord> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(ProductReviewRecord::getProductCode, productCode)
            .orderByDesc(ProductReviewRecord::getReviewDateTime);
        return baseMapper.selectList(lambdaQueryWrapper).get(0);
    }

    /**
     * 根据商品编号删除所有审核中的记录
     *
     * @param productCode
     * @return
     */
    public Boolean deletePendingByProductCode(String productCode) {
        log.info("进入【根据商品编号删除所有审核中的记录】 productCode = {}", productCode);
        LambdaQueryWrapper<ProductReviewRecord> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(ProductReviewRecord::getProductCode, productCode)
            .eq(ProductReviewRecord::getReviewState, ProductVerifyStateEnum.Pending.name());
        return baseMapper.delete(lambdaQueryWrapper) > 0;
    }

    /**
     * 根据商品编码集合获取审核数据集合
     * @param productCodeList
     * @return
     */
    public List<ProductReviewRecord> queryListByProductCodeList(List<String> productCodeList) {
        LambdaQueryWrapper<ProductReviewRecord> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.in(ProductReviewRecord::getProductCode, productCodeList)
        .eq(ProductReviewRecord::getReviewState, ProductVerifyStateEnum.Pending);
        return baseMapper.selectList(lambdaQueryWrapper);
    }

    public List<ProductReviewRecord> queryPendingListByIds(List<Long> reviewRecordIds) {
        log.info("进入【商品某个商品审核中的记录】 reviewRecordIds = {}", JSONUtil.toJsonStr(reviewRecordIds));
        LambdaQueryWrapper<ProductReviewRecord> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.in(ProductReviewRecord::getId, reviewRecordIds)
            .eq(ProductReviewRecord::getReviewState, ProductVerifyStateEnum.Pending.name());
        return baseMapper.selectList(lambdaQueryWrapper);
    }

    /**
     * 根据Item No查询指定状态和审核类型的记录数量
     * @param productSkuCode
     * @param reviewTypeEnum
     * @param productVerifyStateEnum
     * @return
     */
    public Boolean existsByProductSkuCode(String productSkuCode, ProductReviewTypeEnum reviewTypeEnum,
                                      ProductVerifyStateEnum productVerifyStateEnum) {
        return baseMapper.existsByProductSkuCode(productSkuCode, reviewTypeEnum.name(), productVerifyStateEnum.name()) > 0;
    }

    /**
     * 批量查询SKU的审核记录存在性
     * @param productSkuCodes SKU编码列表
     * @param reviewTypeEnum 审核类型
     * @param productVerifyStateEnum 审核状态
     * @return Map<String, Boolean> SKU编码 -> 是否存在审核记录
     */
    public Map<String, Boolean> existsByProductSkuCodes(List<String> productSkuCodes, ProductReviewTypeEnum reviewTypeEnum,
                                                        ProductVerifyStateEnum productVerifyStateEnum) {
        if (CollUtil.isEmpty(productSkuCodes)) {
            return new HashMap<>();
        }

        // 批量查询存在审核记录的SKU编码
        List<String> existingSkuCodes = baseMapper.existsByProductSkuCodes(productSkuCodes, reviewTypeEnum.name(), productVerifyStateEnum.name());
        Set<String> existingSkuSet = new HashSet<>(existingSkuCodes);

        // 构建结果Map
        Map<String, Boolean> result = new HashMap<>();
        for (String skuCode : productSkuCodes) {
            result.put(skuCode, existingSkuSet.contains(skuCode));
        }

        log.info("【批量查询SKU审核记录】查询{}个SKU，{}个存在{}状态的{}审核记录",
                productSkuCodes.size(), existingSkuCodes.size(), productVerifyStateEnum.name(), reviewTypeEnum.name());

        return result;
    }

    @InMethodLog("根据商品SKU查询最新的审核记录")
    public ProductReviewRecordVo queryNewestRecordByProductSkuCode(String productSkuCode, ProductReviewTypeEnum reviewTypeEnum,
                                                     ProductVerifyStateEnum productVerifyStateEnum) {
        return baseMapper.queryNewestRecordByProductSkuCode(productSkuCode, reviewTypeEnum.name(), productVerifyStateEnum.name());
    }
    @InMethodLog("根据商品SKU查询最新的审核记录")
    public Long getIdByProductSkuCode(String productSkuCode, ProductReviewTypeEnum productReviewTypeEnum,
                                      ProductVerifyStateEnum productVerifyStateEnum) {
        return baseMapper.getIdByProductSkuCode(productSkuCode, productReviewTypeEnum.name(), productVerifyStateEnum.name());
    }

}
