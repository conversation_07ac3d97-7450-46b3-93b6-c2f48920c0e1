package com.zsmall.product.entity.iservice;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hengjian.common.core.utils.MapstructUtils;
import com.hengjian.common.core.utils.StringUtils;
import com.hengjian.common.mybatis.core.page.PageQuery;
import com.hengjian.common.mybatis.core.page.TableDataInfo;
import com.zsmall.common.enums.OperationSymbolEnum;
import com.zsmall.common.enums.productSku.PriceItemTypeEnum;
import com.zsmall.product.entity.domain.ProductSkuPriceRuleItem;
import com.zsmall.product.entity.domain.bo.productSkuPrice.ProductSkuPriceRuleItemBo;
import com.zsmall.product.entity.domain.vo.productSkuPrice.ProductSkuPriceRuleItemVo;
import com.zsmall.product.entity.mapper.ProductSkuPriceRuleItemMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 商品sku价格计算公式Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-05-22
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class IProductSkuPriceRuleItemService {

    private final ProductSkuPriceRuleItemMapper baseMapper;

    /**
     * 查询商品sku价格计算公式
     */
    public ProductSkuPriceRuleItemVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询商品sku价格计算公式列表
     */
    public TableDataInfo<ProductSkuPriceRuleItemVo> queryPageList(ProductSkuPriceRuleItemBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<ProductSkuPriceRuleItem> lqw = buildQueryWrapper(bo);
        Page<ProductSkuPriceRuleItemVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询商品sku价格计算公式列表
     */
    public List<ProductSkuPriceRuleItemVo> queryList(ProductSkuPriceRuleItemBo bo) {
        LambdaQueryWrapper<ProductSkuPriceRuleItem> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<ProductSkuPriceRuleItem> buildQueryWrapper(ProductSkuPriceRuleItemBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<ProductSkuPriceRuleItem> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getProductSkuPriceRuleId() != null, ProductSkuPriceRuleItem::getProductSkuPriceRuleId, bo.getProductSkuPriceRuleId());
        lqw.eq(StringUtils.isNotBlank(bo.getPriceItemType()), ProductSkuPriceRuleItem::getPriceItemType, bo.getPriceItemType());
        lqw.eq(bo.getPriceCal() != null, ProductSkuPriceRuleItem::getPriceCal, bo.getPriceCal());
        lqw.eq(bo.getPriceCalValue() != null, ProductSkuPriceRuleItem::getPriceCalValue, bo.getPriceCalValue());
        return lqw;
    }

    /**
     * 新增商品sku价格计算公式
     */
    public Boolean insertByBo(ProductSkuPriceRuleItemBo bo) {
        ProductSkuPriceRuleItem add = MapstructUtils.convert(bo, ProductSkuPriceRuleItem.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改商品sku价格计算公式
     */
    public Boolean updateByBo(ProductSkuPriceRuleItemBo bo) {
        ProductSkuPriceRuleItem update = MapstructUtils.convert(bo, ProductSkuPriceRuleItem.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(ProductSkuPriceRuleItem entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除商品sku价格计算公式
     */
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    public Boolean isExistByRuleId(Long ruleId) {
        return CollUtil.isEmpty(this.queryListByRuleId(ruleId)) ? false : true;
    }

    public List<ProductSkuPriceRuleItemVo> queryListByRuleId(Long ruleId) {
        LambdaQueryWrapper<ProductSkuPriceRuleItem> lqw = new LambdaQueryWrapper<>();
        lqw.eq(ProductSkuPriceRuleItem::getProductSkuPriceRuleId, ruleId);
        return baseMapper.selectVoList(lqw);
    }

    public Boolean saveBatch(List<ProductSkuPriceRuleItem> items) {
        return baseMapper.insertBatch(items);
    }

    public List<ProductSkuPriceRuleItemVo> parseRuleItemListByRuleInfo(Long productSkuPriceRuleId, Integer unitPriceCal, BigDecimal unitPriceCalValue, Integer operationFeeCal, BigDecimal operationFeeCalValue, Integer finalDeliveryFeeCal, BigDecimal finalDeliveryFeeCalValue) {
        ProductSkuPriceRuleItemVo unitPriceEntity = new ProductSkuPriceRuleItemVo(productSkuPriceRuleId, PriceItemTypeEnum.UnitPrice.name(), unitPriceCal, unitPriceCalValue);
        ProductSkuPriceRuleItemVo operationFeeFeeEntity = new ProductSkuPriceRuleItemVo(productSkuPriceRuleId, PriceItemTypeEnum.OperationFee.name(), operationFeeCal, operationFeeCalValue);
        ProductSkuPriceRuleItemVo finalDeliveryFee = new ProductSkuPriceRuleItemVo(productSkuPriceRuleId, PriceItemTypeEnum.FinalDeliveryFee.name(), finalDeliveryFeeCal, finalDeliveryFeeCalValue);
        List<ProductSkuPriceRuleItemVo> ruleItemList = new ArrayList<>();
        ruleItemList.add(unitPriceEntity);
        ruleItemList.add(operationFeeFeeEntity);
        ruleItemList.add(finalDeliveryFee);
        return ruleItemList;
    }

    public List<ProductSkuPriceRuleItemVo> getItemListByRuleId(Long ruleId) {
        log.info("进入【通过价格公式id获取公式明细数据】方法, ruleId = {}", ruleId);
        LambdaQueryWrapper<ProductSkuPriceRuleItem> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProductSkuPriceRuleItem::getProductSkuPriceRuleId, ruleId);
        return baseMapper.selectVoList(queryWrapper);
    }

    public List<ProductSkuPriceRuleItemVo> getItemListByRuleIds(List<Long> ruleIds) {
        log.info("进入【通过价格公式id获取公式明细数据】方法, ruleIds = {}", JSONUtil.toJsonStr(ruleIds));
        LambdaQueryWrapper<ProductSkuPriceRuleItem> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(ProductSkuPriceRuleItem::getProductSkuPriceRuleId, ruleIds);
        return this.baseMapper.selectVoList(queryWrapper);
    }

    public Map<Long, String> getPriceFormulaByRuleIds(List<Long> ruleIds) {
        log.info("进入【根据规则第三集合获取获取定价公式】方法, ruleIds = {}", JSONUtil.toJsonStr(ruleIds));
        List<ProductSkuPriceRuleItemVo> itemVoList = this.getItemListByRuleIds(ruleIds);
        Map<Long, String> map = new HashMap<>();
        ruleIds.forEach(ruleId -> {
            List<ProductSkuPriceRuleItemVo> items = itemVoList.stream().filter(item -> ObjectUtil.equals(item.getProductSkuPriceRuleId(), ruleId)).collect(Collectors.toList());
            if (CollUtil.isEmpty(items)) return;
            String unitPrice = "";
            String operationFee = "";
            String finalDeliveryFee = "";
            for (ProductSkuPriceRuleItemVo item : items) {
                if (StrUtil.equals(item.getPriceItemType(), PriceItemTypeEnum.UnitPrice.name())) {
//                    unitPrice = "(" + PriceItemTypeEnum.UnitPrice.name() + OperationSymbolEnum.fromCode(item.getPriceCal()) + item.getPriceCalValue() + ")";
                    unitPrice = "(" + "商品单价" + OperationSymbolEnum.fromCode(item.getPriceCal()) + item.getPriceCalValue() + ")";
                }
                if (StrUtil.equals(item.getPriceItemType(), PriceItemTypeEnum.OperationFee.name())) {
//                    operationFee = "(" + PriceItemTypeEnum.UnitPrice.name() + OperationSymbolEnum.fromCode(item.getPriceCal()) + item.getPriceCalValue() + ")";
                    operationFee = "(" + "操作费" + OperationSymbolEnum.fromCode(item.getPriceCal()) + item.getPriceCalValue() + ")";
                }
                if (StrUtil.equals(item.getPriceItemType(), PriceItemTypeEnum.FinalDeliveryFee.name())) {
//                    finalDeliveryFee = "(" + PriceItemTypeEnum.UnitPrice.name() + OperationSymbolEnum.fromCode(item.getPriceCal()) + item.getPriceCalValue() + ")";
                    finalDeliveryFee = "(" + "尾程派送费" + OperationSymbolEnum.fromCode(item.getPriceCal()) + item.getPriceCalValue() + ")";
                }
            }
            map.put(ruleId, unitPrice + "+" + operationFee + "+" + finalDeliveryFee);
        });
        return map;
    }

    public Boolean updateDeleteMarkByRuleId(Long ruleId) {
        log.info("进入【根据公式id批量修改删除明细信息】方法, ruleId = {}", ruleId);
        List<ProductSkuPriceRuleItemVo> itemVos = this.queryListByRuleId(ruleId);
        if (CollUtil.isEmpty(itemVos)) {
            return true;
        }
        List<Long> ids = itemVos.stream().map(ProductSkuPriceRuleItemVo::getId).collect(Collectors.toList());
        Boolean aBoolean = this.deleteWithValidByIds(ids, false);
        return aBoolean;
    }

    public Boolean matchItems(List<ProductSkuPriceRuleItemVo> items, List<ProductSkuPriceRuleItemVo> newItems) {
        for (ProductSkuPriceRuleItemVo item : items) {
            List<ProductSkuPriceRuleItemVo> collect = newItems.stream().filter(newItem ->
                StrUtil.equals(newItem.getPriceItemType(), item.getPriceItemType()) &&
                    NumberUtil.equals(newItem.getPriceCal(), item.getPriceCal()) &&
                    NumberUtil.equals(newItem.getPriceCalValue(), item.getPriceCalValue())
            ).collect(Collectors.toList());
            if (CollUtil.isEmpty(collect)) {
                return false;
            }
        }
        return true;
    }
}
