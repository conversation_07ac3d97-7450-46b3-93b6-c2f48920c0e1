package com.zsmall.product.entity.iservice;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hengjian.common.core.utils.MapstructUtils;
import com.hengjian.common.mybatis.core.page.PageQuery;
import com.hengjian.common.mybatis.core.page.TableDataInfo;
import com.hengjian.common.tenant.helper.TenantHelper;
import com.zsmall.product.entity.domain.ProductSkuPrice;
import com.zsmall.product.entity.domain.RuleLevelProductPrice;
import com.zsmall.product.entity.domain.bo.productSkuPrice.ProductSkuPriceBo;
import com.zsmall.product.entity.domain.vo.productSkuPrice.ProductSkuPriceVo;
import com.zsmall.product.entity.mapper.ProductSkuPriceMapper;
import java.util.*;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 商品SKU定价Service业务层处理
 *
 * <AUTHOR> Li
 * @date 2023-05-22
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class IProductSkuPriceService extends ServiceImpl<ProductSkuPriceMapper, ProductSkuPrice> {

    private final ProductSkuPriceMapper baseMapper;


    /**
     * 查询商品SKU定价
     */
    public ProductSkuPriceVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询商品SKU定价列表
     */
    public TableDataInfo<ProductSkuPriceVo> queryPageList(ProductSkuPriceBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<ProductSkuPrice> lqw = buildQueryWrapper(bo);
        Page<ProductSkuPriceVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询商品SKU定价列表
     */
    public List<ProductSkuPriceVo> queryList(ProductSkuPriceBo bo) {
        LambdaQueryWrapper<ProductSkuPrice> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    public List<ProductSkuPrice> queryListByIds(List<Long> ids) {
        LambdaQueryWrapper<ProductSkuPrice> lqw = Wrappers.lambdaQuery();
        lqw.in(ProductSkuPrice::getId, ids);
        return baseMapper.selectList(lqw);
    }

    private LambdaQueryWrapper<ProductSkuPrice> buildQueryWrapper(ProductSkuPriceBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<ProductSkuPrice> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getProductSkuId() != null, ProductSkuPrice::getProductSkuId, bo.getProductSkuId());
        lqw.eq(bo.getOriginalUnitPrice() != null, ProductSkuPrice::getOriginalUnitPrice, bo.getOriginalUnitPrice());
        lqw.eq(bo.getOriginalOperationFee() != null, ProductSkuPrice::getOriginalOperationFee, bo.getOriginalOperationFee());
        lqw.eq(bo.getOriginalFinalDeliveryFee() != null, ProductSkuPrice::getOriginalFinalDeliveryFee, bo.getOriginalFinalDeliveryFee());
        lqw.eq(bo.getOriginalPickUpPrice() != null, ProductSkuPrice::getOriginalPickUpPrice, bo.getOriginalPickUpPrice());
        lqw.eq(bo.getOriginalDropShippingPrice() != null, ProductSkuPrice::getOriginalDropShippingPrice, bo.getOriginalDropShippingPrice());
        lqw.eq(bo.getPlatformUnitPrice() != null, ProductSkuPrice::getPlatformUnitPrice, bo.getPlatformUnitPrice());
        lqw.eq(bo.getPlatformOperationFee() != null, ProductSkuPrice::getPlatformOperationFee, bo.getPlatformOperationFee());
        lqw.eq(bo.getPlatformFinalDeliveryFee() != null, ProductSkuPrice::getPlatformFinalDeliveryFee, bo.getPlatformFinalDeliveryFee());
        lqw.eq(bo.getPlatformPickUpPrice() != null, ProductSkuPrice::getPlatformPickUpPrice, bo.getPlatformPickUpPrice());
        lqw.eq(bo.getPlatformDropShippingPrice() != null, ProductSkuPrice::getPlatformDropShippingPrice, bo.getPlatformDropShippingPrice());
        lqw.eq(bo.getMsrp() != null, ProductSkuPrice::getMsrp, bo.getMsrp());
        return lqw;
    }

    /**
     * 新增商品SKU定价
     */
    public Boolean insertByBo(ProductSkuPriceBo bo) {
        ProductSkuPrice add = MapstructUtils.convert(bo, ProductSkuPrice.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 新增商品SKU定价
     *
     * @param entity
     * @return
     */
    public Boolean insert(ProductSkuPrice entity) {
        log.info("进入【新增商品SKU定价】 entity = {}", JSONUtil.toJsonStr(entity));
        return baseMapper.insert(entity) > 0;
    }

    /**
     * 批量新增商品SKU定价
     * @param list
     * @return
     */
    public Boolean batchInsert(List<ProductSkuPrice> list) {
        return baseMapper.insertOrUpdateBatch(list);
    }

    /**
     * 修改商品SKU定价
     */
    public Boolean updateByBo(ProductSkuPriceBo bo) {
        ProductSkuPrice update = MapstructUtils.convert(bo, ProductSkuPrice.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(ProductSkuPrice entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除商品SKU定价
     */
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }


    public ProductSkuPriceVo getBySkuId(Long skuId) {
        log.info("进入【根据SkuId查询商品价格】方法, skuId = {}", skuId);
        LambdaQueryWrapper<ProductSkuPrice> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProductSkuPrice::getProductSkuId, skuId);
        return baseMapper.selectVoOne(queryWrapper);
    }

    public List<ProductSkuPriceVo> getSitePriceBySkuId(Long skuId) {
        log.info("进入【根据SkuId查询商品价格】方法, skuId = {}", skuId);
        LambdaQueryWrapper<ProductSkuPrice> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProductSkuPrice::getProductSkuId, skuId);
        return baseMapper.selectVoList(queryWrapper);
    }

    /**
     * 根据Sku主键查询商品价格,多站点改造后,此处用默认us站点价格
     *
     * @param productSkuId
     * @return
     */
    public ProductSkuPrice queryByProductSkuId(Long productSkuId) {
        log.info("进入【根据Sku主键查询商品价格】方法, productSkuId = {}", productSkuId);
        LambdaQueryWrapper<ProductSkuPrice> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProductSkuPrice::getProductSkuId, productSkuId);
        queryWrapper.eq(ProductSkuPrice::getCountryCode, "US");
        return baseMapper.selectOne(queryWrapper);
    }

    public List<ProductSkuPrice> queryListByProductSkuId(Long productSkuId) {
        log.info("进入【根据Sku主键查询商品价格】方法, productSkuId = {}", productSkuId);
        LambdaQueryWrapper<ProductSkuPrice> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProductSkuPrice::getProductSkuId, productSkuId);
        return baseMapper.selectList(queryWrapper);
    }
    public ProductSkuPrice queryByProductSkuIdAndSiteId(Long productSkuId,Long siteId) {
        log.info("进入【根据Sku主键查询商品价格】方法, productSkuId = {}", productSkuId);
        LambdaQueryWrapper<ProductSkuPrice> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProductSkuPrice::getProductSkuId, productSkuId);
        queryWrapper.eq(ProductSkuPrice::getSiteId, siteId);
        return baseMapper.selectOne(queryWrapper);
    }

    public List<ProductSkuPrice> getProductSkuPriceListByProductSkuIds(List<Long> productSkuIds) {
            log.info("进入【通过skuId集合获取商品价格数据集合】方法, productSkuIds = {}", productSkuIds);
            LambdaQueryWrapper<ProductSkuPrice> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.in(ProductSkuPrice::getProductSkuId, productSkuIds);
            return baseMapper.selectList(queryWrapper);
    }

    public List<ProductSkuPrice> getListByProductId(Long productId) {
        return TenantHelper.ignore(() -> baseMapper.getListByProductId(productId));
    }

    /**
     * 根据Sku主键删除
     * @param productSkuId
     * @return
     */
    public Boolean deleteByProductSkuId(Long productSkuId) {
        log.info("进入【根据Sku主键数组删除】 productSkuId = {}", JSONUtil.toJsonStr(productSkuId));
        LambdaQueryWrapper<ProductSkuPrice> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProductSkuPrice::getProductSkuId, productSkuId);
        return baseMapper.delete(queryWrapper) > 0;
    }

    /**
     * 根据Sku主键数组删除
     *
     * @param productSkuIdList
     * @return
     */
    public Boolean deleteByProductSkuIdList(List<Long> productSkuIdList) {
        log.info("进入【根据Sku主键数组删除】 productSkuIdList = {}", JSONUtil.toJsonStr(productSkuIdList));
        LambdaQueryWrapper<ProductSkuPrice> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(ProductSkuPrice::getProductSkuId, productSkuIdList);
        return baseMapper.delete(queryWrapper) > 0;
    }

    /**
     * 根据商品SKU唯一编号查询商品SKU定价
     * @param productSkuCode
     * @return
     */
    public ProductSkuPrice queryByProductSkuCode(String productSkuCode) {
        log.info("进入【根据商品SKU唯一编号查询商品SKU详情】 productSkuCode = {}", productSkuCode);
        LambdaQueryWrapper<ProductSkuPrice> lqw = new LambdaQueryWrapper<>();
        lqw.eq(ProductSkuPrice::getProductSkuCode, productSkuCode);
        return baseMapper.selectOne(lqw);
    }
    public List<ProductSkuPrice> queryListByProductSkuCode(String productSkuCode) {
        log.info("进入【根据商品SKU唯一编号查询商品SKU详情】 productSkuCode = {}", productSkuCode);
        LambdaQueryWrapper<ProductSkuPrice> lqw = new LambdaQueryWrapper<>();
        lqw.eq(ProductSkuPrice::getProductSkuCode, productSkuCode);
        return baseMapper.selectList(lqw);
    }
    /**
     * 根据商品SKU唯一编号查询商品SKU定价
     * @param productSkuCode
     * @param countryCode
     * @return
     */
    public ProductSkuPrice queryByProductSkuCodeAndCountryCode(String productSkuCode,String countryCode) {
        log.info("进入【根据商品SKU唯一编号查询商品SKU详情】 productSkuCode = {}", productSkuCode);
        LambdaQueryWrapper<ProductSkuPrice> lqw = new LambdaQueryWrapper<>();
        lqw.eq(ProductSkuPrice::getProductSkuCode, productSkuCode);
        lqw.eq(ProductSkuPrice::getCountryCode, countryCode);
        return baseMapper.selectOne(lqw);
    }

    public ProductSkuPrice queryByProductSkuCodeAndSite(String productSkuCode,Long siteId) {
        log.info("进入【根据商品SKU唯一编号查询商品SKU详情】 productSkuCode = {}", productSkuCode);
        LambdaQueryWrapper<ProductSkuPrice> lqw = new LambdaQueryWrapper<>();
        lqw.eq(ProductSkuPrice::getProductSkuCode, productSkuCode);
        lqw.eq(ProductSkuPrice::getSiteId, siteId);
        lqw.eq(ProductSkuPrice::getDelFlag, 0);
        return baseMapper.selectOne(lqw);
    }

    /**
     * 分仓查询库存
     * @param productSkuCode
     * @return
     */
    public List<HashMap<String, Object>> getStockWithWarehouseSystemCode(String productSkuCode,String tenantId) {
        log.info("进入【分仓查询库存】 productSkuCode = {},租户ID={}", productSkuCode,tenantId);
        return baseMapper.getStockWithWarehouseSystemCode(productSkuCode,tenantId);
    }

    /**
     * 分仓查询库存使用resultMap返回
     * @param productSkuCode
     * @param tenantId
     * @return
     */
    public List<HashMap<String, Object>> getStockWithWarehouseSystemCodeByResultMap(String productSkuCode,String tenantId) {
        log.info("进入【分仓查询库存】 productSkuCode = {},租户ID={}", productSkuCode,tenantId);
        return baseMapper.getStockWithWarehouseSystemCodeNew(productSkuCode,tenantId);

    }
    public List<HashMap<String, Object>> getStockWithWarehouseSystemCodeBySite(String productSkuCode,String tenantId,String site) {
        log.info("进入【分仓查询站点库存】 productSkuCode = {},租户ID={},站点国家={}", productSkuCode,tenantId,site);
        return baseMapper.getStockWithWarehouseSystemCodeBySite(productSkuCode,tenantId,site);

    }

    /**
     * 分仓查询库存
     * @param productSkuId
     * @return
     */
    public Integer getSoldOutByProductSkuId(Long productSkuId) {
        log.info("进入【根据商品SKU主键查询商品已售数量】 productSkuId = {}", productSkuId);

        return baseMapper.getSoldOutByProductSkuId(productSkuId);
    }


    /**
     * 功能描述：按站点和产品sku id获取列表
     *
     * @param productSkuIds 产品sku id
     * @param siteIds       站点ID
     * @return {@link List }<{@link ProductSkuPrice }>
     * <AUTHOR>
     * @date 2025/01/09
     */
    public List<ProductSkuPrice> getListBySiteAndProductSkuId(ArrayList<Long> productSkuIds, ArrayList<Long> siteIds) {
        log.info("进入【通过skuId集合和站点获取商品价格数据集合】方法, productSkuIds = {},siteIds={}", productSkuIds,siteIds);
        LambdaQueryWrapper<ProductSkuPrice> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(ProductSkuPrice::getProductSkuId, productSkuIds);
        wrapper.in(ProductSkuPrice::getSiteId, siteIds);
        return this.list(wrapper);
    }
    /**
     * 根据商品SKU唯一编号查询商品SKU详情
     *
     * @param productSkuCodeSet 商品SKU唯一编号集合
     * @return 商品SKU价格，键为商品SKU ID和站点的组合，值为商品SKU价格
     */
    public Map<String, ProductSkuPrice> getProductSkuSitePriceMapById(Set<String> productSkuCodeSet) {
        if (CollUtil.isEmpty(productSkuCodeSet)) {
            return Collections.emptyMap();
        }
        LambdaQueryWrapper<ProductSkuPrice> ps = new LambdaQueryWrapper<>();
        ps.in(ProductSkuPrice::getProductSkuCode,productSkuCodeSet);
        List<ProductSkuPrice> productSkuPrices = TenantHelper.ignore(()->baseMapper.selectList(ps));
        // 在键冲突的情况下保留现有的值
        return productSkuPrices.stream()
                               .collect(
                                   Collectors.toMap(
                                       productSkuPrice ->
                                           productSkuPrice.getProductSkuId() + "-" + productSkuPrice.getCountryCode(),
                                       productSkuPrice -> productSkuPrice,
                                       (existing, replacement) -> existing // 在键冲突的情况下保留现有的值
                                   ));
    }

    /**
     * 根据商品SKU唯一编号查询商品SKU价格
     *
     * @param productSkuCodeSet 商品SKU唯一编号集合
     * @return 商品SKU价格，键为商品SKU ID和站点的组合，值为商品SKU价格
     */

    public Map<String, ProductSkuPrice> getProductSkuSitePriceMapByCode(Set<String> productSkuCodeSet) {
        if (CollUtil.isEmpty(productSkuCodeSet)) {
            return Collections.emptyMap();
        }
        LambdaQueryWrapper<ProductSkuPrice> ps = new LambdaQueryWrapper<>();
        ps.in(ProductSkuPrice::getProductSkuCode,productSkuCodeSet);
        List<ProductSkuPrice> productSkuPrices = TenantHelper.ignore(()->baseMapper.selectList(ps));
        // 在键冲突的情况下保留现有的值
        return productSkuPrices.stream()
                               .collect(
                                   Collectors.toMap(
                                       productSkuPrice ->
                                           productSkuPrice.getProductSkuCode() + "-" + productSkuPrice.getCountryCode(),
                                       productSkuPrice -> productSkuPrice,
                                       (existing, replacement) -> existing // 在键冲突的情况下保留现有的值
                                   ));
    }

    /**
     * 根据商品SKU唯一编号查询商品会员价
     * @param productSkuCodeSet 商品SKU唯一编号集合
     * @return 商品SKU详情映射，键为商品SKU ID和站点的组合，值为商品SKU详情
     */
    public Map<String, RuleLevelProductPrice> getRuleLevelProductPriceSitePriceMapById(Set<String> productSkuCodeSet, String tenantId) {
        if (CollUtil.isEmpty(productSkuCodeSet)) {
            return Collections.emptyMap();
        }
        //判断是否开启测算
//        Integer isCalculation = baseMapper.getIsCalculation(tenantId);
//        if (isCalculation!=1){
//            return Collections.emptyMap();
//        }
        List<RuleLevelProductPrice> ruleLevelProductPrices = TenantHelper.ignore(()->baseMapper.getRuleLevelProductPriceSitePriceMap(productSkuCodeSet,tenantId));
        return ruleLevelProductPrices.stream()
                                     .collect(
                                         Collectors.toMap(
                                             ruleLevelProductPrice ->
                                                 ruleLevelProductPrice.getProductSkuId() + "-" + ruleLevelProductPrice.getCountryCode(),
                                             ruleLevelProductPrice -> ruleLevelProductPrice,
                                             (existing, replacement) -> existing // 在键冲突的情况下保留现有的值
                                         ));
    }

    /**
     * 根据商品SKU唯一编号查询商品会员价
     * @param productSkuCodeSet 商品SKU唯一编号集合
     * @return 商品SKU详情映射，键为商品SKU ID和站点的组合，值为商品SKU详情
     */
    public Map<String, RuleLevelProductPrice> getRuleLevelProductPriceSitePriceMapByCode(Set<String> productSkuCodeSet,String tenantId) {
        if (CollUtil.isEmpty(productSkuCodeSet)) {
            return Collections.emptyMap();
        }
        //判断是否开启测算
//        Integer isCalculation = baseMapper.getIsCalculation(tenantId);
//        if (isCalculation!=1){
//            return Collections.emptyMap();
//        }
        List<RuleLevelProductPrice> ruleLevelProductPrices = TenantHelper.ignore(()->baseMapper.getRuleLevelProductPriceSitePriceMap(productSkuCodeSet,tenantId));
        return ruleLevelProductPrices.stream()
                                     .collect(
                                         Collectors.toMap(
                                             ruleLevelProductPrice ->
                                                 ruleLevelProductPrice.getProductSkuCode() + "-" + ruleLevelProductPrice.getCountryCode(),
                                             ruleLevelProductPrice -> ruleLevelProductPrice,
                                             (existing, replacement) -> existing // 在键冲突的情况下保留现有的值
                                         ));
    }

    /**
     * 批量查询商品SKU价格
     * @param productSkuIds SKU主键集合
     * @return SKU价格列表
     */
    public List<ProductSkuPrice> queryByProductSkuIds(List<Long> productSkuIds) {
        log.info("进入【批量查询商品SKU价格】 productSkuIds = {}", JSONUtil.toJsonStr(productSkuIds));
        if (CollUtil.isEmpty(productSkuIds)) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<ProductSkuPrice> lqw = new LambdaQueryWrapper<>();
        lqw.in(ProductSkuPrice::getProductSkuId, productSkuIds);
        lqw.eq(ProductSkuPrice::getDelFlag, 0);
        // 注意：与原始 list() 方法保持一致，不使用特殊的租户处理
        return baseMapper.selectList(lqw);
    }
}
