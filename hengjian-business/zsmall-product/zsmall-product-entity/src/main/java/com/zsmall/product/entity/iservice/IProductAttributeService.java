package com.zsmall.product.entity.iservice;

import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hengjian.common.core.utils.MapstructUtils;
import com.hengjian.common.core.utils.StringUtils;
import com.hengjian.common.log.annotation.InMethodLog;
import com.hengjian.common.mybatis.core.page.PageQuery;
import com.hengjian.common.mybatis.core.page.TableDataInfo;
import com.zsmall.common.enums.product.AttributeTypeEnum;
import com.zsmall.product.entity.domain.ProductAttribute;
import com.zsmall.product.entity.domain.bo.ProductAttributeBo;
import com.zsmall.product.entity.domain.vo.ProductAttributeVo;
import com.zsmall.product.entity.mapper.ProductAttributeMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 商品SPU属性Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-05-29
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class IProductAttributeService extends ServiceImpl<ProductAttributeMapper, ProductAttribute> {

    private final ProductAttributeMapper baseMapper;

    /**
     * 查询商品SPU属性
     */
    public ProductAttributeVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询商品SPU属性（实体类）
     *
     * @param id
     * @return
     */
    public ProductAttribute queryEntityById(Long id) {
        log.info("进入【查询商品SPU属性（实体类）】 id = {}", id);
        return baseMapper.selectById(id);
    }

    /**
     * 查询商品SPU属性列表
     */
    public TableDataInfo<ProductAttributeVo> queryPageList(ProductAttributeBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<ProductAttribute> lqw = buildQueryWrapper(bo);
        Page<ProductAttributeVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询商品SPU属性列表
     */
    public List<ProductAttributeVo> queryList(ProductAttributeBo bo) {
        LambdaQueryWrapper<ProductAttribute> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    /**
     * 查询商品SPU属性（最多一个结果）
     *
     * @param bo
     * @return
     */
    public ProductAttribute queryOneByBo(ProductAttributeBo bo) {
        log.info("进入【查询商品SPU属性（最多一个结果）】 bo = {}", JSONUtil.toJsonStr(bo));
        return baseMapper.selectOne(buildQueryWrapper(bo));
    }

    private LambdaQueryWrapper<ProductAttribute> buildQueryWrapper(ProductAttributeBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<ProductAttribute> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getProductId() != null, ProductAttribute::getProductId, bo.getProductId());
        lqw.eq(StringUtils.isNotBlank(bo.getAttributeType()), ProductAttribute::getAttributeType, bo.getAttributeType());
        lqw.like(StringUtils.isNotBlank(bo.getAttributeName()), ProductAttribute::getAttributeName, bo.getAttributeName());
        // lqw.eq(StringUtils.isNotBlank(bo.getAttributeValue()), ProductAttribute::getAttributeValue, bo.getAttributeValue());
        lqw.eq(bo.getAttributeSort() != null, ProductAttribute::getAttributeSort, bo.getAttributeSort());
        lqw.eq(bo.getAttributeSourceId() != null, ProductAttribute::getAttributeSourceId, bo.getAttributeSourceId());
        return lqw;
    }

    /**
     * 新增商品SPU属性
     */
    public Boolean insertByBo(ProductAttributeBo bo) {
        ProductAttribute add = MapstructUtils.convert(bo, ProductAttribute.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改商品SPU属性
     */
    public Boolean updateByBo(ProductAttributeBo bo) {
        ProductAttribute update = MapstructUtils.convert(bo, ProductAttribute.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(ProductAttribute entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除商品SPU属性
     */
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    /**
     * 根据商品主键和属性类型查询
     *
     * @param productId
     * @param attachmentTypeEnum
     * @return
     */
    public List<ProductAttribute> queryByProductIdAndAttributeType(Long productId, AttributeTypeEnum attachmentTypeEnum) {
        log.info("进入【根据商品主键和属性类型查询】 productId = {}, attachmentTypeEnum = {}", productId, attachmentTypeEnum);
        LambdaQueryWrapper<ProductAttribute> lqw = Wrappers.lambdaQuery();
        lqw.eq(ProductAttribute::getProductId, productId).eq(ProductAttribute::getAttributeType, attachmentTypeEnum)
            .orderByAsc(ProductAttribute::getAttributeSort);
        return baseMapper.selectList(lqw);
    }

    @InMethodLog("根据商品主键和属性类型查询")
    public List<ProductAttribute> queryByProductId(Long productId) {
        LambdaQueryWrapper<ProductAttribute> lqw = Wrappers.lambdaQuery();
        lqw.eq(ProductAttribute::getProductId, productId)
            .orderByAsc(ProductAttribute::getAttributeSort);
        return baseMapper.selectList(lqw);
    }

    /**
     * 根据商品主键查询所有已存在的属性主键
     * @param productId
     * @return
     */
    public List<Long> queryIdsByProductId(Long productId) {
        log.info("进入【根据商品主键查询所有已存在的属性主键】 productId = {}", productId);
        return baseMapper.queryIdsByProductId(productId);
    }

    /**
     * 批量保存
     * @param entityList
     * @return
     */
    public Boolean insertOrUpdateBatch(Collection<ProductAttribute> entityList) {
        log.info("进入【批量保存】 entityList = {}", JSONUtil.toJsonStr(entityList));
        return baseMapper.insertOrUpdateBatch(entityList);
    }

    /**
     * 根据商品主键删除
     * @param productId
     * @return
     */
    public Boolean deleteByProductId(Long productId) {
        log.info("进入【根据商品主键删除】 productId = {}", productId);
        LambdaQueryWrapper<ProductAttribute> lqw = Wrappers.lambdaQuery();
        lqw.eq(ProductAttribute::getProductId, productId);
        return baseMapper.delete(lqw) > 0;
    }

    @InMethodLog("是否存在必填的属性")
    public Boolean existRequiredAttribute(Long productId) {
        return baseMapper.existRequiredAttribute(productId);
    }

}
