package com.zsmall.product.entity.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zsmall.product.entity.domain.RuleLevelProductPrice;
import com.zsmall.product.entity.domain.member.MemberRuleRelation;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【rule_level_product_price】的数据库操作Mapper
* @createDate 2024-05-15 11:51:47
* @Entity
*/
public interface RuleLevelProductPriceMapper extends BaseMapper<RuleLevelProductPrice> {

    List<RuleLevelProductPrice> getBatchPrice(@Param("levelIdGroups") List<Long> levelIds,@Param("productSkuIdGroups") List<Long> productIds,@Param("tenantId")String tenantId,@Param("siteId")Long siteId);

    void updateSkuPriceBatch(@Param("list") List<RuleLevelProductPrice> vo);

    String getSysDictData(@Param("dictLabel") String dictLabel);

    MemberRuleRelation selectByIdForMemberRuleRelation(@Param("id")Long id);
    void updateRuleLevelProductPriceNullValue(@Param("ru") RuleLevelProductPrice ruleLevelProductPrice);



//    void add(List<MemberSkuPriceVo> vo);
}




