package com.zsmall.product.entity.domain.dto.productSku;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2022/9/21 10:41
 */
@Data
public class ProductSkuByDayDto {


    /**
     * 日期
     */
    private String day;

    /**
     * 代发价
     */
    private BigDecimal dropShippingPrice;

    /**
     * 自提价
     */
    private BigDecimal pickUpPrice;

    /**
     * 库存
     */
    private Integer inventoryNum;

    /**
     *
     */
    private String productSkuCode;

    /**
     * 分销商id
     */
    private String userCode;


}
