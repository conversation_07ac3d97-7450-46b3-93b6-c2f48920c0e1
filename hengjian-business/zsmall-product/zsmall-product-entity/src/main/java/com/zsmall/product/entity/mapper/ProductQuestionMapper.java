package com.zsmall.product.entity.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.hengjian.common.mybatis.core.mapper.BaseMapperPlus;
import com.zsmall.product.entity.domain.ProductQuestion;
import com.zsmall.product.entity.domain.vo.prodcutQuestion.ProductQuestionVo;
import org.apache.ibatis.annotations.Param;

/**
 * 商品问答Mapper接口
 *
 * <AUTHOR>
 * @date 2023-07-26
 */
public interface ProductQuestionMapper extends BaseMapperPlus<ProductQuestion, ProductQuestionVo> {

    /**
     * 查询编码是否存在
     * @param code
     * @return
     */
    @InterceptorIgnore(tenantLine = "true")
    boolean existQuestionCode(@Param("questionCode") String code);
}
