<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zsmall.product.entity.mapper.ProductSkuStatisticsMapper">

    <select id="queryProductSkuPage" resultType="com.zsmall.product.entity.domain.dto.productSku.ProductSkuOrderManagerDTO">
        SELECT sku.id,
        sku.product_sku_code AS itemNo,
        sku.name,
        sku.sku,
        psa.attachment_show_url AS showUrl,
        sku.tenant_id,
        sku.create_time,
        sku.update_time,
        sku.stock_total,
        IFNULL(psp.platform_pick_up_price, 0) AS pick_up_price,
        IFNULL(psp.platform_drop_shipping_price, 0) AS drop_shipping_price,
        IFNULL(op.sales_num, 0) AS sales_num,
        IFNULL(sp.drop_num_by_dis, 0) AS drop_num_by_dis,
        IFNULL(p.download_count, 0) AS downloaded_by_dis,
        IFNULL(op.oid, 0) AS order_total_num,
        IFNULL(ops.restock_total_num, 0) AS restock_total_num,
        IFNULL(SUM(cast(op.order_total_price AS DECIMAL(19, 2))), 0) AS order_total_price,
        IFNULL(op.order_deal_effectiveness, 0) AS order_deal_effectiveness
        FROM product_sku sku
        INNER JOIN product p ON p.id = sku.product_id
        INNER JOIN product_sku_price psp ON psp.product_sku_id = sku.id
        LEFT JOIN (SELECT attachment_show_url, product_sku_id
        FROM product_sku_attachment
        WHERE del_flag = '0'
        GROUP BY product_sku_id) AS psa ON psa.product_sku_id = sku.id
        LEFT JOIN (SELECT sdp.product_id AS pid,
        COUNT(DISTINCT sdp.tenant_id) AS drop_num_by_dis
        FROM tenant_favorites sdp
        WHERE sdp.del_flag = '0'
        AND EXISTS (SELECT 1 FROM product p WHERE p.id = sdp.product_id AND p.del_flag = '0')
        AND EXISTS (SELECT 1
        FROM sys_tenant st
        WHERE st.tenant_id = sdp.tenant_id AND st.tenant_type = 'Distributor')
        GROUP BY sdp.product_id) AS sp ON sp.pid = sku.product_id
        LEFT JOIN (SELECT oi.product_sku_code,
        COUNT(DISTINCT oi.id) AS oid,
        IFNULL(SUM(cast(o.platform_payable_total_amount AS DECIMAL(19, 2))),
        0) AS order_total_price,
        IFNULL(SUM(oi.total_quantity - oi.restock_quantity), 0) AS sales_num,
        CEIL(
        SUM(
        IF
        (
        oi.dispatched_time IS NULL,
        0,
        ABS(
        TIMESTAMPDIFF(SECOND, o.pay_time, oi.dispatched_time)))) /
        COUNT(oi.dispatched_time) / 3600
        ) AS order_deal_effectiveness
        FROM order_item oi
        INNER JOIN orders o ON o.id = oi.order_id
        WHERE o.order_state  IN ('Paid', 'Refunded', 'Verifying')
        GROUP BY oi.product_sku_code) AS op ON op.product_sku_code = sku.product_sku_code
        LEFT JOIN (SELECT oi.product_sku_code,
        COUNT(DISTINCT ori.id) AS restock_total_num
        FROM order_refund_item ori
        INNER JOIN order_refund r ON ori.order_refund_id = r.id
        INNER JOIN order_item oi ON ori.order_item_id = oi.id
        WHERE r.refund_state != 'Reject'
        AND oi.product_sku_code IS NOT NULL
        GROUP BY oi.product_sku_code
        ORDER BY oi.product_sku_code DESC) AS ops ON ops.product_sku_code = sku.product_sku_code
        WHERE p.del_flag = '0'
        AND sku.del_flag = '0'
        AND (p.product_type IS NULL OR p.product_type = 'NormalProduct')
        AND sku.sku IS NOT NULL
        <if test="dto.queryType == 'ItemNo' and dto.queryValue != ''">
            AND sku.product_sku_code like CONCAT('%', #{dto.queryValue}, '%')
        </if>
        <if test="dto.queryType == 'SKU' and dto.queryValue != ''">
            AND sku.sku like CONCAT('%', #{dto.queryValue}, '%')
        </if>
        <if test="dto.queryType == 'UserCode' and dto.queryValue != ''">
            AND p.tenant_id like CONCAT('%', #{dto.queryValue}, '%')
        </if>
        GROUP BY sku.product_sku_code;
    </select>

    <select id="queryProductSkuSupplierPage" resultType="com.zsmall.product.entity.domain.dto.productSku.ProductSkuOrderSupplierDTO">
        select s.tenant_id,
        IFNULL(s.company_name, '') AS company_name,
        IFNULL(u.email, '') AS email,
        IFNULL(u.phonenumber, '') AS phone_number,
        u.create_time,
        IFNULL(skus.sku_stock_num,0) AS sku_stock_num,
        IFNULL(skus.sku_num,0) AS sku_num,
        IFNULL(op.sales_num,0) AS sales_num,
        IFNULL(sp.drop_num_by_dis,0) AS drop_num_by_dis,
        IFNULL(op.oid, 0) AS order_total_num,
        IFNULL(ops.restock_total_num, 0) AS restock_total_num,
        IFNULL(op.sku_order_num, 0) AS sku_order_num,
        IFNULL(op.order_total_price, 0) AS order_total_price,
        IFNULL(op.order_deal_effectiveness, 0) AS order_deal_effectiveness
        from sys_tenant s
        INNER JOIN sys_user u ON s.tenant_id = u.tenant_id
        LEFT JOIN (select oi.supplier_tenant_id as supplier_tenant_id, COUNT(DISTINCT o.id) as oid,COUNT(DISTINCT
        oi.product_sku_code) AS
        sku_order_num,
        IFNULL(SUM(cast(oi.platform_payable_total_amount as decimal(19, 2))), 0) AS order_total_price,
        IFNULL(SUM(oi.total_quantity - oi.restock_quantity), 0) sales_num,
        CEIL(SUM(IF(oi.dispatched_time IS NULL, 0, ABS(TIMESTAMPDIFF(SECOND,o.pay_time,oi.dispatched_time)))) /
        COUNT(oi.dispatched_time) / 3600) AS
        order_deal_effectiveness
        from order_item oi
        INNER JOIN orders o ON o.id = oi.order_id
        where o.order_state  IN ('Paid', 'Refunded', 'Verifying')  and o.order_type = 'Normal'
        GROUP BY oi.supplier_tenant_id) AS op ON op.supplier_tenant_id = s.tenant_id
        LEFT JOIN (select oi.supplier_tenant_id,
        COUNT(DISTINCT ori.id) AS restock_total_num
        from order_refund_item ori
        LEFT JOIN order_refund r ON ori.order_refund_id = r.id
        LEFT JOIN order_item oi ON ori.order_item_id = oi.id
        LEFT JOIN orders o ON o.id = oi.order_id
        where r.refund_state != 'Reject' and o.order_type = 'Normal'
        GROUP BY oi.supplier_tenant_id) AS ops ON ops.supplier_tenant_id = s.tenant_id
        LEFT JOIN (SELECT p.tenant_id, COUNT(DISTINCT sdp.tenant_id) AS drop_num_by_dis
        FROM product p
        LEFT JOIN tenant_favorites sdp ON sdp.product_id = p.id
        where p.del_flag = '0'
        AND sdp.del_flag = '0'
        AND EXISTS (SELECT 1 FROM sys_tenant s WHERE s.tenant_id = sdp.tenant_id AND s.tenant_type = 'Distributor')
        GROUP BY p.tenant_id) AS sp ON sp.tenant_id = s.tenant_id
        LEFT JOIN (SELECT sku.tenant_id,COUNT(DISTINCT sku.product_sku_code) AS sku_num, COUNT(DISTINCT
        IF(sku.stock_total = 0 or sku.stock_total is NULL, NULL
        ,sku.product_sku_code)) AS sku_stock_num
        FROM product_sku sku
        WHERE sku.del_flag = '0'
        AND EXISTS (SELECT 1 from product p where p.id = sku.product_id AND p.del_flag = '0')
        GROUP BY sku.tenant_id) AS skus ON skus.tenant_id = s.tenant_id
        WHERE s.tenant_type = 'Supplier'
        AND s.product_source_type = 'OverseasSpotProducts'
        <if test="dto.queryValue != null and dto.queryValue != ''">
            AND (s.tenant_id like CONCAT('%', #{dto.queryValue}, '%')
            OR s.company_name like CONCAT('%', #{dto.queryValue}, '%'))
        </if>
        <if test="dto.startTime != null and dto.startTime != '' and dto.endTime != null and dto.endTime != ''">
            AND s.create_time &gt;= CONCAT(#{dto.startTime},' 00:00:00')
            AND s.create_time &lt;= CONCAT(#{dto.endTime},' 23:59:59')
        </if>
        GROUP BY s.tenant_id;
    </select>


    <select id="queryProductSkuDistributorPage"
            resultType="com.zsmall.product.entity.domain.dto.productSku.ProductSkuOrderDistributorDTO">
        select s.tenant_id,
        IFNULL(s.company_name, '') AS company_name,
        IFNULL(u.email, '') AS email,
        IFNULL(u.phonenumber, '') AS phone_number,
        u.create_time,
        IFNULL(COUNT(DISTINCT pm.product_sku_code), 0) AS sku_distribution_num,
        IFNULL(op.sales_num, 0) AS sales_num,
        IFNULL(sp.drop_num_by_dis, 0) AS drop_num_by_dis,
        IFNULL(dp.downloaded_by_dis, 0) AS downloaded_by_dis,
        IFNULL(op.oid, 0) AS order_total_num,
        IFNULL(ops.restock_total_num, 0) AS restock_total_num,
        IFNULL(op.sku_order_num, 0) AS sku_order_num,
        IFNULL(op.order_total_price, 0) AS order_total_price
        from sys_tenant s
        INNER JOIN sys_user u ON s.tenant_id = u.tenant_id
        LEFT JOIN (select o.tenant_id AS sid,
        COUNT(DISTINCT oi.id) as oid,
        COUNT(DISTINCT oi.product_sku_code) AS
        sku_order_num,
        IFNULL(SUM(oi.total_quantity - oi.restock_quantity), 0) AS sales_num,
        IFNULL(SUM(cast(oi.platform_payable_total_amount as decimal(19, 2))),
        0) AS order_total_price
        from order_item oi
        INNER JOIN orders o ON o.id = oi.order_id
        where o.order_state  IN ('Paid', 'Refunded', 'Verifying') AND o.order_type = 'Normal'
        GROUP BY o.tenant_id) AS op ON op.sid = s.tenant_id
        LEFT JOIN (select o.tenant_id,
        COUNT(DISTINCT ori.id) AS restock_total_num
        from order_refund_item ori
        JOIN order_refund r ON ori.order_refund_id = r.id
        JOIN order_item oi ON ori.order_item_id = oi.id
        JOIN orders o ON o.id = oi.order_id AND
        o.order_state  IN ('Paid', 'Refunded', 'Verifying')
        where r.refund_state != 'Reject' AND o.order_type = 'Normal'
        GROUP BY o.tenant_id) AS ops ON ops.tenant_id = s.tenant_id
        LEFT JOIN (SELECT sdp.tenant_id as sid, COUNT(sdp.id) AS drop_num_by_dis
        FROM tenant_favorites sdp
        where sdp.del_flag = '0'
        and exists (select 1
        from product p
        where sdp.product_id = p.id
        AND p.del_flag = '0'
        AND p.shelf_state = 'OnShelf')
        GROUP BY sdp.tenant_id) AS sp ON sp.sid = s.tenant_id
        LEFT JOIN (select tenant_id,COUNT(id) as downloaded_by_dis
        from download_record
        where download_type = 'FavoritesProductPackage'
        GROUP by tenant_id) AS dp ON dp.tenant_id = s.tenant_id
        LEFT JOIN product_mapping pm on pm.tenant_id = s.tenant_id
        AND pm.del_flag = '0'
        AND pm.sync_state IN ('Synced', 'Mapped')
        and exists (select 1 from product p WHERE pm.product_code = p.product_code and p.del_flag = '0')
        WHERE s.tenant_type = 'Distributor'
        <if test="dto.queryValue != null and dto.queryValue != ''">
            AND (s.tenant_id like CONCAT('%', #{dto.queryValue}, '%')
            OR s.company_name like CONCAT('%', #{dto.queryValue}, '%'))
        </if>
        <if test="dto.startTime != null and dto.startTime != '' and dto.endTime != null and dto.endTime != ''">
            AND u.create_time &gt;= CONCAT(#{dto.startTime}, ' 00:00:00')
            AND u.create_time &lt;= CONCAT(#{dto.endTime}, ' 23:59:59')
        </if>
        GROUP BY s.id;
    </select>

    <select id="getDayProductSkuByProductSkuCode"
            resultType="com.zsmall.product.entity.domain.dto.productSku.ProductSkuByDayDto">
        SELECT day,
        drop_shipping_price,
        pick_up_price,
        stock_total as inventory_num,
        product_sku_code
        FROM anls_product_sku_day
        WHERE product_sku_code = #{productSkuCode}
        AND day &gt;= #{startTime}
        AND day &lt;= #{endTime}
        GROUP BY day
    </select>

    <select id="getProductSkuOrdersByDay"
            resultType="com.zsmall.product.entity.domain.dto.productSku.AnalysisProductSkuByDayDto">
        SELECT day,
        product_sku_code              as productSkuCode,
        order_num                     as orderNum,
        platform_payable_total_amount AS totalDropShippingPrice,
        issued_on_behalf_total_price  as issuedOnBehalfTotalPrice,
        self_lifting_total_price      as selfLiftingTotalPrice,
        restock_num                   as restockNum,
        sales_num                     as salesNum,
        issued_on_behalf_order_num    as issuedOnBehalfOrderNum,
        self_lifting_order_num        as selfLiftingOrderNum
        FROM anls_product_sku_orders_day
        WHERE product_sku_code = #{productSkuCode}
        AND day &gt;= #{startTime}
        AND day &lt;= #{endTime}
        GROUP BY day
    </select>

    <select id="getDayProductDropByProductSkuCode"
            resultType="com.zsmall.product.entity.domain.dto.productSku.AnalysisProductSkuByDayDto">
        SELECT proc.day,
        proc.product_id as productId,
        proc.distributor_num   as bulkNum
        FROM anls_product_drop_day proc
        LEFT JOIN product_sku sku ON sku.product_id = proc.product_id
        WHERE sku.product_sku_code = #{productSkuCode}
        AND day &gt;= #{startTime}
        AND day &lt;= #{endTime}
        GROUP BY day
    </select>

    <select id="getProductSkuOrdersByChannel"
            resultType="com.zsmall.product.entity.domain.dto.productSku.AnalysisProductSkuByDayDto">
        select COUNT(DISTINCT o.id)                                                   AS orderNum,
        IFNULL(SUM(cast(oi.platform_payable_total_amount as decimal(19, 2))), 0) AS totalOriginPrice,
        IFNULL(SUM(oi.total_quantity - oi.restock_quantity), 0)                    salesNum,
        o.channel_type,
        oi.product_sku_code
        from order_item oi
        INNER JOIN orders o ON o.id = oi.order_id
        where oi.product_sku_code = #{productSkuCode}
        AND o.order_type = 'Normal'
        AND o.order_state  IN ('Paid', 'Refunded', 'Verifying')
        AND o.pay_time &gt;= CONCAT(#{startTime}, ' 00:00:00')
        AND o.pay_time &lt;= CONCAT(#{endTime}, ' 23:59:59')
        GROUP BY o.channel_type;
    </select>

    <select id="getDisListByProductSkuCode" resultType="com.zsmall.product.entity.domain.dto.productSku.DistributorDataDto">
        SELECT o.tenant_id, IFNULL(SUM(cast(oi.platform_payable_total_amount as decimal(19, 2))), 0) AS totalOriginPrice
        FROM order_item oi
        INNER JOIN orders o ON o.id = oi.order_id
        WHERE oi.product_sku_code = #{productSkuCode}
        AND o.order_state  IN ('Paid', 'Refunded', 'Verifying')
        AND o.pay_time &gt;= CONCAT(#{startTime}, ' 00:00:00')
        AND o.pay_time &lt;= CONCAT(#{endTime}, ' 23:59:59')
        GROUP BY o.tenant_id
        ORDER BY totalOriginPrice DESC;
    </select>

    <select id="getDayProductSkuByProductSkuCodeAndEndTime"
            resultType="com.zsmall.product.entity.domain.dto.productSku.ProductSkuByDayDto">
        SELECT day,
        drop_shipping_price,
        pick_up_price,
        stock_total as inventory_num,
        product_sku_code
        FROM anls_product_sku_day
        WHERE product_sku_code = #{productSkuCode}
        AND day &lt; #{endTime}
        GROUP BY day
        ORDER BY day DESC
        LIMIT 1;
    </select>

    <select id="getProductSkuOrderDisByDay"
            resultType="com.zsmall.product.entity.domain.dto.productSku.AnalysisProductSkuByDayDto">
        SELECT day,
        SUM(sku_order_num)                     AS sku_order_num,
        SUM(order_num)                     AS order_num,
        SUM(issued_on_behalf_order_num)    AS issued_on_behalf_order_num,
        SUM(self_lifting_order_num)        AS self_lifting_order_num,
        SUM(platform_payable_total_amount) AS totalDropShippingPrice,
        SUM(issued_on_behalf_total_price)  AS issued_on_behalf_total_price,
        SUM(self_lifting_total_price)      AS self_lifting_total_price,
        SUM(sales_num)                     AS sales_num,
        distributor_tenant_id              AS tenantId
        FROM anls_distributor_orders_day
        WHERE distributor_tenant_id = #{tenantId}
        AND day &gt;= #{startTime}
        AND day &lt;= #{endTime}
        GROUP BY day
    </select>

    <select id="getProductSkuOrderDistributionByDay"
            resultType="com.zsmall.product.entity.domain.dto.productSku.AnalysisProductSkuByDayDto">
        SELECT day,
        sku_distribution_num,
        tenant_id
        FROM anls_product_sku_distribution_day
        WHERE tenant_id = #{tenantId}
        AND day &gt;= #{startTime}
        AND day &lt;= #{endTime}
        GROUP BY day
    </select>

    <select id="getProductSkuOrdersDisByChannel"
            resultType="com.zsmall.product.entity.domain.dto.productSku.AnalysisProductSkuByDayDto">
        select COUNT(DISTINCT o.id)                                                   AS orderNum,
        IFNULL(SUM(cast(oi.platform_payable_total_amount as decimal(19, 2))), 0) AS totalOriginPrice,
        IFNULL(SUM(oi.total_quantity - oi.restock_quantity), 0)                    salesNum,
        o.channel_type,
        o.tenant_id
        from order_item oi
        INNER JOIN orders o ON o.id = oi.order_id
        where o.tenant_id = #{tenantId}
        AND o.order_type = 'Normal'
        AND o.order_state  IN ('Paid', 'Refunded', 'Verifying')
        AND o.pay_time &gt;= CONCAT(#{startTime}, ' 00:00:00')
        AND o.pay_time &lt;= CONCAT(#{endTime}, ' 23:59:59')
        GROUP BY o.channel_type;
    </select>

    <select id="getSkuListByTenantId" resultType="com.zsmall.product.entity.domain.dto.productSku.DistributorOrdersDataDto">
        select p.product_code,
        IFNULL(SUM(cast(oi.platform_payable_total_amount as decimal(19, 2))), 0) AS totalOriginPrice,
        IFNULL(SUM(oi.total_quantity - oi.restock_quantity), 0)                    salesNum
        from order_item oi
        INNER JOIN orders o ON o.id = oi.order_id
        INNER JOIN order_item_product_sku ops ON ops.order_item_id = oi.id
        INNER JOIN product p ON p.product_code = ops.product_code
        where o.tenant_id = #{tenantId}
        AND o.order_type = 'Normal'
        AND o.order_state  IN ('Paid', 'Refunded', 'Verifying')
        AND o.pay_time &gt;= CONCAT(#{startTime}, ' 00:00:00')
        AND o.pay_time &lt;= CONCAT(#{endTime}, ' 23:59:59')
        GROUP BY p.product_code
        ORDER BY totalOriginPrice DESC;
    </select>

    <select id="getSupListByTenantId" resultType="com.zsmall.product.entity.domain.dto.productSku.DistributorOrdersDataDto">
        select oi.supplier_tenant_id                                                   AS tenantId,
        IFNULL(SUM(cast(oi.platform_payable_total_amount as decimal(19, 2))), 0) AS totalOriginPrice,
        IFNULL(SUM(oi.total_quantity - oi.restock_quantity), 0)                 AS salesNum
        from order_item oi
        INNER JOIN orders o ON o.id = oi.order_id
        where oi.tenant_id = #{tenantId}
        AND o.order_type = 'Normal'
        AND o.order_state  IN ('Paid', 'Refunded', 'Verifying')
        AND o.pay_time &gt;= CONCAT(#{startTime}, ' 00:00:00')
        AND o.pay_time &lt;= CONCAT(#{endTime}, ' 23:59:59')
        GROUP BY oi.supplier_tenant_id
        ORDER BY totalOriginPrice DESC;

    </select>

    <select id="getProductSkuOrderSupByDay"
            resultType="com.zsmall.product.entity.domain.dto.productSku.AnalysisProductSkuByDayDto">
        SELECT day,
        COUNT(order_num)                   AS order_num,
        SUM(platform_payable_total_amount) AS totalDropShippingPrice,
        SUM(issued_on_behalf_total_price)  AS issued_on_behalf_total_price,
        SUM(self_lifting_total_price)      AS self_lifting_total_price,
        SUM(restock_num)                   AS restock_num,
        SUM(sales_num)                     AS sales_num,
        SUM(issued_on_behalf_order_num)    AS issued_on_behalf_order_num,
        SUM(self_lifting_order_num)        AS self_lifting_order_num,
        supplier_tenant_id                 AS tenantId
        FROM anls_supplier_orders_day
        WHERE supplier_tenant_id = #{tenantId}
        AND day &gt;= #{startTime}
        AND day &lt;= #{endTime}
        GROUP BY supplier_tenant_id, day
    </select>

    <select id="getProductSkuOrdersSupByChannel"
            resultType="com.zsmall.product.entity.domain.dto.productSku.AnalysisProductSkuByDayDto">
        select COUNT(DISTINCT o.id)                                                   AS orderNum,
        IFNULL(SUM(cast(oi.platform_payable_total_amount as decimal(19, 2))), 0) AS totalOriginPrice,
        IFNULL(SUM(oi.total_quantity - oi.restock_quantity), 0)                 AS salesNum,
        o.channel_type,
        oi.supplier_tenant_id                                                   AS tenantId
        from order_item oi
        INNER JOIN orders o ON o.id = oi.order_id
        where oi.supplier_tenant_id = #{tenantId}
        AND o.order_type = 'Normal'
        AND o.order_state  IN ('Paid', 'Refunded', 'Verifying')
        AND o.pay_time &gt;= CONCAT(#{startTime}, ' 00:00:00')
        AND o.pay_time &lt;= CONCAT(#{endTime}, ' 23:59:59')
        GROUP BY o.channel_type;
    </select>

    <select id="getDisListByTenantId" resultType="com.zsmall.product.entity.domain.dto.productSku.DistributorDataDto">
        SELECT oi.tenant_id, IFNULL(SUM(cast(oi.platform_payable_total_amount as decimal(19, 2))), 0) AS totalOriginPrice
        FROM order_item oi
        INNER JOIN orders o ON o.id = oi.order_id
        WHERE oi.supplier_tenant_id = #{tenantId}
        AND o.order_type = 'Normal'
        AND o.order_state  IN ('Paid', 'Refunded', 'Verifying')
        AND o.pay_time &gt;= CONCAT(#{startTime}, ' 00:00:00')
        AND o.pay_time &lt;= CONCAT(#{endTime}, ' 23:59:59')
        GROUP BY oi.tenant_id
        ORDER BY totalOriginPrice DESC;
    </select>

    <select id="getDayProductDropByTenantId"
            resultType="com.zsmall.product.entity.domain.dto.productSku.AnalysisProductSkuByDayDto">
        SELECT DATE_FORMAT(sdp.`create_time`, '%Y-%m-%d') AS day,
        COUNT(DISTINCT sdp.tenant_id)              AS bulkNum,
        p.tenant_id
        FROM tenant_favorites sdp
        JOIN product p ON p.id = sdp.product_id
        WHERE p.tenant_id = #{tenantId}
        AND sdp.del_flag = '0'
        AND sdp.`create_time` &gt;= CONCAT(#{startTime}, ' 00:00:00')
        AND sdp.`create_time` &lt;= CONCAT(#{endTime}, ' 23:59:59')
        GROUP BY DATE_FORMAT(sdp.`create_time`, '%Y-%m-%d')
    </select>

    <select id="getDayProductSkuByTenantId"
            resultType="com.zsmall.product.entity.domain.dto.productSku.ProductSkuByDayDto">
        SELECT day,
        SUM(stock_total) as inventory_num,
        tenant_id
        FROM anls_product_sku_day
        WHERE tenant_id = #{tenantId}
        AND day &gt;= #{startTime}
        AND day &lt;= #{endTime}
        GROUP BY day
    </select>

    <select id="getDayProductSkuByTenantIdAndEndTime"
            resultType="com.zsmall.product.entity.domain.dto.productSku.ProductSkuByDayDto">
        SELECT day,
        SUM(stock_total) as inventory_num,
        tenant_id
        FROM anls_product_sku_day
        WHERE tenant_id = #{tenantId}
        AND day &lt; #{endTime}
        GROUP BY day
        ORDER BY day DESC
        LIMIT 1;
    </select>

</mapper>
