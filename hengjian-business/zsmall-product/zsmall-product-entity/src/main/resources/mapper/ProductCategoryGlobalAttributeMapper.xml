<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zsmall.product.entity.mapper.ProductCategoryGlobalAttributeMapper">

    <resultMap id="BaseResultMap" type="com.zsmall.product.entity.domain.ProductCategoryGlobalAttribute">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="tenantId" column="tenant_id" jdbcType="VARCHAR"/>
            <result property="globalAttributeId" column="global_attribute_id" jdbcType="BIGINT"/>
            <result property="productCategoryId" column="product_category_id" jdbcType="BIGINT"/>
            <result property="isRequired" column="is_required" jdbcType="TINYINT"/>
            <result property="customValues" column="custom_values" jdbcType="OTHER"/>
            <result property="delFlag" column="del_flag" jdbcType="CHAR"/>
            <result property="createBy" column="create_by" jdbcType="BIGINT"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateBy" column="update_by" jdbcType="BIGINT"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,tenant_id,global_attribute_id,
        product_category_id,is_required,custom_values,
        del_flag,create_by,create_time,
        update_by,update_time
    </sql>

    <select id="queryByCategoryIdAndGlobalAttributeId" resultType="com.zsmall.product.entity.domain.ProductCategoryGlobalAttribute">
        SELECT pcga.*
        FROM product_category_global_attribute pcga
        WHERE pcga.del_flag = '0'
          AND pcga.product_category_id = #{categoryId}
          AND pcga.global_attribute_id = #{globalAttributeId}
    </select>

    <select id="queryCategoryIdsByGlobalAttributeId" resultType="java.lang.Long">
        SELECT pc.id
        FROM product_category_global_attribute pcga JOIN product_category pc on pcga.product_category_id = pc.id
        WHERE pcga.del_flag = '0' AND pcga.global_attribute_id = #{globalAttributeId} ORDER by pc.category_level ASC
    </select>
</mapper>
