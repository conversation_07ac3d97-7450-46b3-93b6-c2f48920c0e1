<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zsmall.product.entity.mapper.TenantFavoritesMapper">

    <select id="queryAllProductCode" resultType="java.lang.String">
        SELECT DISTINCT(tf.product_code)
        FROM tenant_favorites tf
        WHERE tf.del_flag = '0'
    </select>

    <resultMap id="TenantFavoritesListVoMap" type="com.zsmall.product.entity.domain.vo.tenantFavorites.TenantFavoritesListVo">
        <result column="product_id"/>
        <result column="id" property="favoritesId"/>
        <result column="name" property="productName"/>
        <result column="product_code" property="productCode"/>
        <result column="product_type" property="productType"/>
        <result column="supported_logistics" property="supportedLogistics"/>
        <result column="supported_logistics_array" property="supportedLogisticsArray" typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
        <result column="shelf_state" property="shelfState"/>
        <result column="min_price" property="minPrice"/>
        <result column="stock_total" property="stockTotal"/>
        <result column="on_sale_skus" property="onSaleSkus"/>
        <association property="productImage" column="product_id" select="com.zsmall.product.entity.mapper.ProductSkuAttachmentMapper.queryFirstImageUrlByProductId"/>
    </resultMap>
    <select id="queryPage" resultMap="TenantFavoritesListVoMap">
        SELECT tf.id,
               tf.product_id,
               p.name,
               p.product_code,
               p.product_type,
               p.supported_logistics,
               pwd.delivery_type                  AS 'supported_logistics_array',
               p.shelf_state,
               (SELECT CASE
                           WHEN p.supported_logistics = 'DropShippingOnly' THEN MIN(vpss.platform_drop_shipping_price)
                           ELSE MIN(vpss.platform_pick_up_price) END
                FROM view_product_sku_sales vpss
                WHERE p.id = vpss.product_id
                  AND vpss.sku_del_flag = '0'
                  AND vpss.sku_shelf_state = 'OnShelf'
                GROUP BY vpss.product_id)         AS 'min_price',
               vps.stock_total,
               (SELECT COUNT(ps.id)
                FROM product_sku ps
                WHERE ps.product_id = tf.product_id
                  AND ps.del_flag = '0'
                  AND ps.shelf_state = 'OnShelf') AS 'on_sale_skus'
        FROM tenant_favorites tf
                 JOIN product p on tf.product_id = p.id
                 JOIN view_product_stock vps on p.id = vps.product_id
                 LEFT JOIN product_wholesale_detail pwd ON p.id = pwd.product_id
        WHERE tf.del_flag = '0'
          AND p.del_flag = '0'
          AND tf.tenant_id = #{tenantId}
        <if test="@cn.hutool.core.text.CharSequenceUtil@isNotBlank(bo.productType)">
            AND p.product_code = #{bo.queryType}
        </if>
        <if test="@cn.hutool.core.text.CharSequenceUtil@isNotBlank(bo.queryType) and @cn.hutool.core.text.CharSequenceUtil@isNotBlank(bo.queryValue)">
            <if test="bo.queryType == 'ProductName'">
                AND p.name LIKE CONCAT('%', #{bo.queryValue}, '%')
            </if>
            <if test="bo.queryType == 'ProductSkuCode'">
                AND EXISTS(SELECT 1
                           FROM product_sku ps
                           WHERE ps.product_id = tf.product_id
                             AND ps.product_sku_code = #{bo.queryValue})
            </if>
        </if>
        ORDER BY tf.create_time DESC
    </select>

    <select id="favoriteOrNot" resultType="java.lang.Boolean">
        SELECT COUNT(tf.id)
        FROM tenant_favorites tf
        WHERE tf.del_flag = '0'
          AND tf.product_code = #{productCode}
          AND tf.tenant_id = #{tenantId}
    </select>

    <select id="queryProductIdsByFavoritesIds" resultType="java.lang.Long">
        SELECT DISTINCT tf.product_id FROM tenant_favorites tf JOIN product p on tf.product_id = p.id WHERE tf.del_flag = '0' AND p.del_flag = '0' AND p.shelf_state = 'OnShelf'
        AND tf.id IN
        <foreach collection="favoritesIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="selectTenantFavoritesListVoPage" resultType="com.zsmall.product.entity.domain.TenantFavorites">
        select  tf.*,
        tf.product_sku_code as productSkuCode,
        tf.product_sku_id as productSkuId,
        tf.product_code as productCode
        from tenant_favorites as tf
        inner join product_sku as ps
        on tf.product_sku_id = ps.id
        where tf.del_flag=0
        and ps.del_flag=0
        and tf.tenant_id=#{bo.tenantId}
        <if test="bo.skuId != null and bo.skuId != ''">
            and tf.product_sku_code=#{bo.skuId}
        </if>
        <if test="bo.sku != null and bo.sku != ''">
            and  ps.sku =#{bo.sku}
        </if>
        <if test="bo.productSkuName != null and bo.productSkuName != ''">
            and ps.name like concat('%',#{bo.productSkuName},'%')
        </if>


        <if test="bo.skuShelfState != null and bo.skuShelfState != ''">
            and ps.shelf_state=#{bo.skuShelfState}
        </if>
        <if test="bo.tenantFavoritesIds != null and bo.tenantFavoritesIds.size() != 0">
            and tf.id in
            <foreach collection="bo.tenantFavoritesIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="bo.site != null and bo.site != ''">
            and tf.site =#{bo.site}
        </if>
        group by tf.id
        order by tf.id desc
    </select>

    <delete id="deleteBySpu">
        update tenant_favorites set del_flag=1 where product_code= #{productCode} and tenant_id=#{tenantId}
    </delete>

    <delete id="deleteBySku">
        update tenant_favorites set del_flag=1 where product_sku_code= #{productSkuCode} and tenant_id=#{tenantId}
    </delete>

    <select id="getIsFavoritesIds" resultType="java.lang.Boolean">
        select count(*) > 0 as result
        from tenant_favorites tf
        where del_flag=0
        <if test="productSkuCode!= null and productSkuCode!= ''">
            and tf.product_sku_code=#{productSkuCode}
        </if>
        <if test="tenantId!= null and tenantId!= ''">
            and tf.tenant_id=#{tenantId}
        </if>
    </select>


</mapper>
