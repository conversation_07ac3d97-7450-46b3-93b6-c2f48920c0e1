<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zsmall.product.entity.mapper.ProductCategoryMapper">

  <!--  <resultMap id="BaseResultMap" type="com.zsmall.product.entity.domain.UpcEntity">
        <id property="id" column="id" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="effectiveStatus" column="effective_status" jdbcType="VARCHAR"/>
        <result property="usageScope" column="usage_scope" jdbcType="VARCHAR"/>
        <result property="brand" column="brand" jdbcType="VARCHAR"/>
        <result property="storeId" column="store_id" jdbcType="BIGINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        ,create_time,update_time,
        effective_status,usage_scope,brand,
        store_id
    </sql>

    <select id="getUsageUpcs" resultMap="BaseResultMap">
        select u.* from upc u
        where u.effective_status = 'Valid'
        <if test="storeId != null">
            and u.store_id = #{storeId}
        </if>
        <if test="brand != null AND brand != ''">
            and u.brand = #{brand}
        </if>
        and not exists(
        select 1 from upc_usage_record uur where
        uur.upc = u.id and uur.channel_type = #{channelType}
        and uur.stateType in ('Preempt', 'Assigned'))
    </select>
    <select id="listUpcPage" resultType="com.zsmall.product.entity.domain.dto.product.UsageUpcDTO">
        SELECT u.id,u.brand,usc.name AS channelType,uur.update_time,uur.state_yype
        FROM upc u
        INNER JOIN upc_sales_channel usc ON usc.state_type = 'Valid' AND usc.usage_type = 'UPC_QUERY'
        LEFT JOIN upc_usage_record uur ON u.id = uur.upc AND usc.name = uur.channel_type
        WHERE u.effective_status = 'Valid'
        AND u.storeId = #{storeId}
        <if test="channelType != null AND channelType != ''">
            and usc.name = #{channelType}
        </if>
        <if test="id != null AND id != ''">
            and and u.id = #{id}
        </if>
        ORDER BY u.id asc, usc.name asc, uur.create_time desc
    </select>
    <select id="listUpcUsedPage" resultType="com.zsmall.product.entity.domain.dto.product.UsageUpcDTO">


        SELECT u.id,u.brand,uur.channel_type,uur.update_time,uur.state_type
        FROM upc u
        LEFT JOIN upc_usage_record uur ON u.id = uur.upc
        WHERE u.effective_status = 'Valid'
        AND u.store_id = #{storeId}
        <if test="id != null AND id != ''">
            and u.id = #{id}
        </if>
        <if test="channelType != null AND channelType != ''">
            and uur.channel_type = #{channelType}
        </if>
        AND uur.state_type = 'Assigned'
        ORDER BY u.id asc, uur.channel_type asc, uur.create_time desc
    </select>
    <select id="listUpcUnUsedPage" resultType="com.zsmall.product.entity.domain.dto.product.UsageUpcDTO">


        SELECT u.id,u.brand,usc.name AS channelType,uur.update_time,uur.state_type
        FROM upc u
        INNER JOIN upc_sales_channel usc ON usc.state_type = 'Valid' AND usc.usage_type = 'UPC_QUERY'
        LEFT JOIN upc_usage_record uur ON u.id = uur.upc AND usc.name = uur.channel_type
        WHERE u.effective_status = 'Valid'
        AND u.store_id = #{storeId}
        <if test="id != null AND id != ''">
            and u.id = #{id}
        </if>
        <if test="channelType != null AND channelType != ''">
            and usc.name = #{channelType}
        </if>
        AND (uur.stateType != 'Assigned' or uur.state_type is null)
        ORDER BY u.id asc, usc.name asc, uur.create_time desc
    </select>-->
</mapper>
