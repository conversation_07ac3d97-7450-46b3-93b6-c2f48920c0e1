<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zsmall.product.entity.mapper.TaskStockSyncMapper">

    <resultMap id="BaseResultMap" type="com.zsmall.product.entity.domain.TaskStockSync">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="productSkuId" column="product_sku_id" jdbcType="BIGINT"/>
            <result property="productSkuIdList" column="product_sku_id_list" jdbcType="OTHER"
                    typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
            <result property="taskState" column="task_state" jdbcType="VARCHAR"/>
            <result property="taskExecuteMessage" column="task_execute_message" jdbcType="VARCHAR"/>
            <result property="delFlag" column="del_flag" jdbcType="CHAR"/>
            <result property="createBy" column="create_by" jdbcType="BIGINT"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateBy" column="update_by" jdbcType="BIGINT"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

</mapper>
