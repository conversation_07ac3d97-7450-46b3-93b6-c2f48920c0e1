<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zsmall.product.entity.mapper.RuleLevelProductPriceMapper">

    <resultMap id="BaseResultMap" type="com.zsmall.product.entity.domain.RuleLevelProductPrice">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="ruleCustomizerTenantId" column="rule_customizer_tenant_id" jdbcType="VARCHAR"/>
            <result property="levelId" column="level_id" jdbcType="BIGINT"/>
            <result property="originalUnitPrice" column="original_unit_price" jdbcType="DECIMAL"/>
            <result property="originalOperationFee" column="original_operation_fee" jdbcType="DECIMAL"/>
            <result property="originalFinalDeliveryFee" column="original_final_delivery_fee" jdbcType="DECIMAL"/>
            <result property="originalPickUpPrice" column="original_pick_up_price" jdbcType="DECIMAL"/>
            <result property="originalDropShippingPrice" column="original_drop_shipping_price" jdbcType="DECIMAL"/>
            <result property="productSkuId" column="product_sku_id" jdbcType="BIGINT"/>
            <result property="productId" column="product_id" jdbcType="BIGINT"/>
            <result property="platformUnitPrice" column="platform_unit_price" jdbcType="DECIMAL"/>
            <result property="platformOperationFee" column="platform_operation_fee" jdbcType="DECIMAL"/>
            <result property="platformFinalDeliveryFee" column="platform_final_delivery_fee" jdbcType="DECIMAL"/>
            <result property="platformPickUpPrice" column="platform_pick_up_price" jdbcType="DECIMAL"/>
            <result property="platformDropShippingPrice" column="platform_drop_shipping_price" jdbcType="DECIMAL"/>
            <result property="delFlag" column="del_flag" jdbcType="TINYINT"/>
            <result property="createBy" column="create_by" jdbcType="BIGINT"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateBy" column="update_by" jdbcType="BIGINT"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,rule_customizer_tenant_id,level_id,
        original_unit_price,original_operation_fee,original_final_delivery_fee,
        original_pick_up_price,original_drop_shipping_price,product_sku_id,
        product_id,platform_unit_price,platform_operation_fee,
        platform_final_delivery_fee,platform_pick_up_price,platform_drop_shipping_price,
        del_flag,create_by,create_time,
        update_by,update_time
    </sql>


    <update id="updateSkuPriceBatch">
        <foreach collection="list" item="item" separator=";">
            UPDATE rule_level_product_price
            SET
            <if test="item.originalUnitPrice != null ">
                original_unit_price =#{item.originalUnitPrice},
            </if>
            <if test="item.originalOperationFee != null ">
                original_operation_fee =#{item.originalOperationFee},
            </if>
            <if test="item.originalFinalDeliveryFee != null ">
                original_final_delivery_fee =#{item.originalFinalDeliveryFee},
            </if>
            <if test="item.originalPickUpPrice != null ">
                original_pick_up_price =#{item.originalPickUpPrice},
            </if>
            <if test="item.originalDropShippingPrice != null ">
                original_drop_shipping_price =#{item.originalDropShippingPrice}
            </if>
            WHERE
            rule_customizer_tenant_id = #{item.ruleCustomizerTenantId} AND
            level_id = #{item.levelId} AND
            product_sku_id = #{item.productSkuId}
        </foreach>
    </update>
    <select id="getBatchPrice" resultType="com.zsmall.product.entity.domain.RuleLevelProductPrice">
        SELECT * FROM rule_level_product_price
        WHERE rule_customizer_tenant_id = #{tenantId}
        <if test="levelIdGroups != null and levelIdGroups.size() > 0">
            AND level_id in
            <foreach collection="levelIdGroups" item="levelId" open="(" separator="," close=")">
                #{levelId}
            </foreach>

        </if>
        <if test="productSkuIdGroups != null ">
            AND product_sku_id IN
            <foreach collection="productSkuIdGroups" item="productSkuId" open="(" separator="," close=")">
                #{productSkuId}
            </foreach>
        </if>
        <if test=" siteId!=null">
            and site_id = #{siteId}
        </if>
    </select>
    <select id="getSysDictData" resultType="java.lang.String">
        select dict_value from sys_dict_data where dict_label=#{dictLabel};
    </select>
    <select id="selectByIdForMemberRuleRelation"
            resultType="com.zsmall.product.entity.domain.member.MemberRuleRelation">
        select * from member_rule_relation where id = #{id} and del_flag = 0
    </select>

    <update id="updateRuleLevelProductPriceNullValue">
        update rule_level_product_price
        set rule_customizer_tenant_id=#{ru.ruleCustomizerTenantId},
            level_id=#{ru.levelId},
            original_unit_price = #{ru.originalUnitPrice},
            original_operation_fee = #{ru.originalOperationFee},
            original_final_delivery_fee =#{ru.originalFinalDeliveryFee},
            original_pick_up_price =#{ru.originalPickUpPrice},
            original_drop_shipping_price =#{ru.originalDropShippingPrice},
            site_id = #{ru.siteId},
            currency = #{ru.currency},
            country_code = #{ru.countryCode},
            currency_symbol = #{ru.currencySymbol},
            product_sku_id=#{ru.productSkuId,jdbcType=BIGINT},
            product_id=#{ru.productId,jdbcType=BIGINT},
            platform_unit_price=#{ru.platformUnitPrice},
            platform_operation_fee=#{ru.platformOperationFee},
            platform_final_delivery_fee=#{ru.platformFinalDeliveryFee},
            platform_pick_up_price=#{ru.platformPickUpPrice},
            platform_drop_shipping_price=#{ru.platformDropShippingPrice},
            del_flag=#{ru.delFlag},
            create_by=#{ru.createBy},
            create_time=#{ru.createTime},
            update_by=#{ru.updateBy},
            update_time=#{ru.updateTime}
        where id = #{ru.id}
    </update>
</mapper>
