<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zsmall.product.entity.mapper.ProductMappingExtendRakutenInfoMapper">
  <resultMap id="BaseResultMap" type="com.zsmall.product.entity.domain.ProductMappingExtendRakutenInfo">
    <!--@mbg.generated-->
    <!--@Table product_mapping_extend_rakuten_info-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="tenant_id" jdbcType="VARCHAR" property="tenantId" />
    <result column="tagline" jdbcType="VARCHAR" property="tagline" />
    <result column="genre_id" jdbcType="VARCHAR" property="genreId" />
    <result column="del_flag" jdbcType="CHAR" property="delFlag" />
    <result column="create_by" jdbcType="BIGINT" property="createBy" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_by" jdbcType="BIGINT" property="updateBy" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, tenant_id, tagline, genre_id, del_flag, create_by, create_time, update_by, update_time
  </sql>
</mapper>