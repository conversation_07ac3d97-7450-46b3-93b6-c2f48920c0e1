<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zsmall.product.entity.mapper.ProductWholesaleTieredPriceLogMapper">

    <resultMap id="BaseResultMap" type="com.zsmall.product.entity.domain.ProductWholesaleTieredPriceLog">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="productId" column="product_id" jdbcType="BIGINT"/>
            <result property="productWholesaleTieredPriceId" column="product_wholesale_tiered_price_id" jdbcType="BIGINT"/>
            <result property="minimumQuantity" column="minimum_quantity" jdbcType="INTEGER"/>
            <result property="estimatedOperationFee" column="estimated_operation_fee" jdbcType="DECIMAL"/>
            <result property="estimatedShippingFee" column="estimated_shipping_fee" jdbcType="DECIMAL"/>
            <result property="estimatedHandleTime" column="estimated_handle_time" jdbcType="INTEGER"/>
            <result property="delFlag" column="del_flag" jdbcType="CHAR"/>
            <result property="createBy" column="create_by" jdbcType="BIGINT"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateBy" column="update_by" jdbcType="BIGINT"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,product_id,product_wholesale_tiered_price_id,
        minimum_quantity,estimated_operation_fee,estimated_shipping_fee,
        estimated_handle_time,del_flag,create_by,
        create_time,update_by,update_time
    </sql>
</mapper>
