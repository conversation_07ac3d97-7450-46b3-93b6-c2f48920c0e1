<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zsmall.product.entity.mapper.ProductMappingMapper">

    <resultMap id="ProductMappingTableMap"
               type="com.zsmall.product.entity.domain.vo.productMapping.ProductMappingTableVo">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="productSkuCode" column="product_sku_code" jdbcType="VARCHAR"/>
        <result property="channelAlias" column="channel_alias" jdbcType="VARCHAR"/>
        <result property="channelType" column="channel_type" jdbcType="VARCHAR"/>
        <result property="productName" column="product_name" jdbcType="VARCHAR"/>
        <result property="activityCode" column="activity_code" jdbcType="VARCHAR"/>
        <result property="activityState" column="activity_state" jdbcType="VARCHAR"/>
        <result property="specComposeName" column="spec_compose_name" jdbcType="VARCHAR"/>
        <result property="specValName" column="spec_val_name" jdbcType="VARCHAR"/>
        <result property="stockTotal" column="stock_total" jdbcType="INTEGER"/>
        <result property="markUpValue" column="mark_up_value" jdbcType="DECIMAL"/>
        <result property="finalPrice" column="final_price" jdbcType="DECIMAL"/>
        <result property="syncState" column="sync_state" jdbcType="VARCHAR"/>
        <result property="syncMessage" column="sync_message" jdbcType="OTHER" typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
        <result property="imageSavePath" column="image_save_path" jdbcType="VARCHAR"/>
        <result property="imageShowUrl" column="image_show_url" jdbcType="VARCHAR"/>
    </resultMap>


    <select id="queryProductMappingPage" resultMap="ProductMappingTableMap" resultType="com.zsmall.product.entity.domain.vo.productMapping.ProductMappingTableVo">
        SELECT pm.id,
               pm.product_sku_code,
               (SELECT tsc.channel_alias FROM tenant_sales_channel tsc WHERE pm.channel_id = tsc.id) AS 'channel_alias',
               pm.channel_id,
               pm.channel_type,
               pm.product_name,
               pm.activity_code,
               IF(pm.activity_code IS NULL, NULL, (SELECT pai.activity_state
                                                   FROM product_activity_item pai
                                                   WHERE pai.activity_code = pm.activity_code))      AS 'activity_state',
               pm.spec_compose_name,
               pm.spec_val_name,
               pm.mapping_sku,
               IF(pm.activity_code IS NOT NULL, (SELECT pai.quantity_surplus
                                                 FROM product_activity_item pai
                                                 WHERE pai.activity_code = pm.activity_code),
                  ps.stock_total)                                                                    AS 'stock_total',
               pm.mark_up_value,
               pm.final_price,
               pm.sync_state,
               pm.sync_message,
               pm.image_save_path,
               pm.image_show_url,
               pm.price_changes,
               pm.supplier_tenant_id AS 'supplierTenantId',
               pm.product_sku_id AS 'productSkuId',
               pm.channel_sku,
                pm.channel_sku_id,
                pm.channel_sku_item_number,
                pm.site,
                pm.currency
        FROM product_mapping pm
                 JOIN product_sku ps ON pm.product_sku_id = ps.id
        WHERE pm.tenant_id = #{tenantId}
          AND pm.del_flag = '0'
        <if test="queryBo.site != null and queryBo.site != ''">
            and pm.site=#{queryBo.site}
        </if>
        <if test="@cn.hutool.core.text.CharSequenceUtil@isNotBlank(queryBo.channelType)">
            AND pm.channel_type = #{queryBo.channelType}
        </if>
        <if test="@cn.hutool.core.text.CharSequenceUtil@isNotBlank(queryBo.syncState)">
            and pm.sync_state = #{queryBo.syncState}
        </if>
        <if test="@cn.hutool.core.collection.CollUtil@isNotEmpty(queryBo.channelIds)">
            AND pm.channel_id IN
            <foreach collection="queryBo.channelIds" item="channelId" open="(" separator="," close=")">
                #{channelId}
            </foreach>
        </if>
        <if test="@cn.hutool.core.text.CharSequenceUtil@isNotBlank(queryBo.queryValue)">
            <if test="@cn.hutool.core.text.CharSequenceUtil@equals(queryBo.queryType, 'ProductName')">
                AND pm.product_name LIKE CONCAT('%', #{queryBo.queryValue}, '%')
            </if>
            <if test="@cn.hutool.core.text.CharSequenceUtil@equals(queryBo.queryType, 'ProductSkuCode')">
                AND pm.product_sku_code = #{queryBo.queryValue}
            </if>
            <if test="@cn.hutool.core.text.CharSequenceUtil@equals(queryBo.queryType, 'channelSku')">
                AND pm.channel_sku = #{queryBo.queryValue}
            </if>
            <if test="@cn.hutool.core.text.CharSequenceUtil@equals(queryBo.queryType, 'channelSkuItemNumber')">
                AND pm.channel_sku_item_number = #{queryBo.queryValue}
            </if>
        </if>
        ORDER BY pm.id DESC
    </select>

    <select id="queryListByChannelAndItemNoAndState" resultType="com.zsmall.product.entity.domain.ProductMapping">
        SELECT pm.*
        FROM product_mapping pm JOIN sys_tenant st on pm.tenant_id = st.tenant_id
        WHERE pm.del_flag = '0'
          <if test="productSkuCode != null">
              AND pm.product_sku_code = #{productSkuCode}
          </if>
          AND pm.channel_type = #{channelType}
          AND pm.sync_state = #{syncState}
          AND st.status = '0' AND st.del_flag = '0'
          AND EXISTS(SELECT 1 FROM tenant_sales_channel tas WHERE tas.id = pm.channel_id AND tas.del_flag = '0')
    </select>
    <select id="queryProductMappingPageForTikTok"
            resultType="com.zsmall.product.entity.domain.vo.productMapping.ProductMappingTableVo">
        select pm.*,ps.stock_total from product_mapping pm
            inner join product_sku ps
                on pm.product_sku_code=ps.product_sku_code
        where pm.tenant_id =#{tenantId}
        and pm.channel_type = #{queryBo.channelType}
        <if test="queryBo.site != null and queryBo.site != ''">
            and pm.site=#{queryBo.site}
        </if>
        <if test="@cn.hutool.core.collection.CollUtil@isNotEmpty(queryBo.channelIds)">
            AND pm.channel_id IN
            <foreach collection="queryBo.channelIds" item="channelId" open="(" separator="," close=")">
                #{channelId}
            </foreach>
        </if>
        <if test="@cn.hutool.core.text.CharSequenceUtil@isNotBlank(queryBo.queryValue)">
            <if test="@cn.hutool.core.text.CharSequenceUtil@equals(queryBo.queryType, 'ProductName')">
                AND pm.product_name LIKE CONCAT('%', #{queryBo.queryValue}, '%')
            </if>
            <if test="@cn.hutool.core.text.CharSequenceUtil@equals(queryBo.queryType, 'ProductSkuCode')">
                AND pm.product_sku_code = #{queryBo.queryValue}
            </if>
            <if test="@cn.hutool.core.text.CharSequenceUtil@equals(queryBo.queryType, 'channelSku')">
                AND pm.channel_sku = #{queryBo.queryValue}
            </if>
            <if test="@cn.hutool.core.text.CharSequenceUtil@equals(queryBo.queryType, 'channelSkuItemNumber')">
                AND pm.channel_sku_item_number = #{queryBo.queryValue}
            </if>
        </if>
        <if test="@cn.hutool.core.text.CharSequenceUtil@isNotBlank(queryBo.syncState)">
            and pm.sync_state = #{queryBo.syncState}
        </if>
        order by pm.id desc
    </select>

    <select id="getProductMappingBySendMq" resultType="com.zsmall.product.entity.domain.ProductMapping">
        select  pm.* from  product_mapping pm
        inner join tenant_sales_channel tsc
            on pm.channel_id= tsc.id
        where pm.sync_state='Mapped'
        and pm.del_flag='0'
        and tsc.del_flag='0'
        <if test="channelType != null and channelType.size() != 0">
            and tsc.channel_type in
            <foreach collection="channelType" item="channelType" open="(" separator="," close=")">
                #{channelType}
            </foreach>
        </if>
        and tsc.connect_str is not null

    </select>
</mapper>
