<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zsmall.product.entity.mapper.ProductWholesaleDetailMapper">

    <resultMap id="BaseResultMap" type="com.zsmall.product.entity.domain.ProductWholesaleDetail">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="productId" column="product_id" jdbcType="BIGINT"/>
            <result property="minimumQuantity" column="minimum_quantity" jdbcType="INTEGER"/>
            <result property="depositRatio" column="deposit_ratio" jdbcType="DECIMAL"/>
            <result property="reservedTime" column="reserved_time" jdbcType="INTEGER"/>
            <result property="deliveryType" column="delivery_type" jdbcType="OTHER" typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
            <result property="warehouseSystemCode" column="warehouse_system_code" jdbcType="VARCHAR"/>
            <result property="logisticsTemplateNo" column="logistics_template_no" jdbcType="VARCHAR"/>
            <result property="delFlag" column="del_flag" jdbcType="CHAR"/>
            <result property="createBy" column="create_by" jdbcType="BIGINT"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateBy" column="update_by" jdbcType="BIGINT"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,product_id,minimum_quantity,
        deposit_ratio,reserved_time,delivery_type,
        warehouse_system_code,logistics_template_no,del_flag,
        create_by,create_time,update_by,
        update_time
    </sql>
</mapper>
