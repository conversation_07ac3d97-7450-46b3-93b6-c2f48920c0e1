<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zsmall.product.entity.mapper.ProductQuestionAnswerMapper">

    <select id="existAnswerCode" resultType="java.lang.Boolean">
        SELECT COUNT(pqa.id)
        FROM product_question_answer pqa
        WHERE pqa.answer_code = #{answerCode}
    </select>

    <select id="getMaxSort" resultType="java.lang.Integer">
        select max(pqa.sort)
        from product_question_answer pqa
        WHERE pqa.question_id = #{questionId};
    </select>
</mapper>
