<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zsmall.product.entity.mapper.ProductSkuAttributeMapper">

    <resultMap id="BaseResultMap" type="com.zsmall.product.entity.domain.ProductSkuAttribute">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="productSkuId" column="product_sku_id" jdbcType="BIGINT"/>
            <result property="productAttributeId" column="product_attribute_id" jdbcType="BIGINT"/>
            <result property="attributeType" column="attribute_type" jdbcType="VARCHAR"/>
            <result property="attributeName" column="attribute_name" jdbcType="VARCHAR"/>
            <result property="attributeValue" column="attribute_value" jdbcType="VARCHAR"/>
            <result property="attributeSort" column="attribute_sort" jdbcType="INTEGER"/>
            <result property="attributeSourceId" column="attribute_source_id" jdbcType="BIGINT"/>
            <result property="delFlag" column="del_flag" jdbcType="CHAR"/>
            <result property="createBy" column="create_by" jdbcType="BIGINT"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateBy" column="update_by" jdbcType="BIGINT"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,product_sku_id,product_attribute_id,
        attribute_type,attribute_name,attribute_value,
        attribute_sort,attribute_source_id,del_flag,
        create_by,create_time,update_by,
        update_time
    </sql>
</mapper>
