<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zsmall.product.entity.mapper.ProductAttributeMapper">

    <select id="queryIdsByProductId" resultType="java.lang.Long">
        SELECT pa.id
        FROM product_attribute pa
        WHERE pa.product_id = #{productId}
          AND pa.del_flag = '0'
        ORDER BY pa.attribute_sort ASC, pa.attribute_type ASC
    </select>

    <select id="existRequiredAttribute" resultType="java.lang.Boolean">
        SELECT COUNT(pa.id)
        FROM product_attribute pa
        WHERE pa.del_flag = '0'
          AND pa.product_id = #{productId}
          AND (pa.attribute_values IS NULL OR JSON_LENGTH(pa.attribute_values) = 0)
          AND pa.attribute_source_id IS NOT NULL
          AND EXISTS (SELECT 1
                      FROM product_global_attribute pga
                      WHERE pga.del_flag = '0'
                        AND pga.id = pa.attribute_source_id
                        AND pga.attribute_belong = 'Platform'
                        AND pga.is_required = '1')
    </select>
</mapper>
