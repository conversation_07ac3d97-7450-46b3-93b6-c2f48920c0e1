<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zsmall.product.entity.mapper.ProductMappingExtendWayfairWarehouseMapper">

    <select id="queryByThirdWarehouseId"
            resultType="com.zsmall.product.entity.domain.ProductMappingExtendWayfairWarehouse">
        SELECT pme.*
        FROM product_mapping_extend_wayfair_warehouse pme
        WHERE pme.del_flag = '0'
          AND pme.third_warehouse_id LIKE CONCAT('%', #{thirdWarehouseId}, '%')
          AND EXISTS(SELECT 1
                     FROM warehouse w
                     WHERE w.warehouse_system_code = pme.warehouse_system_code
                       AND w.del_flag = '0')
          AND EXISTS(SELECT 1
                     FROM product_sku_stock pss
                     WHERE pss.warehouse_system_code = pme.warehouse_system_code
                       AND pss.del_flag = '0'
                       AND pss.product_sku_code = #{productSkuCode}
                       AND pss.stock_available >= #{quantity}
                       AND pss.stock_state = 1)
          AND pme.channel_id = #{channelId}
    </select>
</mapper>
