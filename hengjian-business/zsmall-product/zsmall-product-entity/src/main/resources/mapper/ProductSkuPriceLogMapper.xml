<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zsmall.product.entity.mapper.ProductSkuPriceLogMapper">

    <resultMap id="BaseResultMap" type="com.zsmall.product.entity.domain.ProductSkuPriceLog">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="productSkuPriceId" column="product_sku_price_id" jdbcType="BIGINT"/>
            <result property="productSkuId" column="product_sku_id" jdbcType="BIGINT"/>
            <result property="originalUnitPrice" column="original_unit_price" jdbcType="DECIMAL"/>
            <result property="originalOperationFee" column="original_operation_fee" jdbcType="DECIMAL"/>
            <result property="originalFinalDeliveryFee" column="original_final_delivery_fee" jdbcType="DECIMAL"/>
            <result property="originalPickUpPrice" column="original_pick_up_price" jdbcType="DECIMAL"/>
            <result property="originalDropShippingPrice" column="original_drop_shipping_price" jdbcType="DECIMAL"/>
            <result property="platformUnitPrice" column="platform_unit_price" jdbcType="DECIMAL"/>
            <result property="platformOperationFee" column="platform_operation_fee" jdbcType="DECIMAL"/>
            <result property="platformFinalDeliveryFee" column="platform_final_delivery_fee" jdbcType="DECIMAL"/>
            <result property="platformPickUpPrice" column="platform_pick_up_price" jdbcType="DECIMAL"/>
            <result property="platformDropShippingPrice" column="platform_drop_shipping_price" jdbcType="DECIMAL"/>
            <result property="msrp" column="msrp" jdbcType="DECIMAL"/>
            <result property="delFlag" column="del_flag" jdbcType="CHAR"/>
            <result property="createBy" column="create_by" jdbcType="BIGINT"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateBy" column="update_by" jdbcType="BIGINT"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,product_sku_price_id,product_sku_id,
        original_unit_price,original_operation_fee,original_final_delivery_fee,
        original_pick_up_price,original_drop_shipping_price,platform_unit_price,
        platform_operation_fee,platform_final_delivery_fee,platform_pick_up_price,
        platform_drop_shipping_price,msrp,del_flag,
        create_by,create_time,update_by,
        update_time
    </sql>
</mapper>
