<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zsmall.product.entity.mapper.ProductSkuAttachmentMapper">

    <resultMap id="BaseResultMap" type="com.zsmall.product.entity.domain.ProductSkuAttachment">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="productSkuId" column="product_sku_id" jdbcType="BIGINT"/>
            <result property="attachmentName" column="attachment_name" jdbcType="VARCHAR"/>
            <result property="attachmentOriginalName" column="attachment_original_name" jdbcType="VARCHAR"/>
            <result property="attachmentSuffix" column="attachment_suffix" jdbcType="VARCHAR"/>
            <result property="attachmentSavePath" column="attachment_save_path" jdbcType="VARCHAR"/>
            <result property="attachmentShowUrl" column="attachment_show_url" jdbcType="VARCHAR"/>
            <result property="attachmentSort" column="attachment_sort" jdbcType="INTEGER"/>
            <result property="attachmentType" column="attachment_type" jdbcType="VARCHAR"/>
            <result property="delFlag" column="del_flag" jdbcType="CHAR"/>
            <result property="createBy" column="create_by" jdbcType="BIGINT"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateBy" column="update_by" jdbcType="BIGINT"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,product_sku_id,attachment_name,
        attachment_original_name,attachment_suffix,attachment_save_path,
        attachment_show_url,attachment_sort,attachment_type,
        del_flag,create_by,create_time,
        update_by,update_time
    </sql>

    <select id="getFirstByProductSkuCode" resultMap="BaseResultMap">
        SELECT psa.*
        FROM product_sku_attachment psa
            JOIN product_sku sku ON sku.id = psa.product_sku_id
        WHERE sku.product_sku_code = #{productSkuCode}
          AND sku.del_flag = '0' AND psa.del_flag = '0'
        ORDER BY psa.attachment_sort ASC
    </select>

    <select id="queryFirstImageByProductId"
            resultType="com.zsmall.product.entity.domain.vo.ProductSkuAttachmentVo">
        SELECT psa.*
        FROM product_sku_attachment psa
                 JOIN product_sku ps on psa.product_sku_id = ps.id
        WHERE psa.attachment_type = 'Image'
          AND psa.del_flag = '0'
          AND ps.del_flag = '0' AND ps.product_id = #{productId}
        ORDER BY ps.sort ASC, psa.attachment_sort ASC
        LIMIT 0,1
    </select>

    <select id="queryIdsByProductSkuId" resultType="java.lang.Long">
        SELECT psa.id
        FROM product_sku_attachment psa
                 JOIN product_sku ps on psa.product_sku_id = ps.id
        WHERE psa.del_flag = '0' AND psa.product_sku_id = #{productSkuId}
    </select>

    <select id="queryFirstImageByProductSkuId"
            resultType="com.zsmall.product.entity.domain.vo.ProductSkuAttachmentVo">
        SELECT psa.*
        FROM product_sku_attachment psa
                 JOIN product_sku ps on psa.product_sku_id = ps.id
        WHERE psa.product_sku_id = #{productSkuId} AND psa.del_flag = '0'
        ORDER BY psa.attachment_sort ASC
        LIMIT 0,1
    </select>

    <select id="queryFirstImageByProductSkuCode"
            resultType="com.zsmall.product.entity.domain.vo.ProductSkuAttachmentVo">
        SELECT psa.*
        FROM product_sku_attachment psa
                 JOIN product_sku ps on psa.product_sku_id = ps.id
        WHERE ps.product_sku_code = #{productSkuCode} AND psa.del_flag = '0'
        ORDER BY psa.attachment_sort ASC
        LIMIT 0,1
    </select>

    <select id="queryFirstImageUrlByProductId" resultType="java.lang.String">
        SELECT psa.attachment_show_url
        FROM product_sku_attachment psa
                 JOIN product_sku ps on psa.product_sku_id = ps.id
        WHERE psa.attachment_type = 'Image'
          AND psa.del_flag = '0'
          AND ps.del_flag = '0'
          AND ps.product_id = #{productId}
        ORDER BY ps.sort ASC, psa.attachment_sort ASC
        LIMIT 0,1
    </select>

    <select id="queryFirstImageUrlByProductSkuCode" resultType="java.lang.String">
        SELECT psa.attachment_show_url
        FROM product_sku_attachment psa
        JOIN product_sku ps on psa.product_sku_id = ps.id
        WHERE ps.product_sku_code = #{productSkuCode} AND psa.del_flag = '0'
        ORDER BY psa.attachment_sort ASC
        LIMIT 0,1
    </select>

    <select id="queryFirstImageUrlByProductSkuId" resultType="java.lang.String">
        SELECT psa.attachment_show_url
        FROM product_sku_attachment psa
        JOIN product_sku ps on psa.product_sku_id = ps.id
        WHERE ps.id = #{productSkuId} AND psa.del_flag = '0'
        ORDER BY psa.attachment_sort ASC
        LIMIT 0,1
    </select>

    <select id="queryAllImageByProductSkuCode" resultType="java.lang.String">
        SELECT psa.attachment_show_url
        FROM product_sku_attachment psa
        JOIN product_sku ps on psa.product_sku_id = ps.id
        WHERE ps.product_sku_code = #{productSkuCode} AND psa.del_flag = '0'
        ORDER BY psa.attachment_sort ASC
    </select>
    <select id="queryFirstImageByProductIds"
            resultType="com.zsmall.product.entity.domain.vo.ProductSkuAttachmentVo">
        WITH RankedAttachments AS (
        SELECT
        psa.*,ps.product_id,
        ROW_NUMBER() OVER(PARTITION BY ps.product_id ORDER BY ps.sort ASC, psa.attachment_sort ASC) AS rn
        FROM
        product_sku_attachment psa
        JOIN product_sku ps ON psa.product_sku_id = ps.id
        WHERE
        psa.attachment_type = 'Image'
        AND psa.del_flag = '0'
        AND ps.del_flag = '0'
        <if test="tenantId != null and tenantId != '000000'">
            AND ps.tenant_id = #{tenantId}
        </if>

        <if test="productIds != null and productIds.size() > 0">
            AND ps.product_id IN
            <foreach item="productId" collection="productIds" open="(" separator="," close=")">
                #{productId}
            </foreach>

        </if>
        )
        SELECT * FROM RankedAttachments WHERE rn = 1;
    </select>

    <select id="queryFirstImageByProductSkuCodes" resultType="com.zsmall.product.entity.domain.vo.ProductSkuAttachmentVo">
        WITH RankedAttachments AS (
        SELECT psa.*,
               ROW_NUMBER() OVER (PARTITION BY ps.product_sku_code ORDER BY psa.attachment_sort ASC) AS rn,
               ps.product_sku_code as attachmentOriginalName
        FROM product_sku_attachment AS psa
                 INNER JOIN product_sku AS ps
                            ON psa.product_sku_id = ps.id
        WHERE ps.del_flag = 0
          and psa.del_flag = 0
          AND psa.attachment_type = 'Image'
          AND ps.product_sku_code IN
        <foreach item="productSkuCode" collection="productSkuCode" open="(" separator="," close=")">
            #{productSkuCode}
        </foreach>
        )
        SELECT *
        FROM RankedAttachments
        WHERE rn = 1;
    </select>
</mapper>
