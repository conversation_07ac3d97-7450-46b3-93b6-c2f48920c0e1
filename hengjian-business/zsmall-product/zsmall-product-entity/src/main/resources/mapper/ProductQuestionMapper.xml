<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zsmall.product.entity.mapper.ProductQuestionMapper">

    <select id="existQuestionCode" resultType="java.lang.Boolean">
        SELECT COUNT(pq.id)
        FROM product_question pq
        WHERE pq.question_code = #{questionCode}
    </select>
</mapper>
