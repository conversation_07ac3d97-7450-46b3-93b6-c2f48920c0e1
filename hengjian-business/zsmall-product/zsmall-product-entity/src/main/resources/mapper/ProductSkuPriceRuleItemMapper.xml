<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zsmall.product.entity.mapper.ProductSkuPriceRuleItemMapper">

    <resultMap id="BaseResultMap" type="com.zsmall.product.entity.domain.ProductSkuPriceRuleItem">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="productSkuPriceRuleId" column="product_sku_price_rule_id" jdbcType="BIGINT"/>
            <result property="priceItemType" column="price_item_type" jdbcType="VARCHAR"/>
            <result property="priceCal" column="price_cal" jdbcType="INTEGER"/>
            <result property="priceCalValue" column="price_cal_value" jdbcType="DECIMAL"/>
            <result property="delFlag" column="del_flag" jdbcType="CHAR"/>
            <result property="createBy" column="create_by" jdbcType="BIGINT"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateBy" column="update_by" jdbcType="BIGINT"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,product_sku_price_rule_id,price_item_type,
        price_cal,price_cal_value,del_flag,
        create_by,create_time,update_by,
        update_time
    </sql>
</mapper>
