<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zsmall.product.entity.mapper.ProductSkuPriceRuleRelationMapper">

    <resultMap id="BaseResultMap" type="com.zsmall.product.entity.domain.ProductSkuPriceRuleRelation">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="productSkuPriceId" column="product_sku_price_id" jdbcType="BIGINT"/>
            <result property="productSkuId" column="product_sku_id" jdbcType="BIGINT"/>
            <result property="productSkuCode" column="product_sku_code" jdbcType="CHAR"/>
            <result property="productSkuPriceRuleId" column="product_sku_price_rule_id" jdbcType="BIGINT"/>
            <result property="delFlag" column="del_flag" jdbcType="CHAR"/>
            <result property="createBy" column="create_by" jdbcType="BIGINT"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateBy" column="update_by" jdbcType="BIGINT"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,product_sku_price_id,product_sku_id,product_sku_code,
        product_sku_price_rule_id,del_flag,create_by,
        create_time,update_by,update_time
    </sql>
    <select id="getItemNosByRuleCode" resultType="java.lang.String">
        SELECT sku.product_sku_code
        FROM product_sku_price_rule rule
                 JOIN product_sku_price_rule_relation rela ON rela.product_sku_price_rule_id = rule.id AND rela.del_flag = '0'
                 JOIN product_sku sku ON sku.id = rela.product_sku_id AND sku.shelf_state = 'Onshelf'
        WHERE rule.rule_code = #{ruleCode}
          AND rule.del_flag = '0'
        GROUP BY sku.product_sku_code
    </select>

    <select id="getByProductSkuCode" resultType="com.zsmall.product.entity.domain.ProductSkuPriceRuleRelation">
        SELECT rela.*
        FROM product_sku_price_rule_relation rela
                 JOIN product_sku sku ON sku.id = rela.product_sku_id
        WHERE sku.product_sku_code = #{productSkuCode}
          AND rela.del_flag = '0'
    </select>


</mapper>
