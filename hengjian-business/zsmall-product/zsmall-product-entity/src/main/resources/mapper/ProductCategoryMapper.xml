<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zsmall.product.entity.mapper.ProductCategoryMapper">

    <resultMap id="BaseResultMap" type="com.zsmall.product.entity.domain.ProductCategory">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="parentId" column="parent_id" jdbcType="BIGINT"/>
            <result property="categoryLevel" column="category_level" jdbcType="INTEGER"/>
            <result property="categoryName" column="category_name" jdbcType="VARCHAR"/>
            <result property="categoryEnglishName" column="category_english_name" jdbcType="VARCHAR"/>
            <result property="categoryOtherName" column="category_other_name" typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
            <result property="categorySort" column="category_sort" jdbcType="INTEGER"/>
            <result property="imageOssId" column="image_oss_id" jdbcType="BIGINT"/>
            <result property="iconOssId" column="icon_oss_id" jdbcType="BIGINT"/>
            <result property="categoryImageSavePath" column="category_image_save_path" jdbcType="VARCHAR"/>
            <result property="categoryImageShowUrl" column="category_image_show_url" jdbcType="VARCHAR"/>
            <result property="categoryIconSavePath" column="category_icon_save_path" jdbcType="VARCHAR"/>
            <result property="categoryIconShowUrl" column="category_icon_show_url" jdbcType="VARCHAR"/>
            <result property="categoryState" column="category_state" jdbcType="TINYINT"/>
            <result property="delFlag" column="del_flag" jdbcType="CHAR"/>
            <result property="createBy" column="create_by" jdbcType="BIGINT"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateBy" column="update_by" jdbcType="BIGINT"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="ProductCategoryList">
        id,parent_id,category_level,
        category_name,category_english_name,category_other_name,category_sort,image_oss_id,icon_oss_id,
        category_image_save_path,category_image_show_url,category_icon_save_path,
        category_icon_show_url,category_state,del_flag,
        create_by,create_time,update_by,
        update_time
    </sql>

    <select id="queryCategoryNameChainByIdOrderByLevelASC" resultType="java.lang.String">
        WITH RECURSIVE t1 AS (SELECT t0.*
                              FROM product_category t0
                              WHERE t0.id = #{belongCategoryId}
                              UNION ALL
                              SELECT t2.*
                              FROM product_category t2,
                                   t1
                              WHERE t2.id = t1.parent_id)
        SELECT group_concat(t1.category_name ORDER BY t1.category_level ASC SEPARATOR '/' )
        FROM t1
        WHERE t1.del_flag = 0
    </select>

    <select id="queryCategoryChainByIdOrderByLevelASC" resultType="com.zsmall.product.entity.domain.ProductCategory">
        WITH RECURSIVE t1 AS (SELECT t0.*
                              FROM product_category t0
                              WHERE t0.id = #{belongCategoryId}
                              UNION ALL
                              SELECT t2.*
                              FROM product_category t2,
                                   t1
                              WHERE t2.id = t1.parent_id)
        SELECT t1.*
        FROM t1
        WHERE t1.del_flag = 0
        ORDER BY t1.category_level ASC
    </select>

    <select id="queryCategoryChainById" resultType="com.zsmall.product.entity.domain.ProductCategory">
        WITH RECURSIVE t1 AS (SELECT t0.*
                              FROM product_category t0
                              WHERE t0.id = #{belongCategoryId}
                              UNION ALL
                              SELECT t2.*
                              FROM product_category t2, t1
                              WHERE t2.id = t1.parent_id)
        SELECT *
        FROM t1
        WHERE t1.del_flag = 0
    </select>

    <select id="queryCategoryChainByIdDesc" resultType="com.zsmall.product.entity.domain.ProductCategory">
        WITH RECURSIVE t1 AS (SELECT t0.*
                              FROM product_category t0
                              WHERE t0.id = #{belongCategoryId}
                              UNION ALL
                              SELECT t2.*
                              FROM product_category t2, t1
                              WHERE t2.parent_id = t1.id)
        SELECT *
        FROM t1
        WHERE t1.del_flag = 0
    </select>

    <select id="queryAllCategoryChainById" resultType="com.zsmall.product.entity.domain.ProductCategory">
        WITH RECURSIVE ancestors AS (
            SELECT t0.*
            FROM product_category t0
            WHERE t0.id = #{belongCategoryId}
            UNION ALL
            SELECT t1.*
            FROM product_category t1
                     JOIN ancestors a ON a.parent_id = t1.id
        ),
       descendants AS (
           SELECT t0.*
           FROM product_category t0
           WHERE t0.id = #{belongCategoryId}
           UNION ALL
           SELECT t2.*
           FROM product_category t2
                    JOIN descendants d ON d.id = t2.parent_id
       )
        SELECT *
        FROM ancestors
        WHERE del_flag = 0
        UNION
        SELECT *
        FROM descendants
        WHERE del_flag = 0
    </select>

</mapper>
