<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zsmall.product.entity.mapper.ProductSkuPriceMapper">

    <resultMap id="BaseResultMap" type="com.zsmall.product.entity.domain.ProductSkuPrice">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="productSkuId" column="product_sku_id" jdbcType="BIGINT"/>
            <result property="productSkuCode" column="product_sku_code" jdbcType="BIGINT"/>
            <result property="originalUnitPrice" column="original_unit_price" jdbcType="DECIMAL"/>
            <result property="originalOperationFee" column="original_operation_fee" jdbcType="DECIMAL"/>
            <result property="originalFinalDeliveryFee" column="original_final_delivery_fee" jdbcType="DECIMAL"/>
            <result property="originalPickUpPrice" column="original_pick_up_price" jdbcType="DECIMAL"/>
            <result property="originalDropShippingPrice" column="original_drop_shipping_price" jdbcType="DECIMAL"/>
            <result property="platformUnitPrice" column="platform_unit_price" jdbcType="DECIMAL"/>
            <result property="platformOperationFee" column="platform_operation_fee" jdbcType="DECIMAL"/>
            <result property="platformFinalDeliveryFee" column="platform_final_delivery_fee" jdbcType="DECIMAL"/>
            <result property="platformPickUpPrice" column="platform_pick_up_price" jdbcType="DECIMAL"/>
            <result property="platformDropShippingPrice" column="platform_drop_shipping_price" jdbcType="DECIMAL"/>
            <result property="msrp" column="msrp" jdbcType="DECIMAL"/>
            <result property="delFlag" column="del_flag" jdbcType="CHAR"/>
            <result property="createBy" column="create_by" jdbcType="BIGINT"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateBy" column="update_by" jdbcType="BIGINT"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,product_sku_id,product_sku_code,original_unit_price,
        original_operation_fee,original_final_delivery_fee,original_pick_up_price,
        original_drop_shipping_price,platform_unit_price,platform_operation_fee,
        platform_final_delivery_fee,platform_pick_up_price,platform_drop_shipping_price,
        msrp,del_flag,create_by,
        create_time,update_by,update_time
    </sql>

    <select id="getListByProductId" resultType="com.zsmall.product.entity.domain.ProductSkuPrice">
        SELECT
        psp.platform_drop_shipping_price,
        psp.platform_pick_up_price
        FROM
        product_sku_price psp
            INNER JOIN product_sku ps ON ps.id = psp.product_sku_id
        WHERE
        ps.product_id = #{productId}
    </select>

    <select id="getStockWithWarehouseSystemCode" resultType="java.util.HashMap">
        SELECT
            w.warehouse_name as warehouseName,
            pss.stock_available as  pickUpStock,
            CASE
            WHEN drop_shipping_stock_available = 0 THEN 0
            WHEN drop_shipping_stock_available = 1 THEN stock_available
            END AS dropShippingStock,
            CONCAT_WS(',',wa.address1,wa.city, wa.state,wa.zip_code , wa.country) AS warehouseAddress,
            wa.address2 as detailedAddress
        FROM
            product_sku_stock AS pss
                INNER JOIN warehouse w ON pss.warehouse_system_code = w.warehouse_system_code
                INNER JOIN warehouse_address wa on wa.warehouse_id=w.id
        WHERE
            pss.del_flag = '0'
          AND w.del_flag = '0'
          AND w.warehouse_state=1
           and w.tenant_id=#{tenantId,jdbcType=VARCHAR}
          AND pss.product_sku_code = #{productSkuCode}
    </select>

    <resultMap id="stockResultMap" type="java.util.HashMap">
        <result column="warehouse_name" property="warehouseName"  javaType="java.lang.String" />
        <result column="stock_available" property="stockAvailable"  javaType="java.lang.Integer" />
        <result column="warehouseAddress" property="warehouseAddress" javaType="java.lang.String"/>
        <result column="detailedAddress" property="detailedAddress" javaType="java.lang.String"/>
        <result column="drop_shipping_stock_available" property="dropShippingStockAvailable" javaType="java.lang.Integer" />
    </resultMap>

    <select id="getStockWithWarehouseSystemCodeNew" resultMap="stockResultMap">
        SELECT
            w.warehouse_name,
            pss.stock_available,
            CONCAT_WS(',',wa.address1,wa.city, wa.state,wa.zip_code , wa.country) AS warehouseAddress,
            wa.address2 as detailedAddress,
            pss.drop_shipping_stock_available
        FROM
            product_sku_stock AS pss
                INNER JOIN warehouse w ON pss.warehouse_system_code = w.warehouse_system_code
                INNER JOIN warehouse_address wa on wa.warehouse_id=w.id
        WHERE
            pss.del_flag = '0'
          AND w.del_flag = '0'
          AND w.warehouse_state=1
          and w.tenant_id=#{tenantId,jdbcType=VARCHAR}
          AND pss.product_sku_code = #{productSkuCode}
    </select>

    <select id="getSoldOutByProductSkuId" resultType="java.lang.Integer">
        select
            sum((case
                     when (oi.order_state in ('Paid', 'Verifying', 'Refunded')) then oi.total_quantity
                     else 0 end))                               AS sold_quantity
        from ((((product_sku sku join product p
                 on ((sku.product_id = p.id))) left join order_item oi
                on ((oi.product_sku_code = sku.product_sku_code))))) where sku.id = #{productSkuId};
    </select>

    <select id="getRuleLevelProductPriceSitePriceMap"
            resultType="com.zsmall.product.entity.domain.RuleLevelProductPrice">
        SELECT mlpp.*,ps.product_sku_code as productSkuCode
        FROM rule_level_product_price mlpp
                 INNER JOIN member_level ml ON ml.id = mlpp.level_id
                 INNER JOIN member_rule_relation mrr ON mlpp.level_id = mrr.level_id
                 INNER JOIN product_sku ps ON ps.id = mlpp.product_sku_id
            AND ps.tenant_id = mlpp.rule_customizer_tenant_id
        WHERE mrr.del_flag = 0
          AND   ml.del_flag = 0
          AND ml.status = 0
          AND mlpp.del_flag = 0
          AND mrr.rule_follower_tenant_id = #{tenantId}
          AND ps.product_sku_code IN
        <foreach collection="productSkuCodeSet" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="getStockWithWarehouseSystemCodeBySite" resultType="java.util.HashMap">
        select  w.warehouse_name as warehouseName,
                pss.stock_available as  pickUpStock,
                CASE
                    WHEN drop_shipping_stock_available = 0 THEN 0
                    WHEN drop_shipping_stock_available = 1 THEN stock_available
                    END AS dropShippingStock,
                CONCAT_WS(',',wa.address1,wa.city, wa.state,wa.zip_code , wa.country) AS warehouseAddress,
                wa.address2 as detailedAddress
        from site_country_currency scc
                 inner join warehouse_delivery_country wadc on scc.country_code = wadc.country_code
                 inner join warehouse w on w.id = wadc.warehouse_id
                 inner join warehouse_address wa on wa.warehouse_id = w.id
                 inner join product_sku_stock pss on pss.warehouse_system_code = w.warehouse_system_code
        WHERE
        pss.del_flag = '0'
        AND w.del_flag = '0'
        AND w.warehouse_state=1
        and w.tenant_id=#{tenantId,jdbcType=VARCHAR}
        AND pss.product_sku_code = #{productSkuCode}
        and scc.country_code=#{site}
        order by pickUpStock desc
    </select>

    <select id="getWarehouseCodeBySite"
            resultType="com.zsmall.product.entity.domain.dto.productSku.ProductSkuWarehouseByCountryCode">
        select  w.warehouse_code as warehouseCode,
        pss.stock_available as  pickUpStock,
        CASE
        WHEN drop_shipping_stock_available = 0 THEN 0
        WHEN drop_shipping_stock_available = 1 THEN stock_available
        END AS dropShippingStock
        from site_country_currency scc
        inner  join warehouse_delivery_country wadc on scc.country_code = wadc.country_code
        inner join warehouse w on w.id = wadc.warehouse_id
        inner join product_sku_stock pss on pss.warehouse_system_code = w.warehouse_system_code
        WHERE
        pss.del_flag = '0'
        AND w.del_flag = '0'
        AND w.warehouse_state=1
        AND pss.product_sku_code = #{productSkuCode}
        and scc.country_code=#{countryCode}
    </select>

    <select id="getIsCalculation" resultType="java.lang.Integer">
        select is_calculation
        from sys_tenant
        where
        del_flag=0 and
        tenant_id = #{tenantId}
    </select>




</mapper>
