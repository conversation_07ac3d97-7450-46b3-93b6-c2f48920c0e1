<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zsmall.product.entity.mapper.ProductReviewChangeDetailMapper">

    <resultMap id="BaseResultMap" type="com.zsmall.product.entity.domain.ProductReviewChangeDetail">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="reviewRecordId" column="review_record_id" jdbcType="BIGINT"/>
            <result property="productCode" column="product_code" jdbcType="VARCHAR"/>
            <result property="productSkuCode" column="product_sku_code" jdbcType="VARCHAR"/>
            <result property="fieldName" column="field_name" jdbcType="VARCHAR"/>
            <result property="fieldValueBefore" column="field_value_before" jdbcType="VARCHAR"/>
            <result property="fieldValueAfter" column="field_value_after" jdbcType="VARCHAR"/>
            <result property="allowUpdate" column="allow_update" jdbcType="INTEGER"/>
            <result property="delFlag" column="del_flag" jdbcType="CHAR"/>
            <result property="createBy" column="create_by" jdbcType="BIGINT"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateBy" column="update_by" jdbcType="BIGINT"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,review_record_id,product_code,
        product_sku_code,field_name,field_value_before,
        field_value_after,allow_update,del_flag,
        create_by,create_time,update_by,
        update_time
    </sql>

    <select id="queryByTaskTodo" resultMap="BaseResultMap">
        SELECT prcd.*
        FROM product_review_change_detail prcd
                 JOIN product_review_record prr ON prcd.review_record_id = prr.id
                 JOIN task_sku_price_change tspc ON tspc.review_record_id = prr.id
        WHERE (prcd.product_sku_code = #{productSkuCode} OR prcd.product_sku_code IS NULL)
          AND tspc.task_state = 'Todo';
    </select>

</mapper>
