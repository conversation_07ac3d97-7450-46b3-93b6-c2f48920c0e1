<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zsmall.product.entity.mapper.ProductSkuWholesalePriceMapper">

    <resultMap id="BaseResultMap" type="com.zsmall.product.entity.domain.ProductSkuWholesalePrice">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="productId" column="product_id" jdbcType="BIGINT"/>
            <result property="productSkuId" column="product_sku_id" jdbcType="BIGINT"/>
            <result property="tieredPriceId" column="tiered_price_id" jdbcType="BIGINT"/>
            <result property="originUnitPrice" column="origin_unit_price" jdbcType="DECIMAL"/>
            <result property="platformUnitPrice" column="platform_unit_price" jdbcType="DECIMAL"/>
            <result property="delFlag" column="del_flag" jdbcType="CHAR"/>
            <result property="createBy" column="create_by" jdbcType="BIGINT"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateBy" column="update_by" jdbcType="BIGINT"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,product_id,product_sku_id,
        tiered_price_id,origin_unit_price,platform_unit_price,
        del_flag,create_by,create_time,
        update_by,update_time
    </sql>
</mapper>
