<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zsmall.product.entity.mapper.ProductGlobalAttributeMapper">

    <resultMap id="BaseResultMap" type="com.zsmall.product.entity.domain.ProductGlobalAttribute">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="tenantId" column="tenant_id" jdbcType="VARCHAR"/>
        <result property="attributeBelong" column="attribute_belong" jdbcType="VARCHAR"/>
        <result property="attributeName" column="attribute_name" jdbcType="VARCHAR"/>
        <result property="attributeOtherName" column="attribute_other_name" jdbcType="OTHER"
                typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
        <result property="attributeValues" column="attribute_values" jdbcType="OTHER"
                typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
        <result property="attributeNotes" column="attribute_notes" jdbcType="VARCHAR"/>
        <result property="attributeScope" column="attribute_scope" jdbcType="VARCHAR"/>
        <result property="attributeState" column="attribute_state" jdbcType="TINYINT"/>
        <result property="bindingCategory" column="binding_category" jdbcType="VARCHAR"/>
        <result property="isSupportCustom" column="is_support_custom" jdbcType="TINYINT"/>
        <result property="isBasicAttribute" column="is_basic_attribute" jdbcType="TINYINT"/>
        <result property="isRequired" column="is_required" jdbcType="TINYINT"/>
        <result property="delFlag" column="del_flag" jdbcType="CHAR"/>
        <result property="createBy" column="create_by" jdbcType="BIGINT"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateBy" column="update_by" jdbcType="BIGINT"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <select id="queryByProductCategoryId"
            resultMap="ForSelectResultMap"
            resultType="com.zsmall.product.entity.domain.vo.productGlobalAttribute.ProductGlobalAttributeSimpleVo">
        SELECT pga.id,
               pga.attribute_name,
               pga.is_support_custom,
               pga.is_required,
               pga.attribute_values
        FROM product_global_attribute pga
        WHERE pga.attribute_state = 1
          AND pga.attribute_scope = #{attributeScope}
          AND pga.attribute_belong = #{attributeBelong}
          AND (pga.binding_category = 'AllCategory' OR (
                    pga.binding_category = 'SpecifyCategory' AND EXISTS(SELECT 1
                                                                        FROM product_category_global_attribute pcga
                                                                        WHERE pcga.del_flag = '0'
                                                                          AND pcga.global_attribute_id = pga.id
                                                                          AND pcga.product_category_id = #{productCategoryId})
            ))
          AND pga.del_flag = '0'
    </select>

    <resultMap id="ForSelectResultMap" type="com.zsmall.product.entity.domain.vo.productGlobalAttribute.ProductGlobalAttributeSimpleVo">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="attributeName" column="attribute_name" jdbcType="VARCHAR"/>
        <result property="isSupportCustom" column="is_support_custom" jdbcType="TINYINT"/>
        <result property="isRequired" column="is_required" jdbcType="TINYINT"/>
        <result property="attributeValues" column="attribute_values" jdbcType="OTHER"
                typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
    </resultMap>



    <select id="queryForCategory"
            resultMap="ForSelectResultMap"
            resultType="com.zsmall.product.entity.domain.vo.productGlobalAttribute.ProductGlobalAttributeSimpleVo">
        SELECT
            pga.id,
            pga.attribute_name,
            pga.is_support_custom,
            pga.attribute_values
        FROM product_global_attribute pga
        WHERE pga.attribute_state = 1
        AND pga.attribute_scope = #{attributeScope}
        AND pga.attribute_belong = #{attributeBelong}
        AND pga.del_flag = '0'
    </select>

    <select id="queryByAttributeNameAndScopeAndCategory"
            resultMap="BaseResultMap"
            resultType="com.zsmall.product.entity.domain.ProductGlobalAttribute">
        SELECT pga.*
        FROM product_global_attribute pga
        WHERE pga.del_flag = '0'
          AND pga.attribute_name = #{attributeName}
          AND pga.attribute_scope = #{attributeScope}
          <if test="tenantId != null">
              AND pga.tenant_id = #{tenantId}
          </if>
          AND (pga.binding_category = 'AllCategory' OR EXISTS (SELECT 1
                       FROM product_category_global_attribute pcga
                       WHERE pcga.global_attribute_id = pga.id
                         AND pcga.del_flag = '0'
                         AND pcga.product_category_id = #{belongCategoryId}))
    </select>

    <select id="queryByProductCategoryIdAndRequired"
            resultMap="ForSelectResultMap"
            resultType="com.zsmall.product.entity.domain.vo.productGlobalAttribute.ProductGlobalAttributeSimpleVo">
        SELECT pga.id,
               pga.attribute_name,
               pga.is_support_custom,
               pga.is_required,
               pga.attribute_values
        FROM product_global_attribute pga
        WHERE pga.attribute_state = 1
          AND pga.attribute_scope = #{attributeScope}
          AND pga.attribute_belong = #{attributeBelong}
          AND pga.is_required = #{isRequired}
          AND (pga.binding_category = 'AllCategory' OR (
                    pga.binding_category = 'SpecifyCategory' AND EXISTS(SELECT 1
                                                                        FROM product_category_global_attribute pcga
                                                                        WHERE pcga.del_flag = '0'
                                                                          AND pcga.global_attribute_id = pga.id
                                                                          AND pcga.product_category_id = #{productCategoryId})
            ))
          AND pga.del_flag = '0'
    </select>
</mapper>
