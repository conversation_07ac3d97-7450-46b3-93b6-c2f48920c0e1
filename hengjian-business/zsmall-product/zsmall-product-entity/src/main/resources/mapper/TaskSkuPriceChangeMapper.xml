<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zsmall.product.entity.mapper.TaskSkuPriceChangeMapper">

    <resultMap id="BaseResultMap" type="com.zsmall.product.entity.domain.TaskSkuPriceChange">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="productCode" column="product_code" jdbcType="VARCHAR"/>
            <result property="executeDate" column="execute_date" jdbcType="VARCHAR"/>
            <result property="reviewRecordId" column="review_record_id" jdbcType="BIGINT"/>
            <result property="taskState" column="task_state" jdbcType="VARCHAR"/>
            <result property="delFlag" column="del_flag" jdbcType="CHAR"/>
            <result property="createBy" column="create_by" jdbcType="BIGINT"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateBy" column="update_by" jdbcType="BIGINT"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,product_code,execute_date,
        review_record_id,task_state,del_flag,
        create_by,create_time,update_by,
        update_time
    </sql>
</mapper>
