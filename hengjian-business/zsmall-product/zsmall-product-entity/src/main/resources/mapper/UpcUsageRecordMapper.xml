<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zsmall.product.entity.mapper.ProductCategoryMapper">
<!--
    <resultMap id="BaseResultMap" type="com.zsmall.product.entity.domain.UpcUsageRecordEntity">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="channelType" column="channel_type" jdbcType="VARCHAR"/>
        <result property="productCode" column="product_code" jdbcType="VARCHAR"/>
        <result property="sku" column="sku" jdbcType="VARCHAR"/>
        <result property="stateType" column="state_type" jdbcType="VARCHAR"/>
        <result property="storeId" column="store_id" jdbcType="BIGINT"/>
        <result property="upc" column="upc" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        ,create_time,update_time,
        channel_type,product_code,sku,
        state_type,store_id,upc
    </sql>

    <select id="getUnassignedUpcs" resultMap="BaseResultMap">
        select uur
        from upc_usage_record uur
        where uur.state_type = 'Preempt'
          and uur.channel_type = #{channelType}
          and exists(select 1 from upc u where u.brand = #{brand} and u.storeId = #{storeId} and u.id = uur.upc)
    </select>-->
</mapper>
