<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zsmall.product.entity.mapper.ProductSkuPriceRuleMapper">

    <resultMap id="BaseResultMap" type="com.zsmall.product.entity.domain.ProductSkuPriceRule">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="ruleName" column="rule_name" jdbcType="VARCHAR"/>
            <result property="ruleCode" column="rule_code" jdbcType="VARCHAR"/>
            <result property="applicableType" column="applicable_type" jdbcType="INTEGER"/>
            <result property="applicableValue" column="applicable_value" typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
            <result property="delFlag" column="del_flag" jdbcType="CHAR"/>
            <result property="createBy" column="create_by" jdbcType="BIGINT"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateBy" column="update_by" jdbcType="BIGINT"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,rule_name,rule_code,
        applicable_type,applicable_value,del_flag,
        create_by,create_time,update_by,
        update_time
    </sql>

    <select id="existRuleCode" resultType="java.lang.Boolean">
        SELECT COUNT(pspr.id) FROM product_sku_price_rule pspr WHERE pspr.rule_code = #{ruleCode}
    </select>

    <select id="queryByPriceRange" resultMap="BaseResultMap">
        SELECT pspr.*
        FROM product_sku_price_rule pspr
        WHERE pspr.applicable_value ->> '$[0]' &lt;= #{value}
          AND #{value} &lt;= pspr.applicable_value ->> '$[1]'
          AND pspr.applicable_type = 1
          AND pspr.del_flag = '0'
        ORDER BY pspr.create_time DESC, pspr.id DESC
    </select>

    <select id="getRuleCodeByProductSkuId" resultType="java.lang.String">
        SELECT
            r.rule_code
        FROM
            product_sku_price_rule r
                JOIN product_sku_price_rule_relation rr
                     ON r.id = rr.product_sku_price_rule_id
        WHERE rr.product_sku_id = #{productSkuId}
          AND r.del_flag = '0'
    </select>


    <select id="getListByType" resultMap="BaseResultMap">
        SELECT r.* FROM product_sku_price_rule r
        WHERE r.del_flag = '0' AND r.id != 1
        <if test="ruleCode != null and ruleCode != ''">
            AND r.rule_code != #{ruleCode}
        </if>
        <if test="applicableType != null">
            AND r.applicable_type = #{applicableType}
        </if>
    </select>

    <update id="updateDeleteMarkByRuleCode" parameterType="java.lang.String">
        update product_sku_price_rule set delete_mark = 1 where rule_code = #{ruleCode}
    </update>
</mapper>
