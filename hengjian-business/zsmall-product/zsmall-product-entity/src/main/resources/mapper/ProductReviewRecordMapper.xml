<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zsmall.product.entity.mapper.ProductReviewRecordMapper">

    <resultMap id="BaseResultMap" type="com.zsmall.product.entity.domain.ProductReviewRecord">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="productCode" column="product_code" jdbcType="VARCHAR"/>
        <result property="reviewOpinion" column="review_opinion" jdbcType="VARCHAR"/>
        <result property="reviewOpinionOption" column="review_opinion_option" typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
        <result property="submitDateTime" column="submit_date_time" jdbcType="TIMESTAMP"/>
        <result property="submitUserId" column="submit_user_id" jdbcType="BIGINT"/>
        <result property="reviewUserId" column="review_user_id" jdbcType="BIGINT"/>
        <result property="reviewDateTime" column="review_date_time" jdbcType="TIMESTAMP"/>
        <result property="reviewType" column="review_type" jdbcType="VARCHAR"/>
        <result property="reviewState" column="review_state" jdbcType="VARCHAR"/>
        <result property="delFlag" column="del_flag" jdbcType="CHAR"/>
        <result property="createBy" column="create_by" jdbcType="BIGINT"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateBy" column="update_by" jdbcType="BIGINT"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <resultMap id="VoResultMap" type="com.zsmall.product.entity.domain.vo.ProductReviewRecordVo">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="productCode" column="product_code" jdbcType="VARCHAR"/>
        <result property="reviewOpinion" column="review_opinion" jdbcType="VARCHAR"/>
        <result property="reviewOpinionOption" column="review_opinion_option" typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
        <result property="submitDateTime" column="submit_date_time" jdbcType="TIMESTAMP"/>
        <result property="submitUserId" column="submit_user_id" jdbcType="BIGINT"/>
        <result property="reviewUserId" column="review_user_id" jdbcType="BIGINT"/>
        <result property="reviewDateTime" column="review_date_time" jdbcType="TIMESTAMP"/>
        <result property="reviewType" column="review_type" jdbcType="VARCHAR"/>
        <result property="reviewState" column="review_state" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        ,product_code,review_opinion,
        review_opinion_option,submit_date_time,submit_user_id,
        review_user_id,review_date_time,review_type,
        review_state,del_flag,create_by,
        create_time,update_by,update_time
    </sql>

    <update id="setAllPendingToAbandoned">
        update product_review_record
        set review_state = 'Abandoned'
        where product_code = #{productCode}
          and review_state in ('Pending', 'Draft')
          and id != #{nowRecordId}
    </update>

    <select id="queryPage" resultMap="VoResultMap" resultType="com.zsmall.product.entity.domain.vo.ProductReviewRecordVo">
        SELECT prr.*,
        p.tenant_id
        FROM product_review_record prr
        JOIN product p ON prr.product_code = p.product_code
        WHERE p.del_flag = '0'
        AND prr.del_flag = '0'
        AND EXISTS
        (SELECT 1
        FROM product_sku ps
        JOIN product_review_change_detail prcd ON ps.product_sku_code = prcd.product_sku_code
        WHERE ps.del_flag = '0'
        AND prcd.review_record_id = prr.id
        AND prcd.del_flag = '0')
        <if test="query.siteId != null">
            and EXISTS(select 1 from product_review_change_detail prcd2 where prcd2.site_id = #{query.siteId} and prcd2.review_record_id = prr.id)
        </if>
        -- 查询类型: 商品名称-ProductName，SKU ID - SKU ID,SKU - SKU,SPU-SPU,供应商id-supplierTenantId
        <if test="@cn.hutool.core.text.CharSequenceUtil@isNotBlank(query.queryValue)">
            <if test="@cn.hutool.core.text.CharSequenceUtil@equals(query.queryType, 'ProductName')">
                AND p.name LIKE CONCAT('%', #{query.queryValue}, '%')
            </if>
            <if test="@cn.hutool.core.text.CharSequenceUtil@equals(query.queryType, 'SKU ID')">
                AND EXISTS(SELECT 1
                FROM product_sku ps
                WHERE ps.product_id = p.id
                AND ps.product_sku_code LIKE CONCAT('%',
                #{query.queryValue}, '%')
                AND ps.del_flag = '0')
            </if>
            <if test="@cn.hutool.core.text.CharSequenceUtil@equals(query.queryType, 'SKU')">
                AND EXISTS(SELECT 1
                FROM product_sku ps
                WHERE ps.product_id = p.id
                AND ps.sku LIKE CONCAT('%',
                #{query.queryValue}, '%')
                AND ps.del_flag = '0')
            </if>
            <if test="@cn.hutool.core.text.CharSequenceUtil@equals(query.queryType, 'SPU')">
                and p.product_code LIKE CONCAT('%',
                #{query.queryValue}, '%')
            </if>
            <if test="@cn.hutool.core.text.CharSequenceUtil@equals(query.queryType, 'SupplierTenantId')">
                and p.tenant_id LIKE CONCAT('%',
                #{query.queryValue}, '%')
            </if>
        </if>

        <if test="query.verifyState != null and query.verifyState != ''">
            AND prr.review_state = #{query.verifyState}
        </if>
        <if test="query.verifyType != null and query.verifyType != ''">
            AND prr.review_type = #{query.verifyType}
        </if>
        <if test="query.submitUserId != null">
            AND prr.submit_user_id = #{query.submitUserId}
        </if>
        AND prr.del_flag = '0'
        <if test="query.verifyState == 'Pending'">
            ORDER BY prr.submit_date_time ASC
        </if>
        <if test="query.verifyState == 'Accepted' || query.verifyState == 'Rejected'">
            ORDER BY prr.review_date_time DESC
        </if>

    </select>

    <select id="queryProductSkuCodeByRecord" resultType="java.lang.String">
        SELECT DISTINCT prcd.product_sku_code
        FROM product_review_record prr
                 JOIN product_review_change_detail prcd ON prr.id = prcd.review_record_id
        WHERE prr.id = #{recordId}
          AND prcd.product_sku_code IS NOT NULL
    </select>

    <select id="existsByProductSkuCode" resultType="java.lang.Long">
        SELECT COUNT(DISTINCT prr.id)
        FROM product_review_record prr
                 JOIN product_review_change_detail prcd ON prr.id = prcd.review_record_id
        WHERE prcd.product_sku_code = #{productSkuCode}
          AND prr.review_type = #{reviewType}
          AND prr.review_state = #{verifyState}
          AND prr.del_flag = '0'
          AND prcd.del_flag = '0'
    </select>

    <select id="existsByProductSkuCodes" resultType="java.lang.String">
        SELECT DISTINCT prcd.product_sku_code
        FROM product_review_record prr
                 JOIN product_review_change_detail prcd ON prr.id = prcd.review_record_id
        WHERE prr.review_type = #{reviewType}
          AND prr.review_state = #{verifyState}
          AND prr.del_flag = '0'
          AND prcd.del_flag = '0'
        <if test="productSkuCodes != null and productSkuCodes.size() != 0">
            AND prcd.product_sku_code IN
            <foreach collection="productSkuCodes" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="queryNewestRecordByProductSkuCode"
            resultMap="VoResultMap"
            resultType="com.zsmall.product.entity.domain.vo.ProductReviewRecordVo">
        SELECT prr.*
        FROM product_review_record prr
        WHERE  prr.review_type = #{reviewType}
          AND prr.review_state = #{verifyState}
          AND prr.del_flag = '0'
          AND EXISTS (SELECT 1 FROM product_review_change_detail prcd WHERE prr.id = prcd.review_record_id AND prcd.product_sku_code = #{productSkuCode} AND prcd.del_flag = '0')
        LIMIT 0,1
    </select>
    <select id="queryRecordByProductCodesAndReviewType"
            resultType="com.zsmall.product.entity.domain.ProductReviewRecord">
        SELECT DISTINCT prr.*
        FROM (SELECT *,
        ROW_NUMBER() OVER(PARTITION BY product_code ORDER BY review_date_time DESC) AS rn
        FROM product_review_record prr
        WHERE review_state = #{reviewType}

        <if test="productCodes != null and productCodes.size() > 0">
            and
            prr.product_code IN
            <foreach item="productCode" collection="productCodes" open="(" separator="," close=")">
                #{productCode}
            </foreach>
        </if>

        ) AS prr
        WHERE rn = 1;
    </select>
    <select id="getIdByProductSkuCode" resultType="java.lang.Long">
        SELECT DISTINCT prr.id
        FROM product_review_record prr
                 JOIN product_review_change_detail prcd ON prr.id = prcd.review_record_id
        WHERE prcd.product_sku_code = #{productSkuCode}
          AND prr.review_type = #{reviewType}
          AND prr.review_state = #{verifyState}
          AND prr.del_flag = '0'
          AND prcd.del_flag = '0'
    </select>
</mapper>
