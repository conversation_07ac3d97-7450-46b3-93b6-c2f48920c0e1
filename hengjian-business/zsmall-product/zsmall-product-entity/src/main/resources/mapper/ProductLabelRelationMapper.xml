<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zsmall.product.entity.mapper.ProductLabelRelationMapper">

    <select id="queryLabelNameByProductId" resultType="java.lang.String">
        SELECT GROUP_CONCAT(pl.label_name)
        FROM product_label_relation plr
                 JOIN product_label pl on plr.label_id = pl.id
        WHERE plr.del_flag = '0'
          AND plr.product_id = #{productId}
    </select>
</mapper>
