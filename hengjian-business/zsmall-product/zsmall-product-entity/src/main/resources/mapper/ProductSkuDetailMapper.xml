<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zsmall.product.entity.mapper.ProductSkuDetailMapper">

    <resultMap id="BaseResultMap" type="com.zsmall.product.entity.domain.ProductSkuDetail">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="productSkuId" column="product_sku_id" jdbcType="BIGINT"/>
        <result property="length" column="length" jdbcType="DECIMAL"/>
        <result property="width" column="width" jdbcType="DECIMAL"/>
        <result property="height" column="height" jdbcType="DECIMAL"/>
        <result property="lengthUnit" column="length_unit" jdbcType="VARCHAR"/>
        <result property="weight" column="weight" jdbcType="DECIMAL"/>
        <result property="weightUnit" column="weight_unit" jdbcType="VARCHAR"/>
        <result property="packLength" column="pack_length" jdbcType="DECIMAL"/>
        <result property="packWidth" column="pack_width" jdbcType="DECIMAL"/>
        <result property="packHeight" column="pack_height" jdbcType="DECIMAL"/>
        <result property="packLengthUnit" column="pack_length_unit" jdbcType="VARCHAR"/>
        <result property="packWeight" column="pack_weight" jdbcType="DECIMAL"/>
        <result property="packWeightUnit" column="pack_weight_unit" jdbcType="VARCHAR"/>
        <result property="samePacking" column="same_packing" jdbcType="TINYINT"/>
        <result property="processingTime" column="processing_time" jdbcType="DECIMAL"/>
        <result property="transportMethod" column="transport_method" jdbcType="VARCHAR"/>
        <result property="description" column="description" jdbcType="VARCHAR"/>
        <result property="delFlag" column="del_flag" jdbcType="CHAR"/>
        <result property="createBy" column="create_by" jdbcType="BIGINT"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateBy" column="update_by" jdbcType="BIGINT"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,product_sku_id,`length`,
        width,height,length_unit,
        weight,weight_unit,pack_length,
        pack_width,pack_height,pack_length_unit,
        pack_weight,pack_weight_unit,same_packing,
        processing_time,transport_method,description,
        del_flag,create_by,create_time,
        update_by,update_time
    </sql>

    <select id="getListByProductId" resultType="com.zsmall.product.entity.domain.ProductSkuDetail">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        product_sku_detail psd INNER JOIN product_sku ps ON ps.id = psd.product_sku_id
        WHERE
        ps.product_id = #{productId}
    </select>

    <select id="queryByProductSkuCode" resultType="com.zsmall.product.entity.domain.ProductSkuDetail">
        SELECT psd.* FROM product_sku_detail psd JOIN product_sku ps on psd.product_sku_id = ps.id WHERE ps.product_sku_code = #{productSkuCode}
    </select>

</mapper>
