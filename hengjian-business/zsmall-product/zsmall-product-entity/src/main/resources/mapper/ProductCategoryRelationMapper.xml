<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zsmall.product.entity.mapper.ProductCategoryRelationMapper">

    <select id="existProductCodeRelationByCategoryIds" resultType="java.lang.String">
        SELECT DISTINCT(p.product_code)
        FROM product_category_relation pcr
                 JOIN product p on pcr.product_id = p.id
        WHERE p.del_flag = '0'
          AND pcr.del_flag = '0'
          AND pcr.product_category_id IN
        <foreach item="item" index="index" collection="categoryIds" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
</mapper>
