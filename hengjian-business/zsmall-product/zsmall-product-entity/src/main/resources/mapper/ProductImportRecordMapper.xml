<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zsmall.product.entity.mapper.ProductImportRecordMapper">

    <resultMap id="QueryPageMap"
               type="com.zsmall.product.entity.domain.vo.productImport.ProductImportRecordVo">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="importRecordNo" column="import_record_no" jdbcType="VARCHAR"/>
        <result property="importFileName" column="import_file_name" jdbcType="VARCHAR"/>
        <result property="importProducts" column="import_products" jdbcType="INTEGER"/>
        <result property="importMessage" column="import_message" jdbcType="OTHER" typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
        <result property="importState" column="import_state" jdbcType="VARCHAR"/>
    </resultMap>

    <select id="existImportRecordNo" resultType="java.lang.Boolean">
        SELECT COUNT(oir.id) FROM product_import_record oir WHERE oir.import_record_no = #{importRecordNo}
    </select>

    <select id="queryPage" resultMap="QueryPageMap" resultType="com.zsmall.product.entity.domain.vo.productImport.ProductImportRecordVo">
        SELECT oir.*
        FROM product_import_record oir
        WHERE oir.del_flag = '0' ORDER BY oir.create_time DESC
    </select>
</mapper>
