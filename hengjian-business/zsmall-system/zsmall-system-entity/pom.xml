<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>zsmall-system</artifactId>
        <groupId>com.zsmall</groupId>
        <version>${zsmall.version}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>zsmall-system-entity</artifactId>
    <groupId>com.zsmall</groupId>
    <name>ZS-Mall系统实体模块</name>

    <properties>
        <maven.compiler.source>11</maven.compiler.source>
        <maven.compiler.target>11</maven.compiler.target>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.zsmall</groupId>
            <artifactId>zsmall-common</artifactId>
        </dependency>

        <dependency>
            <groupId>com.hengjian</groupId>
            <artifactId>hengjian-common-tenant</artifactId>
        </dependency>

        <dependency>
            <groupId>com.hengjian</groupId>
            <artifactId>hengjian-common-mybatis</artifactId>
        </dependency>

        <dependency>
            <groupId>com.hengjian</groupId>
            <artifactId>hengjian-common-sensitive</artifactId>
        </dependency>

        <dependency>
            <groupId>com.hengjian</groupId>
            <artifactId>hengjian-extend-excel-translation</artifactId>
        </dependency>
        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-swagger-common</artifactId>
            <version>3.0.0</version>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
            <version>1.2.83</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.xuxueli</groupId>
            <artifactId>xxl-conf-core</artifactId>
        </dependency>
<!--        <dependency>-->
<!--            <groupId>com.zsmall</groupId>-->
<!--            <artifactId>zsmall-order-entity</artifactId>-->
<!--        </dependency>-->
        <dependency>
            <groupId>com.hengjian</groupId>
            <artifactId>hengjian-stream-mq</artifactId>
        </dependency>
    </dependencies>

</project>
