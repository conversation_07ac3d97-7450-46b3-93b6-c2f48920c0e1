package com.zsmall.system.entity.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.hengjian.common.tenant.core.NoDeptTenantEntity;
import com.zsmall.common.enums.order.LogisticsTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import com.zsmall.common.enums.order.LogisticsTypeEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;


/**
 * 租户渠道对象 tenant_sales_channel
 *
 * <AUTHOR> Li
 * @date 2023-06-07
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("tenant_sales_channel")
public class TenantSalesChannel extends NoDeptTenantEntity {


    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * erp店铺标识
     */
    private String thirdChannelFlag;

    /**
     * 渠道名称
     */
    private String channelName;

    /**
     * 渠道类型
     */
    private String channelType;
    /**
     * temu渠道类型 1跨境 2本土
     */
    private Integer temuChannelType;

    /**
     * 各渠道通用的key字段
     * 注意：亚马逊-该值存应用的AppId
     */
    private String privateKey;

    /**
     * 渠道连接秘钥 - 根据不同的渠道，内容意义不一样
     * Shopify: accessToken
     * Wayfire: clientSecret
     */
    private String clientSecret;

    /**
     * 商店访问地址
     */
    private String channelUrl;

    /**
     * 渠道店铺别名
     */
    private String channelAlias;

    /**
     * 授权时间
     */
    private LocalDate authorizationAt;

    /**
     * 接口信息
     */
    private String connectStr;

    /**
     * 状态
     */
    private Integer state;
    /**
     *    CREATED("待授权","created"),
     *
     *     RUNING("正常同步","runing"),
     *
     *
     *     INACTIVE("暂停同步", "inactive"),
     *
     *     NORMAL("授权正常", "normal"),
     *
     *     EXCEPTION("授权异常", "exce"),
     */
    private String status;
    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;

    /**
     * 站点
     */
    private String site;

    /**
     * 帐户名称
     */
    private String accountName;
    private String area;
    private String storeName;

    /**
     * 物流类型枚举
     */
    @ApiModelProperty(value = "物流类型枚举,PickUp,DropShipping",required = true)
    private LogisticsTypeEnum logisticsType;
}
