package com.zsmall.system.entity.domain.vo.salesChannel;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.hengjian.common.excel.annotation.ExcelDictFormat;
import com.hengjian.common.excel.convert.ExcelDictConvert;
import com.zsmall.common.enums.order.LogisticsTypeEnum;
import com.zsmall.system.entity.domain.ChannelWarehouseInfo;
import com.zsmall.system.entity.domain.TenantSalesChannel;
import io.github.linpeilie.annotations.AutoMapper;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;


/**
 * 租户渠道视图对象 tenant_sales_channel
 *
 * <AUTHOR> Li
 * @date 2023-06-07
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = TenantSalesChannel.class)
public class TenantSalesChannelVo implements Serializable {


    private static final long serialVersionUID = 1L;


    /**
     * erp店铺标识
     */
    private String thirdChannelFlag;

    /**
     * 主键
     */
    private Long id;

    /**
     * 渠道名称
     */
    @ExcelProperty(value = "渠道名称")
    private String channelName;

    /**
     * 渠道类型
     */
    @ExcelProperty(value = "渠道类型", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "biz_sales_channel_type")
    private String channelType;

    /**
     * temu渠道类型 1跨境 2本土
     */
    private Integer temuChannelType;
    /**
     * 渠道店铺别名
     */
    @ExcelProperty(value = "渠道店铺别名")
    private String channelAlias;

    /**
     * 商店访问地址
     */
    private String channelUrl;

    /**
     * 状态
     */
    @ExcelProperty(value = "状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "biz_global_state")
    private Integer state;

    /**
     * 各渠道通用的key字段
     */
    private String privateKey;

    /**
     * 渠道连接秘钥 - 根据不同的渠道，内容意义不一样
     * Shopify: accessToken
     * Wayfire: clientSecret
     */
    private String clientSecret;

    /**
     * 站点
     */
    private String site;

    /**
     * 帐户名称
     */
    private String accountName;
    private String area;
    private String storeName;
    // 站点集合
    @ApiModelProperty(value = "站点集合",required = true)
    private List<String> siteList;
    /**
     * 物流类型枚举
     */
    @ApiModelProperty(value = "物流类型枚举,PickUp,DropShipping",required = true)
    private LogisticsTypeEnum logisticsType;

    @ApiModelProperty(value = "temu回显")
    private String usAccessToken;
    @ApiModelProperty(value = "temu回显")
    private String cnAccessToken;

    @ApiModelProperty(value = "授权信息")
    private String connectStr;

    @ApiModelProperty(value = "Temu类型 本土显示：appKey" )
    private String appKey;
    @ApiModelProperty(value = "Temu类型 本土显示：appSecret" )
    private String appSecret;
    @ApiModelProperty(value = "Temu类型 本土显示：accessToken" )
    private String accessToken;

    // 授权字段 userName
    private String userName;
    // 授权字段 userPass
    private String userPass;
    // 授权字段 serviceToken
    private String serviceToken;
    // 授权字段 apiUri
    private String apiUri;

    /**
     * 渠道仓库信息
     */
    private List<ChannelWarehouseInfo> channelWarehouseInfoList;
}
