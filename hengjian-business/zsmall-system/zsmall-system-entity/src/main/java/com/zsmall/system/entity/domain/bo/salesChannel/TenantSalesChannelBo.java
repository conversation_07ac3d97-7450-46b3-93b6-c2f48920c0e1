package com.zsmall.system.entity.domain.bo.salesChannel;

import com.hengjian.common.core.validate.AddGroup;
import com.hengjian.common.core.validate.EditGroup;
import com.hengjian.common.mybatis.core.domain.BaseEntity;
import com.zsmall.common.enums.order.LogisticsTypeEnum;
import com.zsmall.system.entity.domain.ChannelWarehouseInfo;
import com.zsmall.system.entity.domain.TenantSalesChannel;
import io.github.linpeilie.annotations.AutoMapper;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 租户渠道业务对象 tenant_sales_channel
 *
 * <AUTHOR> Li
 * @date 2023-06-07
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = TenantSalesChannel.class, reverseConvertGenerate = false)
public class TenantSalesChannelBo extends BaseEntity {

    private Long id;

    /**
     * 渠道名称
     */
    @ApiModelProperty(required = true)
    @NotBlank(message = "{zsmall.store.channelNameIsEmpty}", groups = { AddGroup.class, EditGroup.class })
    private String channelName;

    /**
     * 多渠道店铺标识
     */
    @ApiModelProperty(required = true)
    private String thirdChannelFlag;

    /**
     * 渠道类型
     */
    @ApiModelProperty(required = true)
    @NotBlank(message = "{zsmall.store.channelTypeIsEmpty}", groups = { AddGroup.class, EditGroup.class })
    private String channelType;

    /**
     * temu渠道类型 1跨境 2本土
     */
    private Integer temuChannelType;
    /**
     * 各渠道通用的key字段
     */
    @ApiModelProperty(required = true)
    private String privateKey;

    /**
     * 渠道连接秘钥
     */
    @ApiModelProperty(required = true)
    private String clientSecret;

    /**
     * 商店访问地址
     */
    @ApiModelProperty(required = true)
    private String channelUrl;

    /**
     * 渠道店铺名称
     */
    @ApiModelProperty(required = true)
    private String storeName;

    /**
     * 渠道店铺别名
     */
    @ApiModelProperty(required = true)
    private String channelAlias;

    /**
     * 状态
     */
    @ApiModelProperty(required = true)
    @NotNull(message = "状态不能为空", groups = { AddGroup.class, EditGroup.class })
    private Integer state;

    /**
     * app 配置表Id
     */
    @ApiModelProperty(required = true)
    private String appConfigId;


    /**
     *    CREATED("待授权","created"),
     *
     *     RUNING("正常同步","runing"),
     *
     *
     *     INACTIVE("暂停同步", "inactive"),
     *
     *     NORMAL("授权正常", "normal"),
     *
     *     EXCEPTION("授权异常", "exce"),
     */
    private String status;

    // 站点集合
    @ApiModelProperty(value = "站点集合",required = true)
    private List<String> siteList;
    @ApiModelProperty(value = "跨境显示 temu必须",required = true)
    private String usAccessToken;
    @ApiModelProperty(value = "跨境显示 temu可传",required = true)
    private String cnAccessToken;
    @ApiModelProperty(value = "区域,亚马逊必传",required = true)
    private String area;
    @ApiModelProperty(value = "站点")
    private String site;
    @ApiModelProperty(value = "账号" )
    private String accountName;
    @ApiModelProperty(value = "Temu类型 本土显示：appKey" )
    private String appKey;
    @ApiModelProperty(value = "Temu类型 本土显示：appSecret" )
    private String appSecret;
    @ApiModelProperty(value = "Temu类型 本土显示：accessToken" )
    private String accessToken;

    /**
     * 物流类型枚举
     */
    @ApiModelProperty(value = "物流类型枚举,PickUp,DropShipping",required = true)
    private LogisticsTypeEnum logisticsType;

    // 授权字段 userName
    private String userName;
    // 授权字段 userPass
    private String userPass;
    // 授权字段 serviceToken
    private String serviceToken;
    // 授权字段 apiUri
    private String apiUri;

    /**
     * 渠道仓库信息
     */
    private List<ChannelWarehouseInfo> channelWarehouseInfoList;
    /**
     * SC渠道仓库信息专用
     */
    public List<AmazonScChannelWarehouseInfo> amazonScChannelWarehouseInfos;

    /**
     * SC渠道仓库信息专用
     */
    @Data
    public static class AmazonScChannelWarehouseInfo{
        /**
         * 国家
         */
        private String siteType;
        /**
         * 渠道仓库信息
         */
        private List<ChannelWarehouseInfo> warehouseInfos;
    }
    /**
     * 1新增 2更新
     */
    private Integer RequestMode;
}
