<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>zsmall-system</artifactId>
        <groupId>com.zsmall</groupId>
        <version>${zsmall.version}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>zsmall-system-biz</artifactId>
    <groupId>com.zsmall</groupId>
    <name>ZS-Mall系统业务模块</name>

    <properties>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.hengjian</groupId>
            <artifactId>hengjian-common-doc</artifactId>
        </dependency>
        <dependency>
            <groupId>com.hengjian</groupId>
            <artifactId>hengjian-system</artifactId>
        </dependency>

        <dependency>
            <groupId>com.hengjian</groupId>
            <artifactId>hengjian-extend-emailsms</artifactId>
        </dependency>

        <dependency>
            <groupId>com.zsmall</groupId>
            <artifactId>zsmall-system-entity</artifactId>
        </dependency>

        <!-- zsmall shop starting -->
        <dependency>
            <groupId>com.zsmall.extend.shop</groupId>
            <artifactId>zsmall-shop-starter</artifactId>
        </dependency>
        <!-- zsmall shop end -->

        <!-- 支付 Payoneer -->
        <dependency>
            <groupId>com.github.javen205</groupId>
            <artifactId>IJPay-Payoneer</artifactId>
        </dependency>

        <dependency>
            <groupId>com.zsmall</groupId>
            <artifactId>zsmall-product-biz</artifactId>
            <optional>true</optional>
        </dependency>

        <dependency>
            <groupId>com.zsmall</groupId>
            <artifactId>zsmall-order-entity</artifactId>
        </dependency>

        <dependency>
            <groupId>com.zsmall.extend.event</groupId>
            <artifactId>zsmall-extend-event</artifactId>
        </dependency>

        <dependency>
            <groupId>com.zsmall.extend.payment</groupId>
            <artifactId>zsmall-payment-payoneer</artifactId>
        </dependency>
        <dependency>
            <groupId>com.zsmall.extend.payment</groupId>
            <artifactId>zsmall-payment-airwallex</artifactId>
        </dependency>

        <dependency>
            <groupId>com.zsmall</groupId>
            <artifactId>zsmall-activity-entity</artifactId>
        </dependency>
        <dependency>
            <groupId>com.xuxueli</groupId>
            <artifactId>xxl-conf-core</artifactId>
        </dependency>

    </dependencies>

</project>
