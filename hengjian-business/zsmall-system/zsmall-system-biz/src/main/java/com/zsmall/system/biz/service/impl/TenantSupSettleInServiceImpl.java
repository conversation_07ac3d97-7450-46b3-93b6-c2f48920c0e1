package com.zsmall.system.biz.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hengjian.common.core.domain.R;
import com.hengjian.common.core.enums.LanguageType;
import com.hengjian.common.core.enums.TenantType;
import com.hengjian.common.core.exception.RStatusCodeException;
import com.hengjian.common.core.utils.ServletUtils;
import com.hengjian.common.core.utils.StringUtils;
import com.hengjian.common.log.annotation.InMethodLog;
import com.hengjian.common.mybatis.core.page.PageQuery;
import com.hengjian.common.mybatis.core.page.TableDataInfo;
import com.hengjian.common.redis.utils.RedisUtils;
import com.hengjian.common.satoken.utils.LoginHelper;
import com.hengjian.common.tenant.helper.TenantHelper;
import com.hengjian.extend.verifycode.constant.VerifyCodeConstant;
import com.hengjian.extend.verifycode.utils.VerifyCodeUtils;
import com.hengjian.system.domain.vo.SysTenantVo;
import com.hengjian.system.domain.vo.SysUserVo;
import com.hengjian.system.service.ISysTenantService;
import com.hengjian.system.service.ISysUserService;
import com.zsmall.common.constant.RedisConstants;
import com.zsmall.common.domain.bo.AttachmentBo;
import com.zsmall.common.enums.ExtraPerfectionFlag;
import com.zsmall.common.enums.statuscode.ZSMallStatusCodeEnum;
import com.zsmall.common.enums.tenantSettleIn.*;
import com.zsmall.product.entity.domain.vo.category.ProductCategoryVo;
import com.zsmall.product.entity.iservice.IProductCategoryService;
import com.zsmall.system.biz.service.TenantSupSettleInService;
import com.zsmall.system.entity.domain.*;
import com.zsmall.system.entity.domain.bo.settleInBasic.ReviewRecordBo;
import com.zsmall.system.entity.domain.bo.settleInBasic.SupSettleInExtendVo;
import com.zsmall.system.entity.domain.bo.settleInBasic.SupSettleInGetBo;
import com.zsmall.system.entity.domain.bo.settleInBasic.SupSettleInInfoBo;
import com.zsmall.system.entity.domain.dto.settleInBasic.SupSettleInContactVo;
import com.zsmall.system.entity.domain.dto.settleInBasic.UserSupReviewRecordDto;
import com.zsmall.system.entity.domain.dto.settleInBasic.UserSupReviewRecordParamDto;
import com.zsmall.system.entity.domain.vo.settleInBasic.*;
import com.zsmall.system.entity.domain.vo.worldLocation.WorldLocationVo;
import com.zsmall.system.entity.iservice.*;
import com.zsmall.system.entity.util.SettleInReflectUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.Duration;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/8/21 12:07
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TenantSupSettleInServiceImpl implements TenantSupSettleInService {

    private final ITenantSupSettleInBasicService iTenantSupSettleInBasicService;
    private final ITenantSupSettleInReviewRecordService iTenantSupSettleInReviewRecordService;
    private final ITenantSupSettleInBasicAttachmentService iTenantSupSettleInBasicAttachmentService;
    private final ITenantSupSettleInExtendedService iTenantSupSettleInExtendedService;
    private final ITenantSupSettleInExtendedLogService iTenantSupSettleInExtendedLogService;
    private final ITenantSupSettleInExtendedAttachmentService iTenantSupSettleInExtendedAttachmentService;
    private final ITenantSupSettleInExtendedAttachmentLogService iTenantSupSettleInExtendedAttachmentLogService;
    private final ITenantSupSettleInContactService iTenantSupSettleInContactService;
    private final ITenantSupSettleInContactLogService iTenantSupSettleInContactLogService;
    private final ITenantSupSettleInBasicLogService iTenantSupSettleInBasicLogService;
    private final ITenantSupSettleInBasicAttachmentLogService iTenantSupSettleInBasicAttachmentLogService;
    private final IWorldLocationService iWorldLocationService;
    private final ISysTenantService iSysTenantService;
    private final ISysUserService sysUserService;
    private final IProductCategoryService iProductCategoryService;


    @InMethodLog(value = "供应商入驻信息保存")
    @Override
    @Transactional(rollbackFor = {Exception.class, RStatusCodeException.class})
    public R<Void> saveSupSettleIn(SupSettleInInfoBo bo) throws Exception {
        if (!LoginHelper.getTenantTypeEnum().equals(TenantType.Supplier)) {
            return R.fail(ZSMallStatusCodeEnum.CURRENT_USER_CANNOT_USE);
        }
        //参数校验
        Boolean checkResult = checkSettleInInfo(bo);
        if (!checkResult) {
            return R.fail(ZSMallStatusCodeEnum.REQUIRED_CANNOT_EMPTY);
        }

        //如果该供应商入驻信息是待审核状态，则直接返回
        TenantSupSettleInReviewRecord reviewRecord = iTenantSupSettleInReviewRecordService.getByTenantId(null);
        if (reviewRecord != null) {
            SettleInReviewRecordEnum reviewState = reviewRecord.getReviewState();
            if (reviewState.equals(SettleInReviewRecordEnum.Reviewing) || reviewState.equals(SettleInReviewRecordEnum.ReviewedAgain)) {
                return R.fail(ZSMallStatusCodeEnum.USER_SUP_SETTLE_IN_TO_BE_REVIEWED);
            }
            if (reviewState.equals(SettleInReviewRecordEnum.Rejected) || reviewState.equals(SettleInReviewRecordEnum.Approved)) {
                reviewRecord.setReviewState(SettleInReviewRecordEnum.ReviewedAgain);
            }
        } else {
            reviewRecord = new TenantSupSettleInReviewRecord();
            reviewRecord.setReviewState(SettleInReviewRecordEnum.Reviewing);
            // 设置弹窗缓存
            RedisUtils.setCacheObject(RedisConstants.EXTRA_PERFECTION_FLAG + LoginHelper.getTenantId() + SettleInReviewRecordEnum.Reviewing, "False", Duration.ofSeconds(5184000));
        }
        boolean recordSave = iTenantSupSettleInReviewRecordService.saveOrUpdate(reviewRecord);
        if (!recordSave) {
            throw new Exception("保存供应商入驻审核信息失败！");
        }

        SupSettleInBasicBo basicBody = bo.getBasicBody();
        List<SupSettleInAttachmentVo> attachmentBodyList = basicBody.getReqAttachmentBodyList();
        SupSettleInExtendVo extendedBody = bo.getExtendedBody();
        AttachmentBo attachmentBo = extendedBody.getAttachmentBo();
        ContactBo reqcontactBody = bo.getContactBody();
        List<SupSettleInContactVo> contactBodyList = new ArrayList();
        contactBodyList.add(reqcontactBody.getBusinessContactBody());
        contactBodyList.add(reqcontactBody.getFinancialContactBody());
        contactBodyList.add(reqcontactBody.getAfterSalesContactBody());

        //填充供应商入驻基础信息及附件
        Long recordId = reviewRecord.getId();
        Long stateId = basicBody.getStateId();
        TenantSupSettleInBasic oldBasic = iTenantSupSettleInBasicService.getByTenantId(null);
        Long basicId = oldBasic != null ? oldBasic.getId() : null;
        DocumentTypeEnum documentTypeEnum = DocumentTypeEnum.fromName(basicBody.getDocumentType());
        TenantSupSettleInBasic newBasic = new TenantSupSettleInBasic();
        if (ObjUtil.isNotNull(oldBasic)) {
            //添加变更记录信息
            iTenantSupSettleInBasicLogService.saveLog(recordId, oldBasic);
            newBasic = BeanUtil.copyProperties(oldBasic, TenantSupSettleInBasic.class);
        }
        newBasic.setCompanyName(basicBody.getCompanyName());
        newBasic.setSocialCreditCode(basicBody.getSocialCreditCode());
        newBasic.setCountryId(basicBody.getCountryId());
        newBasic.setStateId(stateId);
        newBasic.setStateText(basicBody.getStateText());
        newBasic.setCityText(basicBody.getCityText());
        newBasic.setRegisteredAddress(basicBody.getRegisteredAddress());
        newBasic.setLegalPersonPlace(basicBody.getLegalPersonPlace());
        newBasic.setDocumentType(ObjUtil.isNotNull(documentTypeEnum) ? documentTypeEnum.getCode() : null);
        newBasic.setDocumentNumber(basicBody.getDocumentNumber());
        newBasic.setLegalPersonName(basicBody.getLegalPersonName());
//        if(StringUtils.isNotEmpty(basicBody.getCompanyTaxRegistrationNumber())){
//            String regex = "^(?=.*[a-zA-Z])(?=.*[0-9])[a-zA-Z0-9]{1,30}$";
//            if(Pattern.compile(regex).matcher(basicBody.getCompanyTaxRegistrationNumber()).matches()){
//                newBasic.setCompanyTaxRegistrationNumber(basicBody.getCompanyTaxRegistrationNumber());
//            }else {
//                throw new Exception("公司税务登记号不符合要求！长度最大30，数字和字母组成");
//            }
//        }else {
//            throw new Exception("公司税务登记号不能为空!");
//        }
        if (!iTenantSupSettleInBasicService.saveOrUpdate(newBasic)) {
            throw new Exception("保存供应商入驻基础信息失败！");
        }

        //获取基础附件信息
        List<TenantSupSettleInBasicAttachment> attachmentList = iTenantSupSettleInBasicAttachmentService.getListByBasicId(newBasic.getId());
        if (CollUtil.size(attachmentList) > 0) {
            iTenantSupSettleInBasicAttachmentLogService.saveLog(recordId, attachmentList);
            //先删除原先的附件数据，再重新添加新的
            iTenantSupSettleInBasicAttachmentService.deleteByBasicId(basicId);
        }
        //基础信息附件填充
        if (CollUtil.isNotEmpty(attachmentBodyList)) {
            List<TenantSupSettleInBasicAttachment> basicAttachments = new ArrayList<>();
            for (SupSettleInAttachmentVo attachmentBody : attachmentBodyList) {
                FileNatureEnum fileNatureEnum = FileNatureEnum.fromName(attachmentBody.getFileNature());
                TenantSupSettleInBasicAttachment basicAttachment = new TenantSupSettleInBasicAttachment();
                basicAttachment.setOssId(attachmentBody.getOssId());
                basicAttachment.setAttachmentName(attachmentBody.getAttachmentName());
                basicAttachment.setAttachmentOriginalName(attachmentBody.getAttachmentOriginalName());
                basicAttachment.setAttachmentSuffix(attachmentBody.getAttachmentSuffix());
                basicAttachment.setAttachmentShowUrl(attachmentBody.getAttachmentShowUrl());
                basicAttachment.setAttachmentSavePath(attachmentBody.getAttachmentSavePath());
                basicAttachment.setAttachmentSort(attachmentBody.getAttachmentSort());
                basicAttachment.setAttachmentType(attachmentBody.getAttachmentType());
                basicAttachment.setFileNature(fileNatureEnum == null ? null : fileNatureEnum.getCode());
                basicAttachment.setBasicId(newBasic.getId());
                basicAttachments.add(basicAttachment);
            }
            if (CollUtil.isNotEmpty(basicAttachments)) {
                boolean basicAttachmentSaveBatch = iTenantSupSettleInBasicAttachmentService.saveBatch(basicAttachments);
                if (!basicAttachmentSaveBatch) {
                    throw new Exception("保存供应商入驻基础信息附件失败！");
                }
            }
        }


        //填充供应商入驻扩展信息及附件
        TenantSupSettleInExtended oldExtended = iTenantSupSettleInExtendedService.getByBasicId(newBasic.getId());
        TenantSupSettleInExtended newExtended = new TenantSupSettleInExtended();
        if (ObjUtil.isNotNull(oldExtended)) {
            //比较两个扩展信息是否有变动并添加变更记录信息
            iTenantSupSettleInExtendedLogService.saveLog(recordId, oldExtended);
            newExtended = BeanUtil.copyProperties(oldExtended, TenantSupSettleInExtended.class);
        }
        //填充扩展信息
        if (CollUtil.isNotEmpty(extendedBody.getOtherChannels())) {
            extendedBody.getOtherChannels().remove("otherChannels");
        }
        newExtended.setBasicId(newBasic.getId());
        newExtended.setCompanyNature(CompanyNatureEnum.fromName(extendedBody.getCompanyNature()).getCode());
        newExtended.setMainCategories(extendedBody.getMainCategories());
        newExtended.setIsOnlineRetailersExperience(extendedBody.getIsOnlineRetailersExperience());
        newExtended.setMainProductionCountryId(extendedBody.getMainProductionCountryId());
        newExtended.setIsOverseasWarehouse(extendedBody.getIsOverseasWarehouse());
        // 没有海外仓信息将海外仓地址信息设置为null
        if(!extendedBody.getIsOverseasWarehouse()){
            newExtended.setStateId(null);
            newExtended.setStateText(null);
            newExtended.setCityText(null);
            newExtended.setWarehouseAddress(null);
        }else {
            newExtended.setStateId(extendedBody.getStateId());
            newExtended.setStateText(extendedBody.getStateText());
            newExtended.setCityText(extendedBody.getCityText());
            newExtended.setWarehouseAddress(extendedBody.getWarehouseAddress());
        }
        newExtended.setCountryId(extendedBody.getCountryId());
        JSONArray otherChannels = extendedBody.getOtherChannels();
        if (otherChannels == null || otherChannels.size() == 0) {
            newExtended.setOtherChannels(null);
        }else {
            newExtended.setOtherChannels(otherChannels);
        }
        newExtended.setOtherChannelText(extendedBody.getOtherChannelText());
        newExtended.setDeliverTimeLimit(extendedBody.getDeliverTimeLimit());
        newExtended.setSalesPriceRecentYear(extendedBody.getSalesPriceRecentYear());
        if (!iTenantSupSettleInExtendedService.saveOrUpdate(newExtended)) {
            throw new Exception("保存供应商入驻扩展信息失败！");
        }

        //填充扩展信息附件
        TenantSupSettleInExtendedAttachment oldExtendedAttachment = iTenantSupSettleInExtendedAttachmentService.getByExtendedId(newExtended.getId());
        if (ObjUtil.isNotNull(oldExtendedAttachment)) {
            iTenantSupSettleInExtendedAttachmentLogService.saveLog(recordId, oldExtendedAttachment);
            iTenantSupSettleInExtendedAttachmentService.removeById(oldExtendedAttachment);
        }
        TenantSupSettleInExtendedAttachment newExtendedAttachment = new TenantSupSettleInExtendedAttachment();
        newExtendedAttachment.setExtendedId(newExtended.getId());
        newExtendedAttachment.setOssId(attachmentBo.getOssId());
        newExtendedAttachment.setAttachmentName(attachmentBo.getAttachmentName());
        newExtendedAttachment.setAttachmentOriginalName(attachmentBo.getAttachmentOriginalName());
        newExtendedAttachment.setAttachmentSuffix(attachmentBo.getAttachmentSuffix());
        newExtendedAttachment.setAttachmentSavePath(attachmentBo.getAttachmentSavePath());
        newExtendedAttachment.setAttachmentShowUrl(attachmentBo.getAttachmentShowUrl());
        newExtendedAttachment.setAttachmentSort(attachmentBo.getAttachmentSort());
        newExtendedAttachment.setAttachmentType(attachmentBo.getAttachmentType());
        boolean extendedAttachmentSave = iTenantSupSettleInExtendedAttachmentService.saveOrUpdate(newExtendedAttachment);
        if (!extendedAttachmentSave) {
            throw new Exception("保存扩展信息附加失败！");
        }

        //填充供应商入驻联系人信息
        List<TenantSupSettleInContact> oldContacts = iTenantSupSettleInContactService.getListByBasicId(basicId);
        if (CollUtil.isNotEmpty(oldContacts)) {
            iTenantSupSettleInContactLogService.saveLog(recordId, oldContacts);
        }
        List<TenantSupSettleInContact> contactList = new ArrayList<>();
        for (SupSettleInContactVo contactBody : contactBodyList) {
            if (contactBody == null) {
                continue;
            }
//            ContactTypeEnum contactTypeEnum = ContactTypeEnum.fromName(contactBody.getContactType());
//            InstantMessagingAppType msgAppTypeEnum = InstantMessagingAppType.valueOf(contactBody.getMsgAppType());
            List<TenantSupSettleInContact> collect = oldContacts.stream().filter(old -> StrUtil.equals(contactBody.getContactType(), old.getContactType())).collect(Collectors.toList());
            TenantSupSettleInContact newContact = new TenantSupSettleInContact();
            if (CollUtil.isNotEmpty(collect)) {
                newContact = collect.get(0);
            }
            newContact.setBasicId(newBasic.getId());
            newContact.setFirstName(contactBody.getFirstName());
            newContact.setLastName(contactBody.getLastName());
            newContact.setName(contactBody.getFirstName() + contactBody.getLastName());
            newContact.setAreaCode(contactBody.getAreaCode());
            newContact.setPhoneNumber(contactBody.getPhoneNumber());
            newContact.setEmail(contactBody.getEmail());
            newContact.setMsgAppType(contactBody.getMsgAppType());
            newContact.setMsgAppAccount(contactBody.getMsgAppAccount());
            newContact.setCountryId(contactBody.getCountryId());
            newContact.setStateId(contactBody.getStateId());
            newContact.setStateText(contactBody.getStateText());
            newContact.setCityText(contactBody.getCityText());
            newContact.setAddress(contactBody.getAddress());
            newContact.setContactType(contactBody.getContactType());
            contactList.add(newContact);
        }
        if (CollUtil.isNotEmpty(contactList)) {
            if (!iTenantSupSettleInContactService.saveOrUpdateBatch(contactList)) {
                throw new Exception("批量保存公司联系人信息失败！");
            }
        }
        return R.ok();
    }


    @InMethodLog(value = "修改供应商入驻基础信息")
    @Override
    public R<Void> updateSupSettleInBasic(SupSettleInBasicBo bo) throws Exception {
        //如果该供应商入驻信息是待审核状态，则直接返回
        TenantSupSettleInReviewRecord reviewRecord = iTenantSupSettleInReviewRecordService.getByTenantId(null);
        if (reviewRecord != null) {
            SettleInReviewRecordEnum reviewState = reviewRecord.getReviewState();
            if (reviewState.equals(SettleInReviewRecordEnum.Reviewing) || reviewState.equals(SettleInReviewRecordEnum.ReviewedAgain)) {
                return R.fail(ZSMallStatusCodeEnum.USER_SUP_SETTLE_IN_TO_BE_REVIEWED);
            }
            reviewRecord.setReviewState(SettleInReviewRecordEnum.ReviewedAgain);
        } else {
            reviewRecord = new TenantSupSettleInReviewRecord();
            reviewRecord.setReviewState(SettleInReviewRecordEnum.Reviewing);
        }
        boolean recordResult = iTenantSupSettleInReviewRecordService.saveOrUpdate(reviewRecord);
        if (!recordResult) {
            throw new Exception("保存供应商入驻审核信息失败！");
        }
        Long recordId = reviewRecord.getId();
        List<SupSettleInAttachmentVo> attachmentBodyList = bo.getReqAttachmentBodyList();
        //根据用户编码获取供应商基础信息
        TenantSupSettleInBasic oldBasic = iTenantSupSettleInBasicService.getByTenantId(LoginHelper.getTenantId());
        TenantSupSettleInBasic newBasic = new TenantSupSettleInBasic();
        if (ObjUtil.isNotNull(oldBasic)) {
            //添加变更记录信息
            iTenantSupSettleInBasicLogService.saveLog(recordId, oldBasic);
            newBasic = BeanUtil.copyProperties(oldBasic, TenantSupSettleInBasic.class);
        }
        DocumentTypeEnum documentTypeEnum = DocumentTypeEnum.fromName(bo.getDocumentType());
        newBasic.setCompanyName(bo.getCompanyName());
        newBasic.setSocialCreditCode(bo.getSocialCreditCode());
        newBasic.setCountryId(bo.getCountryId());
        newBasic.setStateId(bo.getStateId());
        newBasic.setStateText(bo.getStateText());
        newBasic.setCityText(bo.getCityText());
        newBasic.setRegisteredAddress(bo.getRegisteredAddress());
        newBasic.setLegalPersonPlace(bo.getLegalPersonPlace());
        newBasic.setDocumentType(ObjUtil.isNotNull(documentTypeEnum) ? documentTypeEnum.getCode() : null);
        newBasic.setDocumentNumber(bo.getDocumentNumber());
        newBasic.setLegalPersonName(bo.getLegalPersonName());
        if (!iTenantSupSettleInBasicService.saveOrUpdate(newBasic)) {
            throw new Exception("保存供应商入驻基础信息失败！");
        }
        Long basicId = newBasic.getId();
        //获取基础附件信息
        List<TenantSupSettleInBasicAttachment> attachmentList = iTenantSupSettleInBasicAttachmentService.getListByBasicId(newBasic.getId());
        if (CollUtil.size(attachmentList) > 0) {
            iTenantSupSettleInBasicAttachmentLogService.saveLog(recordId, attachmentList);
            //先删除原先的附件数据，再重新添加新的
            iTenantSupSettleInBasicAttachmentService.deleteByBasicId(basicId);
        }
        //基础信息附件填充
        if (CollUtil.isNotEmpty(attachmentBodyList)) {
            List<TenantSupSettleInBasicAttachment> basicAttachments = new ArrayList<>();
            for (SupSettleInAttachmentVo attachmentBody : attachmentBodyList) {
                FileNatureEnum fileNatureEnum = FileNatureEnum.fromName(attachmentBody.getFileNature());
                TenantSupSettleInBasicAttachment basicAttachment = new TenantSupSettleInBasicAttachment();
                basicAttachment.setOssId(attachmentBody.getOssId());
                basicAttachment.setAttachmentName(attachmentBody.getAttachmentName());
                basicAttachment.setAttachmentOriginalName(attachmentBody.getAttachmentOriginalName());
                basicAttachment.setAttachmentSuffix(attachmentBody.getAttachmentSuffix());
                basicAttachment.setAttachmentShowUrl(attachmentBody.getAttachmentShowUrl());
                basicAttachment.setAttachmentSavePath(attachmentBody.getAttachmentSavePath());
                basicAttachment.setAttachmentSort(attachmentBody.getAttachmentSort());
                basicAttachment.setAttachmentType(attachmentBody.getAttachmentType());
                basicAttachment.setFileNature(fileNatureEnum == null ? null : fileNatureEnum.getCode());
                basicAttachment.setBasicId(newBasic.getId());
                basicAttachments.add(basicAttachment);
            }
            if (CollUtil.isNotEmpty(basicAttachments)) {
                boolean basicAttachmentSaveBatch = iTenantSupSettleInBasicAttachmentService.saveBatch(basicAttachments);
                if (!basicAttachmentSaveBatch) {
                    throw new Exception("保存供应商入驻基础信息附件失败！");
                }
            }
        }
        return R.ok();
    }


    @InMethodLog(value = "修改供应商入驻联系人信息")
    @Override
    public R<Void> updateSupSettleInContact(ContactBo bo) throws Exception {
        List<SupSettleInContactVo> contactBodyList = new ArrayList<>();
        contactBodyList.add(bo.getBusinessContactBody());
        contactBodyList.add(bo.getFinancialContactBody());
        contactBodyList.add(bo.getAfterSalesContactBody());

        //获取当前用户供应商基础信息
        TenantSupSettleInBasic basic = iTenantSupSettleInBasicService.getByTenantId(null);
        //填充供应商入驻联系人信息
        List<TenantSupSettleInContact> oldContacts = iTenantSupSettleInContactService.getListByBasicId(basic.getId());
        List<TenantSupSettleInContact> contactList = new ArrayList<>();
        for (SupSettleInContactVo contactBody : contactBodyList) {
            if (contactBody == null) {
                continue;
            }
            List<TenantSupSettleInContact> collect = oldContacts.stream().filter(old -> StrUtil.equals(contactBody.getContactType(), old.getContactType())).collect(Collectors.toList());
            TenantSupSettleInContact newContact = new TenantSupSettleInContact();
            if (CollUtil.isNotEmpty(collect)) {
                newContact = collect.get(0);
            }
            newContact.setBasicId(basic.getId());
            newContact.setFirstName(contactBody.getFirstName());
            newContact.setLastName(contactBody.getLastName());
            newContact.setName(StrUtil.isBlank(contactBody.getName()) ? contactBody.getFirstName() + contactBody.getLastName() : contactBody.getName());
            newContact.setAreaCode(contactBody.getAreaCode());
            newContact.setPhoneNumber(contactBody.getPhoneNumber());
            newContact.setEmail(contactBody.getEmail());
            newContact.setMsgAppType(contactBody.getMsgAppType());
            newContact.setMsgAppAccount(contactBody.getMsgAppAccount());
            newContact.setCountryId(contactBody.getCountryId());
            newContact.setStateId(contactBody.getStateId());
            newContact.setStateText(contactBody.getStateText());
            newContact.setCityText(contactBody.getCityText());
            newContact.setAddress(contactBody.getAddress());
            newContact.setContactType(contactBody.getContactType());
            contactList.add(newContact);
        }
        if (CollUtil.isNotEmpty(contactList)) {
            if (!iTenantSupSettleInContactService.saveOrUpdateBatch(contactList)) {
                throw new Exception("批量保存公司联系人信息失败！");
            }
        }
        return R.ok();
    }


    @InMethodLog(value = "获取供应商入驻信息")
    @Override
    public R<SupBasicVo> getSupSettleInResp(SupSettleInGetBo bo) throws IllegalAccessException {
        String tenantId = null;
        if (bo != null) {
            tenantId = bo.getTenantId();
        }
        if (LoginHelper.getTenantType().equals(TenantType.Supplier)) {
            tenantId = LoginHelper.getTenantId();
        }
        TenantSupSettleInBasic basic = iTenantSupSettleInBasicService.getByTenantId(tenantId);
        SupSettleInBasicBo basicInfo = new SupSettleInBasicBo();
        if (basic != null) {
            basicInfo = this.getBasicInfo(basic, true);
        } else {
            return R.fail(ZSMallStatusCodeEnum.USER_SUP_SETTLE_IN_NOT_EXISTS);
        }
        List<TenantSupSettleInContact> contacts = iTenantSupSettleInContactService.getListByBasicId(basic.getId());
        ContactBo contactInfo = new ContactBo();
        if (CollUtil.isNotEmpty(contacts)) {
            contactInfo = this.getContactInfo(contacts);
        }
        TenantSupSettleInExtended extended = iTenantSupSettleInExtendedService.getByBasicId(basic.getId());
        SupSettleInExtendVo extendedInfo = new SupSettleInExtendVo();
        if (extended != null) {
            extendedInfo = this.getExtendedInfo(extended, true);
        }
        SupSettleInInfoBo respBody = new SupSettleInInfoBo();
        SysTenantVo sysTenantVo = iSysTenantService.queryByTenantId(basic.getTenantId());
        respBody.setProductSourceType(sysTenantVo.getProductSourceType());
        respBody.setBasicBody(basicInfo);
        respBody.setContactBody(contactInfo);
        respBody.setExtendedBody(extendedInfo);
        SupBasicVo supBasicVo = new SupBasicVo();
        supBasicVo.setFullVo(respBody);

        if (LoginHelper.getTenantTypeEnum().equals(TenantType.Manager) && !bo.getIsTenantManege()) {
            TenantSupSettleInReviewRecord record = iTenantSupSettleInReviewRecordService.getByTenantId(tenantId);
            Date reviewRecordChangeDate = record.getUpdateTime() == null ? record.getCreateTime() : record.getUpdateTime();
            log.info("reviewRecordChangeDate = {}", reviewRecordChangeDate);
            Long recordId = record.getId();
            Long basicId = basic.getId();

            SupSettleInBasicBo basicInfo2 = new SupSettleInBasicBo();
            ContactBo contactInfo2 = new ContactBo();
            SupSettleInExtendVo extendedInfo2 = new SupSettleInExtendVo();
            //获取变更前数据
            TenantSupSettleInBasicLog basicLog = iTenantSupSettleInBasicLogService.getByRecordId(recordId, reviewRecordChangeDate);
            List<TenantSupSettleInBasicAttachmentLog> bAttrLogs = iTenantSupSettleInBasicAttachmentLogService.getByRecordId(recordId, reviewRecordChangeDate);
            List<TenantSupSettleInContactLog> contactLogs= iTenantSupSettleInContactLogService.getByRecordId(recordId, reviewRecordChangeDate);
            TenantSupSettleInExtendedLog extendedLog = iTenantSupSettleInExtendedLogService.getByRecordId(recordId, reviewRecordChangeDate);
            TenantSupSettleInExtendedAttachmentLog eAttrLog = iTenantSupSettleInExtendedAttachmentLogService.getByRecordId(recordId, reviewRecordChangeDate);
            //处理变更基础信息
            if (ObjUtil.isNotNull(basicLog)) {
                TenantSupSettleInBasic oldBasic = BeanUtil.copyProperties(basicLog, TenantSupSettleInBasic.class);
                TenantSupSettleInBasic inBasic = SettleInReflectUtil.compareTenantSupSettleInObj(oldBasic, basic, TenantSupSettleInBasic.class);
                basicInfo2 = this.getBasicInfo(inBasic, false);
            }
            //处理变更联系人信息
            if (CollUtil.isNotEmpty(contactLogs)) {
                ContactTypeEnum[] contactTypes = ContactTypeEnum.values();
                List<TenantSupSettleInContact> oldContacts = new ArrayList<>();
                for (ContactTypeEnum type: contactTypes) {
                    List<TenantSupSettleInContact> collect = contacts.stream().filter(c -> c.getContactType().equals(type.name())).collect(Collectors.toList());
                    List<TenantSupSettleInContactLog> logCollect = contactLogs.stream().filter(c -> c.getContactType().equals(type.name())).collect(Collectors.toList());
                    if (CollUtil.isEmpty(logCollect)) {
                        continue;
                    }
                    if (CollUtil.isNotEmpty(collect)) {
                        TenantSupSettleInContact contact = collect.get(0);
                        TenantSupSettleInContactLog contactLog = logCollect.get(0);
                        TenantSupSettleInContact oldContact = BeanUtil.copyProperties(contactLog, TenantSupSettleInContact.class);
                        TenantSupSettleInContact inContact = SettleInReflectUtil.compareTenantSupSettleInObj(oldContact, contact, TenantSupSettleInContact.class);
                        oldContacts.add(inContact);
                    }
                }
                contactInfo2 = this.getContactInfo(oldContacts);
            }
            //处理变更扩展信息
            if (ObjUtil.isNotNull(extendedLog)) {
                TenantSupSettleInExtended oldExtended = BeanUtil.copyProperties(extendedLog, TenantSupSettleInExtended.class);
                TenantSupSettleInExtended inExtended = SettleInReflectUtil.compareTenantSupSettleInObj(oldExtended, extended, TenantSupSettleInExtended.class);
                extendedInfo2 = this.getExtendedInfo(inExtended, false);
            }
            SupSettleInInfoBo changeVo = new SupSettleInInfoBo();
            changeVo.setBasicBody(basicInfo2);
            changeVo.setContactBody(contactInfo2);
            changeVo.setExtendedBody(extendedInfo2);
            //处理变更基础附件信息(一个变动视为所有变动)
            if (CollUtil.isNotEmpty(bAttrLogs)) {
                boolean isChange = false;
                List<SupSettleInAttachmentVo> reqAttachmentBodyList = basicInfo.getReqAttachmentBodyList();
                if (CollUtil.size(reqAttachmentBodyList) != CollUtil.size(bAttrLogs)) {
                    isChange = true;
                }else {
                    for (TenantSupSettleInBasicAttachmentLog attrLog: bAttrLogs) {
                        String showUrlLog = attrLog.getAttachmentShowUrl();
                        Integer fileNature = attrLog.getFileNature();
                        String fileNatureName = FileNatureEnum.fromCode(fileNature).name();
                        List<SupSettleInAttachmentVo> collect = reqAttachmentBodyList.stream().filter(f -> StrUtil.equals(f.getFileNature(), fileNatureName)).collect(Collectors.toList());
                        if (CollUtil.isEmpty(collect)) {
                            isChange = true;
                        }else {
                            SupSettleInAttachmentVo supSettleInAttachmentVo = collect.get(0);
                            String showUrl = supSettleInAttachmentVo.getAttachmentShowUrl();
                            if (!StrUtil.equals(showUrl, showUrlLog)) {
                                isChange = true;
                            }
                        }
                    }
                }
                if (isChange) {
                    List<SupSettleInAttachmentVo> attachmentList = new ArrayList<>();
                    bAttrLogs.forEach(atta -> {
                        SupSettleInAttachmentVo attachment = BeanUtil.copyProperties(atta, SupSettleInAttachmentVo.class);
                        Integer fileNature = atta.getFileNature();
                        String fileNatureName = FileNatureEnum.fromCode(fileNature).name();
                        attachment.setFileNature(fileNatureName);
                        attachmentList.add(attachment);
                    });
                    basicInfo2.setReqAttachmentBodyList(attachmentList);
                }
            }
            //处理变更扩展附件信息
            if (ObjUtil.isNotNull(eAttrLog)) {
                if (ObjUtil.isNotNull(extendedInfo.getAttachmentBo())) {
                    String showUrl = extendedInfo.getAttachmentBo().getAttachmentShowUrl();
                    String showUrlLog = eAttrLog.getAttachmentShowUrl();
                    if (!StrUtil.equals(showUrl, showUrlLog)) {
                        AttachmentBo attachmentBo = BeanUtil.copyProperties(eAttrLog, AttachmentBo.class);
                        extendedInfo2.setAttachmentBo(attachmentBo);
                    }
                }
            }
            supBasicVo.setChangeVo(changeVo);
        }
        return R.ok(supBasicVo);
    }


    @InMethodLog(value = "获取供应商基础信息")
    @Override
    public R<SupSettleInBasicBo> getBasicInfoResp() {

        String tenantId = LoginHelper.getTenantId();
        SysTenantVo sysTenantVo = iSysTenantService.queryByTenantId(tenantId);
        TenantSupSettleInBasic basic = iTenantSupSettleInBasicService.getByTenantId(tenantId);
        SupSettleInBasicBo basicInfo = new SupSettleInBasicBo();
        if (ObjectUtil.isNotEmpty(basic)) {
            basicInfo = this.getBasicInfo(basic, true);
        } else {//如果基础信息为空，则先赋值老数据
            String companyName = sysTenantVo.getCompanyName();
            basicInfo.setCompanyName(companyName);
        }
        return R.ok(basicInfo);
    }


    @InMethodLog(value = "获取供应商公司联系人信息")
    @Override
    public R<ContactBo> getContactInfoResp() {

        String tenantId = LoginHelper.getTenantId();
        TenantSupSettleInBasic basic = iTenantSupSettleInBasicService.getByTenantId(tenantId);
        List<TenantSupSettleInContact> contacts = iTenantSupSettleInContactService.getListByBasicId(basic.getId());
        ContactBo contactInfo = new ContactBo();
        if (CollUtil.size(contacts) > 0) {
            contactInfo = this.getContactInfo(contacts);
        }
        return R.ok(contactInfo);
    }


    @InMethodLog(value = "获取员工审核记录信息")
    @Override
    public TableDataInfo<ReviewRecordManagerBo> getReviewRecordManager(ReviewRecordBo bo, PageQuery pageQuery) {
        String sortType = bo.getSortType();
        String status = bo.getState();
        String queryType = bo.getQueryType();
        String queryValue = bo.getQueryValue();
        String tenantType = bo.getTenantType();
        List<String> searchDates = bo.getSearchDates();
        String searchDate = bo.getSearchDate();

        UserSupReviewRecordParamDto paramDto = new UserSupReviewRecordParamDto();

        if (StrUtil.isNotBlank(queryType) && StrUtil.isNotBlank(queryValue)) {
            if (StrUtil.equals(queryType, "AccountName")) {
                paramDto.setAccountName(StrUtil.trim(queryValue));
            }
            if (StrUtil.equals(queryType, "Email")) {
                paramDto.setEmail(StrUtil.trim(queryValue));
            }
            if (StrUtil.equals(queryType, "TenantID")) {
                paramDto.setTenantId(StrUtil.trim(queryValue));
            }
            if (StrUtil.equals(queryType, "PhoneNumber")) {
                paramDto.setPhoneNumber(StrUtil.trim(queryValue));
            }
        }
        if (StrUtil.isNotBlank(status) && !StrUtil.equals(status, "All")) {
            paramDto.setReviewState(status);
        }
        if (CollUtil.isNotEmpty(searchDates)) {
            paramDto.setStartTime(searchDates.get(0));
            paramDto.setEndTime(searchDates.get(1));
        }
        if (StrUtil.isNotBlank(searchDate)) {
            paramDto.setDateTime(searchDate);
        }
        if(StrUtil.isNotBlank(tenantType)){
            paramDto.setTenantType(tenantType);
        }

        Integer pageNo = pageQuery.getPageNum();
        Integer pageSize = pageQuery.getPageSize();

        if (pageNo == null || pageNo < 1) {
            pageNo = 1;
        }
        if (pageSize == null || pageSize < 1) {
            pageSize = 10;
        }
        //查询
        OrderItem orderItem;
        if (StrUtil.equals("RegisteredTimeDesc", sortType)) {//状态
            orderItem = OrderItem.desc("create_time");
        } else  if (StrUtil.equals("RegisteredTimeAsc", sortType)){
            orderItem = OrderItem.asc("create_time");
        }else {
            orderItem = OrderItem.desc("create_time");
        }

        Page<UserSupReviewRecordDto> queryPage = new Page<>(pageNo, pageSize);
        queryPage.addOrder(orderItem);

        IPage<UserSupReviewRecordDto> listPage = iTenantSupSettleInReviewRecordService.getListPage(queryPage, paramDto);
        List<UserSupReviewRecordDto> records = listPage.getRecords();
        List<ReviewRecordManagerBo> respList = new ArrayList();
        records.stream().forEach(r -> {
            String accountName = r.getAccountName();
            String email = r.getEmail();
            Date createTime = r.getCreateTime();
            String country = r.getCountry();
            String reviewState = r.getReviewState();
            String tenantId = r.getTenantId();
            String phoneNumber = r.getPhoneNumber();
            Date updateTime = r.getUpdateTime();
            String reviewReason = r.getReviewReason();
            String tenantTypeVo = r.getTenantType();
            SysTenantVo sysTenantVo = iSysTenantService.queryByTenantId(tenantId);
            String productSourceType = sysTenantVo.getProductSourceType();
            ReviewRecordManagerBo respBody = new ReviewRecordManagerBo();
            respBody.setProductSourceType(productSourceType);
            respBody.setTenantId(tenantId);
            respBody.setAccountName(accountName);
            respBody.setState(reviewState);
            respBody.setCountry(country);
            respBody.setPhoneNumber(phoneNumber);
            respBody.setEmail(email);
            respBody.setRegisteredTime(DateUtil.format(createTime, "yyyy-MM-dd HH:mm:ss"));
            respBody.setLastOperationTime(DateUtil.format(updateTime, "yyyy-MM-dd HH:mm:ss"));
            respBody.setReviewReason(reviewReason);
            respBody.setTenantType(tenantTypeVo);
            respList.add(respBody);
        });
        return TableDataInfo.build(respList, listPage.getTotal());
    }


    @InMethodLog(value = "员工审核供应商入驻信息")
    @Override
    @Transactional(rollbackFor = Exception.class)
    public R<Void> updateReviewRecordMd(ReviewRecordBo bo) {
        String state = bo.getState();
        String tenantId = bo.getTenantId();
        String reviewReason = bo.getReviewReason();
        if (StrUtil.isBlank(state)) {
            return R.fail(ZSMallStatusCodeEnum.REQUIRED_CANNOT_EMPTY);
        }
        SettleInReviewRecordEnum reviewStatusEnum = SettleInReviewRecordEnum.valueOf(state);
        TenantSupSettleInReviewRecord reviewRecord = iTenantSupSettleInReviewRecordService.getByTenantId(tenantId);
        reviewRecord.setReviewState(reviewStatusEnum);
        reviewRecord.setReviewTime(new Date());
        reviewRecord.setReviewTenantId(LoginHelper.getTenantId());
        if (StrUtil.isNotBlank(reviewReason)) {
            reviewRecord.setReviewReason(reviewReason);
        }
        boolean reviewRecordUpdate = TenantHelper.ignore(() ->iTenantSupSettleInReviewRecordService.updateById(reviewRecord));
        if (!reviewRecordUpdate) {
            log.error("修改供应商入驻审核记录信息失败！");
            throw new RStatusCodeException(ZSMallStatusCodeEnum.USER_SUP_REVIEW_RECORD_UPDATE_ERROR);
        }
        if (reviewRecord.getReviewState().equals(SettleInReviewRecordEnum.Rejected)) {
            RedisUtils.deleteObject(RedisConstants.EXTRA_PERFECTION_FLAG + reviewRecord.getTenantId());
        }
        SysTenantVo sysTenantVo = iSysTenantService.queryByTenantId(tenantId);
        //入驻信息审核通过则修改供应商租户信息
        if (StrUtil.equals(state, SettleInReviewRecordEnum.Approved.name())) {
            // 缓存处理
            Object cacheObject = RedisUtils.getCacheObject(RedisConstants.EXTRA_PERFECTION_FLAG + LoginHelper.getTenantId() + SettleInReviewRecordEnum.Rejected.name());
            if(null != cacheObject){
                RedisUtils.deleteObject(RedisConstants.EXTRA_PERFECTION_FLAG + LoginHelper.getTenantId() + SettleInReviewRecordEnum.Rejected.name());
            }
            // 已经完善的信息不用再次修改完善标识
            if(null != sysTenantVo && null != sysTenantVo.getExtraPerfectionFlag() && sysTenantVo.getExtraPerfectionFlag().equals(ExtraPerfectionFlag.False.getCode())) {
                if(null != sysTenantVo.getTenantType()){
                    // 供应商
                    if(StrUtil.equals(sysTenantVo.getTenantType(), TenantType.Supplier.name())){
                        TenantSupSettleInBasic basic = iTenantSupSettleInBasicService.getByTenantId(reviewRecord.getTenantId());
                        sysTenantVo.setCompanyName(basic.getCompanyName());
                        sysTenantVo.setLicenseNumber(basic.getSocialCreditCode());
                        List<TenantSupSettleInContact> contacts = iTenantSupSettleInContactService.getListByBasicId(basic.getId());
                        TenantSupSettleInContact contact = contacts.get(0);
                        sysTenantVo.setContactUserName(contact.getName());
                        sysTenantVo.setContactPhone(contact.getPhoneNumber());
                        sysTenantVo.setContactEmail(contact.getEmail());
                        // 添加基础信息完善标识
                        sysTenantVo.setExtraPerfectionFlag(ExtraPerfectionFlag.True.getCode());
                        iSysTenantService.updateByVo(sysTenantVo);
                    }
                    // 分销商
                    if(StrUtil.equals(sysTenantVo.getTenantType(), TenantType.Distributor.name())){
                        // 添加基础信息完善标识
                        sysTenantVo.setExtraPerfectionFlag(ExtraPerfectionFlag.True.getCode());
                        iSysTenantService.updateByVo(sysTenantVo);
                    }
                }
            }
        }
        ThreadUtil.execute(() -> {
            // 入驻审核结果 发送email或信息通知
            List<SysUserVo> sysUserVos = sysUserService.selectTenantUserByRoleAdmin(reviewRecord.getTenantId());
            SysUserVo sysUserVo = sysUserVos.get(0);
            String phoneNumber = sysUserVo.getPhonenumber();
            String areaCode = sysUserVo.getAreaCode();
            String email = sysUserVo.getEmail();
            if (StrUtil.isNotBlank(phoneNumber)) {
                String key = StrUtil.equals(state, SettleInReviewRecordEnum.Rejected.name())
                    ? VerifyCodeConstant.Config.KEY_SMS_TEMPLATE_SETTLEIN_REJECTED
                    : VerifyCodeConstant.Config.KEY_SMS_TEMPLATE_SETTLEIN_ACCEPTED;
                VerifyCodeUtils.settleInSendPhoneNumber(phoneNumber, areaCode, key);
            } else {
                String key = StrUtil.equals(state, SettleInReviewRecordEnum.Rejected.name())
                    ? VerifyCodeConstant.Config.KEY_EMAIL_TEMPLATE_SETTLEIN_REJECTED
                    : VerifyCodeConstant.Config.KEY_EMAIL_TEMPLATE_SETTLEIN_ACCEPTED;
                VerifyCodeUtils.settleInSendEmail(email, sysUserVo.getLanguage(), key);
            }
        });
        return R.ok();
    }

    @InMethodLog(value = "批量员工审核供应商入驻信息")
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchUpdateReviewRecordMd(List<ReviewRecordBo> reviewRecordBoList) {
        for (ReviewRecordBo reviewRecordBo : reviewRecordBoList){
            String state = reviewRecordBo.getState();
            String tenantId = reviewRecordBo.getTenantId();
            String reviewReason = reviewRecordBo.getReviewReason();
            if (StrUtil.isBlank(state)) {
                throw new RStatusCodeException(ZSMallStatusCodeEnum.REQUIRED_CANNOT_EMPTY);
            }
            SettleInReviewRecordEnum reviewStatusEnum = SettleInReviewRecordEnum.valueOf(state);
            TenantSupSettleInReviewRecord reviewRecord = iTenantSupSettleInReviewRecordService.getByTenantId(tenantId);
            if (reviewRecord.getReviewState().equals(SettleInReviewRecordEnum.Rejected)) {
                continue;
            }
            reviewRecord.setReviewState(reviewStatusEnum);
            reviewRecord.setReviewTime(new Date());
            reviewRecord.setReviewTenantId(LoginHelper.getTenantId());
            if (StrUtil.isNotBlank(reviewReason)) {
                reviewRecord.setReviewReason(reviewReason);
            }
            boolean reviewRecordUpdate = TenantHelper.ignore(() ->iTenantSupSettleInReviewRecordService.updateById(reviewRecord));
            if (!reviewRecordUpdate) {
                log.error("修改供应商入驻审核记录信息失败！");
                throw new RStatusCodeException(ZSMallStatusCodeEnum.USER_SUP_REVIEW_RECORD_UPDATE_ERROR);
            }
            SysTenantVo sysTenantVo = iSysTenantService.queryByTenantId(tenantId);
            //入驻信息审核通过则修改供应商租户信息
            if (StrUtil.equals(state, SettleInReviewRecordEnum.Approved.name())) {
                // 缓存处理
                Object cacheObject = RedisUtils.getCacheObject(RedisConstants.EXTRA_PERFECTION_FLAG + LoginHelper.getTenantId() + SettleInReviewRecordEnum.Rejected.name());
                if(null != cacheObject){
                    RedisUtils.deleteObject(RedisConstants.EXTRA_PERFECTION_FLAG + LoginHelper.getTenantId() + SettleInReviewRecordEnum.Rejected.name());
                }
                // 已经完善的信息不用再次修改完善标识
                if(null != sysTenantVo && null != sysTenantVo.getExtraPerfectionFlag() && sysTenantVo.getExtraPerfectionFlag().equals(ExtraPerfectionFlag.False.getCode())){
                    if(null != sysTenantVo.getTenantType()){
                        // 供应商
                        if(StrUtil.equals(sysTenantVo.getTenantType(), TenantType.Supplier.name())){
                            TenantSupSettleInBasic basic = iTenantSupSettleInBasicService.getByTenantId(reviewRecord.getTenantId());
                            sysTenantVo.setCompanyName(basic.getCompanyName());
                            sysTenantVo.setLicenseNumber(basic.getSocialCreditCode());
                            List<TenantSupSettleInContact> contacts = iTenantSupSettleInContactService.getListByBasicId(basic.getId());
                            TenantSupSettleInContact contact = contacts.get(0);
                            sysTenantVo.setContactUserName(contact.getName());
                            sysTenantVo.setContactPhone(contact.getPhoneNumber());
                            sysTenantVo.setContactEmail(contact.getEmail());
                            // 添加基础信息完善标识
                            sysTenantVo.setExtraPerfectionFlag(ExtraPerfectionFlag.True.getCode());
                            iSysTenantService.updateByVo(sysTenantVo);
                        }
                        // 分销商
                        if(StrUtil.equals(sysTenantVo.getTenantType(), TenantType.Distributor.name())){
                            // 添加基础信息完善标识
                            sysTenantVo.setExtraPerfectionFlag(ExtraPerfectionFlag.True.getCode());
                            iSysTenantService.updateByVo(sysTenantVo);
                        }
                    }
                }
            }
            ThreadUtil.execute(() -> {
                // 入驻审核结果 发送email或信息通知
                List<SysUserVo> sysUserVos = sysUserService.selectTenantUserByRoleAdmin(reviewRecord.getTenantId());
                SysUserVo sysUserVo = sysUserVos.get(0);
                String phoneNumber = sysUserVo.getPhonenumber();
                String areaCode = sysUserVo.getAreaCode();
                String email = sysUserVo.getEmail();
                if (StrUtil.isNotBlank(phoneNumber)) {
                    String key = StrUtil.equals(state, SettleInReviewRecordEnum.Rejected.name())
                        ? VerifyCodeConstant.Config.KEY_SMS_TEMPLATE_SETTLEIN_REJECTED
                        : VerifyCodeConstant.Config.KEY_SMS_TEMPLATE_SETTLEIN_ACCEPTED;
                    VerifyCodeUtils.settleInSendPhoneNumber(phoneNumber, areaCode, key);
                } else {
                    String key = StrUtil.equals(state, SettleInReviewRecordEnum.Rejected.name())
                        ? VerifyCodeConstant.Config.KEY_EMAIL_TEMPLATE_SETTLEIN_REJECTED
                        : VerifyCodeConstant.Config.KEY_EMAIL_TEMPLATE_SETTLEIN_ACCEPTED;
                    VerifyCodeUtils.settleInSendEmail(email, sysUserVo.getLanguage(), key);
                }
            });
        }
    }


    @InMethodLog(value = "获取当前用户的入驻信息是否完善状态")
    @Override
    public R<SupSettleInPerfectStateBoVo> getIsPerfectInfoStatus() {
        String tenantId = LoginHelper.getTenantId();
        TenantSupSettleInReviewRecord reviewRecordEntity = iTenantSupSettleInReviewRecordService.getByTenantId(tenantId);
        TenantSupSettleInBasic basicEntity = iTenantSupSettleInBasicService.getByTenantId(tenantId);
        TenantSupSettleInContact businessEntity = iTenantSupSettleInContactService.getListByBasicIdAndContactType(basicEntity.getId(), ContactTypeEnum.BusinessContact.name());
        TenantSupSettleInExtended extendedEntity = iTenantSupSettleInExtendedService.getByBasicId(basicEntity.getId());

        SupSettleInPerfectStateBoVo body = new SupSettleInPerfectStateBoVo();
        if (ObjectUtil.isNotNull(reviewRecordEntity)) {
            String stateName = reviewRecordEntity.getReviewState().name();
            if (StrUtil.equals(stateName, SettleInReviewRecordEnum.Reviewing.name())
                || StrUtil.equals(stateName, SettleInReviewRecordEnum.ReviewedAgain.name())) {
                body.setReviewRecordStatus(SettleInReviewRecordEnum.Reviewing.name());
            } else if (StrUtil.equals(stateName, SettleInReviewRecordEnum.Approved.name())) {
                body.setReviewRecordStatus("ViewDetail");
            } else {
                body.setReviewRecordStatus(stateName);
            }
        } else {
            body.setReviewRecordStatus("WaitPerfect");
        }
        if (ObjectUtil.isNotNull(basicEntity)) {
            body.setBasicStatus(true);
        }
        if (ObjectUtil.isNotNull(businessEntity)) {
            body.setContactStatus(true);
        }
        if (ObjectUtil.isNotNull(extendedEntity)) {
            body.setExtendedStatus(true);
        }

        return R.ok(body);
    }

    @InMethodLog(value = "设置是否完善信息标识（用于判断是否弹出完善供应商入驻信息页面）")
    @Override
    public void setIsPerfectInfoSign() {
        Date currentDate = new Date();
        log.info("当前时间 currentDate = {}", DateUtil.format(currentDate, "yyyy-MM-dd HH:mm:ss"));

        Date endDate = DateUtil.endOfDay(currentDate);
//        Date endDate = DateUtil.offsetMinute(currentDate, 10);// 10中后清除缓存（测试用）
        log.info("当天结束时间 endDate = {}", DateUtil.format(endDate, "yyyy-MM-dd HH:mm:ss"));
        long between = DateUtil.between(currentDate, endDate, DateUnit.SECOND);
        log.info("相差秒数（秒） expirationTime= {}", between);
        RedisUtils.setCacheObject(RedisConstants.EXTRA_PERFECTION_FLAG + LoginHelper.getTenantId(), "False", Duration.ofSeconds(between));
    }

    @Override
    public void setIsPerfectInfoSignNew(String reviewState) {
        // 审核中状态删除缓存
        if(StrUtil.equals(reviewState, SettleInReviewRecordEnum.Reviewing.name())){
            RedisUtils.deleteObject(RedisConstants.EXTRA_PERFECTION_FLAG + LoginHelper.getTenantId() + reviewState);
        }
        // 驳回状态添加缓存
        if(StrUtil.equals(reviewState, SettleInReviewRecordEnum.Rejected.name())){
            RedisUtils.setCacheObject(RedisConstants.EXTRA_PERFECTION_FLAG + LoginHelper.getTenantId() + reviewState, "False", Duration.ofSeconds(5184000));
        }
    }


    @InMethodLog(value = "根据用户基础信息实体获取供应商入驻响应信息")
    private SupSettleInBasicBo getBasicInfo(TenantSupSettleInBasic basic, boolean isNewData) {
        log.info("UserSupSettleInBasicEntity = {}", JSONUtil.toJsonStr(basic));
        String companyName = basic.getCompanyName();
        String socialCreditCode = basic.getSocialCreditCode();
        Long countryId = basic.getCountryId();
        Long stateId = basic.getStateId();
        String cityText = basic.getCityText();
        String stateText = basic.getStateText();
        String registeredAddress = basic.getRegisteredAddress();
        String legalPersonName = basic.getLegalPersonName();
        String legalPersonPlace = basic.getLegalPersonPlace();
        Integer documentType = basic.getDocumentType();
        String documentNumber = basic.getDocumentNumber();
//        String companyTaxRegistrationNumber = basic.getCompanyTaxRegistrationNumber();
        String documentName = documentType == null ? null : DocumentTypeEnum.fromCode(documentType).name();
        SupSettleInBasicBo basicBody = new SupSettleInBasicBo();
        basicBody.setCompanyName(companyName);
        basicBody.setSocialCreditCode(socialCreditCode);
        basicBody.setRegisteredAddress(registeredAddress);
        basicBody.setLegalPersonName(legalPersonName);
        basicBody.setLegalPersonPlace(legalPersonPlace);
        basicBody.setDocumentType(documentName);
        basicBody.setDocumentNumber(documentNumber);
        basicBody.setCountryId(countryId);
        basicBody.setStateId(stateId);
        basicBody.setCityText(cityText);
        basicBody.setStateText(stateText);
//        basicBody.setCompanyTaxRegistrationNumber(companyTaxRegistrationNumber);
        if (ObjUtil.isNotNull(countryId)) {
            WorldLocationVo worldLocationVo = iWorldLocationService.queryById(countryId);
            JSONObject locationOtherName = worldLocationVo.getLocationOtherName();
            basicBody.setCountryName(locationOtherName);
        }
        if (ObjUtil.isNotNull(stateId)) {
            WorldLocationVo worldLocationVo = iWorldLocationService.queryById(stateId);
            JSONObject locationOtherName = worldLocationVo.getLocationOtherName();
            basicBody.setStateName(locationOtherName);
        }
        if (basic.getId() != null && isNewData) {
            List<TenantSupSettleInBasicAttachment> attachments = iTenantSupSettleInBasicAttachmentService.getListByBasicId(basic.getId());
            if (CollUtil.isNotEmpty(attachments)) {
                List<SupSettleInAttachmentVo> attachmentList = new ArrayList<>();
                attachments.forEach(atta -> {
                    SupSettleInAttachmentVo attachment = BeanUtil.copyProperties(atta, SupSettleInAttachmentVo.class);
                    Integer fileNature = atta.getFileNature();
                    String fileNatureName = FileNatureEnum.fromCode(fileNature).name();
                    attachment.setFileNature(fileNatureName);
                    attachmentList.add(attachment);
                });
                basicBody.setReqAttachmentBodyList(attachmentList);
            }
        }
        return basicBody;
    }


    @InMethodLog(value = "根据用户入驻公司联系人实体集合获取供应商公司联系人响应信息")
    private ContactBo getContactInfo(List<TenantSupSettleInContact> contacts) {

        ContactBo reqContactBody = new ContactBo();
        contacts.forEach(contact -> {

            SupSettleInContactVo contactInfoBody = getContactInfoDetail(contact);
            String contactType = contactInfoBody.getContactType();
            log.info("contactType = {}", contactType);
            if (StrUtil.equals(ContactTypeEnum.BusinessContact.name(), contactInfoBody.getContactType())) {
                reqContactBody.setBusinessContactBody(contactInfoBody);
            } else if (StrUtil.equals(ContactTypeEnum.FinancialContact.name(), contactInfoBody.getContactType())) {
                reqContactBody.setFinancialContactBody(contactInfoBody);
            } else {
                reqContactBody.setAfterSalesContactBody(contactInfoBody);
            }
        });

        return reqContactBody;
    }


    @InMethodLog(value = "根据用户入驻扩展实体获取供应商扩展响应信息")
    private SupSettleInExtendVo getExtendedInfo(TenantSupSettleInExtended extended, boolean isNewData) {

        Integer companyNature = extended.getCompanyNature();

        JSONArray mainCategories = extended.getMainCategories();
        Boolean isOnlineRetailersExperience = extended.getIsOnlineRetailersExperience();
        Long mainProductionCountryId = extended.getMainProductionCountryId();
        Boolean isOverseasWarehouse = extended.getIsOverseasWarehouse();
        Long countryId = extended.getCountryId();
        Long stateId = extended.getStateId();
        String cityText = extended.getCityText();
        String stateText = extended.getStateText();
        String warehouseAddress = extended.getWarehouseAddress();
        JSONArray otherChannels = extended.getOtherChannels();
        String otherChannelText = extended.getOtherChannelText();
        String deliverTimeLimit = extended.getDeliverTimeLimit();
        BigDecimal salesPriceRecentYear = extended.getSalesPriceRecentYear();
        String companyNatureName = companyNature == null ? null : CompanyNatureEnum.fromCode(companyNature).name();
        SupSettleInExtendVo extendedBody = new SupSettleInExtendVo();
        extendedBody.setCompanyNature(companyNatureName);
        extendedBody.setMainCategories(mainCategories);
        if (CollUtil.isNotEmpty(mainCategories)) {
            List<ProductCategoryVo> categories = iProductCategoryService.queryListByIds(mainCategories.toList(Long.class));
            List<String> categoryNames = categories.stream().map(ProductCategoryVo::getCategoryName).collect(Collectors.toList());
            extendedBody.setMainCategorieNames(CollUtil.isEmpty(categoryNames) ? null : StrUtil.join(",", categoryNames));
        }
        extendedBody.setIsOnlineRetailersExperience(isOnlineRetailersExperience);
        if (isOnlineRetailersExperience != null) {
            String text = null;
            if (isOnlineRetailersExperience) {
                text = StrUtil.equals(ServletUtils.getHeaderLanguage(), LanguageType.zh_CN.name()) ? "是" : "YES";
            } else {
                text = StrUtil.equals(ServletUtils.getHeaderLanguage(), LanguageType.zh_CN.name()) ? "否" : "NO";
            }
            extendedBody.setIsOnlineRetailersExperienceText(text);
        }
        extendedBody.setMainProductionCountryId(mainProductionCountryId);
        extendedBody.setIsOverseasWarehouse(isOverseasWarehouse);
        if (isOverseasWarehouse != null) {
            String text = null;
            if (isOverseasWarehouse) {
                text = StrUtil.equals(ServletUtils.getHeaderLanguage(), LanguageType.zh_CN.name()) ? "是" : "YES";
            } else {
                text = StrUtil.equals(ServletUtils.getHeaderLanguage(), LanguageType.zh_CN.name()) ? "否" : "NO";
            }
            extendedBody.setIsOverseasWarehouseText(text);
        }
        if (StrUtil.isNotBlank(warehouseAddress) && !StrUtil.equals("null", warehouseAddress)) {
            extendedBody.setWarehouseAddress(warehouseAddress);
        }
        extendedBody.setCountryId(countryId);
        extendedBody.setStateId(stateId);
        extendedBody.setCityText(cityText);
        extendedBody.setStateText(stateText);
        extendedBody.setOtherChannelText(otherChannelText);
        extendedBody.setOtherChannels(otherChannels);
        extendedBody.setDeliverTimeLimit(deliverTimeLimit);
        extendedBody.setSalesPriceRecentYear(salesPriceRecentYear);

        if (ObjUtil.isNotNull(countryId)) {
            WorldLocationVo worldLocationVo = iWorldLocationService.queryById(countryId);
            JSONObject locationOtherName = worldLocationVo.getLocationOtherName();
            extendedBody.setCountryName(locationOtherName);
        }
        if (ObjUtil.isNotNull(stateId)) {
            WorldLocationVo worldLocationVo = iWorldLocationService.queryById(stateId);
            JSONObject locationOtherName = worldLocationVo.getLocationOtherName();
            extendedBody.setStateName(locationOtherName);
        }
        if (ObjUtil.isNotNull(mainProductionCountryId)) {
            WorldLocationVo worldLocationVo = iWorldLocationService.queryById(mainProductionCountryId);
            JSONObject locationOtherName = worldLocationVo.getLocationOtherName();
            extendedBody.setMainProductionCountryName(locationOtherName);
        }

        if (extended.getId() != null && isNewData) {
            //扩展信息附件
            TenantSupSettleInExtendedAttachment attachment = iTenantSupSettleInExtendedAttachmentService.getByExtendedId(extended.getId());
            if (attachment != null) {
                AttachmentBo attachmentBo = BeanUtil.copyProperties(attachment, AttachmentBo.class);
                extendedBody.setAttachmentBo(attachmentBo);
                log.info("attachmentBo = {}", JSONUtil.toJsonStr(extendedBody.getAttachmentBo()));
            }
        }
        return extendedBody;
    }


    @InMethodLog(value = "入驻信息联系人转化成body")
    private SupSettleInContactVo getContactInfoDetail(TenantSupSettleInContact contact) {

        String firstName = contact.getFirstName();
        String lastName = contact.getLastName();
        String name = contact.getName();
        String areaCode = contact.getAreaCode();
        String phoneNumber = contact.getPhoneNumber();
        String email = contact.getEmail();
        String msgAppType = contact.getMsgAppType() == null ? null : contact.getMsgAppType();
        String contactType = contact.getContactType();
        String msgAppAccount = contact.getMsgAppAccount();
        Long countryId = contact.getCountryId();
        Long stateId = contact.getStateId();
        String stateText = contact.getStateText();
        String cityText = contact.getCityText();
        String address = contact.getAddress();

        SupSettleInContactVo contactBody = new SupSettleInContactVo();
        contactBody.setContactType(contactType);
        contactBody.setFirstName(firstName);
        contactBody.setLastName(lastName);
        contactBody.setName(name);
        contactBody.setAreaCode(areaCode);
        contactBody.setMsgAppType(msgAppType);
        contactBody.setPhoneNumber(phoneNumber);
        contactBody.setEmail(email);
        contactBody.setMsgAppAccount(msgAppAccount);
        contactBody.setAddress(address);
        contactBody.setCountryId(countryId);
        contactBody.setStateText(stateText);
        contactBody.setStateId(stateId);
        contactBody.setCityText(cityText);
        if (ObjUtil.isNotNull(countryId)) {
            WorldLocationVo worldLocationVo = iWorldLocationService.queryById(countryId);
            JSONObject locationOtherName = worldLocationVo.getLocationOtherName();
            contactBody.setCountryName(locationOtherName);
        }
        if (ObjUtil.isNotNull(stateId)) {
            WorldLocationVo worldLocationVo = iWorldLocationService.queryById(stateId);
            JSONObject locationOtherName = worldLocationVo.getLocationOtherName();
            contactBody.setStateName(locationOtherName);
        }
        return contactBody;
    }


    @InMethodLog(value = "供应商入驻信息保存参数校验")
    private Boolean checkSettleInInfo(SupSettleInInfoBo bo) {

        SupSettleInBasicBo basicBody = bo.getBasicBody();
        List<SupSettleInAttachmentVo> attachmentBodyList = basicBody.getReqAttachmentBodyList();
        SupSettleInExtendVo extendedBody = bo.getExtendedBody();
        AttachmentBo attachmentBo = extendedBody.getAttachmentBo();
        ContactBo contactBody = bo.getContactBody();
        if (contactBody == null || contactBody.getBusinessContactBody() == null) {
            log.error("入驻公司联系人信息必填。");
            return false;
        }
        String companyName = basicBody.getCompanyName();
        String socialCreditCode = basicBody.getSocialCreditCode();
        Long stateId = basicBody.getStateId();
        String stateText = basicBody.getStateText();
        String cityText = basicBody.getCityText();
        String registeredAddress = basicBody.getRegisteredAddress();
        String legalPersonPlace = basicBody.getLegalPersonPlace();
        String documentType = basicBody.getDocumentType();
        String documentNumber = basicBody.getDocumentNumber();
        String legalPersonName = basicBody.getLegalPersonName();
        //基础信息必填校验
    /*if (!ObjectUtil.isAllNotEmpty(companyName, socialCreditCode, socialCreditCode,
      registeredAddress, legalPersonPlace, legalPersonPlace, documentType, documentNumber, legalPersonName)) {
      log.error("入驻基础信息必填。");
      return false;
    }*/
        if (!ObjectUtil.isAllNotEmpty(companyName, registeredAddress)) {
            log.error("入驻基础信息必填。");
            return false;
        }
        if (ObjectUtil.isAllEmpty(stateId, stateText) || ObjectUtil.isAllEmpty(cityText)) {
            log.error("入驻基础州和城市信息必填。");
            return false;
        }
        if (CollUtil.isEmpty(attachmentBodyList)) {
            log.error("入驻基础附件信息必填。");
            return false;
        }
        //入驻基础附件信息校验
        boolean BLCheck = false;
//    boolean LRCCheck = false;
        boolean ICCheck = false;
        for (SupSettleInAttachmentVo ba : attachmentBodyList) {
            String fileNature = ba.getFileNature();
            if (StrUtil.equals(fileNature, FileNatureEnum.BRC.name())) {
                continue;
            }
            if (StrUtil.equals(fileNature, FileNatureEnum.BL.name())) {
                BLCheck = true;
            }
      /*if (StrUtil.equals(fileNature, FileNatureEnum.LRC.name())) {
        LRCCheck = true;
      }*/
            if (StrUtil.equals(fileNature, FileNatureEnum.IC.name())) {
                ICCheck = true;
            }
        }
        if (!ICCheck) {
            if (!BLCheck) {
                log.error("入驻基础附件信息必填。");
                return false;
            }
        }
        //扩展信息校验
        String companyNature = extendedBody.getCompanyNature();
        JSONArray mainCategories = extendedBody.getMainCategories();
        Boolean isOnlineRetailersExperience = extendedBody.getIsOnlineRetailersExperience();
        Long mainProductionCountryCode = extendedBody.getMainProductionCountryId();
        Boolean isOverseasWarehouse = extendedBody.getIsOverseasWarehouse();
        Long stateId1 = extendedBody.getStateId();
        String stateText1 = extendedBody.getStateText();
        String cityText1 = extendedBody.getCityText();
        String warehouseAddress = extendedBody.getWarehouseAddress();
        JSONArray otherChannels = extendedBody.getOtherChannels();
        String otherChannelText = extendedBody.getOtherChannelText();
        String deliverTimeLimit = extendedBody.getDeliverTimeLimit();
        BigDecimal salesPriceRecentYear = extendedBody.getSalesPriceRecentYear();
        String showUrl = attachmentBo.getAttachmentShowUrl();
        if (!ObjectUtil.isAllNotEmpty(companyNature, mainCategories, isOnlineRetailersExperience, mainProductionCountryCode, deliverTimeLimit, salesPriceRecentYear, showUrl)) {
            log.error("入驻扩展信息必填信息必填。");
            return false;
        }
        if (isOverseasWarehouse != null && isOverseasWarehouse) {
            if (ObjectUtil.isAllEmpty(stateId1, stateText1) || ObjectUtil.isAllEmpty(cityText1) || warehouseAddress == null) {
                log.error("入驻扩展州和城市信息必填。");
                return false;
            }
        }
        if (CollUtil.isEmpty(otherChannels) && StrUtil.isBlank(otherChannelText)) {
            log.error("入驻扩展渠道信息必填。");
            return false;
        }
        //入驻联系人必填校验
        SupSettleInContactVo businessContactBody = contactBody.getBusinessContactBody();
        String firstName = businessContactBody.getFirstName();
        String lastName = businessContactBody.getLastName();
        String areaCode = businessContactBody.getAreaCode();
        String phoneNumber = businessContactBody.getPhoneNumber();
        String msgAppType = businessContactBody.getMsgAppType();
        String msgAppAccount = businessContactBody.getMsgAppAccount();
        String email = businessContactBody.getEmail();
        Long stateId2 = businessContactBody.getStateId();
        String stateText2 = businessContactBody.getStateText();
        String cityText2 = businessContactBody.getCityText();
        String address = businessContactBody.getAddress();
        if (!ObjectUtil.isAllNotEmpty(firstName, lastName, areaCode, phoneNumber, msgAppType, msgAppAccount, email, address)) {
            log.error("入驻公司业务联系人信息必填。");
            return false;
        }
        if (ObjectUtil.isAllEmpty(stateId2, stateText2) || ObjectUtil.isAllEmpty(cityText2)) {
            log.error("入驻公司业务联系人州和城市信息必填。");
            return false;
        }
        log.info("入驻信息参数必填项校验通过 end。。。。。。");
        return true;
    }


}
