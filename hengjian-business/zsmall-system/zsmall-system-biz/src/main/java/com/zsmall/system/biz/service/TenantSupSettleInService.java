package com.zsmall.system.biz.service;

import com.hengjian.common.core.domain.R;
import com.hengjian.common.mybatis.core.page.PageQuery;
import com.hengjian.common.mybatis.core.page.TableDataInfo;
import com.zsmall.system.entity.domain.bo.settleInBasic.ReviewRecordBo;
import com.zsmall.system.entity.domain.bo.settleInBasic.SupSettleInGetBo;
import com.zsmall.system.entity.domain.bo.settleInBasic.SupSettleInInfoBo;
import com.zsmall.system.entity.domain.vo.settleInBasic.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/12/15 9:40
 */
public interface TenantSupSettleInService {


    /**
     * 供应商入驻信息保存
     */
    R<Void> saveSupSettleIn(SupSettleInInfoBo bo) throws Exception;

    /**
     * 修改供应商入驻基础信息
     */
    R<Void> updateSupSettleInBasic(SupSettleInBasicBo apiRequest) throws Exception;

    /**
     * 修改供应商入驻联系人信息
     */
    R<Void> updateSupSettleInContact(ContactBo apiRequest) throws Exception;

    /**
     * 获取供应商入驻信息
     */
    R<SupBasicVo> getSupSettleInResp(SupSettleInGetBo apiRequest) throws IllegalAccessException;

    /**
     * 获取供应商基础信息
     */
    R<SupSettleInBasicBo> getBasicInfoResp();

    /**
     * 获取供应商公司联系人信息
     */
    R<ContactBo> getContactInfoResp();

    /**
     * 获取员工审核记录信息
     */
    TableDataInfo<ReviewRecordManagerBo> getReviewRecordManager(ReviewRecordBo bo, PageQuery pageQuery);

    /**
     * 员工审核供应商入驻信息
     */
    R<Void> updateReviewRecordMd(ReviewRecordBo bo);

    /**
     * 批量员工审核供应商入驻信息
     *
     * @param reviewRecordBoList
     */
    void batchUpdateReviewRecordMd(List<ReviewRecordBo> reviewRecordBoList);


    /**
     * 获取当前用户的入驻信息是否完善状态
     */
    R<SupSettleInPerfectStateBoVo> getIsPerfectInfoStatus();

    /**
     * 设置是否完善信息标识（用于判断是否弹出完善供应商入驻信息页面）
     */
    void setIsPerfectInfoSign();

    void setIsPerfectInfoSignNew(String reviewState);

}
