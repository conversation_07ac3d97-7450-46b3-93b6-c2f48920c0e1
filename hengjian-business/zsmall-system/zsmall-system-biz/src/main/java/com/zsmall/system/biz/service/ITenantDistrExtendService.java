package com.zsmall.system.biz.service;

import com.hengjian.common.mybatis.core.page.PageQuery;
import com.hengjian.common.mybatis.core.page.TableDataInfo;
import com.zsmall.system.entity.domain.bo.settleInBasic.TenantDistrExtendBo;
import com.zsmall.system.entity.domain.vo.settleInBasic.TenantDistrExtendVo;

import java.util.Collection;
import java.util.List;

/**
 * 租户分销商-拓展信息Service接口
 *
 * <AUTHOR> Li
 * @date 2023-05-29
 */
public interface ITenantDistrExtendService {

    /**
     * 查询租户分销商-拓展信息
     */
    TenantDistrExtendVo queryById(Long id);

    /**
     * 查询租户分销商-拓展信息列表
     */
    TableDataInfo<TenantDistrExtendVo> queryPageList(TenantDistrExtendBo bo, PageQuery pageQuery);

    /**
     * 查询租户分销商-拓展信息列表
     */
    List<TenantDistrExtendVo> queryList(TenantDistrExtendBo bo);

    /**
     * 保存分销商注册信息，新增和修改使用同一个接口
     */
    Boolean insertByBo(TenantDistrExtendBo bo) throws Exception;

    /**
     * 修改租户分销商-拓展信息
     */
    Boolean updateByBo(TenantDistrExtendBo bo) throws Exception;

    /**
     * 校验并批量删除租户分销商-拓展信息信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    Boolean hintPerfectExtendedInformation();

    /**
     * 获取分销商入驻详情
     */
    TenantDistrExtendVo getInfo(String tenantId);

}
