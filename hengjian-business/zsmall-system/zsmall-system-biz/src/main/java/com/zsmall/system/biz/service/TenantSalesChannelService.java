package com.zsmall.system.biz.service;

import com.hengjian.common.core.exception.RStatusCodeException;
import com.zsmall.system.entity.domain.bo.salesChannel.ChannelSanboxBo;
import com.zsmall.system.entity.domain.bo.salesChannel.ChannelTypeBo;
import com.zsmall.system.entity.domain.bo.salesChannel.TenantChannelBindingBo;
import com.zsmall.system.entity.domain.bo.salesChannel.TenantSalesChannelBo;
import com.zsmall.system.entity.domain.vo.salesChannel.ChannelGroupListVo;

/**
 * 租户销售渠道接口
 *
 */
public interface TenantSalesChannelService {

    /**
     * 获取跳转渠道地址
     * @param bo
     * @return
     */
    String jumpToChannel(TenantSalesChannelBo bo);

    /**
     * 解绑渠道
     *
     * @param id
     * @return
     */
    Boolean unbind(Long id) throws RStatusCodeException;

    /**
     * 给当前用户绑定渠道
     * @param channelType
     * @param base64Access
     */
    void bindChannel(String channelType, String base64Access) throws RStatusCodeException;

    /**
     * 给当前用户绑定渠道
     * @param channelBindingBo
     */
    Boolean bindChannel(TenantChannelBindingBo channelBindingBo) throws RStatusCodeException;

    /**
     * Wayfair沙盒调试验证
     * @param channelSanboxBo
     * @return
     */
    Boolean wayfairSandbox(ChannelSanboxBo channelSanboxBo) throws RStatusCodeException;

    /**
     * 保存前进行业务判断
     * @param tenantSalesChannelBo
     */
    void validEntityBeforeSave(TenantSalesChannelBo tenantSalesChannelBo) throws RStatusCodeException;

    /**
     * 获取已关联的渠道分组
     * @param bo
     * @return
     */
    ChannelGroupListVo getEnableChannelGroup(ChannelTypeBo bo);

    /**
     * 根据字典获取关联的渠道分组
     *
     * @param bo
     * @return
     */
    ChannelGroupListVo getEnableChannelGroupByDict(ChannelTypeBo bo);

    /**
     * 功能描述：获取授权 URL
     *
     * @param channelType 通道类型
     * @param thirdChannelFlag    商店标志
     * @return {@link String }
     * <AUTHOR>
     * @date 2024/01/08
     */
    String getAuthorizeUrl(String channelType, String thirdChannelFlag);

    /**
     * 功能描述：将租户发送到多通道
     *
     * @param thirdChannelFlag    商店标志
     * @param channelType
     * <AUTHOR>
     * @date 2024/01/08
     */
    void sendTenantToMultiChannel(String thirdChannelFlag, String channelType);

    String skipStore(String channelType, String thirdChannelFlag);
}
