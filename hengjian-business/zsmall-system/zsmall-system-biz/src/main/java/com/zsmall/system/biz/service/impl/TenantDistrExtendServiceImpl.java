package com.zsmall.system.biz.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hengjian.common.core.domain.R;
import com.hengjian.common.core.enums.TenantType;
import com.hengjian.common.core.utils.StringUtils;
import com.hengjian.common.log.annotation.InMethodLog;
import com.hengjian.common.mybatis.core.page.PageQuery;
import com.hengjian.common.mybatis.core.page.TableDataInfo;
import com.hengjian.common.redis.utils.RedisUtils;
import com.hengjian.common.satoken.utils.LoginHelper;
import com.hengjian.common.tenant.helper.TenantHelper;
import com.hengjian.extend.utils.SystemEventUtils;
import com.hengjian.system.domain.vo.SysTenantVo;
import com.hengjian.system.mapper.SysTenantMapper;
import com.hengjian.system.service.ISysTenantService;
import com.zsmall.common.constant.RedisConstants;
import com.zsmall.common.enums.statuscode.ZSMallStatusCodeEnum;
import com.zsmall.common.enums.tenantSettleIn.CecExperienceEnum;
import com.zsmall.common.enums.tenantSettleIn.SettleInReviewRecordEnum;
import com.zsmall.product.entity.domain.vo.category.ProductCategoryVo;
import com.zsmall.product.entity.iservice.IProductCategoryService;
import com.zsmall.system.biz.service.ITenantDistrExtendService;
import com.zsmall.system.entity.domain.TenantDistrExtend;
import com.zsmall.system.entity.domain.TenantInstantMessagingApp;
import com.zsmall.system.entity.domain.TenantSupSettleInReviewRecord;
import com.zsmall.system.entity.domain.bo.settleInBasic.TenantDistrExtendBo;
import com.zsmall.system.entity.domain.vo.settleInBasic.TenantDistrExtendVo;
import com.zsmall.system.entity.domain.vo.settleInBasic.TenantInstantMessagingAppVo;
import com.zsmall.system.entity.domain.vo.worldLocation.WorldLocationVo;
import com.zsmall.system.entity.iservice.ITenantSupSettleInReviewRecordService;
import com.zsmall.system.entity.iservice.IWorldLocationService;
import com.zsmall.system.entity.mapper.TenantDistrExtendMapper;
import com.zsmall.system.entity.mapper.TenantInstantMessagingAppMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Duration;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 租户分销商-拓展信息Service业务层处理
 *
 * <AUTHOR> Li
 * @date 2023-05-29
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class TenantDistrExtendServiceImpl implements ITenantDistrExtendService {

    private final static String HINT_PERFECT_EXTINFO_KEY = "HPEI_";
    private final TenantDistrExtendMapper baseMapper;
    private final SysTenantMapper sysTenantMapper;
    private final ISysTenantService sysTenantService;
    private final TenantInstantMessagingAppMapper instantMessagingAppMapper;
    private final IWorldLocationService worldLocationService;
    private final IProductCategoryService iProductCategoryService;
    private final ITenantSupSettleInReviewRecordService iTenantSupSettleInReviewRecordService;


    /**
     * 查询租户分销商-拓展信息
     */
    @Override
    public TenantDistrExtendVo queryById(Long id) {
        TenantDistrExtendVo vo = baseMapper.selectVoById(id);

        String tenantId = vo.getTenantId();
        SysTenantVo sysTenantVo = sysTenantService.queryByTenantId(tenantId);
        LambdaQueryWrapper<TenantInstantMessagingApp> lqw = new LambdaQueryWrapper<>();
        lqw.eq(TenantInstantMessagingApp::getTenantId, tenantId);
        TenantInstantMessagingAppVo appVo = TenantHelper.ignore(() -> instantMessagingAppMapper.selectVoOne(lqw));

        vo.setCompanyName(sysTenantVo.getCompanyName());
        vo.setContactEmail(sysTenantVo.getContactEmail());
        vo.setContactUserName(sysTenantVo.getContactUserName());
        vo.setContactPhone(sysTenantVo.getContactPhone());
        vo.setMessagingAppNumber(appVo.getMessagingAppNumber());
        vo.setMessagingAppType(appVo.getMessagingAppType());

        return vo;
    }

    /**
     * 查询租户分销商-拓展信息列表
     */
    @Override
    public TableDataInfo<TenantDistrExtendVo> queryPageList(TenantDistrExtendBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<TenantDistrExtend> lqw = buildQueryWrapper(bo);
        Page<TenantDistrExtendVo> result = TenantHelper.ignore(() -> baseMapper.selectVoPage(pageQuery.build(), lqw));
        List<TenantDistrExtendVo> records = result.getRecords();
        if (CollUtil.isNotEmpty(records)) {
            for (TenantDistrExtendVo vo : records) {
                String tenantId = vo.getTenantId();
                SysTenantVo sysTenantVo = sysTenantService.queryByTenantId(tenantId);
                LambdaQueryWrapper<TenantInstantMessagingApp> lqw1 = new LambdaQueryWrapper<>();
                lqw1.eq(TenantInstantMessagingApp::getTenantId, tenantId);
                TenantInstantMessagingAppVo appVo = TenantHelper.ignore(() -> instantMessagingAppMapper.selectVoOne(lqw1));

                vo.setCompanyName(sysTenantVo.getCompanyName());
                vo.setContactEmail(sysTenantVo.getContactEmail());
                vo.setContactUserName(sysTenantVo.getContactUserName());
                vo.setContactPhone(sysTenantVo.getContactPhone());
                vo.setMessagingAppNumber(appVo.getMessagingAppNumber());
                vo.setMessagingAppType(appVo.getMessagingAppType());
            }
        }
        return TableDataInfo.build(result);
    }

    /**
     * 查询租户分销商-拓展信息列表
     */
    @Override
    public List<TenantDistrExtendVo> queryList(TenantDistrExtendBo bo) {
        LambdaQueryWrapper<TenantDistrExtend> lqw = buildQueryWrapper(bo);
        List<TenantDistrExtendVo> records = baseMapper.selectVoList(lqw);
        if (CollUtil.isNotEmpty(records)) {
            for (TenantDistrExtendVo vo : records) {
                String tenantId = vo.getTenantId();
                SysTenantVo sysTenantVo = sysTenantService.queryByTenantId(tenantId);
                LambdaQueryWrapper<TenantInstantMessagingApp> lqw1 = new LambdaQueryWrapper<>();
                lqw1.eq(TenantInstantMessagingApp::getTenantId, tenantId);
                TenantInstantMessagingAppVo appVo = TenantHelper.ignore(() -> instantMessagingAppMapper.selectVoOne(lqw1));

                vo.setCompanyName(sysTenantVo.getCompanyName());
                vo.setContactEmail(sysTenantVo.getContactEmail());
                vo.setContactUserName(sysTenantVo.getContactUserName());
                vo.setContactPhone(sysTenantVo.getContactPhone());
                vo.setMessagingAppNumber(appVo.getMessagingAppNumber());
                vo.setMessagingAppType(appVo.getMessagingAppType());
            }
        }
        return records;
    }

    private LambdaQueryWrapper<TenantDistrExtend> buildQueryWrapper(TenantDistrExtendBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<TenantDistrExtend> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getHasCompany() != null, TenantDistrExtend::getHasCompany, bo.getHasCompany());
        lqw.eq(StringUtils.isNotBlank(bo.getMainCategories()), TenantDistrExtend::getMainCategories, bo.getMainCategories());
        lqw.eq(bo.getTeamSize() != null, TenantDistrExtend::getTeamSize, bo.getTeamSize());
        lqw.eq(bo.getCompanyStateId() != null, TenantDistrExtend::getCompanyStateId, bo.getCompanyStateId());
        lqw.eq(StringUtils.isNotBlank(bo.getCompanyStateText()), TenantDistrExtend::getCompanyStateText, bo.getCompanyStateText());
//        lqw.eq(bo.getCompanyCityId() != null, TenantDistrExtend::getCompanyCityId, bo.getCompanyCityId());
        lqw.eq(StringUtils.isNotBlank(bo.getCompanyCityText()), TenantDistrExtend::getCompanyCityText, bo.getCompanyCityText());
        lqw.eq(StringUtils.isNotBlank(bo.getCompanyContactAddress()), TenantDistrExtend::getCompanyContactAddress, bo.getCompanyContactAddress());
        lqw.eq(StringUtils.isNotBlank(bo.getRecentAnnualSalesScale()), TenantDistrExtend::getRecentAnnualSalesScale, bo.getRecentAnnualSalesScale());
        lqw.eq(bo.getCecExperience() != null, TenantDistrExtend::getCecExperience, bo.getCecExperience());
        lqw.eq(StringUtils.isNotBlank(bo.getOtherExperience()), TenantDistrExtend::getOtherExperience, bo.getOtherExperience());
        lqw.eq(StringUtils.isNotBlank(bo.getTenantId()), TenantDistrExtend::getTenantId, bo.getTenantId());
        return lqw;
    }

    /**
     * 保存分销商注册信息，新增和修改使用同一个接口
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertByBo(TenantDistrExtendBo bo) throws Exception {
        LambdaQueryWrapper<TenantDistrExtend> tenantDistrExtendLambdaQueryWrapper = Wrappers.lambdaQuery();
        tenantDistrExtendLambdaQueryWrapper.eq(TenantDistrExtend::getTenantId, LoginHelper.getTenantId());
        TenantDistrExtend tenantDistrExtend = baseMapper.selectOne(tenantDistrExtendLambdaQueryWrapper);
        TenantDistrExtend add = BeanUtil.toBean(bo, TenantDistrExtend.class);
        if(null != tenantDistrExtend){
            add.setId(tenantDistrExtend.getId());
        }
        validEntityBeforeSave(add);
        // 保存扩展信息
        baseMapper.insertOrUpdate(add);

        String instantMessagingAppType = bo.getInstantMessagingAppType();
        String messagingAppNumber = bo.getMessagingAppNumber();

        LambdaQueryWrapper<TenantInstantMessagingApp> lambdaQueryWrapper = Wrappers.lambdaQuery();
        lambdaQueryWrapper.eq(TenantInstantMessagingApp::getTenantId, LoginHelper.getTenantId());
        TenantInstantMessagingAppVo tenantInstantMessagingAppVo = instantMessagingAppMapper.selectVoOne(lambdaQueryWrapper);
        if (null != tenantInstantMessagingAppVo) {
            tenantInstantMessagingAppVo.setMessagingAppNumber(messagingAppNumber);
            tenantInstantMessagingAppVo.setMessagingAppType(instantMessagingAppType);
            TenantInstantMessagingApp instantMessagingApp = BeanUtil.toBean(tenantInstantMessagingAppVo, TenantInstantMessagingApp.class);
            // 更新联系方式APP
            instantMessagingAppMapper.updateById(instantMessagingApp);
        }else {
            TenantInstantMessagingApp instantMessagingApp = new TenantInstantMessagingApp();
            instantMessagingApp.setMessagingAppNumber(messagingAppNumber);
            instantMessagingApp.setMessagingAppType(instantMessagingAppType);
            // 插入联系方式APP
            instantMessagingAppMapper.insert(instantMessagingApp);
        }
        // 更改租户信息,添加审批流程，此处不修改租户的完善信息
        String areaCode = bo.getAreaCode();
        String phoneNumber = bo.getPhoneNumber();
        String email = bo.getEmail();
        SysTenantVo sysTenantVo = new SysTenantVo();
        sysTenantVo.setTenantId(LoginHelper.getTenantId());
        sysTenantVo.setContactUserName(bo.getContactUserName());
        sysTenantVo.setContactPhone(areaCode + " " + phoneNumber);
        sysTenantVo.setContactEmail(email);
        sysTenantVo.setCompanyName(bo.getCompanyName());
        SystemEventUtils.updateTenant(sysTenantVo);

        // 入驻审核信息
        TenantSupSettleInReviewRecord reviewRecord = iTenantSupSettleInReviewRecordService.getByTenantId(LoginHelper.getTenantId());
        if (reviewRecord != null) {
            SettleInReviewRecordEnum reviewState = reviewRecord.getReviewState();
            if (reviewState.equals(SettleInReviewRecordEnum.Reviewing) || reviewState.equals(SettleInReviewRecordEnum.ReviewedAgain)) {
                throw new Exception("分销商入驻信息审核中，请勿重复提交");
            }
            if (reviewState.equals(SettleInReviewRecordEnum.Rejected) || reviewState.equals(SettleInReviewRecordEnum.Approved)) {
                reviewRecord.setReviewState(SettleInReviewRecordEnum.ReviewedAgain);
            }
        } else {
            reviewRecord = new TenantSupSettleInReviewRecord();
            reviewRecord.setTenantId(LoginHelper.getTenantId());
            reviewRecord.setReviewState(SettleInReviewRecordEnum.Reviewing);
            // 设置弹窗缓存
            RedisUtils.setCacheObject(RedisConstants.EXTRA_PERFECTION_FLAG + LoginHelper.getTenantId() + SettleInReviewRecordEnum.Reviewing, "False", Duration.ofSeconds(5184000));
        }
        boolean recordSave = iTenantSupSettleInReviewRecordService.saveOrUpdate(reviewRecord);
        if (!recordSave) {
            throw new Exception("保存分销商入驻审核信息失败！");
        }
        return true;
    }

    /**
     * 修改租户分销商-拓展信息
     */
    @Override
    public Boolean updateByBo(TenantDistrExtendBo bo) throws Exception {
        TenantDistrExtend update = BeanUtil.toBean(bo, TenantDistrExtend.class);
        validEntityBeforeSave(update);
        // 更新联系信息
        String areaCode = bo.getAreaCode();
        String phoneNumber = bo.getPhoneNumber();
        String email = bo.getEmail();
        SysTenantVo sysTenantVo = new SysTenantVo();
        sysTenantVo.setTenantId(LoginHelper.getTenantId());
        sysTenantVo.setContactUserName(bo.getContactUserName());
        sysTenantVo.setContactPhone(areaCode + " " + phoneNumber);
        sysTenantVo.setContactEmail(email);
        sysTenantVo.setCompanyName(bo.getCompanyName());
        SystemEventUtils.updateTenant(sysTenantVo);
        // 更新即时通讯软件信息
        LambdaQueryWrapper<TenantInstantMessagingApp> lambdaQueryWrapper = Wrappers.lambdaQuery();
        lambdaQueryWrapper.eq(TenantInstantMessagingApp::getTenantId, LoginHelper.getTenantId());
        TenantInstantMessagingAppVo tenantInstantMessagingAppVo = instantMessagingAppMapper.selectVoOne(lambdaQueryWrapper);
        String messagingAppNumber = bo.getMessagingAppNumber();
        String instantMessagingAppType = bo.getInstantMessagingAppType();
        if (null != tenantInstantMessagingAppVo) {
            tenantInstantMessagingAppVo.setMessagingAppNumber(messagingAppNumber);
            tenantInstantMessagingAppVo.setMessagingAppType(instantMessagingAppType);
        }
        TenantInstantMessagingApp instantMessagingApp = BeanUtil.toBean(tenantInstantMessagingAppVo, TenantInstantMessagingApp.class);
        // 更新联系方式APP
        instantMessagingAppMapper.updateById(instantMessagingApp);

        //如果该供应商入驻信息是待审核状态，则直接返回
        TenantSupSettleInReviewRecord reviewRecord = iTenantSupSettleInReviewRecordService.getByTenantId(LoginHelper.getTenantId());
        if (reviewRecord != null) {
            reviewRecord.setReviewState(SettleInReviewRecordEnum.ReviewedAgain);
        }else {
            log.error("修改分销商审核信息，入驻审核信息为空!");
            throw new Exception("修改审核信息异常，请联系管理员");
        }
        boolean recordSave = iTenantSupSettleInReviewRecordService.saveOrUpdate(reviewRecord);
        if (!recordSave) {
            throw new Exception("保存分销商入驻审核信息失败！");
        }
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(TenantDistrExtend tenantDistrExtend) throws Exception {
        if (null != tenantDistrExtend){
            if (null == tenantDistrExtend.getHasCompany() ) {
                throw new Exception("公司主体不能为空!");
            }
            if(null == tenantDistrExtend.getMainCategories()){
                throw new Exception("主营类目不能为空!");
            }
            if(null == tenantDistrExtend.getCecExperience()){
                throw new Exception("跨境电商经验不能为空!");
            }
            if(null == tenantDistrExtend.getTeamSize()){
                throw new Exception("团队规模不能为空!");
            }
            if(null == tenantDistrExtend.getRecentAnnualSalesScale()){
                throw new Exception("近一年年销售规模不能为空!");
            }
            if(null == tenantDistrExtend.getOtherExperience()){
                throw new Exception("其他线上销售渠道经验不能为空!");
            }
            if(StringUtils.isEmpty(tenantDistrExtend.getCompanyTaxRegistrationNumber())){
                throw new Exception("公司税务登记号不能为空!");
            }
            if(StringUtils.isNotEmpty(tenantDistrExtend.getCompanyTaxRegistrationNumber())){
//                String regex = "^(?=.*[a-zA-Z])(?=.*[0-9])[a-zA-Z0-9]{1,30}$";
                String regex = "^.{1,30}$";
                if(!Pattern.compile(regex).matcher(tenantDistrExtend.getCompanyTaxRegistrationNumber()).matches()){
                    throw new Exception("公司税务登记号不符合要求！长度最大30，数字和字母组成");
                }
            }else {
                throw new Exception("公司税务登记号不能为空!");
            }
        }
    }

    /**
     * 批量删除租户分销商-拓展信息
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    @Override
    public Boolean hintPerfectExtendedInformation() {
        String tenantId = LoginHelper.getTenantId();
        SysTenantVo sysTenantVo = sysTenantService.queryByTenantId(tenantId);
        String extraPerfectionFlag = sysTenantVo.getExtraPerfectionFlag();
        log.info("{} 是否完善扩展信息 = {}", tenantId, extraPerfectionFlag);
        if ("0".equals(extraPerfectionFlag)) {
            String key = HINT_PERFECT_EXTINFO_KEY + tenantId;
            boolean existsObject = RedisUtils.isExistsObject(key);
            log.info("{} 存在提示完善扩展信息缓存 = {}", tenantId, existsObject);
            if (!existsObject) {
                Date currentDate = new Date();
                LocalDateTime midnight = LocalDateTime.ofInstant(currentDate.toInstant(), ZoneId.systemDefault()).plusDays(1).withHour(0).withMinute(0).withSecond(0).withNano(0);
                LocalDateTime currentDateTime = LocalDateTime.ofInstant(currentDate.toInstant(), ZoneId.systemDefault());
                long seconds = ChronoUnit.SECONDS.between(currentDateTime, midnight);
                RedisUtils.setCacheObject(key, true, Duration.ofSeconds((int) seconds));
                return true;
            }
        }
        return false;
    }


    @InMethodLog(value = "获取分销商入驻详情")
    @Override
    public TenantDistrExtendVo getInfo(String tenantId) {
        LambdaQueryWrapper<TenantDistrExtend> lqw = Wrappers.lambdaQuery();
        tenantId = LoginHelper.getTenantTypeEnum().equals(TenantType.Distributor) ? LoginHelper.getTenantId() : tenantId;
        lqw.eq(TenantDistrExtend::getTenantId, tenantId);
        TenantDistrExtendVo vo = TenantHelper.ignore(() -> baseMapper.selectVoOne(lqw));
        Long companyStateId = vo.getCompanyStateId();
        if (ObjUtil.isNotEmpty(companyStateId)) {
            WorldLocationVo worldLocationVo = worldLocationService.queryById(companyStateId);
            vo.setCompanyStateName(worldLocationVo.getLocationOtherName());
        }
        if (ObjUtil.isNotEmpty(vo.getCompanyCountryId())) {
            WorldLocationVo worldLocationVo = worldLocationService.queryById(vo.getCompanyCountryId());
            vo.setCompanyCountryName(worldLocationVo.getLocationOtherName());
        }
        String cecExperience = vo.getCecExperience();
        if (StrUtil.isNotBlank(cecExperience)) {
            vo.setCecExperienceText(CecExperienceEnum.fromName(cecExperience));
        }
        String mainCategories = vo.getMainCategories();
        if (StrUtil.isNotBlank(mainCategories)) {
            List<Long> collect = StrUtil.split(mainCategories, ",").stream().map(i -> Long.valueOf(i)).collect(Collectors.toList());
            List<ProductCategoryVo> categories = iProductCategoryService.queryListByIds(collect);
                List<String> categoryNames = categories.stream().map(ProductCategoryVo::getCategoryName).collect(Collectors.toList());
                vo.setMainCategoriesName(CollUtil.isEmpty(categoryNames) ? null : StrUtil.join(",", categoryNames));
        }
        String otherExperience = vo.getOtherExperience();
        if (StrUtil.isNotBlank(otherExperience)) {
            vo.setOtherExperienceArr(StrUtil.split(otherExperience, ","));
        }
        SysTenantVo sysTenantVo = sysTenantService.queryByTenantId(tenantId);
        LambdaQueryWrapper<TenantInstantMessagingApp> lqw1 = new LambdaQueryWrapper<>();
        lqw1.eq(TenantInstantMessagingApp::getTenantId, tenantId);
        TenantInstantMessagingAppVo appVo = TenantHelper.ignore(() -> instantMessagingAppMapper.selectVoOne(lqw1));

        vo.setCompanyName(sysTenantVo.getCompanyName());
        vo.setContactEmail(sysTenantVo.getContactEmail());
        vo.setContactUserName(sysTenantVo.getContactUserName());
        vo.setContactPhone(sysTenantVo.getContactPhone());
        vo.setMessagingAppNumber(appVo.getMessagingAppNumber());
        vo.setMessagingAppType(appVo.getMessagingAppType());
        return vo;
    }

}
