package com.zsmall.system.biz.service.impl;

import cn.dev33.satoken.secure.BCrypt;
import cn.hutool.core.codec.Base64;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.hengjian.common.core.constant.GlobalConstants;
import com.hengjian.common.core.domain.model.LoginUser;
import com.hengjian.common.core.enums.TenantType;
import com.hengjian.common.core.enums.UserType;
import com.hengjian.common.core.exception.RStatusCodeException;
import com.hengjian.common.core.utils.MapstructUtils;
import com.hengjian.common.core.utils.SpringUtils;
import com.hengjian.common.redis.utils.RedisUtils;
import com.hengjian.common.satoken.utils.LoginHelper;
import com.hengjian.common.tenant.helper.TenantHelper;
import com.hengjian.extend.utils.SystemEventUtils;
import com.hengjian.extend.verifycode.utils.EmailSmsSendUtils;
import com.hengjian.stream.mq.constant.RabbitMqConstant;
import com.hengjian.system.domain.vo.SysDictDataVo;
import com.hengjian.system.domain.vo.SysUserVo;
import com.hengjian.system.service.ISysDictTypeService;
import com.zsmall.common.enums.common.ChannelTypeEnum;
import com.zsmall.common.enums.common.GlobalStateEnum;
import com.zsmall.common.enums.order.LogisticsTypeEnum;
import com.zsmall.common.enums.statuscode.ZSMallStatusCodeEnum;
import com.zsmall.extend.shop.config.properties.ShopifyProperties;
import com.zsmall.extend.shop.model.vo.AmznAccessToken;
import com.zsmall.extend.shop.model.vo.ConnectChannelVo;
import com.zsmall.extend.shop.service.TpsAmazonService;
import com.zsmall.extend.shop.service.TpsShopifyService;
import com.zsmall.extend.shop.service.TpsWayfairService;
import com.zsmall.extend.shop.service.business.TpsBusinessShopifyService;
import com.zsmall.extend.shop.shopify.api.admin.FulfillmentServiceApi;
import com.zsmall.extend.shop.shopify.api.model.fulfillment.InFulfillmentServiceCreate;
import com.zsmall.extend.shop.shopify.kit.AccessTokenKit;
import com.zsmall.extend.shop.shopify.kit.ShopifyKit;
import com.zsmall.extend.shop.shopify.model.Shop;
import com.zsmall.extend.shop.shopify.model.ShopifyClientBean;
import com.zsmall.extend.shop.shopify.model.accesstoken.AccessToken;
import com.zsmall.extend.shop.shopify.model.fulfillment.FulfillmentService;
import com.zsmall.extend.utils.ZSMallSystemEventUtils;
import com.zsmall.extend.wayfair.kit.WayfairKit;
import com.zsmall.extend.wayfair.model.WayfairBean;
import com.zsmall.product.biz.service.ProductService;
import com.zsmall.product.entity.iservice.IProductMappingService;
import com.zsmall.system.biz.service.TenantSalesChannelService;
import com.zsmall.system.biz.thirdapi.param.EBayEnvironmentParamter;
import com.zsmall.system.biz.thirdapi.param.OAuth2Api;
import com.zsmall.system.biz.util.RabbitTemplatePlus;
import com.zsmall.system.entity.domain.ChannelWarehouseInfo;
import com.zsmall.system.entity.domain.ShopifyExtraProperties;
import com.zsmall.system.entity.domain.TenantSalesChannel;
import com.zsmall.system.entity.domain.bo.salesChannel.ChannelSanboxBo;
import com.zsmall.system.entity.domain.bo.salesChannel.ChannelTypeBo;
import com.zsmall.system.entity.domain.bo.salesChannel.TenantChannelBindingBo;
import com.zsmall.system.entity.domain.bo.salesChannel.TenantSalesChannelBo;
import com.zsmall.system.entity.domain.vo.salesChannel.ChannelBaseVo;
import com.zsmall.system.entity.domain.vo.salesChannel.ChannelGroupListVo;
import com.zsmall.system.entity.domain.vo.salesChannel.ChannelGroupVo;
import com.zsmall.system.entity.domain.vo.salesChannel.ShopifyExtraPropertiesVo;
import com.zsmall.system.entity.iservice.IChannelWarehouseInfoServiceImpl;
import com.zsmall.system.entity.iservice.IShopifyExtraPropertiesService;
import com.zsmall.system.entity.iservice.ITenantSalesChannelService;
import com.zsmall.system.entity.wrapper.impl.TenantWrapper;
import com.zsmall.warehouse.entity.domain.WarehouseAdminInfo;
import com.zsmall.warehouse.entity.iservice.IWarehouseAdminService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.*;


@Slf4j
@Service
@RequiredArgsConstructor
public class TenantSalesChannelServiceImpl implements TenantSalesChannelService, TpsBusinessShopifyService {

    @Value("${distribution.tenant.sales.channel.amz.vendor.appid}")
    private String amzVendorAppid;

    @Value("${distribution.tenant.sales.channel.amz.appid}")
    private String applicationId;

    @Value("${distribution.tenant.sales.channel.amazonVC.appid}")
    private String amazonVCApplicationId;

    @Value("${distribution.tenant.sales.channel.ebay.appid}")
    private String ebayAppid;

    @Value("${distribution.tenant.sales.channel.ebay.certid}")
    private String ebayCertid;

    @Value("${distribution.tenant.sales.channel.ebay.devid}")
    private String ebayDevid;

    @Value("${distribution.tenant.sales.channel.ebay.runame}")
    private String ebayRuname;

    @Value("${distribution.tenant.sales.channel.ebay.authorizeurl}")
    private String ebayAuthorizeurl;

    @Value("${distribution.tenant.sales.channel.ebay.tokenurl}")
    private String ebayTokenurl;

    @Value("${distribution.tenant.sales.channel.shein.appid}")
    private String sheinAppid;

    @Value("${distribution.tenant.sales.channel.shein.returnurl}")
    private String redirectUrlString;

    @Value("${distribution.tenant.sales.channel.shein.authorizeurl}")
    private String authUrl;

    @Value("${distribution.tenant.sales.channel.microsoft.clientid}")
    private String client_id;

    @Value("${distribution.tenant.sales.channel.microsoft.scope}")
    private String scope;
    @Value("${distribution.tenant.sales.channel.microsoft.redirecturi}")
    private String redirect_uri;

    @Value("${distribution.tenant.sales.channel.ebay.scopes}")
    private String scopeStr;

    private final ITenantSalesChannelService iTenantSalesChannelService;
    private final IProductMappingService iProductMappingService;
    private final IShopifyExtraPropertiesService iShopifyExtraPropertiesService;
    private final ShopifyClientBean shopifyClientBean;
    private final ShopifyProperties shopifyProperties;
    private final ProductService productService;
    private final TpsWayfairService tpsWayfairService;
    private final TenantWrapper tenantWrapper;
    private final RabbitTemplatePlus rabbitTemplatePlus;
    private final ISysDictTypeService iSysDictTypeService;
    private final IWarehouseAdminService warehouseAdminService;

    /**
     * 从数据库中查询对应的AccessToken
     *
     * @param shopDomain
     * @return
     */
    @Override
    public AccessToken getAccessTokenFromDb(String shopDomain) {
        AccessToken accessToken = null;
        TenantSalesChannel tenantSalesChannel = iTenantSalesChannelService.queryByChannelNameWithIgnoreTenant(shopDomain);
        if (tenantSalesChannel != null) {
            accessToken = new AccessToken();
            accessToken.setAccessToken(tenantSalesChannel.getClientSecret());
        }

        return accessToken;
    }

    /**
     * 绑定账户信息
     *
     * @param shop
     * @param accessToken
     * @return
     * @throws RStatusCodeException
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public ConnectChannelVo bindAccount(com.zsmall.extend.shop.shopify.model.Shop shop,
                                        com.zsmall.extend.shop.shopify.model.accesstoken.AccessToken accessToken) throws RStatusCodeException {
        ConnectChannelVo respBody = new ConnectChannelVo();

        String accessTokenString = accessToken.getAccessToken();
        JSONObject accessJSONObject = JSONUtil.parseObj(accessToken);
        String shopDomain = shop.getDomain();
        String email = shop.getEmail();
        accessJSONObject.set("shop", shopDomain);
        log.info("accessToken = {}", accessJSONObject);

        String accessBase64 = Base64.encode(accessJSONObject.toString());
        respBody.setAccess(accessBase64);

        // 判断是否已经授权过
        TenantSalesChannel tenantSalesChannel = iTenantSalesChannelService.queryNotDeletedByChannelTypeAndName(ChannelTypeEnum.Shopify, shopDomain);
        String tenantId = null;
        SysUserVo currentUser;
        if (tenantSalesChannel != null) {
            tenantId = tenantSalesChannel.getTenantId();
            // 已经存在授权记录
            currentUser = SystemEventUtils.getSysTenantAdministrator(tenantId);
            if (currentUser == null) {
                throw new RStatusCodeException(ZSMallStatusCodeEnum.ACCOUNT_IS_FROZEN);
            }
        } else {
            // 未存在授权记录
            currentUser = SystemEventUtils.getSysUserVoByEmailId(email);
            // 邮箱已经被注册，需要前端登录后才能绑定
            if (currentUser != null) {
                String tenantType = currentUser.getTenantType();
                log.info("userId = {}, tenantType = {}", currentUser.getUserId(), tenantType);
                // 分销商账号需要重新登录
                if (ObjectUtil.equals(tenantType, TenantType.Distributor.name())) {
                    respBody.setEmail(email);
                    respBody.setRedirectLogin(true);
                    return respBody;
                } else {
                    // 供货商账号直接提示无法连接，并且反向删除Shopify应用
                    deleteShopifyApp(shop, accessToken);
                    throw new RStatusCodeException(ZSMallStatusCodeEnum.SALES_CHANNEL_ONLY_BULK);
                }
            }
        }

        log.info("tenantId = {}, currentUser is null ? {}", tenantId, (currentUser == null));
        try {
            // 判断是否需要发送短信
            boolean needSendEmail = false;
            String randomPwd = null;
            // 判断是否存在授权用户
            // 需要注意，租户类型没有内容
            if (currentUser != null) {
//                respBody.setIdentityType(currentUser.getTenantType());
                currentUser.setTenantType(TenantType.Distributor.name());
                // TODO 暂时忽略登录引导逻辑
            } else {
                // 如果未存在用户，则需要创建新用户
                currentUser = new SysUserVo();
                currentUser.setEmail(email);
                currentUser.setTenantType(TenantType.Distributor.name());
                currentUser.setUserType(UserType.SYS_USER.getUserType());
                currentUser.setNickName(email);

                randomPwd = RandomUtil.randomString(10);
                String shaRandomPwd = SecureUtil.sha1(randomPwd);
                currentUser.setPassword(BCrypt.hashpw(shaRandomPwd));
                currentUser.setResetPassword(true);
                currentUser = SystemEventUtils.userRegister(TenantType.Distributor.name(), currentUser);
                currentUser.setTenantType(TenantType.Distributor.name());
                tenantId = currentUser.getTenantId();
                needSendEmail = true;
            }
            log.info("currentUser, tenantId = {}, tenantType = {}, id = {}", tenantId, currentUser.getTenantType(), currentUser.getUserId());
            // 获取临时token值
            String tempTokenValue = SystemEventUtils.userTempLogin(currentUser);
            respBody.setToken(tempTokenValue);
            respBody.setIdentityType(TenantType.Distributor.name());
            // 首次登录暂时不使用
            // respBody.setFirstLogin(true);
            createSalesChannel(tenantId, shopDomain, accessTokenString);
            // 邮箱发送
            if (needSendEmail) {
                EmailSmsSendUtils.sendNewPasswordWithEmail(email, randomPwd);

            }

            // 初始化分销商支付信息
            if (StrUtil.equals(currentUser.getTenantType(), TenantType.Distributor.name())) {
                ZSMallSystemEventUtils.initDistrPaymentInfo(tenantId);
            }
            // 初始化钱包
            ZSMallSystemEventUtils.initWalletInfo(tenantId);
        } catch (Exception e) {
            log.error("绑定账户异常：{}", e.getMessage(), e);
            throw new RStatusCodeException(ZSMallStatusCodeEnum.SHOPIFY_REDIRECT_ERROR);
        }

        return respBody;
    }

    /**
     * 创建销售渠道
     *
     * @param tenantId
     * @param shop
     * @param accessTokenString
     */
    private void createSalesChannel(String tenantId, String shop, String accessTokenString) {
        TenantSalesChannel salesChannel = iTenantSalesChannelService.queryEnableByChannelTypeAndName(tenantId, ChannelTypeEnum.Shopify, shop);
        ShopifyExtraPropertiesVo shopifyExtraPropertiesVo = null;
        if (salesChannel == null) {
            salesChannel = new TenantSalesChannel();
            salesChannel.setChannelName(shop);
            salesChannel.setChannelAlias(shop);
            salesChannel.setChannelUrl("https://" + shop + "/" + "admin");
            salesChannel.setChannelType(ChannelTypeEnum.Shopify.getValue());
            salesChannel.setTenantId(tenantId);
            salesChannel.setState(GlobalStateEnum.Valid.getValue());
            shopifyExtraPropertiesVo = createFulfillmentService(shop, salesChannel);

        } else {
            shopifyExtraPropertiesVo = createFulfillmentService(shop, salesChannel);
        }

        // 存储token
        salesChannel.setTenantId(tenantId);
        salesChannel.setClientSecret(accessTokenString);
        iTenantSalesChannelService.saveWithIgnoreTenant(salesChannel);
        ShopifyExtraProperties shopifyExtraProperties = MapstructUtils.convert(shopifyExtraPropertiesVo, ShopifyExtraProperties.class);
        shopifyExtraProperties.setSalesChannelId(salesChannel.getId());
        iShopifyExtraPropertiesService.saveOrUpdate(shopifyExtraProperties);
    }


    /**
     * 创建与销售渠道关联的Shopify履约服务
     *
     * @param shopDomain
     */
    private ShopifyExtraPropertiesVo createFulfillmentService(String shopDomain, TenantSalesChannel salesChannel) {
        ShopifyExtraPropertiesVo shopifyExtraPropertiesVo = iShopifyExtraPropertiesService.queryByDomain(shopDomain);

        Long salesChannelId = salesChannel.getId();
        String tenantId = salesChannel.getTenantId();
        if (shopifyExtraPropertiesVo == null) {
            shopifyExtraPropertiesVo = new ShopifyExtraPropertiesVo();
            shopifyExtraPropertiesVo.setSalesChannelId(salesChannelId);
        }
//        shopifyExtraPropertiesVo.setAccessToken(accessToken);

        try {
            // 如果履约服务判断履约服务是否存在
            FulfillmentServiceApi fulfillmentServiceApi = ShopifyKit.create().fulfillmentServiceApi(shopDomain);

            Long fulfillmentServiceId = shopifyExtraPropertiesVo.getFulfillmentServiceId();
            FulfillmentService fulfillmentService = null;
            if (fulfillmentServiceId != null) {
                fulfillmentService = fulfillmentServiceApi.getFulfillmentService(fulfillmentServiceId);
            } else {
                log.info("createFulfillmentService - storeId = {}, channelId = {} Not exist Fulfillment Service, ready to create",
                    tenantId, salesChannelId);

                InFulfillmentServiceCreate serviceCreate = new InFulfillmentServiceCreate();
                serviceCreate.setFormat("json");
                serviceCreate.setName(shopifyProperties.getFulfillmentServiceName());
                serviceCreate.setTrackingSupport(true);
                serviceCreate.setRequiresShippingMethod(true);
                serviceCreate.setInventoryManagement(true);
                serviceCreate.setCallbackUrl(shopifyProperties.getFulfillmentServiceCallback());
                serviceCreate.setFulfillmentOrdersOptIn(true);

                fulfillmentService = fulfillmentServiceApi.createFulfillmentService(serviceCreate);
            }

            if (fulfillmentService != null) {
                shopifyExtraPropertiesVo.setFulfillmentServiceId(fulfillmentService.getId());
                shopifyExtraPropertiesVo.setFulfillmentServiceHandle(fulfillmentService.getHandle());
                shopifyExtraPropertiesVo.setLocationId(fulfillmentService.getLocationId());
            }
        } catch (Exception e) {
            log.error("createFulfillmentService error. message = {}", e.getMessage(), e);
        }
        return shopifyExtraPropertiesVo;
    }

    /**
     * 卸载应用
     *
     * @param shop
     * @param accessToken
     */
    private void deleteShopifyApp(Shop shop, AccessToken accessToken) {
        deleteShopApp(shop.getDomain(), accessToken);
    }

    /**
     * 卸载应用
     *
     * @param domain      域名
     * @param accessToken {@link AccessToken}
     */
    private void deleteShopApp(String domain, AccessToken accessToken) {
        try {
            log.info("deleteShopApp domain = {}, accessToken = {}", domain, JSONUtil.toJsonStr(accessToken));
            AccessTokenKit.delete(domain);
            // 卸载shopify自身应用
            ShopifyKit.create().appApi(domain).uninstall(accessToken);
        } catch (Exception e) {
            log.error("卸载应用异常：{}", e.getMessage(), e);
        }
    }

    /**
     * 卸载应用
     *
     * @param domain      域名
     * @param accessToken {@link String}
     */
    private void deleteShopApp(String domain, String accessToken) {
        log.info("deleteShopApp domain = {}, accessToken = {}", domain, accessToken);
        try {
            AccessTokenKit.delete(domain);
            // 卸载shopify自身应用
            ShopifyKit.create().appApi(domain).uninstall(accessToken);
        } catch (Exception e) {
            log.error("卸载应用异常：{}", e.getMessage(), e);
        }
    }

    /**
     * 获取跳转渠道地址
     *
     * @param bo
     * @return
     */
    @Override
    public String jumpToChannel(TenantSalesChannelBo bo) {
        LoginUser loginUser = LoginHelper.getLoginUser(TenantType.Distributor);

        // 检查是否完善信息
        ZSMallSystemEventUtils.checkDisInfoPerfection(loginUser.getTenantId());

        String channelName = bo.getChannelName();
        String channelType = bo.getChannelType();

        String jumpToUrl = null;
        if (StrUtil.equals(channelType, ChannelTypeEnum.Shopify.getValue())) {
            TpsShopifyService tpsShopifyService = SpringUtils.getBean(TpsShopifyService.class);
            jumpToUrl = tpsShopifyService.generateAuthUrl(channelName);
        } else if (StrUtil.equals(channelType, ChannelTypeEnum.Amazon.getValue())) {
            String appConfigId = bo.getAppConfigId();

            TpsAmazonService tpsAmazonService = SpringUtils.getBean(TpsAmazonService.class);
            jumpToUrl = tpsAmazonService.getAmazonAppRedirectUrl(appConfigId);
        }

        return jumpToUrl;
    }

    /**
     * 解绑渠道
     *
     * @param id
     * @return
     */
    @Override
    public Boolean unbind(Long id) throws RStatusCodeException {
        try {
            LambdaQueryWrapper<TenantSalesChannel> lqw = Wrappers.lambdaQuery();
            lqw.eq(TenantSalesChannel::getId, id);
//        lqw.eq(TenantSalesChannel::getState, GlobalStateEnum.Valid.getValue());
            TenantSalesChannel tenantSalesChannel = iTenantSalesChannelService.getOne(lqw);

            if (tenantSalesChannel != null) {
                Long salesChannelId = tenantSalesChannel.getId();
                String channelType = tenantSalesChannel.getChannelType();
                // 各渠道单独逻辑
                switch (channelType) {
                    case "Shopify":
                        String accessToken = tenantSalesChannel.getClientSecret();
                        String channelName = tenantSalesChannel.getChannelName();
                        log.info("toDeleteShopApp channelName = {}, accessToken = {}", channelName, accessToken);
                        if (StrUtil.isNotBlank(accessToken)) {
                            deleteShopApp(channelName, accessToken);
                        }
                        break;
                    default:
                        break;
                }

                tenantSalesChannel.setState(GlobalStateEnum.Invalid.getValue());
                // 删除所有相关的商品
                productService.deleteProductByChannelId(salesChannelId);

                return iTenantSalesChannelService.removeById(tenantSalesChannel);
            } else {
                log.info("channelId = {} sales channel not found. ", id);
                return true;
            }
        } catch (Exception e) {
            throw new RStatusCodeException(ZSMallStatusCodeEnum.DISCONNECT_SALES_CHANNEL_ERROR);
        }
    }

    /**
     * 给当前用户绑定渠道
     *
     * @param channelType
     * @param base64Access
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void bindChannel(String channelType, String base64Access) throws RStatusCodeException {
        String tenantId = LoginHelper.getTenantId();
        log.info("bindChannel params: channelType = {}, base64Access = {}", channelType, base64Access);
        String accessBase64 = Base64.decodeStr(base64Access);
        JSONObject jsonObject = JSONUtil.parseObj(accessBase64);
        log.info("bindChannel accessObject = {}", jsonObject.toString());
        if (StrUtil.equals(channelType, ChannelTypeEnum.Shopify.getValue())) {
            String shop = jsonObject.getStr("shop");
            String accessTokenString = jsonObject.getStr("access_token");

            createSalesChannel(tenantId, shop, accessTokenString);
        } else if (StrUtil.equals(channelType, ChannelTypeEnum.Amazon.getValue())) {
            createAmazonSalesChannel(tenantId, base64Access);
        }
    }

    /**
     * 给当前用户绑定渠道
     *
     * @param channelBindingBo
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean bindChannel(
        TenantChannelBindingBo channelBindingBo) throws RStatusCodeException {

        bindChannel(channelBindingBo.getChannelType(), channelBindingBo.getBase64State());
        return true;
    }


    /**
     * Wayfair沙盒调试验证
     *
     * @param channelSanboxBo
     * @return
     */
    @Override
    public Boolean wayfairSandbox(ChannelSanboxBo channelSanboxBo) throws RStatusCodeException {
        try {
            log.info("调用【Wayfair沙盒测试】接口");
            log.info("接口请求参数：{} ", JSONUtil.toJsonStr(channelSanboxBo));

            String privateKey = channelSanboxBo.getPrivateKey();
            String clientSecret = channelSanboxBo.getClientSecret();
            tpsWayfairService.sandboxTesting(privateKey, clientSecret);

            return true;
        } catch (Exception e) {
            log.error("Wayfair沙盒测试异常 {}", e.getMessage(), e);
            throw new RStatusCodeException(ZSMallStatusCodeEnum.WAYFAIR_SANDBOX_TEST_ERROR_NOT_ORDERS);
        }
    }

    /**
     * 保存前进行业务判断
     *
     * @param tenantSalesChannelBo
     */
    @Override
    public void validEntityBeforeSave(TenantSalesChannelBo tenantSalesChannelBo) throws RStatusCodeException {
        LoginUser loginUser = LoginHelper.getLoginUser(TenantType.Distributor);
        // 检查是否完善信息
        ZSMallSystemEventUtils.checkDisInfoPerfection(loginUser.getTenantId());

//        // 多渠道改造版本:
//        String thirdChannelFlag = tenantSalesChannelBo.getThirdChannelFlag();
//        if(StrUtil.isEmpty(thirdChannelFlag)){
//            tenantSalesChannelBo.setThirdChannelFlag(tenantSalesChannelBo.getStoreName());
//        }

        String channelType = tenantSalesChannelBo.getChannelType();
        String clientSecret = tenantSalesChannelBo.getClientSecret();
        String privateKey = tenantSalesChannelBo.getPrivateKey();

        // 判断是否已创建销售渠道
        boolean exists = iTenantSalesChannelService.existsSalesChannel(channelType, clientSecret, privateKey, tenantSalesChannelBo.getId());
        if (exists) {
            throw new RStatusCodeException(ZSMallStatusCodeEnum.SAME_CHANNEL_STORE);
        }

        if (StrUtil.equals(channelType, ChannelTypeEnum.Wayfair.getValue())) {
            // 如果是wayfair渠道，需要获取下accesstoken，验证是否真实有效
            WayfairKit.create(new WayfairBean(privateKey, clientSecret));
            com.zsmall.extend.wayfair.model.accesstoken.AccessToken accessToken =
                com.zsmall.extend.wayfair.kit.AccessTokenKit.get(privateKey, true);
            if (accessToken == null || (accessToken != null && StrUtil.isBlank(accessToken.getAccessToken()))) {
                // 如果为空，则抛出异常
                throw new RStatusCodeException(ZSMallStatusCodeEnum.WAYFAIR_CONNECT_ERROR);
            }
        }
        //如果是Temu渠道检查 temu渠道类型是否为空
        if (ObjectUtil.equals(ChannelTypeEnum.Temu.name(),channelType)){
            if (ObjectUtil.isEmpty(tenantSalesChannelBo.getTemuChannelType())){
                throw  new RuntimeException("Temu渠道，渠道方式不能为空！");
            }
            //
            //选择本土 ，则显示“usAccessToken”、“cnAccessToken”
            if (tenantSalesChannelBo.getTemuChannelType()==1){
                if (ObjectUtil.isEmpty(tenantSalesChannelBo.getUsAccessToken()) ){
                    throw  new RuntimeException("Temu渠道，店铺类型跨境，usAccessToken 不能为空！");
                }
            }
            //本土则显示“ appKey”, “appSecret”, “accessToken”
            if (tenantSalesChannelBo.getTemuChannelType()==2){
                if (ObjectUtil.isEmpty(tenantSalesChannelBo.getAppKey())){
                    throw  new RuntimeException("Temu渠道，店铺类型本土，appKey 不能为空！");
                }
                if (ObjectUtil.isEmpty(tenantSalesChannelBo.getAppSecret())){
                    throw  new RuntimeException("Temu渠道，店铺类型本土，appSecret 不能为空！");
                }
                if (ObjectUtil.isEmpty(tenantSalesChannelBo.getAccessToken())){
                    throw  new RuntimeException("Temu渠道，店铺类型本土，accessToken 不能为空！");
                }
            }
        }
        //校验类型
        if (ObjectUtil.equals(tenantSalesChannelBo.getLogisticsType(), LogisticsTypeEnum.PickUp)){
            if (ObjectUtil.equals(tenantSalesChannelBo.getChannelType(),ChannelTypeEnum.Amazon_SC.name()) &&
                        ObjectUtil.equals(tenantSalesChannelBo.getRequestMode(),0)){
                if (CollUtil.isEmpty(tenantSalesChannelBo.getAmazonScChannelWarehouseInfos())){
                     throw new RuntimeException("Amazon_SC的渠道,店铺仓库信息不能为空！");
                }
                if (tenantSalesChannelBo.getSiteList().size()!=tenantSalesChannelBo.getAmazonScChannelWarehouseInfos().size()){
                    throw new RuntimeException("Amazon_SC的渠道,国家下店铺仓库信息不能为空！");
                }

                List<TenantSalesChannelBo.AmazonScChannelWarehouseInfo> amazonScChannelWarehouseInfos = tenantSalesChannelBo.getAmazonScChannelWarehouseInfos();
                if (CollUtil.isEmpty(amazonScChannelWarehouseInfos)){
                    throw new RuntimeException("Amazon_SC的渠道,店铺仓库信息不能为空！");
                }
                amazonScChannelWarehouseInfos.forEach(s->{
                    checkChannelWarehouseInfoList(s.getWarehouseInfos(),tenantSalesChannelBo.getChannelType());
                });
            }else {
                if (CollUtil.isEmpty(tenantSalesChannelBo.getChannelWarehouseInfoList())){
                    throw new RuntimeException("Amazon_SC的渠道,店铺仓库信息不能为空！");
                }
                checkChannelWarehouseInfoList(tenantSalesChannelBo.getChannelWarehouseInfoList(),tenantSalesChannelBo.getChannelType());
            }

        }
    }

    public void checkChannelWarehouseInfoList(List<ChannelWarehouseInfo> channelWarehouseInfoList,String channelType){
        if (CollUtil.isEmpty(channelWarehouseInfoList)){
            throw new RuntimeException("自提类型，店铺仓库信息不能为空！");
        }
        Set<String> codes = new HashSet<>();
        if (channelWarehouseInfoList.stream()
                                .map(ChannelWarehouseInfo::getChannelWarehouseCode)
                                .anyMatch(code -> !codes.add(code))){
            throw new RuntimeException("自提类型，店铺仓库标识符不能重复！");
        }
        final String[] excludedChannelTypes = {
            ChannelTypeEnum.Amazon_VC.name(),
            ChannelTypeEnum.Amazon_SC.name(),
            ChannelTypeEnum.EC.name()
        };
        channelWarehouseInfoList.forEach(s->{
            if (!Arrays.asList(excludedChannelTypes).contains(channelType)) {
                if (StrUtil.isBlank(s.getChannelWarehouseName())){
                    throw new RuntimeException("自提类型，店铺仓库名称不能为空！");
                }
            }
            if (StrUtil.isBlank(s.getChannelWarehouseCode())){
                throw new RuntimeException("自提类型，店铺仓库标识符不能为空！");
            }
            if (ObjectUtil.isNull(s.getWarehouseAdminId())){
                throw new RuntimeException("自提类型，管理员仓库ID不能为空！");
            }
            if (s.getChannelWarehouseCode().length()>20){
                throw new RuntimeException("自提类型，店铺仓库标识符长度不能超过20！");
            }
            if (s.getChannelWarehouseName().length()>20){
                throw new RuntimeException("自提类型，店铺仓库名称长度不能超过20！");
            }
            //校验仓库编码合法性
            WarehouseAdminInfo warehouseAdminInfo = TenantHelper.ignore(()->warehouseAdminService.getById(s.getWarehouseAdminId()) );
            if (ObjectUtil.isNull(warehouseAdminInfo)){
                throw new RuntimeException("管理员仓库信息不存在"+s.getWarehouseAdminId());
            }
            //判断
            if (ObjectUtil.notEqual(warehouseAdminInfo.getWarehouseCode(),s.getWarehouseCode())){
                 throw new RuntimeException("管理员仓库编码与渠道仓库编码不一致"+s.getWarehouseAdminId());
            }

        });
    }



    /**
     * 获取已关联的渠道分组
     *
     * @param bo
     * @return
     */
    @Override
    public ChannelGroupListVo getEnableChannelGroup(ChannelTypeBo bo) {
        String reqChannelType = bo.getChannelType();

        List<ChannelGroupVo> channelGroup = new ArrayList<>();

        ChannelTypeEnum[] channelTypes = ChannelTypeEnum.values();
        if (StrUtil.isNotBlank(reqChannelType)) {
            channelTypes = new ChannelTypeEnum[]{ChannelTypeEnum.valueOf(reqChannelType)};
        }

        for (ChannelTypeEnum channelType : channelTypes) {
            List<TenantSalesChannel> channelList = iTenantSalesChannelService.queryByChannelTypeNoDelete(channelType);

            if (CollUtil.isNotEmpty(channelList)) {
                ChannelGroupVo channelGroupBody = new ChannelGroupVo();
                channelGroupBody.setChannelType(channelType.name());

                List<ChannelBaseVo> channelStore = new ArrayList<>();
                for (TenantSalesChannel salesChannel : channelList) {
                    ChannelBaseVo channelBaseBody = new ChannelBaseVo();
                    channelBaseBody.setId(salesChannel.getId());
                    channelBaseBody.setChannelType(channelType.name());
                    channelBaseBody.setChannelAlias(salesChannel.getChannelAlias());
                    channelBaseBody.setChannelName(salesChannel.getChannelName());
                    channelBaseBody.setConnectDate(DateUtil.format(salesChannel.getCreateTime(), "yyyy-MM-dd HH:mm:ss"));
                    channelBaseBody.setState(salesChannel.getState());

                    Long count = iProductMappingService.countByChannelIdNotDelete(salesChannel.getId());
                    channelBaseBody.setProductNum(count);
                    channelBaseBody.setStoreUrl(salesChannel.getChannelUrl());
                    channelStore.add(channelBaseBody);
                }
                channelGroupBody.setChannelStore(channelStore);
                channelGroup.add(channelGroupBody);
            }
        }

        ChannelGroupListVo vo = new ChannelGroupListVo();
        vo.setChannelGroup(channelGroup);
        return vo;
    }

    @Override
    public ChannelGroupListVo getEnableChannelGroupByDict(ChannelTypeBo bo) {

        List<SysDictDataVo> bizSalesChannelTypeList = iSysDictTypeService.selectDictDataByType("biz_sales_channel_type");
        ChannelTypeEnum[] channelTypes = new ChannelTypeEnum[bizSalesChannelTypeList.size()];
        if(CollUtil.isNotEmpty(bizSalesChannelTypeList)){
            for (SysDictDataVo sysDictDataVo : bizSalesChannelTypeList){
                if(StringUtils.isNotEmpty(sysDictDataVo.getDictValue())){
                    channelTypes[bizSalesChannelTypeList.indexOf(sysDictDataVo)] = ChannelTypeEnum.valueOf(sysDictDataVo.getDictValue());
                }
            }
        }
        String reqChannelType = bo.getChannelType();
        List<ChannelGroupVo> channelGroup = new ArrayList<>();
        if (StrUtil.isNotBlank(reqChannelType)) {
            channelTypes = new ChannelTypeEnum[]{ChannelTypeEnum.valueOf(reqChannelType)};
        }
        for (ChannelTypeEnum channelType : channelTypes) {
            List<TenantSalesChannel> channelList = iTenantSalesChannelService.queryByChannelTypeNoDelete(channelType);
            ChannelGroupVo channelGroupBody = new ChannelGroupVo();
            channelGroupBody.setChannelType(channelType.name());
            if (CollUtil.isNotEmpty(channelList)) {
                List<ChannelBaseVo> channelStore = new ArrayList<>();
                for (TenantSalesChannel salesChannel : channelList) {
                    ChannelBaseVo channelBaseBody = new ChannelBaseVo();
                    channelBaseBody.setId(salesChannel.getId());
                    channelBaseBody.setChannelType(channelType.name());
                    channelBaseBody.setChannelAlias(salesChannel.getChannelAlias());
                    channelBaseBody.setChannelName(salesChannel.getChannelName());
                    channelBaseBody.setConnectDate(DateUtil.format(salesChannel.getCreateTime(), "yyyy-MM-dd HH:mm:ss"));
                    channelBaseBody.setState(salesChannel.getState());
                    Long count = iProductMappingService.countByChannelIdNotDelete(salesChannel.getId());
                    channelBaseBody.setProductNum(count);
                    channelBaseBody.setStoreUrl(salesChannel.getChannelUrl());
                    channelStore.add(channelBaseBody);
                }
                channelGroupBody.setChannelStore(channelStore);
            }
            channelGroup.add(channelGroupBody);
        }

        ChannelGroupListVo vo = new ChannelGroupListVo();
        vo.setChannelGroup(channelGroup);
        return vo;
    }

    @Override
    public String getAuthorizeUrl(String channelType, String thirdChannelFlag) {
        String authorizeUrl = "";

        //把 channelFlag 设密钥
//        String encryptionKey = getEncryptionKey(channelFlag);

        if (channelType.equalsIgnoreCase("amazonVendor") || channelType.equalsIgnoreCase("amazonVendords")) {
            //amazon vendor的请求方式
            //示例: https://vendorcentral.amazon.com/apps/authorize/consent?application_id=amzn1.sap.solution.431237db36a-f3dd-4b76-8c82-1a633d6220bd3a&version=beta&state=examplestate

            authorizeUrl = "https://vendorcentral.amazon.com/apps/authorize/consent?application_id=" + amzVendorAppid + "&state=" + thirdChannelFlag + "&version=beta";
        } else if (channelType.equalsIgnoreCase("amazon_vc")){
            //amazon_vc 请求方式
            authorizeUrl = "https://vendorcentral.amazon.com/apps/authorize/consent?application_id=" + amazonVCApplicationId + "&state=" + thirdChannelFlag + "&version=beta";
        } else if (channelType.equalsIgnoreCase("amazon")) {
            //amazon 请求方式
            //示例: https://sellercentral.amazon.com/apps/authorize/consent?application_id=amzn1.sp.solution.b8f3ffc7-8c61-4322-a6dc-a1167bbce942&state=B2B--ADM-Business.amazon&version=betaO
            authorizeUrl = "https://sellercentral.amazon.com/apps/authorize/consent?application_id=" + applicationId + "&state=" + thirdChannelFlag + "&version=beta";

        } else if (channelType.equalsIgnoreCase("ebay")) {
            //ebay 的请求url
            authorizeUrl = generateEbayUrl(thirdChannelFlag);
        } else if (channelType.equalsIgnoreCase("microsoft")) {
            //microsoft 的请求url

            authorizeUrl = "https://login.microsoftonline.com/common/oauth2/v2.0/authorize?client_id=" + client_id + "&response_type=code&redirect_uri=" + redirect_uri + "&response_mode=query&scope=" + scope + "&state=" + thirdChannelFlag;
        } else if (channelType.equalsIgnoreCase("tiktok")) {
            //tiktok 的请求url
            authorizeUrl = "https://services.us.tiktokshop.com/open/authorize?service_id=7330857067428939563&state=" + thirdChannelFlag;
        } else if (ChannelTypeEnum.Shein.getValue().equalsIgnoreCase(channelType)) {
            // shein
            authorizeUrl = generateSheinAuthorizeUrl(thirdChannelFlag);
            log.info("shein authorize url: {}", authorizeUrl);
        }

        return authorizeUrl;
    }

    /**
     * 功能描述：将租户发送到多通道
     *
     * @param thirdChannelFlag 商店标志
     * @param channelType      通道类型
     * <AUTHOR>
     * @date 2024/01/09
     */
    @Override
    public void sendTenantToMultiChannel(String thirdChannelFlag, String channelType) {
        LambdaQueryWrapper<TenantSalesChannel> wrapper = new LambdaQueryWrapper<TenantSalesChannel>()
            .eq(TenantSalesChannel::getChannelType, channelType)
            .eq(TenantSalesChannel::getThirdChannelFlag, thirdChannelFlag);
        TenantSalesChannel channel = iTenantSalesChannelService.getOne(wrapper);
        String msg = tenantWrapper.wrapDataBeforeSending(channel);
        //
        rabbitTemplatePlus.convertAndSend(RabbitMqConstant.SHOP_SYNC_MULTI_EXCHANGE, RabbitMqConstant.SHOP_SYNC_MULTI_KEY, msg);

    }

    @Override
    public String skipStore(String channelType, String thirdChannelFlag) {
        String authorizeUrl = "";

        //把 channelFlag 设密钥
//        String encryptionKey = getEncryptionKey(channelFlag);

//        if (channelType.equalsIgnoreCase("amazonVendor") || channelType.equalsIgnoreCase("amazonVendords")) {
//            //amazon vendor的请求方式
//            //示例: https://vendorcentral.amazon.com/apps/authorize/consent?application_id=amzn1.sap.solution.431237db36a-f3dd-4b76-8c82-1a633d6220bd3a&version=beta&state=examplestate
//
//            authorizeUrl = "https://vendorcentral.amazon.com/apps/authorize/consent?application_id=" + amzVendorAppid + "&state=" + thirdChannelFlag + "&version=beta";
//        } else if (channelType.equalsIgnoreCase("amazon")) {
//            //amazon 请求方式
//            //示例: https://sellercentral.amazon.com/apps/authorize/consent?application_id=amzn1.sp.solution.b8f3ffc7-8c61-4322-a6dc-a1167bbce942&state=B2B--ADM-Business.amazon&version=betaO
//            authorizeUrl = "https://sellercentral.amazon.com/apps/authorize/consent?application_id=" + applicationId + "&state=" + thirdChannelFlag + "&version=beta";
//
//        } else if (channelType.equalsIgnoreCase("ebay")) {
//            //ebay 的请求url
//            authorizeUrl = generateEbayUrl(thirdChannelFlag);
//        } else if (channelType.equalsIgnoreCase("microsoft")) {
//            //microsoft 的请求url
//
//            authorizeUrl = "https://login.microsoftonline.com/common/oauth2/v2.0/authorize?client_id=" + client_id + "&response_type=code&redirect_uri=" + redirect_uri + "&response_mode=query&scope=" + scope + "&state=" + thirdChannelFlag;
//        } else if (channelType.equalsIgnoreCase("tiktok")) {
//            //tiktok 的请求url
//            authorizeUrl = "https://services.us.tiktokshop.com/open/authorize?service_id=7330857067428939563&state=" + thirdChannelFlag;
//        } else

        if (ChannelTypeEnum.Temu.getValue().equalsIgnoreCase(channelType)) {
            // shein
            authorizeUrl = "https://www.temu.com/mall.html?mall_id=634418215742241";
            log.info("temu authorize url: {}", authorizeUrl);
        }
        if (ChannelTypeEnum.TikTok.getValue().equalsIgnoreCase(channelType)) {
            // shein
            authorizeUrl = "https://seller-us.tiktok.com/homepage";
            log.info("tiktok authorize url: {}", authorizeUrl);
        }

        return authorizeUrl;
    }

    /**
     * 生成ebay的链接
     *
     * @param channelFlag
     * @return
     */
    private String generateEbayUrl(String channelFlag) {
        OAuth2Api oauth2Api = new OAuth2Api();

        List<String> scopeList = Lists.newArrayList(StringUtils.split(scopeStr == null ? "" : scopeStr, ","));

        Optional<String> state = Optional.of(channelFlag);
        String authorization_url = oauth2Api.generateUserAuthorizationUrl(getEbayEnvironment(), scopeList, state);
        return authorization_url;
    }

    /**
     * 功能描述：生成 Shein 授权 URL
     *
     * @param channelFlag 通道标志
     * @return {@link String }
     * <AUTHOR>
     * @date 2024/01/08
     */
    private String generateSheinAuthorizeUrl(String channelFlag) {
        //跳转鉴权路径


        //跳转回来的地址,注意用Base64编码


        String redirectUrl = java.util.Base64.getEncoder()
                                             .encodeToString(redirectUrlString.getBytes(StandardCharsets.UTF_8));
        //SHEIN门户的应用appid


        //自定义参数,可用于回调时候的校验是否同一流程

        //拼接
        return authUrl + "?appid=" + sheinAppid + "&redirectUrl=" + redirectUrl + "&state=" + channelFlag;
    }

    private EBayEnvironmentParamter getEbayEnvironment() {

        return new EBayEnvironmentParamter(ebayAppid, ebayCertid, ebayDevid, ebayRuname, ebayAuthorizeurl, ebayTokenurl);
    }

    /**
     * 获取key获取亚马逊店铺信息，并关联
     *
     * @param tenantId
     * @param authorizeState 缓存内容:
     *                       {"selling_partner_id":"AQ1XZTXHXPOY5","spapi_oauth_code":"ANmfYnMnGQLXvIAhnaWT",
     * @return
     */
    private void createAmazonSalesChannel(String tenantId, String authorizeState) throws RStatusCodeException {
        String key = GlobalConstants.GLOBAL_REDIS_KEY + "amazon:" + authorizeState;
        String authorizeString = RedisUtils.getCacheObject(key);
        JSONObject mapObject = JSONUtil.parseObj(authorizeString);
        String sellingPartnerId = mapObject.getStr("selling_partner_id");
        String spApiOauthCode = mapObject.getStr("spapi_oauth_code");
        String appBizId = mapObject.getStr("appBizId");
        if (StrUtil.isBlank(sellingPartnerId)) {
            throw new RStatusCodeException(ZSMallStatusCodeEnum.AMAZON_AUTHENTICATION_BINDING_ERROR);
        }

        // 判断非当前租户，是否已经授权过
        TenantSalesChannel tenantSalesChannel = iTenantSalesChannelService.queryValidByChannelTypeAndNameAndNonTenantId
                                                                              (tenantId, ChannelTypeEnum.Amazon, sellingPartnerId);
        if (tenantSalesChannel != null) {
            throw new RStatusCodeException(ZSMallStatusCodeEnum.AMAZON_SHOP_ALREADY_CONNECT_OTHER);
        }

        TpsAmazonService tpsAmazonService = SpringUtils.getBean(TpsAmazonService.class);

        AmznAccessToken outAccessToken = null;
        try {
            outAccessToken = tpsAmazonService.getToken(appBizId, spApiOauthCode);
            if (outAccessToken == null) {
                log.error("amazon shop getToken error, the token is null.");
                throw new RStatusCodeException(ZSMallStatusCodeEnum.AMAZON_AUTHENTICATION_BINDING_ERROR);
            }
        } catch (IOException e) {
            log.error("amazon shop getToken error, ", e);
            throw new RStatusCodeException(ZSMallStatusCodeEnum.AMAZON_AUTHENTICATION_BINDING_ERROR);
        }

        String refreshToken = outAccessToken.getRefreshToken();
        // 当前授权应用的AppId
        String appId = outAccessToken.getAppId();

        tenantSalesChannel = iTenantSalesChannelService.queryValidByChannelTypeAndNameAndTenantId
                                                           (tenantId, ChannelTypeEnum.Amazon, sellingPartnerId);
        // 判断是否已配置渠道
        if (tenantSalesChannel == null) {
            tenantSalesChannel = new TenantSalesChannel();
            tenantSalesChannel.setTenantId(tenantId);
        }

        tenantSalesChannel.setChannelName(sellingPartnerId);
        tenantSalesChannel.setChannelAlias(sellingPartnerId);
        tenantSalesChannel.setChannelUrl("https://www.amazon.com/s?me=" + sellingPartnerId);
        tenantSalesChannel.setChannelType(ChannelTypeEnum.Amazon.getValue());
        tenantSalesChannel.setTenantId(tenantId);
        tenantSalesChannel.setState(GlobalStateEnum.Valid.getValue());
        // 亚马逊应用Id
        tenantSalesChannel.setPrivateKey(appId);
        tenantSalesChannel.setClientSecret(refreshToken);

        iTenantSalesChannelService.saveWithIgnoreTenant(tenantSalesChannel);
    }
}
