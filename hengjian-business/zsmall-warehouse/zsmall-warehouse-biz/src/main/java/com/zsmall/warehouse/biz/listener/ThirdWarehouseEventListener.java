package com.zsmall.warehouse.biz.listener;

import cn.hutool.json.JSONUtil;
import com.hengjian.common.log.annotation.InMethodLog;
import com.zsmall.common.enums.product.StockManagerEnum;
import com.zsmall.common.enums.warehouse.WarehouseTypeEnum;
import com.zsmall.warehouse.biz.factory.ThirdWarehouseFactory;
import com.zsmall.warehouse.biz.factory.ThirdWarehouseService;
import com.zsmall.warehouse.entity.domain.enums.ThirdWarehouseEventEnum;
import com.zsmall.warehouse.entity.domain.event.ThirdWarehouseEvent;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;

/**
 * 第三方仓库事件监听
 *
 * <AUTHOR>
 * @date 2023/6/17
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ThirdWarehouseEventListener {

    private final ThirdWarehouseFactory thirdWarehouseFactory;

    @EventListener
    @InMethodLog("第三方仓库事件监听")
    public void listener(ThirdWarehouseEvent event) {
        log.info("第三方仓库事件监听 event = {}", JSONUtil.toJsonStr(event));
        StockManagerEnum stockManager = event.getStockManager();
        WarehouseTypeEnum warehouseType = WarehouseTypeEnum.valueOf(stockManager.name());
        ThirdWarehouseService service = thirdWarehouseFactory.getService(warehouseType);
        if (service != null) {
            ThirdWarehouseEventEnum eventType = event.getEvent();
            switch (eventType) {
                case QueryStock:
                    service.queryStockByProductSku(event.getProductSkuCode());
                    break;
                case CreateOrder:
                    service.createOrderByShippingRecord(event.getShippingNo());
                    break;
                case QueryOrder:
                    service.queryOrderByShippingRecord(event.getShippingNoList());
                    break;
                case CancelOrder:
                    service.cancelOrder(event.getShippingNo());
                    break;
                default:
                    break;
            }
        }
    }

}
