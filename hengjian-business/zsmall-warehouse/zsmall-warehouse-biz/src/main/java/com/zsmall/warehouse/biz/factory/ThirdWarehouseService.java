package com.zsmall.warehouse.biz.factory;

import org.springframework.beans.factory.InitializingBean;

import java.util.List;

/**
 * 第三方仓库工厂类接口
 *
 * <AUTHOR>
 * @date 2023/6/17
 */
public interface ThirdWarehouseService extends InitializingBean {

    /**
     * 查询第三方仓储系统库存
     * @param productSkuCode 商品SKU编号
     */
    void queryStockByProductSku(String productSkuCode);

    /**
     * 根据发货记录创建发货单
     * @param shippingNo 发货单编号
     */
    void createOrderByShippingRecord(String shippingNo);

    /**
     * 根据发货记录查询发货单
     * @param shippingNoList
     */
    void queryOrderByShippingRecord(List<String> shippingNoList);

    /**
     * 取消订单
     * @param shippingNo
     */
    void cancelOrder(String shippingNo);

    /**
     * 订单tracking信息回传
     *
     * @param orderNoList
     */
    void ordersTrackingBack(List<String> orderNoList);


    void cancelOrderV2(String orderExtendId, String supplierTenantId);
}
