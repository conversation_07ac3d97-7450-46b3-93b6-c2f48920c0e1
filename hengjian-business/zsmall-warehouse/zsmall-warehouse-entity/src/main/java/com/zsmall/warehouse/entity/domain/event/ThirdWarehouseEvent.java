package com.zsmall.warehouse.entity.domain.event;

import com.hengjian.common.core.utils.SpringUtils;
import com.zsmall.common.enums.product.StockManagerEnum;
import com.zsmall.warehouse.entity.domain.enums.ThirdWarehouseEventEnum;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 第三方仓库事件
 *
 * <AUTHOR>
 * @date 2023/6/17
 */
@Getter
@Setter
@Accessors(chain = true)
public class ThirdWarehouseEvent {

    private String tenantId;

    private StockManagerEnum stockManager;

    private ThirdWarehouseEventEnum event;

    private String productSkuCode;

    private String shippingNo;

    private List<String> shippingNoList;

    public static ThirdWarehouseEvent queryStock(StockManagerEnum stockManager, String productSkuCode) {
        ThirdWarehouseEvent thirdWarehouseEvent = new ThirdWarehouseEvent().setStockManager(stockManager).setEvent(ThirdWarehouseEventEnum.QueryStock).setProductSkuCode(productSkuCode);
        SpringUtils.context().publishEvent(thirdWarehouseEvent);
        return thirdWarehouseEvent;
    }

    public static ThirdWarehouseEvent createOrder(StockManagerEnum stockManager, String shippingNo) {
        ThirdWarehouseEvent thirdWarehouseEvent = new ThirdWarehouseEvent().setStockManager(stockManager).setEvent(ThirdWarehouseEventEnum.CreateOrder).setShippingNo(shippingNo);
        SpringUtils.context().publishEvent(thirdWarehouseEvent);
        return thirdWarehouseEvent;
    }

    public static ThirdWarehouseEvent queryOrder(StockManagerEnum stockManager, List<String> shippingNoList) {
        ThirdWarehouseEvent thirdWarehouseEvent = new ThirdWarehouseEvent().setStockManager(stockManager).setEvent(ThirdWarehouseEventEnum.QueryOrder).setShippingNoList(shippingNoList);
        SpringUtils.context().publishEvent(thirdWarehouseEvent);
        return thirdWarehouseEvent;
    }

    public static ThirdWarehouseEvent cancelOrder(StockManagerEnum stockManager, String shippingNo) {
        ThirdWarehouseEvent thirdWarehouseEvent = new ThirdWarehouseEvent().setStockManager(stockManager).setEvent(ThirdWarehouseEventEnum.CancelOrder).setShippingNo(shippingNo);
        SpringUtils.context().publishEvent(thirdWarehouseEvent);
        return thirdWarehouseEvent;
    }


}
