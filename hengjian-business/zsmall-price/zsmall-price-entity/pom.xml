<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>zsmall-price</artifactId>
        <groupId>com.zsmall</groupId>
        <version>${zsmall.version}</version>
    </parent>
    <dependencies>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-context</artifactId>
        </dependency>
        <dependency>
            <groupId>com.zsmall</groupId>
            <artifactId>zsmall-activity-entity</artifactId>
        </dependency>
        <dependency>
            <groupId>com.zsmall</groupId>
            <artifactId>zsmall-product-entity</artifactId>
        </dependency>
        <dependency>
            <groupId>com.zsmall</groupId>
            <artifactId>zsmall-order-entity</artifactId>
        </dependency>
        <dependency>
            <groupId>com.hengjian</groupId>
            <artifactId>hengjian-system</artifactId>
        </dependency>
        <dependency>
            <groupId>com.zsmall</groupId>
            <artifactId>zsmall-bma-open</artifactId>
            <version>1.0.0</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.zsmall</groupId>
            <artifactId>zsmall-calculate-entity</artifactId>
            <version>1.0.0</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>org.elasticsearch</groupId>
            <artifactId>elasticsearch-core</artifactId>
            <version>7.14.0</version>
            <scope>compile</scope>
        </dependency>
    </dependencies>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>zsmall-price-entity</artifactId>
    <groupId>com.zsmall</groupId>
    <name>ZS-Mall计价实体模块</name>

    <properties>
        <maven.compiler.source>11</maven.compiler.source>
        <maven.compiler.target>11</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

</project>
