package com.zsmall.lottery.support;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hengjian.common.core.domain.R;
import com.hengjian.common.log.annotation.InMethodLog;
import com.hengjian.common.tenant.helper.TenantHelper;
import com.zsmall.common.domain.LocaleMessage;
import com.zsmall.common.enums.common.GlobalStateEnum;
import com.zsmall.common.enums.order.LogisticsTypeEnum;
import com.zsmall.common.enums.order.OrderExceptionEnum;
import com.zsmall.common.enums.order.OrderFlowEnum;
import com.zsmall.common.enums.order.SignalSenderEnum;
import com.zsmall.order.entity.anno.annotaion.PayLimit;
import com.zsmall.order.entity.domain.*;
import com.zsmall.order.entity.domain.dto.OrderPriceCalculateDTO;
import com.zsmall.order.entity.domain.vo.order.OrderCurrencyAmountVO;
import com.zsmall.order.entity.iservice.*;
import com.zsmall.product.entity.domain.ProductSkuStock;
import com.zsmall.product.entity.iservice.IProductSkuStockService;
import com.zsmall.warehouse.entity.domain.Warehouse;
import com.zsmall.warehouse.entity.iservice.IWarehouseService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.Nullable;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.zsmall.common.enums.order.SignalSenderEnum.*;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2024/9/29 15:34
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class PriceBussinessV2Support {
    private final PriceSupportV2 priceSupportV2;
    private final IOrderItemProductSkuService iOrderItemProductSkuService;
    private final IOrderItemPriceService iOrderItemPriceService;
    private final IOrdersService iOrdersService;
    private final IOrderItemService iOrderItemService;
    private final IProductSkuStockService iProductSkuStockService;
    private final IOrderAddressInfoService iOrderAddressService;
    private final IOrderLogisticsInfoService iOrderLogisticsInfoService;
    private final IWarehouseService iWarehouseService;
    /**
     * 功能描述：获取tiktok价格业务处理数据集合
     *
     * @param calculateDTO        计算dto
     * @param tenantId            租户id
     * @param zipCode             邮政编码
     * @param stashList           藏匿清单
     * @param item                项目
     * @param order               秩序
     * @param businessMap         商业地图
     * @param orderItemProductSku
     * @param orderLogisticsInfo
     * @param trackingRecord
     * @return {@link ConcurrentHashMap }<{@link String }, {@link List }<{@link Object }>>
     * <AUTHOR>
     * @date 2024/08/04
     */
    public ConcurrentHashMap<String, List<Object>> getTiktokBusinessMap(OrderPriceCalculateDTO calculateDTO, String tenantId, String zipCode, List<String> stashList, OrderItem item, Orders order, ConcurrentHashMap<String, List<Object>> businessMap, OrderItemProductSku orderItemProductSku, OrderLogisticsInfo orderLogisticsInfo,
                                                                        OrderItemTrackingRecord trackingRecord){

        String logisticsCompanyName = null;

        priceSupportV2.calculationOrderItemPrice(calculateDTO,order.getTenantId(),zipCode,stashList, order, OrderFlowEnum.THIRD_CREATE_ORDER, logisticsCompanyName );
        OrderItemPrice itemPrice = calculateDTO.getOrderItemPrice();
        if(CollUtil.isEmpty(stashList)){
            // 自提的不设置,因为自提的测算不影响价格
            if(LogisticsTypeEnum.DropShipping.equals(order.getLogisticsType())){
                order.setExceptionCode(OrderExceptionEnum.measurement_anomaly.getValue());
            }

        }
        if(LogisticsTypeEnum.DropShipping.equals(order.getLogisticsType())){
            String warehouseSystemCode = calculateDTO.getWarehouseSystemCode();
            orderItemProductSku.setWarehouseSystemCode(warehouseSystemCode);
            orderLogisticsInfo.setLogisticsCompanyName(calculateDTO.getLogisticsCarrierCode());
            orderLogisticsInfo.setLogisticsCarrierCode(calculateDTO.getLogisticsCarrierCode());
            orderLogisticsInfo.setLogisticsServiceName(calculateDTO.getLogisticsCode());
            trackingRecord.setWarehouseSystemCode(warehouseSystemCode);
            trackingRecord.setWarehouseCode(calculateDTO.getWarehouseCode());
            trackingRecord.setLogisticsService(calculateDTO.getLogisticsCode());
            trackingRecord.setLogisticsCarrier(calculateDTO.getLogisticsCarrierCode());
        }

        priceSupportV2.recalculateOrderAmount(order, Collections.singletonList(itemPrice));

        businessMap.computeIfAbsent(SignalSenderEnum.orderItem.name(), k -> Collections.synchronizedList(new ArrayList<>()))
                   .add(item);
        businessMap.computeIfAbsent(SignalSenderEnum.orderItemPrice.name(), k -> Collections.synchronizedList(new ArrayList<>()))
                   .add(itemPrice);
        businessMap.computeIfAbsent(SignalSenderEnum.order.name(), k -> Collections.synchronizedList(new ArrayList<>()))
                   .add(order);
        businessMap.computeIfAbsent(SignalSenderEnum.orderItemProductSku.name(), k -> Collections.synchronizedList(new ArrayList<>()))
                   .add(orderItemProductSku);
        businessMap.computeIfAbsent(SignalSenderEnum.orderLogisticsInfo.name(), k -> Collections.synchronizedList(new ArrayList<>()))
                   .add(orderLogisticsInfo);
        businessMap.computeIfAbsent(SignalSenderEnum.orderItemTrackingRecord.name(), k -> Collections.synchronizedList(new ArrayList<>()))
                   .add(trackingRecord);
        return businessMap;
    }

    /**
     * 功能描述：如果有金额，就得到价格
     *
     * @param calculateDTO        计算dto
     * @param tenantId            租户id
     * @param zipCode             邮政编码
     * @param stashList           藏匿清单
     * @param item                项目
     * @param order               秩序
     * @param businessMap         商业地图
     * @param orderItemProductSku
     * @param orderLogisticsInfo
     * @return {@link ConcurrentHashMap }<{@link String }, {@link List }<{@link Object }>>
     * <AUTHOR>
     * @date 2024/08/06
     */
    public ConcurrentHashMap<String, List<Object>> getPriceIfHaveAmount(OrderPriceCalculateDTO calculateDTO, String tenantId, String zipCode, List<String> stashList, OrderItem item,
                                                                        Orders order, ConcurrentHashMap<String, List<Object>> businessMap,
                                                                        OrderItemProductSku orderItemProductSku,
                                                                        OrderLogisticsInfo orderLogisticsInfo){
        String logisticsCompanyName = null;
        OrderLogisticsInfo logisticsInfo = iOrderLogisticsInfoService.getByOrderNo(order.getOrderNo());
        if(ObjectUtil.isNotEmpty(logisticsInfo)){
            logisticsCompanyName = logisticsInfo.getLogisticsCompanyName();
        }
        LocaleMessage localeMessage = priceSupportV2.calculationOrderItemPrice(calculateDTO, order.getTenantId(), zipCode, stashList, order, OrderFlowEnum.CHECK_BEFORE_PAYMENT, logisticsCompanyName);
        // 仓库/物流更新
        if(LogisticsTypeEnum.DropShipping.equals(order.getLogisticsType())){
            orderItemProductSku.setWarehouseSystemCode(calculateDTO.getWarehouseSystemCode());
            orderLogisticsInfo.setLogisticsCompanyName(calculateDTO.getLogisticsCarrierCode());
            orderLogisticsInfo.setLogisticsCarrierCode(calculateDTO.getLogisticsCarrierCode());
            orderLogisticsInfo.setLogisticsServiceName(calculateDTO.getLogisticsCode());
        }
        OrderItemPrice itemPrice = calculateDTO.getOrderItemPrice();
        if(CollUtil.isEmpty(stashList)){
            // 自提的不设置,因为自提的测算不影响价格
            if(LogisticsTypeEnum.DropShipping.equals(order.getLogisticsType())){
                order.setExceptionCode(OrderExceptionEnum.out_of_stock_exception.getValue());
            }
//            if(LogisticsTypeEnum.PickUp.equals(order.getLogisticsType())){
//                order.setExceptionCode(OrderExceptionEnum.out_of_stock_exception.getValue());
//            }
        }
        if (localeMessage.hasData()){
            order.setPayErrorMessage(localeMessage.toJSON());
        }
        priceSupportV2.recalculateOrderAmount(order, Collections.singletonList(itemPrice));
//        log.info(JSONObject.toJSONString(item));
//        log.info("主订单修改后数据"+JSONObject.toJSONString(order));
//        log.info("子订单修改后数据"+JSONObject.toJSONString(item));
//        log.info("子订单价格修改后数据"+JSONObject.toJSONString(item));
        businessMap.computeIfAbsent(SignalSenderEnum.orderItem.name(), k -> Collections.synchronizedList(new ArrayList<>()))
                   .add(item);
        businessMap.computeIfAbsent(SignalSenderEnum.orderItemPrice.name(), k -> Collections.synchronizedList(new ArrayList<>()))
                   .add(itemPrice);
        businessMap.computeIfAbsent(SignalSenderEnum.order.name(), k -> Collections.synchronizedList(new ArrayList<>()))
                   .add(order);
        businessMap.computeIfAbsent(SignalSenderEnum.orderItemProductSku.name(), k -> Collections.synchronizedList(new ArrayList<>()))
                   .add(orderItemProductSku);
        businessMap.computeIfAbsent(SignalSenderEnum.orderLogisticsInfo.name(), k -> Collections.synchronizedList(new ArrayList<>()))
                   .add(orderLogisticsInfo);
        return businessMap;

    }

    /**
     * 功能描述：重新计算价格并更新
     *
     * @param businessMap 数据
     * <AUTHOR>
     * @date 2024/08/05
     */
    public void recalculatePriceAndUpdate(ConcurrentHashMap<String, List<Object>> businessMap,Integer orderExceptionCode) {
        if(CollUtil.isNotEmpty(businessMap)){
            List<Orders> orders = businessMap.getOrDefault(SignalSenderEnum.order.name(), Collections.emptyList())
                                             .stream()
                                             .map(obj -> (Orders) obj)
                                             .collect(Collectors.toList());
            if (!orders.isEmpty()) {
                iOrdersService.updateOrSetNull(orders, orderExceptionCode);
            }

            // 检查并处理OrderItem
            List<OrderItem> items = businessMap.getOrDefault(orderItem.name(), Collections.emptyList())
                                               .stream()
                                               .map(obj -> (OrderItem) obj)
                                               .collect(Collectors.toList());
            if (!items.isEmpty()) {
                List<String> itemNos = items.stream().map(OrderItem::getOrderItemNo).collect(Collectors.toList());
                iOrderItemPriceService.updateSetNUll(itemNos, orderExceptionCode);
                iOrderItemService.updateOrSetNUll(items, orderExceptionCode);
            }

            // 检查并处理OrderLogisticsInfo
            List<OrderLogisticsInfo> logisticsInfos = businessMap.getOrDefault(orderLogisticsInfo.name(), Collections.emptyList())
                                                                 .stream()
                                                                 .map(obj -> (OrderLogisticsInfo) obj)
                                                                 .collect(Collectors.toList());
            if (!logisticsInfos.isEmpty()) {
                iOrderLogisticsInfoService.updateBatchOrSetNull(logisticsInfos, orderExceptionCode);
            }

            // 检查并处理OrderItemProductSku
            List<OrderItemProductSku> itemProductSkus = businessMap.getOrDefault(orderItemProductSku.name(), Collections.emptyList())
                                                                   .stream()
                                                                   .map(obj -> (OrderItemProductSku) obj)
                                                                   .collect(Collectors.toList());
            if (!itemProductSkus.isEmpty()) {
                iOrderItemProductSkuService.updateOrSetNull(itemProductSkus, orderExceptionCode);
            }
        }

    }
    @PayLimit(timeUnit = TimeUnit.SECONDS,interval = 4)
    public R<String> payOrderBefore(List<String> orderNos) {
        /** 1.支付金额回显
         *   2.支付金额的回填
         *
         *   场景: 1. 订单实际是已经有价格了
         *        2. 订单实际是没有价格的
         *        3. 原本有价格,一算没价格了
         *        针对有价格的订单,直接返回价格
         *        无价格的订单,需要重新计算价格
         * */
        BigDecimal totalPrice =null;
        for (String orderNo : orderNos) {
            BigDecimal price = null;
            Orders order = iOrdersService.getByOrderNo(orderNo);
            LogisticsTypeEnum logisticsType = order.getLogisticsType();
            ConcurrentHashMap<String, List<Object>> businessMap = new ConcurrentHashMap<>();
            if(LogisticsTypeEnum.DropShipping.equals(logisticsType)){
                // 代发
                BigDecimal platformTotalDropShippingPrice = order.getPlatformTotalDropShippingPrice();
                if(ObjectUtil.isEmpty(platformTotalDropShippingPrice)){
                    // 价格为空,重新计算
                    recalculateAndUpdate(order,businessMap);
                    // 拿出businessMap 内的order 的第一个list
                    List<Orders> orders = businessMap.get(SignalSenderEnum.order.name()).stream().map(obj -> (Orders) obj)
                                                     .collect(Collectors.toList());
                    Orders updatedOrder = orders.get(0);
                    price = updatedOrder.getPlatformTotalDropShippingPrice();
                }else {
                    price = platformTotalDropShippingPrice;
                }

            }else if(LogisticsTypeEnum.PickUp.equals(logisticsType)){
                price = order.getPlatformTotalPickUpPrice();
            }
            if(ObjectUtil.isNotEmpty(price)&&ObjectUtil.isEmpty(totalPrice)){
                totalPrice = BigDecimal.ZERO;
                totalPrice = totalPrice.add(price);
            }else if(ObjectUtil.isNotEmpty(price)&&ObjectUtil.isNotEmpty(totalPrice)){
                totalPrice = totalPrice.add(price);
            }

        }

        return totalPrice == null ? R.ok("null") : R.ok(totalPrice.toString());
    }

    /**
     * 功能描述：支付前获取订单金额
     * @param orderNos
     * @return
     */
    @InMethodLog("支付前获取订单金额方法")
    public List<OrderCurrencyAmountVO> payOrderBeforeByCurrency(List<String> orderNos) {
        List<OrderCurrencyAmountVO> orderCurrencyAmountVOS = new ArrayList<>();
        try {
            List<Orders> ordersList = iOrdersService.queryByOrderNoList(orderNos);
            List<String> currencyList = ordersList.stream().map(Orders::getCurrency).distinct().collect(Collectors.toList());
            for(String currency : currencyList){
                orderCurrencyAmountVOS.add(new OrderCurrencyAmountVO(currency));
            }
            for(Orders order : ordersList){
                for(OrderCurrencyAmountVO orderCurrencyAmountVO : orderCurrencyAmountVOS){
                    if(orderCurrencyAmountVO.getCurrency().equals(order.getCurrency())){
                        BigDecimal orderPrice = getOrderPrice(order);
                        if(null == orderPrice){
                            log.error("计算订单价格失败,订单号:{}",order.getOrderNo());
                        }
                        orderCurrencyAmountVO.setAmount(orderCurrencyAmountVO.getAmount() == null ? orderPrice : orderCurrencyAmountVO.getAmount().add(orderPrice));
                        orderCurrencyAmountVO.setCurrencySymbol(order.getCurrencySymbol());
                    }
                }
            }
        }catch (Exception e){
            e.printStackTrace();
            log.error("订单金额获取失败,订单号列表:{},错误信息:{}",orderNos,e.getMessage());
        }
        return orderCurrencyAmountVOS;
    }

    /**
     * 获取订单的售价
     * @param order
     * @return
     */
    public BigDecimal getOrderPrice(Orders order){
        BigDecimal price = BigDecimal.ZERO;
        LogisticsTypeEnum logisticsType = order.getLogisticsType();
        ConcurrentHashMap<String, List<Object>> businessMap = new ConcurrentHashMap<>();
        if(LogisticsTypeEnum.DropShipping.equals(logisticsType)){
            // 代发
            BigDecimal platformTotalDropShippingPrice = order.getPlatformTotalDropShippingPrice();
            if(ObjectUtil.isEmpty(platformTotalDropShippingPrice)){
                // 价格为空,重新计算
                recalculateAndUpdate(order,businessMap);
                // 拿出businessMap 内的order 的第一个list
                List<Orders> orders = businessMap.get(SignalSenderEnum.order.name()).stream().map(obj -> (Orders) obj)
                                                 .collect(Collectors.toList());
                Orders updatedOrder = orders.get(0);
                price = updatedOrder.getPlatformTotalDropShippingPrice();
            }else {
                price = platformTotalDropShippingPrice;
            }

        }else if(LogisticsTypeEnum.PickUp.equals(logisticsType)){
            price = order.getPlatformTotalPickUpPrice();
        }
        return price;
    }

    /**
     * 功能描述：重新计算并更新,适用于计算出价格,不然不会更新
     *
     * @param order       秩序
     * @param businessMap 商业地图
     * @return {@link ConcurrentHashMap }<{@link String }, {@link List }<{@link Object }>>
     * <AUTHOR>
     * @date 2024/08/13
     */
    @Nullable
    public ConcurrentHashMap<String, List<Object>> recalculateAndUpdate(Orders order,ConcurrentHashMap<String, List<Object>> businessMap) {
        List<OrderItem> orderItems = iOrderItemService.getListByOrderId(order.getId());
        OrderAddressInfo addressInfo = iOrderAddressService.getByOrderNo(order.getOrderNo());
        LambdaQueryWrapper<OrderItemProductSku> wrapper = new LambdaQueryWrapper<OrderItemProductSku>().eq(OrderItemProductSku::getOrderNo, order.getOrderNo());
        OrderItemProductSku orderItemProductSku = iOrderItemProductSkuService.getOne(wrapper);
        OrderLogisticsInfo logisticsInfo = iOrderLogisticsInfoService.getByOrderNo(order.getOrderNo());
        if (ObjectUtil.isEmpty(addressInfo)) {
            throw new RuntimeException("订单地址信息不存在");
        }
        if(ObjectUtil.isEmpty(order)){
            throw new RuntimeException("订单不存在");
        }
        String zipCode = addressInfo.getZipCode();
//        warehouse codeMap
//        HashMap<String, String> warehouseHashMap = new HashMap<>();
//        HashMap<String, String> logisticsHashMap = new HashMap<>();
        for (OrderItem item : orderItems) {
            HashMap<String, List<String>> stashMap = TenantHelper.ignore(()->getStashList(Collections.singletonList(item)));
            OrderPriceCalculateDTO calculateDTO = new OrderPriceCalculateDTO();
            calculateDTO.setOrderItem(item);
            calculateDTO.setLogisticsType(order.getLogisticsType());
            // 计算价格
            List<String> stashList = new ArrayList<>();
            if(CollUtil.isNotEmpty(stashMap)){
                stashList = stashMap.get(item.getOrderItemNo());
            }
            // todo 检查站点是否影响
            getPriceIfHaveAmount(calculateDTO, order.getTenantId(),zipCode,stashList,item, order,businessMap,orderItemProductSku,logisticsInfo );

//            warehouseHashMap.put(item.getOrderItemNo(),calculateDTO.getWarehouseSystemCode());
//            logisticsHashMap.put(item.getOrderItemNo(),calculateDTO.getLogisticsCompany());
        }
        // 此流程实际是更新order相关所有价格的流程,此处进行价格更新
        List<Orders> orders = businessMap.get(SignalSenderEnum.order.name()).stream().map(obj -> (Orders) obj)
                                         .collect(Collectors.toList());
        List<OrderItem> items = businessMap.get(orderItem.name()).stream().map(obj -> (OrderItem) obj)
                                           .collect(Collectors.toList());
        List<OrderItemPrice> itemPrices = businessMap.get(SignalSenderEnum.orderItemPrice.name()).stream().map(obj -> (OrderItemPrice) obj)
                                                     .collect(Collectors.toList());

        List<OrderItemProductSku> itemProductSkus = businessMap.get(SignalSenderEnum.orderItemProductSku.name()).stream().map(obj -> (OrderItemProductSku) obj)
                                                               .collect(Collectors.toList());

        List<OrderLogisticsInfo> itemLogistics = businessMap.get(orderLogisticsInfo.name()).stream().map(obj -> (OrderLogisticsInfo) obj)
                                                            .collect(Collectors.toList());
//        for (OrderItemProductSku productSkus : itemProductSkus) {
//            productSkus.setWarehouseSystemCode(warehouseHashMap.get(productSkus.getOrderItemNo()));
//        }

        if (CollUtil.isNotEmpty(orders)) {
            iOrdersService.updateBatchById(orders);
        }
        if (CollUtil.isNotEmpty(items)) {
            iOrderItemService.updateBatchById(items);
        }
        if (CollUtil.isNotEmpty(itemPrices)) {
            iOrderItemPriceService.updateBatchById(itemPrices);
        }
        if (CollUtil.isNotEmpty(itemProductSkus)) {
            iOrderItemProductSkuService.updateBatchById(itemProductSkus);
        }
        if(CollUtil.isNotEmpty(itemLogistics)){
            iOrderLogisticsInfoService.updateBatchById(itemLogistics);
        }
        // 清空
        businessMap.remove(SignalSenderEnum.order.name());
        businessMap.remove(SignalSenderEnum.orderItem.name());
        businessMap.remove(SignalSenderEnum.orderItemPrice.name());
        businessMap.remove(SignalSenderEnum.orderItemProductSku.name());
        businessMap.remove(orderLogisticsInfo.name());
        // 重新放入
        businessMap.computeIfAbsent(SignalSenderEnum.orderItem.name(), k -> Collections.synchronizedList(new ArrayList<>()))
                   .add(items);
        businessMap.computeIfAbsent(SignalSenderEnum.orderItemPrice.name(), k -> Collections.synchronizedList(new ArrayList<>()))
                   .add(itemPrices);
        businessMap.computeIfAbsent(SignalSenderEnum.order.name(), k -> Collections.synchronizedList(new ArrayList<>()))
                   .add(order);
        businessMap.computeIfAbsent(SignalSenderEnum.orderItemProductSku.name(), k -> Collections.synchronizedList(new ArrayList<>()))
                   .add(orderItemProductSku);
        businessMap.computeIfAbsent(orderLogisticsInfo.name(), k -> Collections.synchronizedList(new ArrayList<>()))
                   .add(logisticsInfo);
        return businessMap;
    }

    /**
     * 功能描述：重新计算并更新价格,已经价格的订单 ,临时改的还可以优化
     *
     * @param order       秩序
     * @param businessMap 商业地图
     * <AUTHOR>
     * @date 2024/08/13
     */
    public void recalculateAndUpdateForPrice(Orders order,ConcurrentHashMap<String, List<Object>> businessMap) {
        List<OrderItem> orderItems = iOrderItemService.getListByOrderId(order.getId());
        OrderAddressInfo addressInfo = iOrderAddressService.getByOrderNo(order.getOrderNo());
        LambdaQueryWrapper<OrderItemProductSku> wrapper = new LambdaQueryWrapper<OrderItemProductSku>().eq(OrderItemProductSku::getOrderNo, order.getOrderNo());
        OrderItemProductSku orderItemProductSku = iOrderItemProductSkuService.getOne(wrapper);
        OrderLogisticsInfo logisticsInfo = iOrderLogisticsInfoService.getByOrderNo(order.getOrderNo());
        if (ObjectUtil.isEmpty(addressInfo)) {
            throw new RuntimeException("订单地址信息不存在");
        }
        if(ObjectUtil.isEmpty(order)){
            throw new RuntimeException("订单不存在");
        }

        String zipCode = addressInfo.getZipCode();
        // 实际只有一个
        for (OrderItem item : orderItems) {
            HashMap<String, List<String>> stashMap = TenantHelper.ignore(()->getStashList(Collections.singletonList(item)));
            OrderPriceCalculateDTO calculateDTO = new OrderPriceCalculateDTO();
            calculateDTO.setOrderItem(item);
            calculateDTO.setLogisticsType(order.getLogisticsType());
            // 计算价格
            List<String> stashList = new ArrayList<>();
            if(CollUtil.isNotEmpty(stashMap)){
                stashList = stashMap.get(item.getOrderItemNo());
            }
            // 算价方法
            getPriceIfHaveAmount(calculateDTO, order.getTenantId(),zipCode,stashList,item, order,businessMap,orderItemProductSku, logisticsInfo);
        }
        // 此流程实际是更新order相关所有价格的流程,此处进行价格更新
        List<Orders> orders = businessMap.get(SignalSenderEnum.order.name()).stream().map(obj -> (Orders) obj)
                                         .collect(Collectors.toList());
        List<OrderItem> items = businessMap.get(orderItem.name()).stream().map(obj -> (OrderItem) obj)
                                           .collect(Collectors.toList());
        List<OrderItemPrice> itemPrices = businessMap.get(SignalSenderEnum.orderItemPrice.name()).stream().map(obj -> (OrderItemPrice) obj)
                                                     .collect(Collectors.toList());

        List<OrderItemProductSku> productSkus = businessMap.get(SignalSenderEnum.orderItemProductSku.name()).stream()
                                                           .map(obj -> (OrderItemProductSku) obj)
                                                           .collect(Collectors.toList());

        List<OrderLogisticsInfo> itemLogistics = businessMap.get(orderLogisticsInfo.name()).stream().map(obj -> (OrderLogisticsInfo) obj)
                                                            .collect(Collectors.toList());
        Orders newOrder = orders.get(0);
        if(ObjectUtil.isNotEmpty(newOrder)){
//            for (Orders updateOrder : orders) {
////                if(ObjectUtil.isNotEmpty(updateOrder.getPlatformTotalFinalDeliveryFee())){
////                    updateOrder.setExceptionCode(OrderExceptionEnum.order_pay_exception.getValue());
////                }
//            }
            Integer exceptionCode = newOrder.getExceptionCode();
            if(OrderExceptionEnum.final_delivery_fee_exception.getValue().equals(exceptionCode)||OrderExceptionEnum.measurement_anomaly.getValue().equals(exceptionCode)){
                // 有异常,setNull 先更新基础信息
                iOrdersService.updateBatchById(orders);
                iOrdersService.updateOrSetNull(orders,exceptionCode);
                iOrderItemService.updateOrSetNUll(items,exceptionCode);
                iOrderItemPriceService.updateSetNUll(items.stream().map(OrderItem::getOrderItemNo).collect(Collectors.toList()),exceptionCode);
            }else{
                if (CollUtil.isNotEmpty(orders)) {
                    iOrdersService.updateBatchById(orders);
                }
                // 判断有没有异常,有异常就setNull
                if (CollUtil.isNotEmpty(items)) {
                    iOrderItemService.updateBatchById(items);
                }
                if (CollUtil.isNotEmpty(itemPrices)) {
                    iOrderItemPriceService.updateBatchById(itemPrices);
                }
            }
            iOrderItemProductSkuService.updateOrSetNull(newOrder.getOrderNo(),productSkus.get(0).getWarehouseSystemCode(), productSkus.get(0).getWarehouseSystemCode());
            iOrderLogisticsInfoService.updateBatchById(itemLogistics);
        }

    }


    /**
     * 功能描述：获取仓库清单
     *
     * @param orderItemList 订单项目列表
     * @return {@link HashMap }<{@link String }, {@link List }<{@link String }>>
     * <AUTHOR>
     * @date 2024/08/14
     */
    public HashMap<String, List<String>> getStashList(List<OrderItem> orderItemList) {
        HashMap<String, List<String>> stringHashMap = new HashMap<>();
        for (OrderItem item : orderItemList) {
            LambdaQueryWrapper<ProductSkuStock> eq = new LambdaQueryWrapper<ProductSkuStock>()
                .eq(ProductSkuStock::getProductSkuCode, item.getProductSkuCode())
                .eq(ProductSkuStock::getStockState, GlobalStateEnum.Valid)
                .ge(ProductSkuStock::getStockAvailable,item.getTotalQuantity())
                .eq(ProductSkuStock::getDelFlag, 0);
            List<ProductSkuStock> list = iProductSkuStockService.list(eq);
            List<String> collect = list.stream().map(ProductSkuStock::getWarehouseSystemCode)
                                       .collect(Collectors.toList());
            if(ObjectUtil.isNotEmpty(collect)){
                LambdaQueryWrapper<Warehouse> in = new LambdaQueryWrapper<Warehouse>().in(Warehouse::getWarehouseSystemCode, collect);
                List<Warehouse> list1 = TenantHelper.ignore(()->iWarehouseService.list(in));
                List<String> collect1 = list1.stream().map(Warehouse::getWarehouseCode)
                                             .collect(Collectors.toList());
                stringHashMap.put(item.getOrderItemNo(),collect1);
            }

        }
        return stringHashMap;
    }
//    public HashMap<String, List<String>> getStashList(List<OrderItem> orderItemList) {
//        HashMap<String, List<String>> stringHashMap = new HashMap<>();
//        for (OrderItem item : orderItemList) {
//            LambdaQueryWrapper<ProductSkuStock> eq = new LambdaQueryWrapper<ProductSkuStock>()
//                .eq(ProductSkuStock::getProductSkuCode, item.getProductSkuCode())
//                .ge(ProductSkuStock::getStockAvailable, item.getTotalQuantity())
//                .eq(ProductSkuStock::getStockState, GlobalStateEnum.Valid)
//                .eq(ProductSkuStock::getDelFlag, 0);
//            List<ProductSkuStock> list = iProductSkuStockService.list(eq);
//            List<String> collect = list.stream().map(ProductSkuStock::getWarehouseSystemCode)
//                                       .collect(Collectors.toList());
//            if(ObjectUtil.isNotEmpty(collect)){
//                LambdaQueryWrapper<Warehouse> in = new LambdaQueryWrapper<Warehouse>().in(Warehouse::getWarehouseSystemCode, collect);
//                List<Warehouse> list1 = TenantHelper.ignore(()->iWarehouseService.list(in));
//                List<String> collect1 = list1.stream().map(Warehouse::getWarehouseCode)
//                                             .collect(Collectors.toList());
//                stringHashMap.put(item.getOrderItemNo(),collect1);
//            }
//
//        }
//
//        return stringHashMap;
//    }

//    /**
//     * 功能描述：更新批量补偿
//     *
//     * @param needCompensateOrders 需要补偿订单
//     * @return {@link List }<{@link Orders }>
//     * <AUTHOR>
//     * @date 2024/08/14
//     */
//    public List<String> updateBatchCompensate(List<Orders> needCompensateOrders) {
//        List<Orders> orders = new ArrayList<>();
//        for (Orders needCompensateOrder : needCompensateOrders) {
//            recalculateAndUpdateForPrice(needCompensateOrder,new ConcurrentHashMap<>());
//            // 如果 order 的配送费有值了,就加入到支付逻辑内
//            BigDecimal originalTotalDropShippingPrice = needCompensateOrder.getOriginalTotalDropShippingPrice();
//            if(ObjectUtil.isNotEmpty(originalTotalDropShippingPrice)){
//                orders.add(needCompensateOrder);
//            }
//        }
//        List<String> successList = new ArrayList<>();
//        for (Orders order : orders) {
//
//            if(ZSMallSystemEventUtils.checkAutoPaymentEvent(order.getTenantId())){
//                OrderPayBo bo = new OrderPayBo();
//
//                bo.addOrderNoList(Collections.singletonList(order.getOrderNo()));
//                bo.setPaymentPassword(null);
//                try {
//                    TenantHelper.ignore(() -> {
//                        try {
//                            boolean isSuccess = ordersService.payOrderForErp(bo, order.getTenantId(), true, true);
//                            if(isSuccess){
//                                successList.add(order.getOrderNo());
//                            }
//                            return isSuccess;
//                        } catch (Exception e) {
//                            log.error(e.getMessage());
//                            throw new RuntimeException(e.getMessage());
//                        }
//                    });
//                } catch (Exception e) {
//                    log.error("支付失败", e);
//                }
//            }
//
//        }
//
//        return successList;
//    }
}
