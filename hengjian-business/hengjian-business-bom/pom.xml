<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <groupId>com.hengjian</groupId>
    <artifactId>hengjian-business-bom</artifactId>
    <version>${revision}</version>
    <modelVersion>4.0.0</modelVersion>
    <packaging>pom</packaging>

    <name>
        HengJian业务模块bom
    </name>
    <description>
        Heng<PERSON>ian业务模块bom
    </description>

    <properties>
        <revision>5.0.0-BETA</revision>
        <zsmall.version>1.0.0</zsmall.version>
        <maven.compiler.source>11</maven.compiler.source>
        <maven.compiler.target>11</maven.compiler.target>
    </properties>

    <dependencyManagement>
        <dependencies>

            <!-- ZS-Mall通用模块 -->
            <dependency>
                <groupId>com.zsmall</groupId>
                <artifactId>zsmall-common</artifactId>
                <version>${zsmall.version}</version>
            </dependency>

            <!-- ZS-Mall商城模块 -->
            <dependency>
                <groupId>com.zsmall</groupId>
                <artifactId>zsmall-marketplace</artifactId>
                <version>${zsmall.version}</version>
            </dependency>

            <!-- ↓↓↓ ZS-Mall系统模块 ↓↓↓ -->
            <!-- ZS-Mall系统实体模块 -->
            <dependency>
                <groupId>com.zsmall</groupId>
                <artifactId>zsmall-system-entity</artifactId>
                <version>${zsmall.version}</version>
            </dependency>
            <!-- ZS-Mall系统业务模块 -->
            <dependency>
                <groupId>com.zsmall</groupId>
                <artifactId>zsmall-system-biz</artifactId>
                <version>${zsmall.version}</version>
            </dependency>
            <!-- ZS-Mall系统控制层模块 -->
            <dependency>
                <groupId>com.zsmall</groupId>
                <artifactId>zsmall-system-controller</artifactId>
                <version>${zsmall.version}</version>
            </dependency>
            <!-- ↑↑↑ ZS-Mall系统模块 ↑↑↑ -->

            <!-- ↓↓↓ ZS-Mall商品模块 ↓↓↓ -->
            <!-- ZS-Mall商品实体模块 -->
            <dependency>
                <groupId>com.zsmall</groupId>
                <artifactId>zsmall-product-entity</artifactId>
                <version>${zsmall.version}</version>
            </dependency>
            <!-- ZS-Mall商品业务模块 -->
            <dependency>
                <groupId>com.zsmall</groupId>
                <artifactId>zsmall-product-biz</artifactId>
                <version>${zsmall.version}</version>
            </dependency>
            <!-- ZS-Mall商品控制层模块 -->
            <dependency>
                <groupId>com.zsmall</groupId>
                <artifactId>zsmall-product-controller</artifactId>
                <version>${zsmall.version}</version>
            </dependency>
            <!-- ↑↑↑ ZS-Mall商品模块 ↑↑↑ -->

            <!-- ↓↓↓ ZS-Mall订单模块 ↓↓↓ -->
            <!-- ZS-Mall订单实体模块 -->
            <dependency>
                <groupId>com.zsmall</groupId>
                <artifactId>zsmall-order-entity</artifactId>
                <version>${zsmall.version}</version>
            </dependency>
            <!-- ZS-Mall订单业务模块 -->
            <dependency>
                <groupId>com.zsmall</groupId>
                <artifactId>zsmall-order-biz</artifactId>
                <version>${zsmall.version}</version>
            </dependency>
            <!-- ZS-Mall订单控制层模块 -->
            <dependency>
                <groupId>com.zsmall</groupId>
                <artifactId>zsmall-order-controller</artifactId>
                <version>${zsmall.version}</version>
            </dependency>
            <!-- ↑↑↑ ZS-Mall订单模块 ↑↑↑ -->

            <!-- ↓↓↓ ZS-Mall活动模块 ↓↓↓ -->
            <!-- ZS-Mall活动实体模块 -->
            <dependency>
                <groupId>com.zsmall</groupId>
                <artifactId>zsmall-activity-entity</artifactId>
                <version>${zsmall.version}</version>
            </dependency>
            <!-- ZS-Mall活动业务模块 -->
            <dependency>
                <groupId>com.zsmall</groupId>
                <artifactId>zsmall-activity-biz</artifactId>
                <version>${zsmall.version}</version>
            </dependency>
            <!-- ZS-Mall活动控制层模块 -->
            <dependency>
                <groupId>com.zsmall</groupId>
                <artifactId>zsmall-activity-controller</artifactId>
                <version>${zsmall.version}</version>
            </dependency>
            <!-- ↑↑↑ ZS-Mall活动模块 ↑↑↑ -->

            <!-- ↓↓↓ ZS-Mall仓储模块 ↓↓↓ -->
            <!-- ZS-Mall仓储实体模块 -->
            <dependency>
                <groupId>com.zsmall</groupId>
                <artifactId>zsmall-warehouse-entity</artifactId>
                <version>${zsmall.version}</version>
            </dependency>
            <!-- ZS-Mall仓储业务模块 -->
            <dependency>
                <groupId>com.zsmall</groupId>
                <artifactId>zsmall-warehouse-biz</artifactId>
                <version>${zsmall.version}</version>
            </dependency>
            <!-- ZS-Mall仓储控制层模块 -->
            <dependency>
                <groupId>com.zsmall</groupId>
                <artifactId>zsmall-warehouse-controller</artifactId>
                <version>${zsmall.version}</version>
            </dependency>
            <!-- ↑↑↑ ZS-Mall仓储模块 ↑↑↑ -->


            <!-- ↓↓↓ ZS-Mall扩展模块 ↓↓↓ -->
            <!-- ZS-Mall 扩展模块 核心包 -->
            <dependency>
                <groupId>com.zsmall.extend</groupId>
                <artifactId>zsmall-extend-core</artifactId>
                <version>${zsmall.version}</version>
            </dependency>
            <!-- ZS-Mall 扩展模块 payoneer支付 -->
            <dependency>
                <groupId>com.zsmall.extend.payment</groupId>
                <artifactId>zsmall-payment-payoneer</artifactId>
                <version>${zsmall.version}</version>
            </dependency>
            <dependency>
                <groupId>com.zsmall.extend.payment</groupId>
                <artifactId>zsmall-payment-airwallex</artifactId>
                <version>${zsmall.version}</version>
            </dependency>

            <!-- ZS-Mall 扩展模块 logistics tracking17 -->
            <dependency>
                <groupId>com.zsmall.extend.logistics</groupId>
                <artifactId>zsmall-logistics-tracking17</artifactId>
                <version>${zsmall.version}</version>
            </dependency>
            <!-- ZS-Mall 扩展模块 mws thebizark -->
            <dependency>
                <groupId>com.zsmall.extend.wms</groupId>
                <artifactId>zsmall-wms-thebizark</artifactId>
                <version>${zsmall.version}</version>
            </dependency>
            <!-- ZS-Mall 扩展模块 shop wayfair -->
            <dependency>
                <groupId>com.zsmall.extend.shop</groupId>
                <artifactId>zsmall-shop-wayfair</artifactId>
                <version>${zsmall.version}</version>
            </dependency>
            <!-- ZS-Mall 扩展模块 shop shopify -->
            <dependency>
                <groupId>com.zsmall.extend.shop</groupId>
                <artifactId>zsmall-shop-shopify</artifactId>
                <version>${zsmall.version}</version>
            </dependency>
            <!-- ZS-Mall 扩展模块 shop rakuten -->
            <dependency>
                <groupId>com.zsmall.extend.shop</groupId>
                <artifactId>zsmall-shop-rakuten</artifactId>
                <version>${zsmall.version}</version>
            </dependency>
            <!-- ZS-Mall 扩展模块 shop amazon -->
            <dependency>
                <groupId>com.zsmall.extend.shop</groupId>
                <artifactId>zsmall-shop-amazon</artifactId>
                <version>${zsmall.version}</version>
            </dependency>
            <dependency>
                <groupId>com.zsmall.extend.shop</groupId>
                <artifactId>zsmall-shop-amazon-kit</artifactId>
                <version>${zsmall.version}</version>
            </dependency>
            <dependency>
                <groupId>com.zsmall.extend.shop</groupId>
                <artifactId>zsmall-shop-amazon-business</artifactId>
                <version>${zsmall.version}</version>
            </dependency>
            <dependency>
                <groupId>com.zsmall.extend.shop</groupId>
                <artifactId>zsmall-shop-amazon-business-controller</artifactId>
                <version>${zsmall.version}</version>
            </dependency>

<!--            <dependency>-->
<!--                <groupId>com.zsmall.extend.shop</groupId>-->
<!--                <artifactId>zsmall-shop-tiktok-business-controller</artifactId>-->
<!--                <version>${zsmall.version}</version>-->
<!--            </dependency>-->

<!--            <dependency>-->
<!--                <groupId>com.zsmall.extend.shop</groupId>-->
<!--                <artifactId>zsmall-shop-tiktok-job</artifactId>-->
<!--                <version>${zsmall.version}</version>-->
<!--            </dependency>-->

            <dependency>
                <groupId>com.zsmall.extend.shop</groupId>
                <artifactId>zsmall-shop-amazon-job</artifactId>
                <version>${zsmall.version}</version>
            </dependency>
            <!-- ZS-Mall 扩展模块 shop starter -->
            <dependency>
                <groupId>com.zsmall.extend.shop</groupId>
                <artifactId>zsmall-shop-starter</artifactId>
                <version>${zsmall.version}</version>
            </dependency>
            <!-- ZS-Mall 扩展模块 事件 -->
            <dependency>
                <groupId>com.zsmall.extend.event</groupId>
                <artifactId>zsmall-extend-event</artifactId>
                <version>${zsmall.version}</version>
            </dependency>
            <dependency>
                <groupId>com.zsmall.extend.pdf</groupId>
                <artifactId>zsmall-extend-pdf</artifactId>
                <version>${zsmall.version}</version>
            </dependency>
            <dependency>
                <groupId>com.zsmall.extend.es</groupId>
                <artifactId>zsmall-extend-es</artifactId>
                <version>${zsmall.version}</version>
            </dependency>
            <!-- ↑↑↑ ZS-Mall扩展模块 ↑↑↑ -->

            <!-- ↓↓↓ ZS-Mall任务模块 ↓↓↓ -->
            <dependency>
                <groupId>com.zsmall</groupId>
                <artifactId>zsmall-xxl-job</artifactId>
                <version>${zsmall.version}</version>
            </dependency>
            <!-- ↑↑↑ ZS-Mall任务模块 ↑↑↑ -->
        </dependencies>
    </dependencyManagement>

</project>
