<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <artifactId>hengjian-business</artifactId>
        <groupId>com.hengjian</groupId>
        <version>${revision}</version>
    </parent>

    <artifactId>zsmall-xxl-job</artifactId>
    <groupId>com.zsmall</groupId>
    <version>${zsmall.version}</version>
    <name>ZS-Mall定时器模块</name>
    <description>
        ZS-Mall定时器模块
    </description>

    <properties>
        <maven.compiler.source>11</maven.compiler.source>
        <maven.compiler.target>11</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>

        <!-- 通用工具-->
<!--        <dependency>-->
<!--            <groupId>com.hengjian</groupId>-->
<!--            <artifactId>hengjian-common-core</artifactId>-->
<!--        </dependency>-->

        <!-- 调度模块 -->
        <dependency>
            <groupId>com.hengjian</groupId>
            <artifactId>hengjian-common-job</artifactId>
        </dependency>

        <!-- Mysql驱动包 -->
        <dependency>
            <groupId>com.mysql</groupId>
            <artifactId>mysql-connector-j</artifactId>
        </dependency>

        <dependency>
            <groupId>com.hengjian</groupId>
            <artifactId>hengjian-system</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>hengjian-stream-mq</artifactId>
                    <groupId>com.hengjian</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.zsmall</groupId>
            <artifactId>zsmall-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.zsmall</groupId>
            <artifactId>zsmall-product-biz</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.hengjian</groupId>
                    <artifactId>hengjian-stream-mq</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>hengjian-common-websocket</artifactId>
                    <groupId>com.hengjian</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.zsmall</groupId>
            <artifactId>zsmall-order-biz</artifactId>
        </dependency>
        <dependency>
            <groupId>com.zsmall</groupId>
            <artifactId>zsmall-system-biz</artifactId>
        </dependency>
        <dependency>
            <groupId>com.zsmall</groupId>
            <artifactId>zsmall-activity-biz</artifactId>
        </dependency>
        <dependency>
            <groupId>com.zsmall</groupId>
            <artifactId>zsmall-warehouse-biz</artifactId>
        </dependency>

        <dependency>
            <groupId>com.zsmall.extend.pdf</groupId>
            <artifactId>zsmall-extend-pdf</artifactId>
        </dependency>

        <dependency>
            <groupId>com.zsmall.extend.shop</groupId>
            <artifactId>zsmall-shop-shopify</artifactId>
        </dependency>

        <dependency>
            <groupId>com.zsmall.extend.shop</groupId>
            <artifactId>zsmall-shop-wayfair</artifactId>
        </dependency>

        <dependency>
            <groupId>com.zsmall.extend.shop</groupId>
            <artifactId>zsmall-shop-amazon-job</artifactId>
        </dependency>

        <dependency>
            <groupId>com.zsmall.extend.payment</groupId>
            <artifactId>zsmall-payment-payoneer</artifactId>
        </dependency>
        <dependency>
            <groupId>com.zsmall.extend.shop</groupId>
            <artifactId>zsmall-shop-tiktok-job</artifactId>
            <version>1.0.0</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.hengjian</groupId>
            <artifactId>hengjian-stream-mq</artifactId>
        </dependency>
        <dependency>
            <groupId>com.hengjian</groupId>
            <artifactId>hengjian-common-easyes</artifactId>
        </dependency>
    </dependencies>

    <build>
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>${spring-boot.version}</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
                <version>${maven-jar-plugin.version}</version>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-war-plugin</artifactId>
                <version>${maven-war-plugin.version}</version>
                <configuration>
                    <failOnMissingWebXml>false</failOnMissingWebXml>
                    <warName>${project.artifactId}</warName>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
