package com.zsmall.xxl.job.jobHandler;

import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 测试任务
 *
 * <AUTHOR>
 * @date 2023/6/25
 */
//@Slf4j
//@Service
//@RequiredArgsConstructor
//public class TestJob {
//
//    @XxlJob("test1")
//    public void test1() {
//        log.info("111111111111111111111111111111111");
//    }
//
//}
