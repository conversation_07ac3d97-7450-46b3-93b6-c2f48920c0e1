package com.zsmall.xxl.job.factory.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hengjian.common.core.exception.RStatusCodeException;
import com.hengjian.common.tenant.helper.TenantHelper;
import com.xxl.job.core.context.XxlJobHelper;
import com.zsmall.common.domain.resp.tiktok.search.SearchProductResp;
import com.zsmall.common.domain.resp.tiktok.search.TikTokRespForProduct;
import com.zsmall.common.domain.tiktok.domain.dto.extend.resp.TikTokSyncOrderSearchResp;
import com.zsmall.common.domain.tiktok.domain.dto.req.TikTokSyncOrderSearchReq;
import com.zsmall.common.domain.vo.SearchProductVO;
import com.zsmall.common.domain.vo.XxlJobSearchVO;
import com.zsmall.common.enums.common.ChannelTypeEnum;
import com.zsmall.extend.shop.tiktok.job.entity.constant.UpdateStrategyConstant;
import com.zsmall.extend.shop.tiktok.job.support.TiktokOrderSupport;
import com.zsmall.order.biz.support.OrderSupport;
import com.zsmall.order.entity.domain.Orders;
import com.zsmall.order.entity.domain.ThirdChannelFulfillmentRecord;
import com.zsmall.order.entity.iservice.*;
import com.zsmall.product.entity.domain.ProductMapping;
import com.zsmall.product.entity.iservice.IProductMappingService;
import com.zsmall.system.entity.domain.TenantSalesChannel;
import com.zsmall.system.entity.iservice.ITenantSalesChannelService;
import com.zsmall.xxl.job.factory.ThirdChannelService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2024/1/31 11:38
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TikTokImpl implements ThirdChannelService {
    private final IProductMappingService iProductMappingService;
    private final ITenantSalesChannelService iTenantSalesChannelService;
    private final OrderCodeGenerator orderCodeGenerator;
    private final IOrdersService iOrdersService;
    private final IOrderLogisticsInfoService iOrderLogisticsInfoService;
    private final IOrderAddressInfoService iOrderAddressInfoService;
    private final IOrderItemService iOrderItemService;
    private final IOrderItemProductSkuService iOrderItemProductSkuService;
    private final IOrderItemPriceService iOrderItemPriceService;
    private final IThirdChannelFulfillmentRecordService iThirdChannelFulfillmentRecordService;
    private final OrderSupport orderSupport;





    @Resource
    private TiktokOrderSupport tiktokOrderSupport;

    @Override
    public void afterPropertiesSet() throws Exception {

    }

    @Override
    public void pushProduct(List<ProductMapping> mappingList) {

    }

    @Override
    public void updateProduct(List<ProductMapping> mappingList) {

    }

    @Override
    public void cancelProduct(List<ProductMapping> mappingList) {

    }

    @Override
    public void deleteProduct(List<ProductMapping> mappingList) {

    }

    @Override
    public void pullOrder(String startDate, String endDate) {


    }

    private SearchProductVO getSearchBodyForGetOrders(SearchProductVO body) {
//        body.setBuyerUserid();
//        body.setCreateTimeGe();
//        body.setCreateTimeLt();
//        body.setBuyerRequestCancel(Boolean.FALSE);
//        body.setOrderStatus();
//        body.setShippingType();
//        body.setUpdateTimeGe();
//        body.setUpdateTimeLt();
        return null;
    }

    @Override
    public void pushFulfillment(List<ThirdChannelFulfillmentRecord> fulfillmentRecordList) {

    }

    @Override
    public void updateStock(List<ProductMapping> mappingList, Integer stockTotal) {

    }

    @Override
    public void pullOrderByJson(String voJson, String startDate, String endDate) {
        // json格式 voJson
        // 先全量 再增量 ---后续 假设每24小时拉动一次 拉取的时间跨度是  数据库内最后一条到当前的时间,查完后拿最小时间的记录的id组 去重
        // 或者根据官网提供的 next_page_token 要保留最后一次作业的next_page_token
//        log.info("TikTok定时任务【拉取订单】 - 起始时间 = {} 截止时间 = {}", startDate, endDate);
//        List<TenantSalesChannel> channelList = iTenantSalesChannelService.queryValidByChannelTypeNotTenant(ChannelTypeEnum.TikTok);
//        log.info("TikTok定时任务【拉取订单】 - 有效店铺数量 = {}", CollUtil.size(channelList));
//        for (TenantSalesChannel tenantSalesChannel : channelList) {
//            XxlJobSearchVO vo = JSONObject.parseObject(voJson, XxlJobSearchVO.class);
//            vo.setTenantId(tenantSalesChannel.getTenantId());
//            vo.setThirdChannelFlag(tenantSalesChannel.getThirdChannelFlag());
//            vo.setShopId(String.valueOf(tenantSalesChannel.getId()));
//
//            String updateStrategy = XxlJobHelper.getJobParam();
//            // 全量更新,循环调用接口直到next_token 为null时,停止调用
//            if (UpdateStrategyConstant.ALL.equals(updateStrategy)) {
//                // 全量更新
//                /**
//                 * 全量更新
//                 * 1.调用获取接口,size设置为100,直到返回值的next_token为null时停止调用
//                 * 2.根据数据量的多少,性能不足时可以放入队列消化
//                 * **/
//            }
//            String nextPageToken = null;
//            int i = 0;
//            if (UpdateStrategyConstant.INCREMENT.equals(vo.getUpdateStrategy())) {
//
//                // 增量 需要时间跨度和 page_token
//
//                while (true) {
//
//                    if (i != 0 && StringUtils.isNotBlank(nextPageToken)) {
//                        vo.setPageToken(nextPageToken);
//                    }
//
//                    TikTokSyncOrderSearchResp resp = (TikTokSyncOrderSearchResp) tiktokOrderSupport.getAllOrder(vo,TikTokSyncOrderSearchResp.class);
//                    // 拿到resp做数据拆解 组装 放入list
//
//                    if (StringUtils.isBlank(resp.getNextPageToken())) {
//                        // 当前流程为最后一组
//                        break;
//                    }
//                    // curd next_token 使用上一组的
//                    nextPageToken = resp.getNextPageToken();
//                    i++;
//                }
//
//            }
//        }
        // 更新该渠道下所有店铺的

    }

    @Override
    public void pullProduct(String voJson) {
        // json格式 voJson
        // 先全量 再增量 ---后续 假设每24小时拉动一次 拉取的时间跨度是  数据库内最后一条到当前的时间,查完后拿最小时间的记录的id组 去重
        // 或者根据官网提供的 next_page_token 要保留最后一次作业的next_page_token

//        List<TenantSalesChannel> channelList = iTenantSalesChannelService.queryValidByChannelTypeNotTenant(ChannelTypeEnum.TikTok);
//        List<TenantSalesChannel> channelList = TenantHelper.ignore(()->iTenantSalesChannelService.list(new LambdaQueryWrapper<TenantSalesChannel>().eq(TenantSalesChannel::getTenantId,"DDZ1HGM").eq(TenantSalesChannel::getChannelType,ChannelTypeEnum.TikTok.name())));
//        log.info("TikTok定时任务【拉取产品】 - 有效店铺数量 = {}", CollUtil.size(channelList));
//        for (TenantSalesChannel tenantSalesChannel : channelList) {
//            XxlJobSearchVO vo = JSONObject.parseObject(voJson, XxlJobSearchVO.class);
//            vo.setTenantId(tenantSalesChannel.getTenantId());
//            vo.setThirdChannelFlag(tenantSalesChannel.getThirdChannelFlag());
//            vo.setShopId(String.valueOf(tenantSalesChannel.getId()));
////            String updateStrategy = vo.getUpdateStrategy();
//
//            // 全量更新,循环调用接口直到next_token 为null时,停止调用
////            if (UpdateStrategyConstant.ALL.equals(updateStrategy)) {
//
//                // 全量和增量更新的区别只和有关任务的配置参数有关
//                /**
//                 * 全量更新
//                 * 1.调用获取接口,size设置为100,直到返回值的next_token为null时停止调用
//                 * 2.根据数据量的多少,性能不足时可以放入队列消化
//                 * **/
////            }
//            String nextPageToken = null;
//            int i = 0;
//            if (UpdateStrategyConstant.INCREMENT.equals(vo.getUpdateStrategy())) {
//
//                while (true) {
//
//                    if (i != 0 && StringUtils.isNotBlank(nextPageToken)) {
//                        vo.setPageToken(nextPageToken);
//                    }
//
//                    TikTokRespForProduct resp = (TikTokRespForProduct) tiktokOrderSupport.getAllProduct(vo,TikTokRespForProduct.class);
//                    SearchProductResp body = resp.getData();
//                    // 拿到resp做数据拆解 组装 放入list
//
//                    if (StringUtils.isBlank(body.getNextPageToken())) {
//                        // 当前流程为最后一组
//                        break;
//                    }
//                    // curd next_token 使用上一组的
//                    nextPageToken = body.getNextPageToken();
//                    i++;
//                }
//
//            }
//        }
    }


    /**
     * 将Orders JSON响应信息转化为实体类
     *
     * @param channelOrderList
     * @param channel
     * @return
     */
    private List<Orders> ordersBodyToEntity(List<TikTokSyncOrderSearchReq> channelOrderList,
                                            TenantSalesChannel channel) throws RStatusCodeException {
        List<Orders> orderList = new ArrayList<>();
//        Long channelId = channel.getId();
//        String tenantId = channel.getTenantId();
//
//        for (TikTokSyncOrderSearchReq channelOrder : channelOrderList) {
//            Date createTime = channelOrder.getCreateTime();
//            String channelOrderNo = channelOrder.getTiktokOrderId();
//            String channelOrderName = channelOrder.getTiktokOrderId();
//
//            Orders order = iOrdersService.queryValidOrder(channelId, tenantId, channelOrderNo.toString());
//            if (order == null) {  // 数据库中没有存过该订单，准备创建实体类
//                order = new Orders();
//                String orderNo = orderCodeGenerator.codeGenerate(BusinessCodeEnum.OrderNo);
//                order.setOrderNo(orderNo);
//                order.setTenantId(tenantId);
//                order.setOrderType(OrderType.Normal);
//                order.setChannelType(ChannelTypeEnum.TikTok);
//                order.setLogisticsType(LogisticsTypeEnum.DropShipping);
//                order.setChannelId(channelId);
//                order.setChannelOrderNo(channelOrderNo);
//                order.setChannelOrderName(channelOrderName);
//                order.setChannelOrderTime(createTime);
//
//                // 物流信息
//                OrderLogisticsInfo orderLogisticsInfo = new OrderLogisticsInfo();
//                // Shopify目前都是代发
//                // orderLogisticsInfo.setOrderId(orderId);
//                orderLogisticsInfo.setOrderNo(orderNo);
//                orderLogisticsInfo.setLogisticsType(LogisticsTypeEnum.DropShipping);
//
//                // 收货地址
//                OrderAddressInfo orderAddressInfo = new OrderAddressInfo();
//                List<TiktokSyncOrderAddress> amznSyncOrderAddresses = tiktokOrderSupport.queryOrderAddressByAmazonOrderId(channelOrderNo);
//                if (CollUtil.isNotEmpty(amznSyncOrderAddresses)) {
//                    TiktokSyncOrderAddress tiktokSyncOrderAddress = amznSyncOrderAddresses.get(0);
//                    Address shippingAddress = tiktokSyncOrderAddress.getShippingAddress();
//
//                    String zip = shippingAddress.getPostalCode();
//                    String countryCode = shippingAddress.getCountryCode();
//
//                    orderAddressInfo.setOrderNo(orderNo);
//                    orderAddressInfo.setAddressType(OrderAddressType.ShipAddress);
//                    orderAddressInfo.setRecipient(shippingAddress.getName());
//                    orderAddressInfo.setPhoneNumber(shippingAddress.getPhone());
//                    orderAddressInfo.setCountry(countryCode);
//                    orderAddressInfo.setCountryCode(countryCode);
//                    orderAddressInfo.setState(shippingAddress.getStateOrRegion());
//                    orderAddressInfo.setStateCode(shippingAddress.getStateOrRegion());
//                    orderAddressInfo.setCity(shippingAddress.getCity());
//                    orderAddressInfo.setAddress1(shippingAddress.getAddressLine1());
//                    orderAddressInfo.setAddress2(shippingAddress.getAddressLine2());
//                    orderAddressInfo.setZipCode(zip);
//
//                    orderLogisticsInfo.setZipCode(StrUtil.trim(zip));
//                    orderLogisticsInfo.setLogisticsZipCode(StrUtil.trim(zip));
//
//                    if (StrUtil.contains(zip, "-")) {
//                        // 存在-的邮编，需要分割出前面5位的主邮编
//                        String mainZipCode = StrUtil.trim(StrUtil.split(zip, "-").get(0));
//                        orderLogisticsInfo.setLogisticsZipCode(StrUtil.trim(mainZipCode));
//                    }
//                    orderLogisticsInfo.setLogisticsCountryCode(countryCode);
//                }
//
//
//                List<TiktokSyncOrderItem> amznSyncOrderItems = tiktokOrderSupport.queryOrderItemByAmazonOrderId(channelOrderNo);
//                List<OrderItem> orderItemList = new ArrayList<>();
//                List<OrderItemPrice> orderItemPriceList = new ArrayList<>();
//
//                LocaleMessage localeMessage = new LocaleMessage();
//                Integer totalQuantity = 0;
//
//                if (CollUtil.isEmpty(amznSyncOrderItems)) {
//                    // 没有子订单、跳过当前订单
//                    continue;
//                } else {
//                    for (TiktokSyncOrderItem channelOrderItem : amznSyncOrderItems) {
//                        Long itemId = channelOrderItem.getId();
//                        String tiktokOrderItemId = channelOrderItem.getOrderItemId();
//                        String mappingSku = channelOrderItem.getSellerSku();
//                        Integer quantity = channelOrderItem.getQuantityOrdered();
//                        totalQuantity += quantity;
//
//                        ProductMapping productMapping = iProductMappingService.queryByTenantAndMappingSku(tenantId, channelId, mappingSku);
//                        if (productMapping != null) {
//                            GenerateOrderItemEvent generateOrderItemEvent = new GenerateOrderItemEvent();
//                            generateOrderItemEvent.setDTenantId(tenantId);
//                            generateOrderItemEvent.setOrder(order);
//                            generateOrderItemEvent.setChannelTypeEnum(ChannelTypeEnum.TikTok);
//                            generateOrderItemEvent.setLogisticsType(LogisticsTypeEnum.DropShipping);
//                            generateOrderItemEvent.setCountry(orderAddressInfo.getCountryCode());
//                            generateOrderItemEvent.setActivityCode(productMapping.getActivityCode());
//                            generateOrderItemEvent.setProductSkuCode(productMapping.getProductSkuCode());
//                            generateOrderItemEvent.setTotalQuantity(quantity);
//                            SpringUtils.context().publishEvent(generateOrderItemEvent);
//
//                            OrderItemDTO outDTO = generateOrderItemEvent.getOutDTO();
//                            LocaleMessage message = outDTO.getLocaleMessage();
//                            if (message.hasData()) {
//                                localeMessage.append(message);
//                            }
//
//                            OrderItem orderItem = outDTO.getOrderItem();
//                            OrderItemPrice orderItemPrice = outDTO.getOrderItemPrice();
//                            OrderItemProductSku orderItemProductSku = outDTO.getOrderItemProductSku();
//
//                            orderItem.setChannelItemNo(StrUtil.toStringOrNull(itemId));
//                            orderItem.setTenantId(tenantId);
//                            orderItemProductSku.setTenantId(tenantId);
//
//                            orderItem.setOrderItemPrice(orderItemPrice);
//                            orderItem.setOrderItemProductSku(orderItemProductSku);
//
//                            orderItemList.add(orderItem);
//                            orderItemPriceList.add(orderItemPrice);
//                        } else {
//                            log.info("TikTok订单 {} 不存在映射SKU {}", channelOrderNo, mappingSku);
//                        }
//                    }
//                }
//
//
//                log.info("TikTok订单 {} 有效的子订单数量 {}", channelOrderNo, CollUtil.size(orderItemList));
//                if (CollUtil.isNotEmpty(orderItemList)) {
//                    order.setTotalQuantity(totalQuantity);
//
//                    RecalculateOrderAmountEvent recalculateOrderAmountEvent = new RecalculateOrderAmountEvent();
//                    recalculateOrderAmountEvent.setOrder(order);
//                    recalculateOrderAmountEvent.setOrderItemPriceList(orderItemPriceList);
//                    SpringUtils.publishEvent(recalculateOrderAmountEvent);
//
//                    iOrdersService.save(order);
//                    Long orderId = order.getId();
//
//                    iOrderLogisticsInfoService.save(orderLogisticsInfo.setOrderId(orderId));
//
//                    if (orderAddressInfo.getOrderNo() != null) {
//                        iOrderAddressInfoService.save(orderAddressInfo.setOrderId(orderId));
//                    }
//
//                    for (OrderItem orderItem : orderItemList) {
//                        iOrderItemService.save(orderItem.setOrderId(orderId));
//                        Long orderItemId = orderItem.getId();
//
//                        OrderItemPrice orderItemPrice = orderItem.getOrderItemPrice();
//                        OrderItemProductSku orderItemProductSku = orderItem.getOrderItemProductSku();
//
//                        iOrderItemPriceService.save(orderItemPrice.setOrderItemId(orderItemId));
//                        iOrderItemProductSkuService.save(orderItemProductSku.setOrderItemId(orderItemId));
//                    }
//                    orderList.add(order);
//                }
//            }
//        }
        // 0 0/15 * * * ? 每1小时执行一次
        return orderList;
    }

}





