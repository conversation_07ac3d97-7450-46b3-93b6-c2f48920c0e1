package com.zsmall.xxl.job.jobHandler;

import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.zsmall.xxl.job.service.PayoneerJobService;
import groovy.util.logging.Slf4j;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * payoneer定时任务-执行器
 * <AUTHOR>
 * @date 2023/6/30 10:11
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PayoneerJob {

    private final PayoneerJobService payoneerJobService;

    /**
     * 修改payoneer交易记录并添加钱包
     */
    @XxlJob("updatePayoneerTransactionsStatus")
    public void updatePayoneerTransactionsState() {
        payoneerJobService.updatePayoneerTransactionsState();
    }

    /**
     * 刷新token
     */
    @XxlJob("refreshPayoneerToken")
    public void refreshPayoneerToken() {
        // 获取参数
        String advanceDay = XxlJobHelper.getJobParam();
        payoneerJobService.refreshPayoneerToken(advanceDay);
    }

    /**
     * 刷新token，在数据库表中记录
     */
    @XxlJob("refreshPayoneerTokenRecord")
    public void refreshPayoneerTokenRecord() {
        payoneerJobService.refreshPayoneerTokenRecord();
    }
}
