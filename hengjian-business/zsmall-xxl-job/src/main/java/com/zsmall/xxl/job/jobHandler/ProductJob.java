package com.zsmall.xxl.job.jobHandler;

import com.xxl.job.core.handler.annotation.XxlJob;
import com.zsmall.xxl.job.service.ProductJobService;
import groovy.util.logging.Slf4j;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * 商品定时任务-执行器
 *
 * <AUTHOR>
 * @date 2023/6/25
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ProductJob {
    private final ProductJobService productJobService;

    /**
     * 定时变更价格
     */
    @XxlJob("regularPriceChange")
    public void regularPriceChange() {
        productJobService.regularPriceChange();
    }



}
