package com.zsmall.xxl.job.service.impl;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.ContentType;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.hengjian.common.core.utils.StringUtils;
import com.hengjian.common.tenant.helper.TenantHelper;
import com.ijpay.core.IJPayHttpResponse;
import com.ijpay.payoneer.PayoneerApiConfig;
import com.ijpay.payoneer.PayoneerApiConfigKit;
import com.ijpay.payoneer.enums.PayoneerApiUrl;
import com.ijpay.payoneer.exception.IJPayPayoneerException;
import com.ijpay.payoneer.model.out.PaymentCommitModel;
import com.ijpay.payoneer.model.out.PaymentStatusModel;
import com.xxl.job.core.context.XxlJobHelper;
import com.zsmall.common.enums.transaction.ReceiptReviewStateEnum;
import com.zsmall.common.enums.transaction.TransactionMethodEnum;
import com.zsmall.common.enums.transaction.TransactionStateEnum;
import com.zsmall.extend.payment.config.properties.PayoneerProperties;
import com.zsmall.extend.payment.support.PayoneerSupport;
import com.zsmall.system.biz.service.IPayonnerRefreshTokenRecordService;
import com.zsmall.system.biz.service.TenantPayoneerService;
import com.zsmall.system.entity.domain.PayonnerRefreshTokenRecord;
import com.zsmall.system.entity.domain.TenantPayoneer;
import com.zsmall.system.entity.domain.TransactionReceipt;
import com.zsmall.system.entity.domain.TransactionRecord;
import com.zsmall.system.entity.iservice.ITenantPayoneerService;
import com.zsmall.system.entity.iservice.ITransactionReceiptService;
import com.zsmall.system.entity.iservice.ITransactionRecordService;
import com.zsmall.xxl.job.service.PayoneerJobService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestParam;

import java.nio.charset.StandardCharsets;
import java.util.*;

import static com.ijpay.payoneer.PayoneerApi.post;

/**
 * <AUTHOR>
 * @date 2023/6/30 10:00
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PayoneerJobServiceImpl implements PayoneerJobService {

    private final ITenantPayoneerService iTenantPayoneerService;
    private final ITransactionReceiptService iTransactionReceiptService;
    private final ITransactionRecordService iTransactionRecordService;
    private final PayoneerSupport payoneerSupport;
    private final TenantPayoneerService tenantPayoneerService;
    private final PayoneerProperties payoneerProperties;
    private final IPayonnerRefreshTokenRecordService iPayonnerRefreshTokenRecordService;

    /**
     * 修改payoneer交易记录并添加钱包
     */
    @Override
    public void updatePayoneerTransactionsState() {
        try {
            DateTime dateTime = DateUtil.offsetMinute(new Date(), payoneerProperties.getPaymentExpireOffset());
            log.info("updatePayoneerTransactionsState dateTime = {}", dateTime);
            List<TransactionReceipt> receiptList = iTransactionReceiptService.findListByTypeAndStateAndDate(TransactionMethodEnum.OnlinePayoneer, ReceiptReviewStateEnum.Pending, dateTime);
            if (CollectionUtils.isEmpty(receiptList)) {
                return;
            }
            List<TransactionReceipt> rejectedReceipts = new ArrayList<>();
            List<TransactionRecord> failureRecords = new ArrayList<>();
            log.info("开始循环操作交易记录信息！");
            receiptList.forEach(receipt -> {
                String accountId = receipt.getAccountId();
                boolean existPayoneerAccount = iTenantPayoneerService.isExistPayoneerAccount(accountId);
                if (!existPayoneerAccount) {
                    return;
                }
                String clientReferenceId = receipt.getTransactionReceiptNo();
                Long transactionsId = receipt.getTransactionsId();
                TransactionRecord transactionRecord = iTransactionRecordService.findByTransactionIdWithNotTenant(transactionsId);
                try {
                    PaymentStatusModel paymentStatus = payoneerSupport.getPaymentByAccountIdAndClientReferenceId(accountId, clientReferenceId);
                    //payoneer支付失败记录
                    if (paymentStatus == null || paymentStatus.getStatus() != 2) {
                        log.info("进入添加payoneer支付失败记录信息！");
                        receipt.setReviewState(ReceiptReviewStateEnum.Rejected);
                        transactionRecord.setTransactionState(TransactionStateEnum.Failure);
                        failureRecords.add(transactionRecord);
                        rejectedReceipts.add(receipt);
                    } else {//payoneer支付成功
                        log.info("进入payoneer处理钱包余额相关操作！");
                        // payoneer处理钱包余额相关操作
                        PaymentCommitModel<?> paymentCommitModel = payoneerSupport.paymentCommit(accountId, paymentStatus.getPaymentId());
                        tenantPayoneerService.reconfirmRechargeAndRecordTransaction(transactionRecord, accountId, paymentCommitModel.getSuccessResult());
                    }
                } catch (IJPayPayoneerException ijException) {
                    log.error("IJPayPayoneerException  code = {},error = {},ErrorDescription = {}",
                        ijException.getCode(), ijException.getError(), ijException.getErrorDescription());
                    receipt.setReviewState(ReceiptReviewStateEnum.Rejected);
                    transactionRecord.setTransactionState(TransactionStateEnum.Failure);
                    transactionRecord.setFailureReason(JSONUtil.parseObj(ijException.getErrorDescription()));
                    rejectedReceipts.add(receipt);
                    failureRecords.add(transactionRecord);
                } catch (Exception e) {
                    // 更新交易记录
                    if (transactionRecord != null) {
                        transactionRecord.setTransactionState(TransactionStateEnum.Failure);
                        JSONObject message = JSONUtil.createObj().putOnce("message", e.getMessage());
                        transactionRecord.setFailureReason(message);
                        iTransactionRecordService.updateById(transactionRecord);
                    }
                    log.error("Payoneer Deposit = Exception = {}", e.getMessage(), e);
                }
            });
            if (CollectionUtils.isNotEmpty(rejectedReceipts)) {
                log.info("进入修改payoneer支付失败回执单记录操作！");
                TenantHelper.ignore(() -> iTransactionReceiptService.saveOrUpdateBatch(rejectedReceipts));
            }
            if (CollectionUtils.isNotEmpty(failureRecords)) {
                log.info("进入修改payoneer支付失败交易记录操作！");
                TenantHelper.ignore(() -> iTransactionRecordService.saveOrUpdateBatch(failureRecords));
            }
        } catch (Exception e) {
            XxlJobHelper.log("updatePayoneerTransactionsState error {}", e.getMessage(), e);
            log.error("updatePayoneerTransactionsState error = {}", e.getMessage(), e);
        }
    }

    /**
     * 刷新token
     */
    @Override
    public void refreshPayoneerToken(String advanceDayString) {
        try {
            Integer advanceDay = null;
            if(StrUtil.isNotBlank(advanceDayString)) {
                advanceDay = Integer.parseInt(advanceDayString);
            }

            // 先判断客户端token是否过期，如果即将过期，立即刷新token
            payoneerSupport.refreshClientToken(advanceDay);

            // 获取现有的UserPayoneer列表，逐个校验token，并发起刷新
            List<TenantPayoneer> allPayoneers = iTenantPayoneerService.findAllPayoneers();
            log.info("allPayoneers size = {}", CollUtil.size(allPayoneers));
            Set<String> uniqueAccountIds = new HashSet<>();
            for(TenantPayoneer userPayoneer: allPayoneers) {
                String accountId = userPayoneer.getAccountId();
                if(uniqueAccountIds.contains(accountId)) {
                    XxlJobHelper.log("accountId = {} is processed.", accountId);
                    log.info("accountId = {} is processed.", accountId);
                    continue;
                }

                boolean isOk = payoneerSupport.refreshAccessToken(advanceDay, accountId);
                XxlJobHelper.log("accountId = {}, isOk = {}", accountId, isOk);
                log.info("accountId = {}, isOk = {}", accountId, isOk);
                if(isOk) {
                    uniqueAccountIds.add(accountId);
                }
            }
        } catch (Exception e) {
            XxlJobHelper.log("refreshPayoneerToken error {}", e.getMessage(), e);
            log.error("refreshPayoneerToken error = {}", e.getMessage(), e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void refreshPayoneerTokenRecord() {
        try {
            // 获取现有的UserPayoneer列表，逐个校验token，并发起刷新
            List<TenantPayoneer> allPayoneers = iTenantPayoneerService.findAllPayoneers();
            log.info("payonner get access token allPayoneers size = {}", CollUtil.size(allPayoneers));
            for(TenantPayoneer userPayoneer: allPayoneers) {
                String accountId = userPayoneer.getAccountId();
                String refreshToken = null;
                PayonnerRefreshTokenRecord byAccountId = iPayonnerRefreshTokenRecordService.getByAccountId(accountId);
                if(null != byAccountId && StringUtils.isNotEmpty(byAccountId.getRefreshToken())){
                    refreshToken = byAccountId.getRefreshToken();
                }
                if(StringUtils.isEmpty(refreshToken)){
                    log.info("payonner get access token accountId = {} refreshToken is null", accountId);
                   continue;
                }
                byAccountId.setIsRefresh(2);
                iPayonnerRefreshTokenRecordService.saveOrUpdate(byAccountId);
                final PayoneerApiConfig apiConfig = PayoneerApiConfigKit.getApiConfig();
                Map<String, String> headers = new HashMap(3);
                headers.put("Accept", ContentType.JSON.toString());
                headers.put("Content-Type", ContentType.FORM_URLENCODED.toString());
                headers.put("Authorization", "Basic ".concat(Base64.encode(apiConfig.getClientKey().concat(":").concat(apiConfig.getClientSecret()).getBytes(StandardCharsets.UTF_8))));
                log.debug("payonner get access token headers = {}", JSONUtil.toJsonStr(headers));
                Map<String, Object> params = new HashMap(3);
                params.put("grant_type", "refresh_token");
                params.put("refresh_token", refreshToken);
                String url = (apiConfig.isSandBox() ? PayoneerApiUrl.SANDBOX_OAUTH2_GATEWAY.getUrl() : PayoneerApiUrl.LIVE_OAUTH2_GATEWAY.getUrl()).concat(PayoneerApiUrl.GET_TOKEN.getUrl());
                IJPayHttpResponse httpResponse = post(url, params, headers);
                log.debug("payonner get access token httpResponse => {}", httpResponse.toString());
                if(null != httpResponse && 200 == httpResponse.getStatus()){
                    if(StringUtils.isNotEmpty(httpResponse.getBody())){
                        JSONObject jsonObject = JSONUtil.parseObj(httpResponse.getBody());
                        PayonnerRefreshTokenRecord payonnerRefreshTokenRecord = new PayonnerRefreshTokenRecord();
                        payonnerRefreshTokenRecord.setAccountId(accountId).setTokenType(jsonObject.getStr("token_type")).setAccessToken(jsonObject.getStr("access_token"))
                            .setScope(jsonObject.getStr("scope")).setRefreshToken(jsonObject.getStr("refresh_token")).setConsentedOn(jsonObject.getLong("consented_on"))
                            .setExpiresIn(jsonObject.getLong("expires_in")).setRefreshTokenExpiresIn(jsonObject.getLong("refresh_token_expires_in"));
                        iPayonnerRefreshTokenRecordService.saveOrUpdate(payonnerRefreshTokenRecord);
                    }
                }
            }
        } catch (Exception e) {
            log.error("refreshPayoneerToken error = {}", e.getMessage(), e);
        }
    }


}
