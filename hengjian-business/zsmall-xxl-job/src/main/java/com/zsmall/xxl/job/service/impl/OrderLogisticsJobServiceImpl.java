package com.zsmall.xxl.job.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.hengjian.common.log.annotation.InMethodLog;
import com.hengjian.common.tenant.helper.TenantHelper;
import com.xxl.job.core.context.XxlJobHelper;
import com.zsmall.common.enums.BusinessParameterType;
import com.zsmall.common.enums.order.LogisticsProgress;
import com.zsmall.common.enums.order.OrderStateType;
import com.zsmall.common.enums.orderShippingRecord.ShippingStateEnum;
import com.zsmall.common.enums.warehouse.WarehouseTypeEnum;
import com.zsmall.common.service.BusinessParameterService;
import com.zsmall.order.biz.support.OrderSupport;
import com.zsmall.order.entity.domain.OrderItem;
import com.zsmall.order.entity.domain.OrderItemShippingRecord;
import com.zsmall.order.entity.domain.OrderItemTrackingRecord;
import com.zsmall.order.entity.iservice.IOrderItemService;
import com.zsmall.order.entity.iservice.IOrderItemShippingRecordService;
import com.zsmall.order.entity.iservice.IOrderItemTrackingRecordService;
import com.zsmall.system.biz.support.BillSupport;
import com.zsmall.warehouse.entity.domain.event.ThirdWarehouseEvent;
import com.zsmall.xxl.job.service.OrderLogisticsJobService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/7/6 11:46
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class OrderLogisticsJobServiceImpl implements OrderLogisticsJobService {

    private final BusinessParameterService businessParameterService;
    private final IOrderItemService iOrderItemService;
    private final IOrderItemTrackingRecordService iOrderItemTrackingRecordService;
    private final IOrderItemShippingRecordService iOrderItemShippingRecordService;
    private final OrderSupport orderSupport;
    private final BillSupport billSupport;

    @InMethodLog(value = "定时完结已发货订单")
    @Override
    public void completeDisPatchedOrderJob() {

        // 需要定时器遍历已发货达14天 + 物流状态已送达的订单，改成已完成（已履约）
        String string = businessParameterService.getValueFromString(BusinessParameterType.COMPLETE_DISPATCHED_ORDER_DAYS);
        JSONObject daysObject = JSONUtil.parseObj(string);
        // 发货至少相隔14天确认收货
        Integer _14Days = daysObject.getInt("minDays");
        // 一直未查询到，至少相隔40天才能确认收货
        Integer _40Days = daysObject.getInt("maxDays");
        log.info("completeDisPatchedOrderJob, _14Days = {}, _40Days = {}", _14Days, _40Days);
        XxlJobHelper.log("completeDisPatchedOrderJob, _14Days = {}, _40Days = {}", _14Days, _40Days);

        Set<Long> orderIds = new HashSet<>();
        Set<Long> orderItemIds = new HashSet<>();

        Date now = new Date();
        Date _14Date = DateUtil.offsetDay(now, -_14Days);
        Date _40Date = DateUtil.offsetDay(now, -_40Days);
        log.info("completeDisPatchedOrderJob, _14Date = {}, _40Date = {}", _14Date, _40Date);
        XxlJobHelper.log("completeDisPatchedOrderJob, _14Date = {}, _40Date = {}", _14Date, _40Date);

        // 查询14天-40天以内，正常收货的订单
        List<OrderItem> orderItems = iOrderItemService.getAllTimeRangeDispatchedItems(OrderStateType.Paid, LogisticsProgress.Dispatched, _14Date, _40Date);
        log.info("completeDisPatchedOrderJob, 1 - orderItems.size = {}", CollUtil.size(orderItems));
        XxlJobHelper.log("completeDisPatchedOrderJob, 1 - orderItems.size = {}", CollUtil.size(orderItems));

        List<OrderItem> saveOrderItems = new ArrayList<>();
        for (OrderItem orderItem : orderItems) {
            Long orderItemId = orderItem.getId();
            Long orderId = orderItem.getOrderId();
            String orderItemNo = orderItem.getOrderItemNo();
            List<OrderItemTrackingRecord> trackingRecordList = iOrderItemTrackingRecordService.getListByOrderItemNo(orderItemNo);
            if (CollUtil.isEmpty(trackingRecordList)) {
                continue;
            }
            Set<LogisticsProgress> fulfillmentTypes = trackingRecordList.stream().map(OrderItemTrackingRecord::getLogisticsProgress).collect(Collectors.toSet());
            LogisticsProgress complexType = LogisticsProgress.getComplexType(fulfillmentTypes);

            log.info("completeDisPatchedOrderJob - orderItemNo = {}, complexType = {}", orderItemNo, complexType);
            XxlJobHelper.log("completeDisPatchedOrderJob - orderItemNo = {}, complexType = {}", orderItemNo, complexType);
            if (ObjectUtil.equals(complexType, LogisticsProgress.Fulfilled)) {
                orderItem.setFulfillmentProgress(LogisticsProgress.Fulfilled);
                orderItem.setFulfillmentTime(new Date());
                orderIds.add(orderId);
                orderItemIds.add(orderItemId);
                saveOrderItems.add(orderItem);
            }
        }

        // 查询大于40天的非正常订单
        List<OrderItem> fulfillOrderItems =
            iOrderItemService.getAllUnDispatchedItems(OrderStateType.Paid, LogisticsProgress.Dispatched, _40Date);

        log.info("completeDisPatchedOrderJob, 2 - orderItems.size = {}", CollUtil.size(fulfillOrderItems));
        XxlJobHelper.log("completeDisPatchedOrderJob, 2 - orderItems.size = {}", CollUtil.size(fulfillOrderItems));
        for (OrderItem fulfillOrderItem : fulfillOrderItems) {
            Long orderItemId = fulfillOrderItem.getId();
            if (orderItemIds.contains(orderItemId)) {
                continue;
            }

            Long orderId = fulfillOrderItem.getOrderId();
            fulfillOrderItem.setFulfillmentProgress(LogisticsProgress.Fulfilled);
            fulfillOrderItem.setFulfillmentTime(new Date());
            orderIds.add(orderId);
            saveOrderItems.add(fulfillOrderItem);
        }

        TenantHelper.ignore(() -> {
            iOrderItemService.saveOrUpdateBatch(saveOrderItems);
        });
        // 记账用DTO
        billSupport.generateBillDTOByOrderItem(saveOrderItems, null);

        if (CollUtil.isNotEmpty(orderIds)) {
            // 批量更新主订单的物流状态
            orderSupport.setOrderFulfillmentProgress(orderIds);
        }
    }

    /**
     * 定时更新子订单出货单
     */
    @Override
    public void updateShippingRecordJob() {
        List<ShippingStateEnum> shippingStateEnums = ShippingStateEnum.systemManaged();

        WarehouseTypeEnum[] warehouseTypes = WarehouseTypeEnum.values();
        for (WarehouseTypeEnum warehouseType : warehouseTypes) {
            List<OrderItemShippingRecord> shippingRecordList = iOrderItemShippingRecordService.getListBySystemManaged(warehouseType, shippingStateEnums, true);
            List<String> shippingNoList = shippingRecordList.stream().map(OrderItemShippingRecord::getShippingNo).collect(Collectors.toList());
            log.info("出货单编号 = {}", JSONUtil.toJsonStr(shippingNoList));
            if (CollUtil.isNotEmpty(shippingNoList)) {
                ThirdWarehouseEvent.queryOrder(warehouseType.toStockManager(), shippingNoList);
            }
        }
    }

}
