package com.zsmall.xxl.job.jobHandler;

import com.xxl.job.core.handler.annotation.XxlJob;
import com.zsmall.xxl.job.service.BlogJobService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @date 2023/7/8 10:13
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class BlogJob {

    private final BlogJobService blogJobService;


    /**
     * 定时发布博客
     */
    @Transactional
    @XxlJob("timingPublishBlog")
    public void timingPublishBlog() {
        blogJobService.timingPublishBlog();
    }


}
