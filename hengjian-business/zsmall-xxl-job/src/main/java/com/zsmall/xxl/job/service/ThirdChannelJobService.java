package com.zsmall.xxl.job.service;

/**
 * 第三方渠道定时任务-业务层接口
 *
 * <AUTHOR>
 * @date 2023/6/25
 */
public interface ThirdChannelJobService {

    void timedEvent();

    void pushProduct(String channel);

    void updateProduct();

    void cancelProduct();

    void deleteProduct();

    void pullOrder(String channel, String startDate, String endDate);
    void pullOrder(String channel, String startDate, String endDate,String voJson);

    void pushFulfillment(String channel);

    void updateStock();

    void pullProduct(String name, String o);
}
