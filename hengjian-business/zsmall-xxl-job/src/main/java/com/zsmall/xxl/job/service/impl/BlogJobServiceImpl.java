package com.zsmall.xxl.job.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.hengjian.common.log.annotation.InMethodLog;
import com.zsmall.common.enums.BlogStatusEnum;
import com.zsmall.system.biz.service.BlogService;
import com.zsmall.system.entity.domain.BlogArticleEntity;
import com.zsmall.xxl.job.service.BlogJobService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.joda.time.DateTime;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/7/8 10:16
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class BlogJobServiceImpl implements BlogJobService {

    private final BlogService blogService;

    @InMethodLog(value = "定时发布博客")
    @Override
    public void timingPublishBlog() {

        DateTime now = DateTime.now();
        log.info("timingPublishBlog 执行时间 {}", now);

        LambdaQueryWrapper<BlogArticleEntity> queryWrapper = new LambdaQueryWrapper<>();

        queryWrapper.eq(BlogArticleEntity::getBlogStatus, BlogStatusEnum.Draft.name())
            .isNotNull(BlogArticleEntity::getPublishDate).le(BlogArticleEntity::getPublishDate, LocalDateTime.now());

        List<BlogArticleEntity> list = blogService.list(queryWrapper);
        if (CollectionUtils.isNotEmpty(list)) {
            List<Long> idList = list.stream().map(BlogArticleEntity::getId).collect(Collectors.toList());
            LambdaUpdateWrapper<BlogArticleEntity> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.set(BlogArticleEntity::getBlogStatus, BlogStatusEnum.Published.name())
                .in(BlogArticleEntity::getId, idList);
            boolean update = blogService.update(updateWrapper);
            log.info("passShippingReturn update = {}", update);
        }
        log.info("timingPublishBlog 结束时间 {}", now);
    }




}
