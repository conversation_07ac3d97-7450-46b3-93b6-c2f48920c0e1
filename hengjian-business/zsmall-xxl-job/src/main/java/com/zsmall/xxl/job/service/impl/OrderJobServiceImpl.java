package com.zsmall.xxl.job.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONUtil;
import com.hengjian.common.log.annotation.InMethodLog;
import com.hengjian.common.tenant.helper.TenantHelper;
import com.xxl.job.core.context.XxlJobHelper;
import com.zsmall.common.enums.BusinessParameterType;
import com.zsmall.common.enums.order.OrderStateType;
import com.zsmall.common.service.BusinessParameterService;
import com.zsmall.order.biz.support.OrderSupport;
import com.zsmall.order.entity.domain.Orders;
import com.zsmall.order.entity.iservice.IOrdersService;
import com.zsmall.xxl.job.service.OrderJobService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/7/6 11:08
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class OrderJobServiceImpl implements OrderJobService {

    private final IOrdersService iOrdersService;
    private final OrderSupport orderSupport;
    private final BusinessParameterService businessParameterService;


    @InMethodLog(value = "取消订单任务")
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void cancelOrders() {
        // 该时间后未支付订单会被取消
        Integer intervalHour = businessParameterService.getValueFromInteger(BusinessParameterType.CANCEL_ORDERS_INTERVAL);
        Date date = DateUtil.offsetHour(new Date(), -Integer.valueOf(intervalHour));
        log.info("date:{}", date);
        XxlJobHelper.log("取消订单任务 - date:{}", date);

        List<OrderStateType> orderStateTypes = new ArrayList<>();
        orderStateTypes.add(OrderStateType.UnPaid);
        orderStateTypes.add(OrderStateType.Failed);
        log.info("orderStateTypes:{}", JSONUtil.toJsonStr(orderStateTypes));
        XxlJobHelper.log("取消订单任务 - orderStateTypes:{}", JSONUtil.toJsonStr(orderStateTypes));

        List<Orders> orders = iOrdersService.getOrdersByDate(date, orderStateTypes);

        if (CollUtil.isNotEmpty(orders)) {
            for (Orders order : orders) {
                //重新查询订单，并改为Pending
                List<Orders> orders1 = orderSupport.orderLockToPending(order.getTenantId(), CollUtil.newArrayList(order));
                log.info("orderNo: {}", order.getOrderNo());
                XxlJobHelper.log("取消订单任务 - orderNo: {}", order.getOrderNo());
                orders1.forEach(orders2 -> orders2.setOrderState(OrderStateType.Canceled));
                TenantHelper.ignore(() -> iOrdersService.updateBatchById(orders1));
            }
        }
    }
}
