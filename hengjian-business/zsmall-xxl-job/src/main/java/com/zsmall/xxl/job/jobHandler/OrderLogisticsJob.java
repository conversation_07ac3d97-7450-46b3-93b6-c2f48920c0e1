package com.zsmall.xxl.job.jobHandler;

import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.zsmall.xxl.job.service.OrderLogisticsJobService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 订单物流相关 定时任务
 * <AUTHOR>
 * @date 2023/7/6 11:34
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class OrderLogisticsJob {

    private final OrderLogisticsJobService orderLogisticsJobService;

    /**
     * 定时完结已发货订单
     */
    @XxlJob("completeDisPatchedOrderJob")
    public void completeDisPatchedOrderJob() throws Exception {
        log.info("OrderLogisticsJob => completeDisPatchedOrderJob start.");
        XxlJobHelper.log("OrderLogisticsJob => completeDisPatchedOrderJob start.");
        orderLogisticsJobService.completeDisPatchedOrderJob();
        log.info("OrderLogisticsJob => completeDisPatchedOrderJob end.");
        XxlJobHelper.log("OrderLogisticsJob => completeDisPatchedOrderJob end.");
    }

    /**
     * 定时更新子订单出货单
     */
    @XxlJob("updateShippingRecordJob")
    public void updateShippingRecordJob() throws Exception {
        orderLogisticsJobService.updateShippingRecordJob();
    }

}
