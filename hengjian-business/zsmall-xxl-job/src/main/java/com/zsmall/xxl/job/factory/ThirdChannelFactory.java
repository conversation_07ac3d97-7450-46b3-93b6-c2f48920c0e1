package com.zsmall.xxl.job.factory;

import com.zsmall.common.enums.common.ChannelTypeEnum;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * 第三方渠道-工厂类
 *
 * <AUTHOR>
 * @date 2023/6/25
 */
@Component
public class ThirdChannelFactory {

    public ThirdChannelService getService(ChannelTypeEnum channelType) {
        return map.get(channelType);
    }

    private static Map<ChannelTypeEnum, ThirdChannelService> map = new HashMap<>();

    public static void register(ChannelTypeEnum channelType, ThirdChannelService thirdChannelService) throws Exception {
        if (channelType == null || thirdChannelService == null) {
            throw new Exception("第三方渠道工厂 - 未找到对应实例注册");
        }
        map.put(channelType, thirdChannelService);
    }

}
