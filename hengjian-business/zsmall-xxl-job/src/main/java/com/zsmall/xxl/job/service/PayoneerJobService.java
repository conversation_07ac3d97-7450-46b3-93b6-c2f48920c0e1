package com.zsmall.xxl.job.service;

/**
 * 商品定时任务-业务层接口
 *
 * <AUTHOR>
 * @date 2023/6/28 15:53
 */
public interface PayoneerJobService {


    /**
     * 修改payoneer交易记录并添加钱包
     */
    void updatePayoneerTransactionsState();

    /**
     * 刷新token
     * @param advanceDay 提前天数
     */
    void refreshPayoneerToken(String advanceDay);

    /**
     * 刷新token记录
     */
    void refreshPayoneerTokenRecord();

}
