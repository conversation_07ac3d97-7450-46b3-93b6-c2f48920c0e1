package com.zsmall.xxl.job.jobHandler;

import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.zsmall.common.enums.common.ChannelTypeEnum;
import com.zsmall.xxl.job.service.ThirdChannelJobService;
import groovy.util.logging.Slf4j;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * 第三方渠道定时任务-执行器
 *
 * <AUTHOR>
 * @date 2023/6/25
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ThirdChannelJob {

    private final ThirdChannelJobService thirdChannelJobService;

    /**
     * 渠道定时事件
     */
    @XxlJob("timedEvent")
    public void timedEvent() {
        thirdChannelJobService.timedEvent();
    }

    /**
     * 推送商品
     */
    @XxlJob("pushProduct")
    public void pushProduct() {
        String channel = XxlJobHelper.getJobParam();
        thirdChannelJobService.pushProduct(channel);
    }

    /**
     * 更新商品
     */
    @XxlJob("updateProduct")
    public void updateProduct() {
        thirdChannelJobService.updateProduct();
    }

    /**
     * 取消同步商品
     */
    @XxlJob("cancelProduct")
    public void cancelProduct() {
        thirdChannelJobService.cancelProduct();
    }

    /**
     * 删除商品
     */
    @XxlJob("deleteProduct")
    public void deleteProduct() {
        thirdChannelJobService.deleteProduct();
    }

    /**
     * 拉取订单
     */
    @XxlJob("pullOrder")
    public void pullOrder() {
        String channel = XxlJobHelper.getJobParam();
        thirdChannelJobService.pullOrder(channel, null, null);
    }

//    /**
//     * 拉取订单
//     */
//    @XxlJob("pullOrderForTiktok")
//    public void pullOrderForTiktok() {
//        // 可能channel找不到 往下看 到时候要测试
//        String voJson = XxlJobHelper.getJobParam();
//        thirdChannelJobService.pullOrder(ChannelTypeEnum.TikTok.name(), null, null,voJson);
//    }

    /**
     * 推送履约信息至渠道店铺
     */
    @XxlJob("pushFulfillment")
    public void pushFulfillment() {
        String channel = XxlJobHelper.getJobParam();
        thirdChannelJobService.pushFulfillment(channel);
    }

    /**
     * 更新库存
     */
    @XxlJob("updateStock")
    public void updateStock() {
        thirdChannelJobService.updateStock();
    }


}
