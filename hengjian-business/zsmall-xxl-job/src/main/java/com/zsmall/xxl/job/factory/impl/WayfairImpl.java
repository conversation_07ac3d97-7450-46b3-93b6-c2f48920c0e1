package com.zsmall.xxl.job.factory.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.hengjian.common.core.exception.RStatusCodeException;
import com.hengjian.common.core.utils.SpringUtils;
import com.zsmall.common.domain.LocaleMessage;
import com.zsmall.common.enums.BusinessCodeEnum;
import com.zsmall.common.enums.common.ChannelTypeEnum;
import com.zsmall.common.enums.order.LogisticsTypeEnum;
import com.zsmall.common.enums.order.OrderAddressType;
import com.zsmall.common.enums.order.OrderType;
import com.zsmall.common.support.GlobalBusinessSupport;
import com.zsmall.extend.utils.ZSMallSystemEventUtils;
import com.zsmall.extend.wayfair.enums.ShipSpeed;
import com.zsmall.extend.wayfair.enums.SortOrder;
import com.zsmall.extend.wayfair.kit.WayfairDelegate;
import com.zsmall.extend.wayfair.kit.WayfairKit;
import com.zsmall.extend.wayfair.model.WayfairBean;
import com.zsmall.extend.wayfair.model.common.Address;
import com.zsmall.extend.wayfair.model.common.TransactionStatus;
import com.zsmall.extend.wayfair.model.inventory.Warehouse;
import com.zsmall.extend.wayfair.model.order.InAcceptOrderInput;
import com.zsmall.extend.wayfair.model.order.InOrderQuery;
import com.zsmall.extend.wayfair.model.order.OutPurchaseOrder;
import com.zsmall.extend.wayfair.model.product.PurchaseProduct;
import com.zsmall.extend.wayfair.model.shipping.AcceptInput;
import com.zsmall.extend.wayfair.model.shipping.PurchaseOrderShipping;
import com.zsmall.order.biz.support.OrderSupport;
import com.zsmall.order.entity.domain.*;
import com.zsmall.order.entity.domain.dto.OrderItemDTO;
import com.zsmall.order.entity.domain.event.GenerateOrderItemEvent;
import com.zsmall.order.entity.domain.event.RecalculateOrderAmountEvent;
import com.zsmall.order.entity.iservice.*;
import com.zsmall.product.entity.domain.ProductMapping;
import com.zsmall.product.entity.iservice.*;
import com.zsmall.system.entity.domain.TenantSalesChannel;
import com.zsmall.system.entity.iservice.ITenantSalesChannelService;
import com.zsmall.xxl.job.factory.ThirdChannelFactory;
import com.zsmall.xxl.job.factory.ThirdChannelService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * Wayfair相关业务实现
 *
 * <AUTHOR>
 * @date 2023/6/28
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class WayfairImpl implements ThirdChannelService {

    private final IProductService iProductService;
    private final IProductSkuService iProductSkuService;
    private final IProductSkuPriceService iProductSkuPriceService;
    private final IProductSkuDetailService iProductSkuDetailService;
    private final IProductSkuAttributeService iProductSkuAttributeService;
    private final IProductSkuAttachmentService iProductSkuAttachmentService;
    private final IProductAttributeService iProductAttributeService;
    private final IProductCategoryService iProductCategoryService;
    private final IProductMappingService iProductMappingService;
    private final ITenantSalesChannelService iTenantSalesChannelService;

    private final OrderCodeGenerator orderCodeGenerator;
    private final IOrdersService iOrdersService;
    private final IOrderLogisticsInfoService iOrderLogisticsInfoService;
    private final IOrderAddressInfoService iOrderAddressInfoService;
    private final IOrderItemService iOrderItemService;
    private final IOrderItemProductSkuService iOrderItemProductSkuService;
    private final IOrderItemPriceService iOrderItemPriceService;

    private final GlobalBusinessSupport globalBusinessSupport;
    private final OrderSupport orderSupport;

    @Override
    public void afterPropertiesSet() throws Exception {
        ThirdChannelFactory.register(ChannelTypeEnum.Wayfair, this);
    }


    /**
     * 推送商品至渠道店铺
     *
     * @param mappingList
     */
    @Override
    public void pushProduct(List<ProductMapping> mappingList) {

    }

    /**
     * 更新商品
     *
     * @param mappingList
     */
    @Override
    public void updateProduct(List<ProductMapping> mappingList) {

    }

    /**
     * 取消同步商品
     *
     * @param mappingList
     */
    @Override
    public void cancelProduct(List<ProductMapping> mappingList) {

    }

    /**
     * 删除商品
     *
     * @param mappingList
     */
    @Override
    public void deleteProduct(List<ProductMapping> mappingList) {
        log.info("Wayfair定时任务 - 【删除商品】 商品数量 = {}", CollUtil.size(mappingList));
        iProductMappingService.removeBatchByIds(mappingList);
    }

    /**
     * 从渠道店铺拉取订单
     *
     * @param startDate
     * @param endDate
     */
    @Override
    public void pullOrder(String startDate, String endDate) {
        List<TenantSalesChannel> channelList = iTenantSalesChannelService.queryValidByChannelTypeNotTenant(ChannelTypeEnum.Wayfair);
        for (TenantSalesChannel salesChannel : channelList) {
            String channelName = salesChannel.getChannelName();
            String tenantId = salesChannel.getTenantId();

            try {
                WayfairBean bean = new WayfairBean(true, salesChannel.getPrivateKey(), salesChannel.getClientSecret());
                WayfairDelegate wayfairDelegate = WayfairKit.create(bean);

                // 减去72小时作为起始时间
                LocalDateTime endLocalDate = LocalDateTime.now();
                // 减去72小时作为起始时间
                LocalDateTime startLocalDate = endLocalDate.minusDays(3);
                String startLocalDateString = LocalDateTimeUtil.format(startLocalDate, "yyyy-MM-dd HH:mm:ss");
                InOrderQuery inOrderQuery = new InOrderQuery();
                inOrderQuery.setFromDate(startLocalDateString);
                inOrderQuery.setSortOrder(SortOrder.DESC);
                inOrderQuery.setLimit(20);
                // inOrderQuery.setHasResponse(false);

                List<OutPurchaseOrder> orders = wayfairDelegate.orderApi().queryDropShipOrders(inOrderQuery);

                List<Orders> ordersList = ordersBodyToEntity(orders, salesChannel);
                if (CollUtil.isNotEmpty(ordersList)) {
                    Boolean autoPayment = ZSMallSystemEventUtils.checkAutoPaymentEvent(tenantId);
                    if (autoPayment) {
                        try {
                            orderSupport.orderPayChain(tenantId, ordersList, true, true);
                        } catch (RStatusCodeException e) {
                            log.error("Wayfair店铺[{}]，订单自动支付失败，原因 {}", channelName, e.getMessage(), e);
                        }
                    }
                }

            } catch (Exception e) {
                log.info("Wayfair店铺[{}]，拉取订单失败，原因 {}", channelName, e.getMessage(), e);
            }
        }
    }

    /**
     * 推送履约信息至渠道店铺
     *
     * @param fulfillmentRecordList
     */
    @Override
    public void pushFulfillment(List<ThirdChannelFulfillmentRecord> fulfillmentRecordList) {

    }

    /**
     * 更新库存
     *
     * @param mappingList
     */
    @Override
    public void updateStock(List<ProductMapping> mappingList, Integer stockTotal) {

    }

    @Override
    public void pullOrderByJson(String voJson, String startDate, String endDate) {

    }

    @Override
    public void pullProduct(String json) {

    }

    /**
     * 将Orders JSON响应信息转化为实体类
     *
     * @param wayfairOrderList
     * @param channel
     * @return
     */
    private List<Orders> ordersBodyToEntity(List<OutPurchaseOrder> wayfairOrderList, TenantSalesChannel channel) throws RStatusCodeException {

        List<Orders> orderList = new ArrayList<>();
        Long channelId = channel.getId();
        String tenantId = channel.getTenantId();
        Map<String, String> carrierSwitchMap = globalBusinessSupport.getCarrierSwitchMap();

        for (OutPurchaseOrder wayfairOrder : wayfairOrderList) {
            String wayfairOrderName = wayfairOrder.getPoNumber();

            Orders order = iOrdersService.queryValidOrder(channelId, tenantId, wayfairOrderName);

            if (order == null) {  // 数据库中没有存过该订单，准备创建实体类
                order = new Orders();
                String orderNo = orderCodeGenerator.codeGenerate(BusinessCodeEnum.OrderNo);
                order.setOrderNo(orderNo);
                order.setTenantId(tenantId);
                order.setOrderType(OrderType.Normal);
                order.setChannelType(ChannelTypeEnum.Wayfair);
                // Wayfair都是自提
                order.setLogisticsType(LogisticsTypeEnum.PickUp);
                order.setChannelId(channelId);
                order.setChannelOrderNo(wayfairOrderName);
                order.setChannelOrderName(wayfairOrderName);
                order.setChannelOrderTime(DateUtil.parseDateTime(wayfairOrder.getPoDate()));

                // 物流信息
                OrderLogisticsInfo orderLogisticsInfo = new OrderLogisticsInfo();
                // Wayfair都是自提
                orderLogisticsInfo.setOrderNo(orderNo);
                orderLogisticsInfo.setLogisticsType(LogisticsTypeEnum.PickUp);

                // 收货地址
                Address shipTo = wayfairOrder.getShipTo();
                OrderAddressInfo orderAddressInfo = new OrderAddressInfo();
                if (shipTo != null) {
                    String zip = shipTo.getPostalCode();
                    String countryCode = shipTo.getCountry();

                    orderAddressInfo.setOrderNo(orderNo);
                    orderAddressInfo.setAddressType(OrderAddressType.ShipAddress);
                    orderAddressInfo.setRecipient(shipTo.getName());
                    orderAddressInfo.setPhoneNumber(shipTo.getPhoneNumber());
                    orderAddressInfo.setCountry(countryCode);
                    orderAddressInfo.setCountryCode(countryCode);
                    orderAddressInfo.setState(shipTo.getState());
                    orderAddressInfo.setStateCode(shipTo.getState());
                    orderAddressInfo.setCity(shipTo.getCity());
                    orderAddressInfo.setAddress1(shipTo.getAddress1());
                    orderAddressInfo.setAddress2(shipTo.getAddress2());
                    orderAddressInfo.setZipCode(zip);

                    orderLogisticsInfo.setZipCode(StrUtil.trim(zip));
                    orderLogisticsInfo.setLogisticsZipCode(StrUtil.trim(zip));

                    if (StrUtil.contains(zip, "-")) {
                        // 存在-的邮编，需要分割出前面5位的主邮编
                        String mainZipCode = StrUtil.trim(StrUtil.split(zip, "-").get(0));
                        orderLogisticsInfo.setLogisticsZipCode(StrUtil.trim(mainZipCode));
                    }
                    orderLogisticsInfo.setLogisticsCountryCode(countryCode);
                }

                // 账单地址
                Address billTo = wayfairOrder.getBillTo();
                OrderAddressInfo orderBillingInfo = new OrderAddressInfo();
                if (billTo != null) {
                    orderBillingInfo.setOrderNo(orderNo);
                    orderBillingInfo.setAddressType(OrderAddressType.BillAddress);
                    orderBillingInfo.setRecipient(billTo.getName());
                    orderBillingInfo.setPhoneNumber(billTo.getPhoneNumber());
                    orderBillingInfo.setCountry(billTo.getCountry());
                    orderBillingInfo.setCountryCode(billTo.getCountry());
                    orderBillingInfo.setState(billTo.getState());
                    orderBillingInfo.setStateCode(billTo.getState());
                    orderBillingInfo.setCity(billTo.getCity());
                    orderBillingInfo.setAddress1(billTo.getAddress1());
                    orderBillingInfo.setAddress2(billTo.getAddress2());
                    orderBillingInfo.setZipCode(billTo.getPostalCode());
                }

                // 物流公司
                PurchaseOrderShipping shippingInfo = wayfairOrder.getShippingInfo();
                if (shippingInfo != null) {
                    String carrierCode = shippingInfo.getCarrierCode();
                    if (carrierSwitchMap.get(carrierCode) != null) {
                        carrierCode = carrierSwitchMap.get(carrierCode);
                    }
                    orderLogisticsInfo.setLogisticsCompanyName(carrierCode);
                    orderLogisticsInfo.setLogisticsServiceName(shippingInfo.getCarrierCode());
                }

                // Wayfair指定仓库
                Warehouse warehouse = wayfairOrder.getWarehouse();

                List<PurchaseProduct> products = wayfairOrder.getProducts();
                List<OrderItem> orderItemList = new ArrayList<>();
                List<OrderItemPrice> orderItemPriceList = new ArrayList<>();

                LocaleMessage localeMessage = new LocaleMessage();
                Integer totalQuantity = 0;
                for (PurchaseProduct product : products) {
                    String mappingSku = product.getPartNumber();
                    Integer quantity = NumberUtil.parseInt(product.getQuantity());
                    if (quantity == 0) {
                        continue;
                    }
                    totalQuantity += quantity;

                    ProductMapping productMapping = iProductMappingService.queryByTenantAndMappingSku(tenantId, channelId, mappingSku);
                    if (productMapping != null) {
                        GenerateOrderItemEvent generateOrderItemEvent = new GenerateOrderItemEvent();
                        generateOrderItemEvent.setDTenantId(tenantId);
                        generateOrderItemEvent.setOrder(order);
                        generateOrderItemEvent.setChannelTypeEnum(ChannelTypeEnum.Wayfair);
                        generateOrderItemEvent.setLogisticsType(LogisticsTypeEnum.PickUp);
                        generateOrderItemEvent.setCountry(orderAddressInfo.getCountryCode());
                        generateOrderItemEvent.setActivityCode(productMapping.getActivityCode());
                        generateOrderItemEvent.setProductSkuCode(productMapping.getProductSkuCode());
                        generateOrderItemEvent.setTotalQuantity(quantity);
                        SpringUtils.publishEvent(generateOrderItemEvent);

                        OrderItemDTO outDTO = generateOrderItemEvent.getOutDTO();
                        LocaleMessage message = outDTO.getLocaleMessage();
                        if (message.hasData()) {
                            localeMessage.append(message);
                        }

                        OrderItem orderItem = outDTO.getOrderItem();
                        OrderItemPrice orderItemPrice = outDTO.getOrderItemPrice();
                        OrderItemProductSku orderItemProductSku = outDTO.getOrderItemProductSku();

                        orderItem.setTenantId(tenantId);
                        orderItemProductSku.setTenantId(tenantId);

                        if (warehouse != null && warehouse.getId() != null) {
                            Long warehouseId = warehouse.getId();
                            orderItemProductSku.setChannelWarehouseCode(warehouseId.toString());
                        }

                        orderItem.setOrderItemPrice(orderItemPrice);
                        orderItem.setOrderItemProductSku(orderItemProductSku);

                        orderItemList.add(orderItem);
                        orderItemPriceList.add(orderItemPrice);
                    } else {
                        log.info("Wayfair订单 {} 不存在映射SKU {}", wayfairOrderName, mappingSku);
                    }
                }

                log.info("Wayfair订单 {} 有效的子订单数量 {}", wayfairOrderName, CollUtil.size(orderItemList));
                if (CollUtil.isNotEmpty(orderItemList)) {
                    order.setTotalQuantity(totalQuantity);

                    RecalculateOrderAmountEvent recalculateOrderAmountEvent = new RecalculateOrderAmountEvent();
                    recalculateOrderAmountEvent.setOrder(order);
                    recalculateOrderAmountEvent.setOrderItemPriceList(orderItemPriceList);
                    SpringUtils.publishEvent(recalculateOrderAmountEvent);

                    iOrdersService.save(order);
                    Long orderId = order.getId();

                    iOrderLogisticsInfoService.save(orderLogisticsInfo.setOrderId(orderId));

                    if (orderAddressInfo.getOrderNo() != null) {
                        iOrderAddressInfoService.save(orderAddressInfo.setOrderId(orderId));
                    }

                    if (orderBillingInfo.getOrderNo() != null) {
                        iOrderAddressInfoService.save(orderBillingInfo.setOrderId(orderId));
                    }

                    for (OrderItem orderItem : orderItemList) {
                        iOrderItemService.save(orderItem.setOrderId(orderId));
                        Long orderItemId = orderItem.getId();

                        OrderItemPrice orderItemPrice = orderItem.getOrderItemPrice();
                        OrderItemProductSku orderItemProductSku = orderItem.getOrderItemProductSku();

                        iOrderItemPriceService.save(orderItemPrice.setOrderItemId(orderItemId));
                        iOrderItemProductSkuService.save(orderItemProductSku.setOrderItemId(orderItemId));
                    }

                    // 调用Wayfair接收订单
                    // this.acceptOrder(wayfairSdk, poNumber, purchaseOrder);

                    orderList.add(order);
                }
            }
        }
        return orderList;
    }

    /**
     * 请求Wayfair告知已接受订单
     * @param channelOrderNo
     * @param originPurchaseOrder
     */
    private void acceptOrder(WayfairDelegate wayfairDelegate, String channelOrderNo, OutPurchaseOrder originPurchaseOrder) {
        try {
            // 请求Wayfair告知已接受订单
            PurchaseOrderShipping originShippingInfo = originPurchaseOrder.getShippingInfo();
            ShipSpeed shipSpeed = originShippingInfo.getShipSpeed();
            List<PurchaseProduct> originProducts = originPurchaseOrder.getProducts();

            InAcceptOrderInput<AcceptInput> inAcceptOrderInput = new InAcceptOrderInput<>();
            inAcceptOrderInput.setPoNumber(channelOrderNo);
            inAcceptOrderInput.setShipSpeed(shipSpeed);
            List<AcceptInput> lineItems = new ArrayList<>();
            for (PurchaseProduct originProduct : originProducts) {
                AcceptInput acceptInput = new AcceptInput();
                acceptInput.setPartNumber(originProduct.getPartNumber());
                acceptInput.setQuantity(Integer.parseInt(originProduct.getQuantity()));
                acceptInput.setUnitPrice(originProduct.getPrice());
                acceptInput.setEstimatedShipDate(DateUtil.now());
                lineItems.add(acceptInput);
            }
            inAcceptOrderInput.setLineItems(lineItems);
            log.info("Wayfair接收订单请求 inAcceptOrderInput = {}", JSONUtil.toJsonStr(inAcceptOrderInput));
            TransactionStatus transactionStatus = wayfairDelegate.orderApi().acceptOrder(inAcceptOrderInput);
            log.info("Wayfair接收订单请求 transactionStatus = {}", JSONUtil.toJsonStr(transactionStatus));
        } catch (Exception e) {
            log.error("Wayfair接收订单请求错误，原因 {}", e.getMessage(), e);
        }
    }

}
