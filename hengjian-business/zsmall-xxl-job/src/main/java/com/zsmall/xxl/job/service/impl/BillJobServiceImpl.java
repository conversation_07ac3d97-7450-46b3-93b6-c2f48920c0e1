package com.zsmall.xxl.job.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.hengjian.common.core.enums.TenantType;
import com.hengjian.common.log.annotation.InMethodLog;
import com.hengjian.extend.utils.SystemEventUtils;
import com.hengjian.system.domain.vo.SysOssVo;
import com.hengjian.system.domain.vo.SysTenantVo;
import com.itextpdf.text.pdf.BaseFont;
import com.zsmall.common.enums.bill.*;
import com.zsmall.common.properties.PdfProperties;
import com.zsmall.common.util.DecimalUtil;
import com.zsmall.extend.pdf.dto.GeneratePDFDTO;
import com.zsmall.extend.pdf.util.FreeMarkerUtil;
import com.zsmall.extend.utils.ZSMallSystemEventUtils;
import com.zsmall.system.entity.domain.*;
import com.zsmall.system.entity.domain.vo.bill.BillAbstractVo;
import com.zsmall.system.entity.iservice.*;
import com.zsmall.xxl.job.service.BillJobService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.io.File;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 账单相关定时任务-业务实现层
 *
 * <AUTHOR>
 * @date 2023/7/11
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class BillJobServiceImpl implements BillJobService {

    private final IBillService iBillService;
    private final IBillLogService iBillLogService;
    private final IBillAbstractService iBillAbstractService;
    private final IBillAbstractDetailService iBillAbstractDetailService;
    private final IBillRelationService iBillRelationService;
    private final IBillRelationDetailService iBillRelationDetailService;

    private final PdfProperties pdfProperties;

    /**
     * 生成下一期账单任务
     */
    @Override
    public void generateNextBillJob() {
        List<SysTenantVo> tenantList = SystemEventUtils.queryTenantsByTenantType(TenantType.Supplier);
        log.info("【生成下一期账单任务】 供货商数量 = {}", CollUtil.size(tenantList));
        ZSMallSystemEventUtils.generateBill(tenantList);
    }

    /**
     * 账单结算任务
     */
    @Override
    public void billSettleJob() {
        DateTime now = DateTime.now();
        log.info("账单结算 当前时间 = {}", now);
        String billCycleNo = now.toString("yyyyMMdd");
        log.info("账单结算 当前时间 = {}, 当期周期编号 = {}", now, billCycleNo);

        List<Bill> billList = iBillService.queryByBillCycleNoAndBillStatus(billCycleNo, BillStateEnum.Unsettled);
        log.info("账单结算 当前时间 = {}, 当期周期编号 = {} 需要结算的账单数量 = {}", now, billCycleNo, CollUtil.size(billList));
        if (CollUtil.isNotEmpty(billList)) {

            List<BillLog> billLogList = new ArrayList<>();
            for (Bill billEntity : billList) {
                Long billId = billEntity.getId();
                String tenantId = billEntity.getTenantId();

                // 上期账单id
                Long previousBillId = billEntity.getPreviousBillId();
                WithdrawalStateEnum withdrawalState = billEntity.getWithdrawalState();

                try {
                    BigDecimal currentTotalAmount = billEntity.getCurrentTotalAmount();
                    Double settleTotal = currentTotalAmount.doubleValue();
                    log.info("账单结算 tenantId = {}, billId = {}, currentTotalAmount = {}", tenantId, billId, currentTotalAmount);

                    Bill previousBill = iBillService.getById(previousBillId);
                    BigDecimal previous_CurrentCircularDeposit = BigDecimal.ZERO;
                    if (previousBill != null) {
                        // 上期账单记录的循环保证金
                        previous_CurrentCircularDeposit = previousBill.getCurrentCircularDeposit();
                    }

                    // 本期准备结算的账单记录的上期循环保证金
                    BigDecimal now_previousCircularDeposit = billEntity.getPreviousCircularDeposit();

                    log.info("billId = {}, 上期账单记录的循环保证金 = {}，本期准备结算的账单记录的上期循环保证金 = {}", billId, previous_CurrentCircularDeposit, now_previousCircularDeposit);
                    if (previous_CurrentCircularDeposit.compareTo(now_previousCircularDeposit) != 0) {
                        BillLog billLogEntity = BeanUtil.toBean(billEntity, BillLog.class);
                        billLogEntity.setId(null);
                        billLogEntity.setOriginBillId(billEntity.getId());
                        billLogList.add(billLogEntity);

                        // 上期账单记录的循环保证金 与 本期准备结算的账单记录的上期循环保证金 不想等，需要重新计算
                        BigDecimal currentIncome = billEntity.getCurrentIncome();
                        BigDecimal currentExpenditure = billEntity.getCurrentExpenditure();
                        BigDecimal now_currentCircularDeposit = billEntity.getCurrentCircularDeposit();

                        BigDecimal new_CurrentTotalAmount = NumberUtil.sub(currentIncome, currentExpenditure, now_currentCircularDeposit).add(previous_CurrentCircularDeposit);
                        billEntity.setCurrentTotalAmount(new_CurrentTotalAmount);
                        billEntity.setPreviousCircularDeposit(previous_CurrentCircularDeposit);
                    }

                    billEntity.setBillState(BillStateEnum.Settled);
                    iBillService.saveOrUpdate(billEntity);

                    if (CollUtil.isNotEmpty(billLogList)) {
                        iBillLogService.saveBatch(billLogList);
                    }
                } catch (Exception e) {
                    log.error("账单结算失败 tenantId = {}, billId = {} error {}", tenantId, billId, e.getMessage(), e);
                }
            }
        }
    }

    /**
     * 生成PDF任务（每次结算后生成）
     */
    @Override
    public void generateBillPdfJob() {
        Bill queryEntity = new Bill();
        queryEntity.setBillState(BillStateEnum.Settled);
        queryEntity.setGenerateState(GenerateStateEnum.NotGenerated);
        // 查询所有已结算但未生成的账单
        List<Bill> billList = iBillService.queryByEntity(queryEntity);

        log.info("生成PDF任务 billList = {}", JSONUtil.toJsonStr(billList));
        if (CollUtil.isNotEmpty(billList)) {
            File logoFile = new File(pdfProperties.getLogoPath());
            String logoFileType = "image/jpeg";
            String logoFile64Str = FreeMarkerUtil.fileToBase64Str(logoFile);

            for (Bill billEntity : billList) {
                billEntity.setGenerateState(GenerateStateEnum.Generating);
            }
            iBillService.updateBatchById(billList);

            for (Bill bill : billList) {
                String billNo = bill.getBillNo();
                log.info("账单{}，开始生成PDF", billNo);
                try {
                    this.generateBillPdf(bill, logoFileType, logoFile64Str);
                    bill.setGenerateState(GenerateStateEnum.Generated);
                } catch (Exception e) {
                    bill.setGenerateState(GenerateStateEnum.Failed);
                    log.error("账单{}，生成PDF出现异常 {}", billNo, e.getMessage(), e);
                }
                log.info("账单{}，生成PDF结束", billNo);
                iBillService.saveOrUpdate(bill);
            }
        }
    }

    /**
     * 账单Pdf补生成任务
     *
     * @param billNo
     */
    @Override
    @InMethodLog("账单PDF补生成任务")
    public void generateBillPdfAgainJob(String billNo) {
        if (StrUtil.isNotBlank(billNo)) {
            log.info("账单Pdf补生成 billNo = {}", billNo);
            Bill queryEntity = new Bill();
            queryEntity.setBillNo(billNo);
            List<Bill> billList = iBillService.queryByEntity(queryEntity);
            log.info("账单Pdf补生成 billList.size = {}", CollUtil.size(billList));
            if (CollUtil.isNotEmpty(billList)) {
                Bill bill = billList.get(0);
                bill.setGenerateState(GenerateStateEnum.Generating);
                iBillService.saveOrUpdate(bill);

                File logoFile = new File(pdfProperties.getLogoPath());
                String logoFileType = "image/jpeg";
                String logoFile64Str = FreeMarkerUtil.fileToBase64Str(logoFile);
                try {
                    this.generateBillPdf(bill, logoFileType, logoFile64Str);
                    bill.setGenerateState(GenerateStateEnum.Generated);
                } catch (Exception e) {
                    bill.setGenerateState(GenerateStateEnum.Failed);
                    log.error("账单{}，生成PDF出现异常 {}", billNo, e.getMessage(), e);
                }
                iBillService.saveOrUpdate(bill);
            }
        }
    }

    private void generateBillPdf(Bill bill, String logoFileType, String logoFile64Str)
        throws Exception {
        String billNo = bill.getBillNo();
        try {
            String cardNumber = null;

            // Long previousBillId = bill.getPreviousBillId();
            // 上期账单
            // BillEntity previousBill = iBillService.getById(previousBillId);

            LocalDateTime settlementCycleBegin = bill.getSettlementCycleBegin();
            LocalDateTime settlementCycleEnd = bill.getSettlementCycleEnd();
            BigDecimal currentIncome = bill.getCurrentIncome();
            BigDecimal currentExpenditure = bill.getCurrentExpenditure();
            BigDecimal previousCircularDeposit = bill.getPreviousCircularDeposit();
            BigDecimal currentCircularDeposit = bill.getCurrentCircularDeposit();
            BigDecimal currentTotalAmount = bill.getCurrentTotalAmount();

            String settlementCycleBegin_Str = LocalDateTimeUtil.formatNormal(settlementCycleBegin);
            String settlementCycleEnd_Str = LocalDateTimeUtil.formatNormal(settlementCycleEnd);

            String currentIncome_Str = DecimalUtil.bigDecimalToString(currentIncome);
            String currentExpenditure_Str = DecimalUtil.bigDecimalToString(currentExpenditure);
            String previousCircularDeposit_Str = DecimalUtil.bigDecimalToString(previousCircularDeposit);
            String currentCircularDeposit_Str = DecimalUtil.bigDecimalToString(currentCircularDeposit);
            String currentTotalAmount_Str = DecimalUtil.bigDecimalToString(currentTotalAmount);

            Long billId = bill.getId();

            Map<String, Object> data = new HashMap<>();

            data.put("fileType", logoFileType);
            data.put("file64Str", logoFile64Str);

            data.put("accountName", cardNumber);
            data.put("currentIncome", currentIncome_Str);
            data.put("currentExpenditure", currentExpenditure_Str);
            data.put("previousCircularDeposit", previousCircularDeposit_Str);
            data.put("currentCircularDeposit", currentCircularDeposit_Str);
            data.put("currentTotalAmount", currentTotalAmount_Str);
            data.put("billNo", billNo);
            data.put("settlementCycleBegin", settlementCycleBegin_Str);
            data.put("settlementCycleEnd", settlementCycleEnd_Str);

            // 构建摘要信息
            List<BillAbstract> abstractList = iBillAbstractService.queryByBillId(billId);
            for (BillAbstract billAbstractEntity : abstractList) {
                Long billAbstractId = billAbstractEntity.getId();
                AbstractTypeEnum abstractType = billAbstractEntity.getAbstractType();
                BillAbstractVo respBillAbstractBody = new BillAbstractVo();
                // 摘要字段详情
                List<BillAbstractDetail> abstractDetailList =
                    iBillAbstractDetailService.queryByBillAbstractId(billAbstractId);
                if (CollUtil.isNotEmpty(abstractDetailList)) {
                    for (BillAbstractDetail billAbstractDetailEntity : abstractDetailList) {
                        AbstractFieldTypeEnum fieldType = billAbstractDetailEntity.getFieldType();
                        BigDecimal fieldValue = billAbstractDetailEntity.getFieldValue();
                        respBillAbstractBody.addDetail(fieldType, DecimalUtil.bigDecimalToString(fieldValue));
                    }
                }
                data.put(abstractType + "AbstractList", respBillAbstractBody.getAbstractDetail());
            }

            // 构建分类详情信息
            List<String> relationTypeList = iBillRelationService.queryRelationTypeListByBillId(billId);
            if (CollUtil.isNotEmpty(relationTypeList)) {
                for (String relationType : relationTypeList) {
                    List<BillRelation> relationList =
                        iBillRelationService.queryListByBillIdAndRelationType(billId, relationType);
                    BigDecimal totalAmount =
                        iBillRelationService.queryRelationTypeTotalAmount(billId, relationType, RelationFieldTypeEnum.TotalAmount);

                    List<Map<String, String>> classDetailList = new ArrayList<>();
                    for (BillRelation billRelation : relationList) {
                        Long billRelationId = billRelation.getId();
                        List<BillRelationDetail> billRelationDetailList =
                            iBillRelationDetailService.queryByBillRelationId(billRelationId);
                        Map<String, String> classDetail = new HashMap<>();
                        for (BillRelationDetail billRelationDetail : billRelationDetailList) {
                            RelationFieldTypeEnum fieldTypeEnum = billRelationDetail.getFieldType();
                            String fieldValue = billRelationDetail.getFieldValue();
                            classDetail.put(fieldTypeEnum.getFieldName(), fieldValue);
                        }
                        classDetailList.add(classDetail);
                    }

                    data.put(relationType + "TotalAmount", DecimalUtil.bigDecimalToString(totalAmount));
                    data.put(relationType + "List", classDetailList);
                }
            }

            String fileName = billNo + ".pdf";
            String tempPath = pdfProperties.getTempPath();
            String tempFile = tempPath + File.separator + fileName;

            String templatePath = pdfProperties.getTemplatePath();
            List<String> fontPath = pdfProperties.getFontPath();
            String templateFileName = pdfProperties.getFileName();

            GeneratePDFDTO generatePDFDTO = new GeneratePDFDTO()
                .setDestinationPath(tempFile).setTemplatePath(templatePath)
                .setFileName(templateFileName).setData(data);

            if (CollUtil.isNotEmpty(fontPath)) {
                for (String font : fontPath) {
                    generatePDFDTO.addFont(font, BaseFont.IDENTITY_H, BaseFont.NOT_EMBEDDED);
                }
            }

            FreeMarkerUtil.generatePDFByBean(generatePDFDTO);
            if (FileUtil.exist(tempFile)) {
                File file = FileUtil.newFile(tempFile);
                String key = "billPdf" + File.separator + fileName;
                SysOssVo sysOssVo = SystemEventUtils.uploadFile(FileUtil.getInputStream(file), key);
                Long ossId = sysOssVo.getOssId();
                bill.setOssId(ossId);
                // FileUtil.del(file);
            }
        } catch (RuntimeException e) {
            log.error("账单 {} 生成PDF失败 {}", billNo, e.getMessage(), e);
        } catch (Exception e) {
            e.printStackTrace();
            log.error("账单 {} 生成PDF失败 {}", billNo, e.getMessage(), e);
            throw e;
        }
    }
}
