package com.zsmall.xxl.job.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import com.xxl.job.core.context.XxlJobHelper;
import com.zsmall.common.enums.common.ChannelTypeEnum;
import com.zsmall.extend.shop.amazon.job.entity.domain.bo.AmznOrderSyncAddBo;
import com.zsmall.extend.shop.amazon.job.support.AmazonOrderJobSupport;
import com.zsmall.system.entity.domain.TenantSalesChannel;
import com.zsmall.system.entity.iservice.ITenantSalesChannelService;
import com.zsmall.xxl.job.service.ThirdAmazonJobService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
public class ThirdAmazonJobServiceImpl implements ThirdAmazonJobService {

    private final ITenantSalesChannelService iTenantSalesChannelService;
    private final AmazonOrderJobSupport amazonOrderJobSupport;

    /**
     * 注册亚马逊订单
     *
     * @param endDateTime
     */
    @Override
    public void registerAmazonOrderSync(LocalDateTime endDateTime) {
        List<TenantSalesChannel> tenantSalesChannels = iTenantSalesChannelService.queryValidByChannelTypeNotTenant(ChannelTypeEnum.Amazon);
        log.info("TenantSalesChannel registerAmazonOrderSync size = {}", CollUtil.size(tenantSalesChannels));
        XxlJobHelper.log("TenantSalesChannel registerAmazonOrderSync size = {}", CollUtil.size(tenantSalesChannels));

        if(CollUtil.isEmpty(tenantSalesChannels)) {
            return;
        }
        AmznOrderSyncAddBo orderSyncAddBo = new AmznOrderSyncAddBo(endDateTime);
        List<AmznOrderSyncAddBo.OrderSync> orderSyncs = new ArrayList<>();

        // 同步订单信息
        AmznOrderSyncAddBo.OrderSync orderSync = null;
        for(TenantSalesChannel salesChannel: tenantSalesChannels) {
            String appId = salesChannel.getPrivateKey();
            String channelName = salesChannel.getChannelName();

            // 根据创建时间生成第一次开始时间
            Date createTime = salesChannel.getCreateTime();
            LocalDateTime startDateTime = LocalDateTimeUtil.of(createTime).minusDays(3);
            startDateTime = LocalDateTime.of(startDateTime.toLocalDate(), LocalTime.MIN);
            log.info("appId = {}, channelName = {}, createTime = {}, startDateTime = {}",
                channelName, createTime, startDateTime);


            orderSync = new AmznOrderSyncAddBo.OrderSync()
                .setAppId(appId)
                .setSellerId(channelName)
                .setRefreshToken(salesChannel.getClientSecret())
                .setDefaultStartDateTime(startDateTime);
            orderSyncs.add(orderSync);
        }
        orderSyncAddBo.setOrderSyncs(orderSyncs);
        amazonOrderJobSupport.addOrderSyncs(orderSyncAddBo);

        log.info("TenantSalesChannel registerAmazonOrderSync end!");
    }
}
