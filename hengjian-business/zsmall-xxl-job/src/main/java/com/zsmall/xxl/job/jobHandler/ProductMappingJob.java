package com.zsmall.xxl.job.jobHandler;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.hengjian.stream.mq.constant.RabbitMqConstant;
import com.hengjian.system.service.ISysConfigService;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.zsmall.common.enums.common.ChannelTypeEnum;
import com.zsmall.product.entity.domain.ProductMapping;
import com.zsmall.product.entity.iservice.IProductMappingService;
import com.zsmall.system.entity.domain.TenantSalesChannel;
import com.zsmall.system.entity.iservice.ITenantSalesChannelService;
import groovy.util.logging.Slf4j;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
@RequiredArgsConstructor
@Slf4j
@Component
@Service
public class ProductMappingJob {
    private final static Logger logger = LoggerFactory.getLogger(ProductMappingJob.class);
    @Resource
   private IProductMappingService iProductMappingService;
    @Resource
    private ITenantSalesChannelService iTenantSalesChannelService;
    @Resource
    private RabbitTemplate rabbitTemplate;
    @Resource
    private ISysConfigService sysConfigService;
    /**
     * 定时发送MQ消息，更新产品映射名称/价格/图片
     */
    @XxlJob("sendProductMappingMqRequest")
    public void sendProductMappingMq() {
        //查询符合条件的映射
        String productMappingMqChannelType = sysConfigService.selectConfigByKey("ProductMapping_Mq_ChannelType");
        List<String>  productMappingChannelList= JSONUtil.toList(productMappingMqChannelType, String.class);
        List<ProductMapping>  productMappingList=    iProductMappingService.getBaseMapper().getProductMappingBySendMq(productMappingChannelList);
        productMappingList.forEach(this::sendMqMessageByProductMapping);
    }

    /**
     * 发送MQ消息
     * @param productMapping
     */
    public  void  sendMqMessageByProductMapping(ProductMapping productMapping){
        //查询店铺
        TenantSalesChannel tenantSalesChannel = iTenantSalesChannelService.getBaseMapper().selectById(productMapping.getChannelId());
        if(ObjectUtil.isNull(tenantSalesChannel.getConnectStr())){
            return;
        }
        String productMappingMqChannelType = sysConfigService.selectConfigByKey("ProductMapping_Mq_ChannelType");
        List<String>  productMappingChannelList= JSONUtil.toList(productMappingMqChannelType, String.class);
        if (!productMappingChannelList.contains(tenantSalesChannel.getChannelType())){
            return;
        }
        //去TK那边获取名称/价格/图片信息
        HashMap<String,String> map=new HashMap<>();
        //渠道SKU
        map.put("sellerSku",productMapping.getChannelSku());
        //渠道类型
        map.put("type",tenantSalesChannel.getChannelType());
        //店铺
        map.put("accountFlag",tenantSalesChannel.getChannelName());
        rabbitTemplate.convertAndSend(RabbitMqConstant.MULTICHANNEL_SEND_EXCHANGE,RabbitMqConstant.PRODUCT_MAPPING_REQUEST, JSON.toJSONString(map));
        logger.info("发送商品映射MQ消息成功" + JSON.toJSONString(map));
    }
}
