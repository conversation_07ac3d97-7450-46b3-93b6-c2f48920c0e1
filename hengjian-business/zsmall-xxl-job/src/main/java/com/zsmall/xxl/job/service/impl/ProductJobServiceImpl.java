package com.zsmall.xxl.job.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.hengjian.common.core.exception.RStatusCodeException;
import com.hengjian.common.tenant.helper.TenantHelper;
import com.xxl.job.core.context.XxlJobHelper;
import com.zsmall.common.enums.MyBatisTaskStateType;
import com.zsmall.common.enums.common.ChannelTypeEnum;
import com.zsmall.common.enums.priceLog.PriceOperateLog;
import com.zsmall.common.enums.product.ProductTypeEnum;
import com.zsmall.common.enums.product.SupportedLogisticsEnum;
import com.zsmall.product.biz.support.EsProductSupport;
import com.zsmall.product.biz.support.ProductSupport;
import com.zsmall.product.entity.domain.*;
import com.zsmall.product.entity.domain.dto.productSku.ProductSkuPriceDTO;
import com.zsmall.product.entity.domain.dto.wholesale.WProductPriceDTO;
import com.zsmall.product.entity.iservice.*;
import com.zsmall.xxl.job.service.ProductJobService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/6/28 15:53
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ProductJobServiceImpl implements ProductJobService {

    private final ITaskSkuPriceChangeService iTaskSkuPriceChangeService;
    private final IProductService iProductService;
    private final IProductSkuService iProductSkuService;
    private final IProductReviewRecordService iProductReviewRecordService;
    private final IProductReviewChangeDetailService iProductReviewChangeDetailService;
    private final IProductSkuPriceService iProductSkuPriceService;
    private final IProductSkuPriceLogService iProductSkuPriceLogService;
    private final IProductMappingService iProductMappingService;
    private final IProductSkuPriceRuleService iProductSkuPriceRuleService;
    private final ProductSupport productSupport;
    private final IProductWholesaleDetailService iProductWholesaleDetailService;
    private final IProductSkuWholesalePriceLogService iProductSkuWholesalePriceLogService;
    private final IProductWholesaleTieredPriceService iProductWholesaleTieredPriceService;
    private final IProductSkuWholesalePriceService iProductSkuWholesalePriceService;
    private final IProductWholesaleTieredPriceLogService iProductWholesaleTieredPriceLogService;

    private final EsProductSupport esProductSupport;

    /**
     * 功能描述：定期价格变动,当前业务逻辑尚未使用,因欧洲站改造,此方法已废弃
     *
     * <AUTHOR>
     * @date 2025/02/07
     */
    @Override
    @Deprecated
    public void regularPriceChange() {
        List<TaskSkuPriceChange> taskList = iTaskSkuPriceChangeService.queryTodoByDate(DateUtil.format(new Date(), "yyyy-MM-dd"));
        List<TaskSkuPriceChange> normalProductTaskList = new ArrayList<>();
        List<TaskSkuPriceChange> wholesaleProductTaskList = new ArrayList<>();
        if (CollUtil.isNotEmpty(taskList)) {
            for (TaskSkuPriceChange task : taskList) {
                String productCode = task.getProductCode();
                Product product = iProductService.queryByProductCode(productCode);
                if (ObjectUtil.isNotEmpty(product)) {
                    ProductTypeEnum productType = product.getProductType();
                    if (ObjectUtil.equals(productType, ProductTypeEnum.WholesaleProduct)) {
                        wholesaleProductTaskList.add(task);
                    } else {
                        normalProductTaskList.add(task);
                    }
                }
            }
        }
        if (CollUtil.isNotEmpty(normalProductTaskList)) {
            normalProductPriceChange(normalProductTaskList);
        }
        if (CollUtil.isNotEmpty(wholesaleProductTaskList)) {
            wholesaleProductPriceChange(wholesaleProductTaskList);
        }
    }


    /**
     * 正常商品定时变更价格
     *
     * @param taskList
     */
    private void normalProductPriceChange(List<TaskSkuPriceChange> taskList) {
        Map<String, ProductSkuPrice> productSkuPriceMap = new HashMap<>();
        Set<String> productCodeList = new HashSet<>();

        if (CollUtil.isNotEmpty(taskList)) {
            for (TaskSkuPriceChange task : taskList) {
                try {
                    XxlJobHelper.log("regularPriceChange - task = {}", JSONUtil.toJsonStr(task));
                    Long reviewRecordId = task.getReviewRecordId();

                    Map<String, ProductSkuPriceDTO> skuPriceDTOMap = new HashMap<>();
                    List<ProductReviewChangeDetail> changeDetailList =
                        iProductReviewChangeDetailService.queryByReviewRecordId(reviewRecordId);

                    for (ProductReviewChangeDetail productReviewChangeDetail : changeDetailList) {
                        String productCode = productReviewChangeDetail.getProductCode();
                        String productSkuCode = productReviewChangeDetail.getProductSkuCode();

                        productCodeList.add(productCode);

                        if (StrUtil.isNotBlank(productSkuCode)) {
                            ProductSkuPriceDTO priceDTO = skuPriceDTOMap.get(productSkuCode);
                            if (priceDTO == null) {
                                priceDTO = new ProductSkuPriceDTO();
                            }
                            // code+siteId
                            List<ProductReviewChangeDetail> collect1 = changeDetailList.stream().filter(c -> StrUtil.equals(c.getProductSkuCode(), productSkuCode)).collect(Collectors.toList());
                            for (ProductReviewChangeDetail changeDetail : collect1) {
                                ReflectUtil.setFieldValue(priceDTO, changeDetail.getFieldName(), NumberUtil.toBigDecimal(changeDetail.getFieldValueAfter()));
                                // todo code+siteId
                                skuPriceDTOMap.put(productSkuCode, priceDTO);
                            }
                            ProductSku productSku = iProductSkuService.queryByProductSkuCode(productSkuCode);
                            priceDTO.setProductSkuCode(productSku.getProductSkuCode());
                            priceDTO.setProductSkuId(productSku.getId());
                            ProductSkuPrice productSkuPrice = priceDtoToPrice(priceDTO);
                            productSkuPriceMap.put(productSkuCode, productSkuPrice);
                        } else {
                            String fieldName = productReviewChangeDetail.getFieldName();
                            String fieldValueBefore = productReviewChangeDetail.getFieldValueBefore();
                            String fieldValueAfter = productReviewChangeDetail.getFieldValueAfter();
                            if (StrUtil.equals(fieldName, "supportedLogistics") && !StrUtil.equals(fieldValueAfter, fieldValueBefore)) {
                                Product product = iProductService.queryByProductCodeNotTenant(productCode);
                                product.setSupportedLogistics(SupportedLogisticsEnum.valueOf(fieldValueAfter));
                                TenantHelper.ignore(() -> iProductService.updateById(product));
                            }
                        }
                    }
                    task.setTaskState(MyBatisTaskStateType.Finished.name());
                } catch (Exception e) {
                    task.setTaskState(MyBatisTaskStateType.Abended.name());
                    XxlJobHelper.log("regularPriceChange 异常时间 {} task.id = {}", DateTime.now(), task.getId());
                    XxlJobHelper.log("regularPriceChange error {}", e.getMessage(), e);
                }
            }
        }

        List<ProductMapping> productMappingList = new ArrayList<>();
        List<ProductSkuPrice> priceEntityList = new ArrayList<>();
        log.info("regularPriceChange - productSkuPriceMap = {}", JSONUtil.toJsonStr(productSkuPriceMap));

        if (productSkuPriceMap.size() > 0) {
            productSkuPriceMap.forEach((productSkuCode, newPrice) -> {
                log.info("regularPriceChange - newPrice = {}", JSONUtil.toJsonStr(newPrice));
                ProductSkuPrice oldProductSkuPrice = iProductSkuPriceService.queryByProductSkuCode(productSkuCode);
                if (ObjectUtil.isNotNull(oldProductSkuPrice)) {
                    newPrice.setId(oldProductSkuPrice.getId());
                    //记录价格变化日志
                    iProductSkuPriceLogService.recordPriceChanges(oldProductSkuPrice, PriceOperateLog.Update.name());
                }
                priceEntityList.add(newPrice);

                /**
                 * 1、获取商品映射数据
                 * 2、修改映射数据的最总价格
                 */
                List<ProductMapping> productMappings = iProductMappingService.queryListByProductSkuCode(productSkuCode);
                for (ProductMapping productMapping : productMappings) {
                    if (StrUtil.isBlank(productMapping.getActivityCode()) && ChannelTypeEnum.Shopify.equals(productMapping.getChannelType())) {
                        productMapping.setPriceChanges(true);
                        productMappingList.add(productMapping);
                    }
                }
            });

            iTaskSkuPriceChangeService.updateBatchById(taskList);
            if (CollUtil.isNotEmpty(productMappingList)) {
                iProductMappingService.batchSaveOrUpdateNoTenant(productMappingList);
            }
            if (CollUtil.isNotEmpty(priceEntityList)) {
                iProductSkuPriceService.saveOrUpdateBatch(priceEntityList);
                priceEntityList.forEach(productSkuPriceEntity -> {
                    log.info("productSkuPriceEntity = {}", JSONUtil.toJsonStr(productSkuPriceEntity));
                    Long ruleId = productSkuPriceEntity.getProductSkuPriceRuleId();
                    iProductSkuPriceRuleService.bindRule(productSkuPriceEntity, ruleId);
                });
            }

            List<Product> productList = iProductService.queryByProductCodesIncludeDel(productCodeList);
            esProductSupport.productUpload(productList);
        }
    }

    /**
     * 批发商品定时变更价格
     * @param taskList
     */
    private void wholesaleProductPriceChange(List<TaskSkuPriceChange> taskList) {
        //国外现货批发专用
        Map<String, WProductPriceDTO> wholesaleProductCodeMap = new HashMap();
        log.info("regularPriceChange - taskList.size {}", CollUtil.size(taskList));
        XxlJobHelper.log("regularPriceChange - taskList.size {}", CollUtil.size(taskList));
        if (CollUtil.isNotEmpty(taskList)) {
            for (TaskSkuPriceChange task : taskList) {
                try {
                    log.info("regularPriceChange - task = {}", JSONUtil.toJsonStr(task));
                    XxlJobHelper.log("regularPriceChange - task = {}", JSONUtil.toJsonStr(task));


                    String productCode = task.getProductCode();
                    Long reviewRecordId = task.getReviewRecordId();

                    WProductPriceDTO wProductPriceDTO = wholesaleProductCodeMap.get(productCode);
                    if (ObjectUtil.isEmpty(wProductPriceDTO)) {
                        wProductPriceDTO = new WProductPriceDTO();
                    }
                    List<ProductReviewChangeDetail> changeDetailList =
                        iProductReviewChangeDetailService.queryByReviewRecordId(reviewRecordId);
                    for (ProductReviewChangeDetail changeDetail : changeDetailList) {
                        String productSkuCode = changeDetail.getProductSkuCode();
                        String fieldName = changeDetail.getFieldName();
                        //判断是否是国外现货批发任务

                        String fieldValueBefore = changeDetail.getFieldValueBefore();
                        String fieldValueAfter = changeDetail.getFieldValueAfter();
                        if (StrUtil.equals(fieldName, "productWholesaleTieredPrice")) {
                            List<ProductWholesaleTieredPriceLog> pwtpLogs = JSONUtil.parseArray(fieldValueBefore).toList(ProductWholesaleTieredPriceLog.class);
                            List<ProductWholesaleTieredPrice> pwtps = JSONUtil.parseArray(fieldValueAfter).toList(ProductWholesaleTieredPrice.class);
                            pwtpLogs.forEach(lb -> {
                                lb.setProductWholesaleTieredPriceId(lb.getId());
                                lb.setId(null);
                            });
                            wProductPriceDTO.setPwtps(pwtps);
                            wProductPriceDTO.setPwtpLogs(pwtpLogs);
                        }
                        if (StrUtil.equals(fieldName, "productSkuWholesalePrice")) {
                            List<ProductSkuWholesalePriceLog> pswpLogs = JSONUtil.parseArray(fieldValueBefore).toList(ProductSkuWholesalePriceLog.class);
                            pswpLogs.forEach(lb -> {
                                lb.setProductSkuWholesalePriceId(lb.getId());
                                lb.setId(null);
                            });
                            wProductPriceDTO.setPswpLogs(pswpLogs);
                        }
                        if (StrUtil.equals("productWholesaleDetail", fieldName)) {
                            ProductWholesaleDetail productWholesaleDetailEntity = JSONUtil.toBean(fieldValueAfter, ProductWholesaleDetail.class);
                            wProductPriceDTO.setProductWholesaleDetailEntity(productWholesaleDetailEntity);
                        }
                        if (StrUtil.equals("delWholesalePrice", fieldName)) {
                            List<ProductWholesaleTieredPrice> delTieredPrices = JSONUtil.parseArray(fieldValueBefore).toList(ProductWholesaleTieredPrice.class);
                            wProductPriceDTO.setDelTieredPrices(delTieredPrices);
                        }

                    }
                    if (ObjectUtil.isNotEmpty(wProductPriceDTO)) {
                        wholesaleProductCodeMap.put(productCode, wProductPriceDTO);
                    }
                    task.setTaskState(MyBatisTaskStateType.Finished.name());
                } catch (Exception e) {
                    task.setTaskState(MyBatisTaskStateType.Abended.name());
                    log.info("regularPriceChange 异常时间 {} task.id = {}", DateTime.now(), task.getId());
                    XxlJobHelper.log("regularPriceChange 异常时间 {} task.id = {}", DateTime.now(), task.getId());
                    log.error("regularPriceChange error {}", e.getMessage(), e);
                    XxlJobHelper.log("regularPriceChange error {}", e.getMessage(), e);
                }
            }
        }

        if (wholesaleProductCodeMap.size() > 0) {
            List<String> productCodes = new ArrayList<>();
            wholesaleProductCodeMap.forEach((productCode, wProductPriceDTO) -> {
                productCodes.add(productCode);
                ProductWholesaleDetail productWholesaleDetailEntity = wProductPriceDTO.getProductWholesaleDetailEntity();
                List<ProductSkuWholesalePriceLog> pswpLogs = wProductPriceDTO.getPswpLogs();
                List<ProductWholesaleTieredPrice> pwtps = wProductPriceDTO.getPwtps();
                List<ProductWholesaleTieredPrice> delTieredPrices = wProductPriceDTO.getDelTieredPrices();
                List<ProductWholesaleTieredPriceLog> pwtpLogs = wProductPriceDTO.getPwtpLogs();
                log.info("productWholesaleDetailEntity = {}", JSONUtil.toJsonStr(productWholesaleDetailEntity));
                if (ObjectUtil.isNotEmpty(productWholesaleDetailEntity)) {
                    iProductWholesaleDetailService.saveOrUpdate(productWholesaleDetailEntity);
                }
                log.info("pswpLogs = {}", JSONUtil.toJsonStr(pswpLogs));
                if (CollUtil.size(pswpLogs) > 0) {
                    iProductSkuWholesalePriceLogService.saveOrUpdateBatch(pswpLogs);
                }
                log.info("pwtps = {}", JSONUtil.toJsonStr(pwtps));
                if (CollUtil.size(pwtps) > 0) {
                    iProductWholesaleTieredPriceService.saveOrUpdateBatch(pwtps);
                    List<ProductSkuWholesalePrice> newPswps = new ArrayList<>();
                    pwtps.stream().forEach(np -> {
                        Long id = np.getId();
                        List<ProductSkuWholesalePrice> wholesalePriceEntities = np.getWholesalePrices();
                        wholesalePriceEntities.stream().forEach(wp -> {
                            wp.setTieredPriceId(id);
                        });
                        newPswps.addAll(wholesalePriceEntities);
                    });
                    log.info("newPswps = {}", JSONUtil.toJsonStr(newPswps));
                    if (CollUtil.size(newPswps) > 0) {
                        iProductSkuWholesalePriceService.saveOrUpdateBatch(newPswps);
                    }
                }
                log.info("delTieredPrices = {}", JSONUtil.toJsonStr(delTieredPrices));
                if (CollUtil.isNotEmpty(delTieredPrices)) {
                    List<Long> ids = delTieredPrices.stream().map(ProductWholesaleTieredPrice::getId).collect(Collectors.toList());
                    iProductWholesaleTieredPriceService.removeByIds(ids);
                }
                log.info("pwtpLogs = {}", JSONUtil.toJsonStr(pwtpLogs));
                if (CollUtil.size(pwtpLogs) > 0) {
                    iProductWholesaleTieredPriceLogService.saveOrUpdateBatch(pwtpLogs);
                }
                iTaskSkuPriceChangeService.updateBatchById(taskList);
            });
        }
    }

    /**
     * 商品价格实体转化为价格DTO
     *
     * @param priceDTO
     * @return
     */
    public ProductSkuPrice priceDtoToPrice(ProductSkuPriceDTO priceDTO) throws RStatusCodeException {
        if (priceDTO == null) {
            return null;
        }
        ProductSkuPrice productSkuPrice = new ProductSkuPrice();
        productSkuPrice.setProductSkuId(priceDTO.getProductSkuId());
        productSkuPrice.setProductSkuCode(priceDTO.getProductSkuCode());
        productSkuPrice.setOriginalUnitPrice(priceDTO.getUnitPrice());
        productSkuPrice.setOriginalOperationFee(priceDTO.getOperationFee());
        productSkuPrice.setOriginalFinalDeliveryFee(priceDTO.getFinalDeliveryFee());
        productSkuPrice.setMsrp(priceDTO.getMsrp());
        iProductSkuPriceRuleService.matchRule(productSkuPrice, false);
        log.info("priceDtoToPrice - productSkuPrice = {}", JSONUtil.toJsonStr(productSkuPrice));
        return productSkuPrice;

    }


}
