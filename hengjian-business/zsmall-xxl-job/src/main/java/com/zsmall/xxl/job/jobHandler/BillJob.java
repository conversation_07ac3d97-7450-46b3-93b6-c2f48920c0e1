package com.zsmall.xxl.job.jobHandler;

import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.zsmall.xxl.job.service.BillJobService;
import groovy.util.logging.Slf4j;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * 账单相关定时任务-执行器
 *
 * <AUTHOR>
 * @date 2023/7/7
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class BillJob {

    private final BillJobService billJobService;

    /**
     * 生成下一期账单任务
     */
    @XxlJob("generateNextBillJob")
    public void generateNextBillJob() {
        billJobService.generateNextBillJob();
    }

    /**
     * 账单结算任务
     */
    @XxlJob("billSettleJob")
    public void billSettleJob() {
        billJobService.billSettleJob();
    }

    /**
     * 生成PDF任务（每次结算后生成）
     */
    @XxlJob("generateBillPdfJob")
    public void generateBillPdfJob() {
        billJobService.generateBillPdfJob();
    }

    /**
     * 生成PDF任务（生成失败后补生成）
     */
    @XxlJob("generateBillPdfAgainJob")
    public void generateBillPdfAgainJob() {
        String jobParam = XxlJobHelper.getJobParam();
        billJobService.generateBillPdfAgainJob(jobParam);
    }

}
