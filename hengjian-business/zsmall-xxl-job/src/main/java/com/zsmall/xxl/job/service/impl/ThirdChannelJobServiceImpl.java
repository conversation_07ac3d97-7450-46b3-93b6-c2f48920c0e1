package com.zsmall.xxl.job.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.hengjian.common.log.annotation.InMethodLog;
import com.zsmall.common.enums.TaskStateEnum;
import com.zsmall.common.enums.common.ChannelTypeEnum;
import com.zsmall.common.enums.productMapping.SyncStateEnum;
import com.zsmall.order.entity.domain.ThirdChannelFulfillmentRecord;
import com.zsmall.order.entity.iservice.IThirdChannelFulfillmentRecordService;
import com.zsmall.product.entity.domain.ProductMapping;
import com.zsmall.product.entity.domain.ProductSku;
import com.zsmall.product.entity.domain.TaskStockSync;
import com.zsmall.product.entity.iservice.IProductMappingService;
import com.zsmall.product.entity.iservice.IProductSkuService;
import com.zsmall.product.entity.iservice.ITaskStockSyncService;
import com.zsmall.xxl.job.factory.ThirdChannelFactory;
import com.zsmall.xxl.job.factory.ThirdChannelService;
import com.zsmall.xxl.job.service.ThirdChannelJobService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 第三方渠道定时任务-业务实现层
 *
 * <AUTHOR>
 * @date 2023/6/25
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ThirdChannelJobServiceImpl implements ThirdChannelJobService {

    private final IProductMappingService iProductMappingService;
    private final ThirdChannelFactory thirdChannelFactory;
    private final IThirdChannelFulfillmentRecordService iThirdChannelFulfillmentRecordService;
    private final ITaskStockSyncService iTaskStockSyncService;
    private final IProductSkuService iProductSkuService;

    @Override
    public void timedEvent() {
        List<ChannelTypeEnum> channelTypeEnums = ChannelTypeEnum.jobBusiness();
        for (ChannelTypeEnum channelTypeEnum : channelTypeEnums) {
            thirdChannelFactory.getService(channelTypeEnum).timedEvent();
        }
    }

    /**
     * 功能描述：推送产品,欧洲站改造需求,暂时废弃
     *
     * @param channel 通道
     * <AUTHOR>
     * @date 2025/02/07
     */
    @Override
    @Deprecated
    public void pushProduct(String channel) {
        List<ChannelTypeEnum> channelTypeEnums;
        if (StrUtil.isNotBlank(channel)) {
            channelTypeEnums = CollUtil.newArrayList(ChannelTypeEnum.valueOf(channel));
        } else {
            channelTypeEnums = ChannelTypeEnum.jobBusiness();
        }

        for (ChannelTypeEnum channelTypeEnum : channelTypeEnums) {
            List<ProductMapping> mappingList = iProductMappingService.queryListByChannelAndState(channelTypeEnum, SyncStateEnum.Synchronizing);
            thirdChannelFactory.getService(channelTypeEnum).pushProduct(mappingList);
        }
    }

    @Override
    public void updateProduct() {
        List<ChannelTypeEnum> channelTypeEnums = ChannelTypeEnum.jobBusiness();
        for (ChannelTypeEnum channelTypeEnum : channelTypeEnums) {
            List<ProductMapping> mappingList = iProductMappingService.queryListByChannelAndState(channelTypeEnum, SyncStateEnum.Updating);
            thirdChannelFactory.getService(channelTypeEnum).updateProduct(mappingList);
        }
    }

    @Override
    public void cancelProduct() {
        List<ChannelTypeEnum> channelTypeEnums = ChannelTypeEnum.jobBusiness();
        for (ChannelTypeEnum channelTypeEnum : channelTypeEnums) {
            List<ProductMapping> mappingList = iProductMappingService.queryListByChannelAndState(channelTypeEnum, SyncStateEnum.Cancelling);
            thirdChannelFactory.getService(channelTypeEnum).cancelProduct(mappingList);
        }
    }

    @Override
    public void deleteProduct() {
        List<ChannelTypeEnum> channelTypeEnums = ChannelTypeEnum.jobBusiness();
        for (ChannelTypeEnum channelTypeEnum : channelTypeEnums) {
            List<ProductMapping> mappingList = iProductMappingService.queryListByChannelAndState(channelTypeEnum, SyncStateEnum.Deleting);
            thirdChannelFactory.getService(channelTypeEnum).deleteProduct(mappingList);
        }
    }

    @Override
    @InMethodLog("第三方渠道定时拉取订单任务")
    public void pullOrder(String channel, String startDate, String endDate) {
        List<ChannelTypeEnum> channelTypeEnums;
        if (StrUtil.isNotBlank(channel)) {
            channelTypeEnums = CollUtil.newArrayList(ChannelTypeEnum.valueOf(channel));
        } else {
            channelTypeEnums = ChannelTypeEnum.jobBusiness();
        }

        for (ChannelTypeEnum channelTypeEnum : channelTypeEnums) {
            ThirdChannelService service = thirdChannelFactory.getService(channelTypeEnum);
            if (service != null) {
                service.pullOrder(startDate, endDate);
            }
        }
    }

    @Override
    public void pullOrder(String channel, String startDate, String endDate, String voJson) {
        List<ChannelTypeEnum> channelTypeEnums;
        if (StrUtil.isNotBlank(channel)) {
            channelTypeEnums = CollUtil.newArrayList(ChannelTypeEnum.valueOf(channel));
        } else {
            channelTypeEnums = ChannelTypeEnum.jobBusiness();
        }

        for (ChannelTypeEnum channelTypeEnum : channelTypeEnums) {
            ThirdChannelService service = thirdChannelFactory.getService(channelTypeEnum);
            if (service != null) {
                service.pullOrderByJson(voJson,startDate, endDate);
            }
        }
    }

    @Override
    @InMethodLog("第三方渠道推送履约信息")
    public void pushFulfillment(String channel) {
        List<ChannelTypeEnum> channelTypeEnums;
        if (StrUtil.isNotBlank(channel)) {
            channelTypeEnums = CollUtil.newArrayList(ChannelTypeEnum.valueOf(channel));
        } else {
            channelTypeEnums = ChannelTypeEnum.jobBusiness();
        }

        for (ChannelTypeEnum channelTypeEnum : channelTypeEnums) {
            ThirdChannelService service = thirdChannelFactory.getService(channelTypeEnum);
            if (service != null) {
                List<ThirdChannelFulfillmentRecord> fulfillmentRecords = iThirdChannelFulfillmentRecordService.queryByChannelTypeAndWaitPush(channelTypeEnum);
                service.pushFulfillment(fulfillmentRecords);
            }
        }

    }

    @Override
    public void updateStock() {
        List<TaskStockSync> taskList = iTaskStockSyncService.queryByTaskState(TaskStateEnum.Todo);
        taskList.forEach(task -> task.setTaskState(TaskStateEnum.Running));
        iTaskStockSyncService.updateBatchById(taskList);

        List<ChannelTypeEnum> channelTypeEnums = ChannelTypeEnum.jobBusiness();
        for (TaskStockSync task : taskList) {
            Long taskId = task.getId();
            try {
                List<Long> productSkuIdList = new ArrayList<>();
                Long productSkuId = task.getProductSkuId();
                if (productSkuId != null) {
                    productSkuIdList.add(productSkuId);
                } else {
                    productSkuIdList = task.getProductSkuIdList();
                }

                for (Long skuId : productSkuIdList) {
                    ProductSku productSku = iProductSkuService.queryByIdNoTenant(skuId);
                    Integer stockTotal = productSku.getStockTotal();

                    if (productSku == null) {
                        // task.setTaskState(TaskStateEnum.Failed);
                        // task.setTaskExecuteMessage("商品SKU已被删除");
                        continue;
                    }

                    String productSkuCode = productSku.getProductSkuCode();
                    for (ChannelTypeEnum channelTypeEnum : channelTypeEnums) {
                        List<ProductMapping> mappingList = iProductMappingService.queryListByChannelAndItemNoAndState(channelTypeEnum, productSkuCode, SyncStateEnum.Synced);
                        ThirdChannelService service = thirdChannelFactory.getService(channelTypeEnum);
                        if (service != null) {
                            service.updateStock(mappingList, stockTotal);
                        }
                    }
                }

                task.setTaskState(TaskStateEnum.Finished);
                task.setTaskExecuteMessage(null);
            } catch (Exception e) {
                log.error("【定时更新库存任务】 任务ID = {}，执行失败，原因：{}", taskId, e.getMessage(), e);
                task.setTaskState(TaskStateEnum.Failed);
                task.setTaskExecuteMessage(e.getMessage());
            }
            iTaskStockSyncService.updateById(task);
        }
    }

    @Override
    public void pullProduct(String channel, String json) {
        List<ChannelTypeEnum> channelTypeEnums;
        if (StrUtil.isNotBlank(channel)) {
            channelTypeEnums = CollUtil.newArrayList(ChannelTypeEnum.valueOf(channel));
        } else {
            channelTypeEnums = ChannelTypeEnum.jobBusiness();
        }

        for (ChannelTypeEnum channelTypeEnum : channelTypeEnums) {
            ThirdChannelService service = thirdChannelFactory.getService(channelTypeEnum);
            if (service != null) {
                service.pullProduct(json);
            }
        }
    }
}
