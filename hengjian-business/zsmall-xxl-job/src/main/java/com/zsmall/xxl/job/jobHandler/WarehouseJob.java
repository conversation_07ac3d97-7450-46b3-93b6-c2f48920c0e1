package com.zsmall.xxl.job.jobHandler;

import cn.hutool.core.date.StopWatch;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.zsmall.xxl.job.service.WarehouseXxlJobService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Service;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2024/3/18 16:22
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class WarehouseJob {

    private final WarehouseXxlJobService warehouseXxlJobService;

    /**
     * 功能描述：提取库存作业---全量产品库存拉取
     *
     * <AUTHOR>
     * @date 2024/03/18
     */
    @XxlJob("pullInventoryJob")
    public void pullInventoryJob() {
        log.info("仓库库存拉取任务开始");
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        warehouseXxlJobService.pullInventoryJob();

        stopWatch.stop();
        log.info("仓库库存拉取任务结束,用时信息:{}",stopWatch.prettyPrint());
    }
}
