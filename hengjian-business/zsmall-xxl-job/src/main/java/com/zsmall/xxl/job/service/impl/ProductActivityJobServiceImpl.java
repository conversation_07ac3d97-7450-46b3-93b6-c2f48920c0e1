package com.zsmall.xxl.job.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.json.JSONUtil;
import com.hengjian.common.tenant.helper.TenantHelper;
import com.xxl.job.core.context.XxlJobHelper;
import com.zsmall.activity.biz.support.ProductActiveSupper;
import com.zsmall.activity.biz.support.StorageFeeSupport;
import com.zsmall.common.enums.BusinessCodeEnum;
import com.zsmall.common.enums.productActivity.*;
import com.zsmall.common.enums.transaction.TransactionStateEnum;
import com.zsmall.common.enums.transaction.TransactionSubTypeEnum;
import com.zsmall.common.enums.transaction.TransactionTypeEnum;
import com.zsmall.order.entity.iservice.IOrderItemService;
import com.zsmall.product.biz.support.EsProductSupport;
import com.zsmall.system.biz.service.TenantWalletService;
import com.zsmall.system.biz.support.BillSupport;
import com.zsmall.system.entity.domain.TransactionRecord;
import com.zsmall.system.entity.domain.TransactionsProductActivityItem;
import com.zsmall.system.entity.iservice.ITransactionsProductActivityItemService;
import com.zsmall.system.entity.util.MallSystemCodeGenerator;
import com.zsmall.xxl.job.service.ProductActivityJobService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 商品活动相关定时任务-实现
 *
 * <AUTHOR>
 * @date 2023/8/4
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class ProductActivityJobServiceImpl implements ProductActivityJobService {

//    private final IProductActivityService iProductActivityService;
//    private final IProductActivityItemService iProductActivityItemService;
//    private final IProductActivityCheckoutService iProductActivityCheckoutService;
//    private final IProductActivityStockLockItemService iProductActivityStockLockItemService;
//    private final IProductActivityBuyoutItemService iProductActivityBuyoutItemService;
    private final ITransactionsProductActivityItemService iTransactionsProductActivityItemService;
    private final IOrderItemService iOrderItemService;

//    private final ProductActivitySupport productActivitySupport;
    private final MallSystemCodeGenerator mallSystemCodeGenerator;
    private final TenantWalletService tenantWalletService;
    private final BillSupport billSupport;
    private final EsProductSupport esProductSupport;
    private final StorageFeeSupport storageFeeSupport;

    /**
     * 仓储费扣除定时任务
     */
    @Override
    public void storageFeeDeductionJob() {
        log.info("【仓储费扣除定时任务】 => 开始执行，时间 = {}", DateUtil.now());
//        Date nowDate = new Date();
//        List<StorageFeeDisDTO> resultDTOList = iProductActivityItemService.queryStorageFeeBulkDTO(DateUtil.formatDate(nowDate));
//
//        log.info("仓储费扣除定时任务，正在进行中的活动仓储费查询结果 = {}", JSONUtil.toJsonStr(resultDTOList));
//        XxlJobHelper.log("仓储费扣除定时任务，正在进行中的活动仓储费查询结果 = {}", JSONUtil.toJsonStr(resultDTOList));
//
//        if (CollUtil.isNotEmpty(resultDTOList)) {
//            List<ProductActivityCheckout> checkoutList = new ArrayList<>();
//            for (StorageFeeDisDTO dto : resultDTOList) {
//                Long productActivityId = dto.getProductActivityId();
//                Long productActivityItemId = dto.getProductActivityItemId();
//                String dTenantId = dto.getDTenantId();
//                String sTenantId = dto.getSTenantId();
//                BigDecimal storageFee = dto.getStorageFee();
//                Integer quantitySurplus = dto.getQuantitySurplus();
//
//                //记录扣除的订金到结算表
//                ProductActivityCheckout checkout = new ProductActivityCheckout();
//                checkout.setSupplierTenantId(sTenantId);
//                checkout.setDistributorTenantId(dTenantId);
//                checkout.setProductActivityId(productActivityId);
//                checkout.setProductActivityItemId(productActivityItemId);
//                checkout.setCheckoutType(ActivityCheckoutTypeEnum.StorageFee);
//                checkout.setCheckoutUnitPrice(storageFee);
//                checkout.setCheckoutAmount(NumberUtil.mul(storageFee, quantitySurplus));
//                checkout.setPlatformCheckoutUnitPrice(storageFee);
//                checkout.setPlatformCheckoutAmount(NumberUtil.mul(storageFee, quantitySurplus));
//                checkout.setCheckoutQuantity(quantitySurplus);
//                checkout.setDistributorPay(CheckoutPayEnum.UnPaid);
//
//                // 查询今天是否已经生成过仓储费结算记录
//                boolean paidSettled = iProductActivityCheckoutService.queryPaidCheck(productActivityItemId, nowDate);
//                // 不存在仓储费结算记录才会去扣除分销商的钱包并生成结算记录
//                if (!paidSettled) {
//                    checkoutList.add(checkout);
//                }
//                // checkoutList.add(checkout);
//            }
//
//            // 保存结账记录
//            iProductActivityCheckoutService.saveBatch(checkoutList);
//
//            // 开始扣减分销商的钱包
//            for (ProductActivityCheckout checkout : checkoutList) {
//                String dTenantId = checkout.getDistributorTenantId();
//                Long productActivityItemId = checkout.getProductActivityItemId();
//
//                try {
//                    ProductActivityItem activityItem = iProductActivityItemService.queryOneByEntity(ProductActivityItem.builder().id(productActivityItemId).build());
//                    String activityCode = activityItem.getActivityCode();
//
//                    BigDecimal checkoutAmount = checkout.getCheckoutAmount();
//                    TransactionRecord dTransactionRecord = new TransactionRecord(mallSystemCodeGenerator.codeGenerate(BusinessCodeEnum.TransactionNo));
//                    dTransactionRecord.setTenantId(dTenantId);
//                    dTransactionRecord.setTransactionAmount(checkoutAmount);
//                    dTransactionRecord.setTransactionType(TransactionTypeEnum.Expenditure);
//                    dTransactionRecord.setTransactionSubType(TransactionSubTypeEnum.ProductActivityStorageFee);
//                    dTransactionRecord.setTransactionState(TransactionStateEnum.Processing);
//                    dTransactionRecord.setTransactionNote(activityCode);
//                    tenantWalletService.walletChanges(dTenantId, dTransactionRecord, true);
//                    checkout.setDistributorPay(CheckoutPayEnum.Paid);
//
//                    TransactionsProductActivityItem newRelation = new TransactionsProductActivityItem();
//                    newRelation.setTransactionsId(dTransactionRecord.getId());
//                    newRelation.setProductActivityItemId(productActivityItemId);
//                    iTransactionsProductActivityItemService.save(newRelation);
//
//                    // 记账-活动结算记录
//                    billSupport.generateBillDTOByProductActivityCheckout(checkout, null);
//                } catch (Exception e) {
//                    log.error("仓储费扣除定时任务，分销商{}扣除仓储费异常，原因 = {}", dTenantId, e.getMessage(), e);
//                    checkout.setDistributorPay(CheckoutPayEnum.Failed);
//                    checkout.setPayFailMassage(e.getMessage());
//                }
//            }
//            iProductActivityCheckoutService.updateBatchById(checkoutList);
//            log.info("【仓储费扣除定时任务】 => 结束执行，时间 = {}", DateUtil.now());
//        }
    }

    /**
     * 活动定时过期任务
     */
    @Override
    public void activityExpiredJob() {
        log.info("活动定时过期任务，开始执行，时间 = {}", DateUtil.now());
        //查询所有在进行中的分销商活动
//        List<ProductActivityItem> activityItemList = iProductActivityItemService.queryWillExpiredActivity();
//        log.info("已经过期的活动总数为：{}", CollUtil.size(activityItemList));
//
//        if (CollUtil.isNotEmpty(activityItemList)) {
//            // List<Long> itemIdList = activityItemList.stream().map(ProductActivityItem::getId).collect(Collectors.toList());
//            // log.info("已经过期的活动id集合： itemIdList = {}", JSONUtil.toJsonStr(itemIdList));
//
//            activityItemList.forEach(activityItem -> {
//                String activityCode = activityItem.getActivityCode();
//                log.info("分销商活动编号{}，到期取消 {}", activityCode, JSONUtil.toJsonStr(activityItem));
//                XxlJobHelper.log("分销商活动编号{}，到期取消 {}", activityCode, JSONUtil.toJsonStr(activityItem));
//                //更新供应商库存和分销商订金剩余
//                try {
//                    productActivitySupport.cancelActivityItem(activityItem, ProductActivityItemStateEnum.Expired);
//                } catch (Exception e) {
//                    log.error("子活动编号{}，到期取消出现未知错误，原因 => {}", activityCode, e.getMessage(), e);
//                    XxlJobHelper.log("分销商活动编号{}，到期取消出现未知错误，原因 {}", activityCode, e.getMessage(), e);
//                }
//            });
//        }
//        log.info("活动定时过期任务，结束执行，时间 = {}", DateUtil.now());
    }

    /**
     * 定时完成活动（分销商）任务
     */
    @Override
    public void activityCompleteDisJob() {
        log.info("定时完成活动（分销商）任务，开始执行，时间 = {}", DateUtil.now());

        // 查询已售完活动总数
//        List<ProductActivityItem> activityItemList = iProductActivityItemService.queryByEntity(ProductActivityItem.builder().activityState(ProductActivityItemStateEnum.InProgress).quantitySurplus(0).build());
//        log.info("已售完活动总数为：{}", CollUtil.size(activityItemList));
//        XxlJobHelper.log("已售完活动总数为：{}", CollUtil.size(activityItemList));
//
//        if (CollUtil.isNotEmpty(activityItemList)) {
//            for (ProductActivityItem activityItem : activityItemList) {
//                String activityCode = activityItem.getActivityCode();
//                Integer activitySold = activityItem.getQuantitySold();
//
//                try {
//                    // 查询有效的出货数量
//                    int enableSold = iOrderItemService.queryValidQuantityByActivity(activityCode);
//                    log.info("子活动{}有效的出货数量{}, 活动已售量{}", activityCode, enableSold, activitySold);
//                    XxlJobHelper.log("子活动{}有效的出货数量{}, 活动已售量{}", activityCode, enableSold, activitySold);
//                    if (NumberUtil.compare(enableSold, activitySold) >= 0) {
//                        activityItem.setActivityState(ProductActivityItemStateEnum.Completed);
//                        TenantHelper.ignore(() -> iProductActivityItemService.updateById(activityItem));
//                    }
//                } catch (Exception e) {
//                    log.error("子活动编号{}，完成活动出现未知错误，原因 => {}", activityCode, e.getMessage(), e);
//                    XxlJobHelper.log("子活动编号{}，完成活动出现未知错误，原因 => {}", activityCode, e.getMessage(), e);
//                }
//            }
//
//        }
//        log.info("定时完成活动（分销商）任务，结束执行，时间 = {}", DateUtil.now());
    }

    /**
     * 定时完成活动（供应商）任务
     */
    @Override
    public void activityCompleteSupJob() {
//        List<ProductActivity> activityList = iProductActivityService.queryInProgressAndNoSurplus();
//        log.info("活动总数为：{}", CollUtil.size(activityList));
//
//        if (CollUtil.isNotEmpty(activityList)) {
//            for (ProductActivity activity : activityList) {
//                String activityCode = activity.getActivityCode();
//                String productSkuCode = activity.getProductSkuCode();
//                ProductActivityTypeEnum activityType = activity.getActivityType();
//
//                Integer actItemSurplus = iProductActivityItemService.queryItemSurplusByActivity(activityCode);
//                log.info("主活动{}， 所有进行中分销商剩余数量 => {}", activityCode, actItemSurplus);
//                if (actItemSurplus > 0) {
//                    // 还有分销商没卖完，无法完成活动，跳过
//                    log.info("主活动{}， 还有分销商没卖完，无法完成活动，跳过");
//                    continue;
//                }
//
//                List<ProductActivityItem> activityItemList = iProductActivityItemService.queryByEntity(ProductActivityItem.builder().activityCodeParent(activityCode).build());
//
//                //查询有效的出单量总和
//                Integer totalValidSold = 0;
//                for (ProductActivityItem activityItem : activityItemList) {
//                    ProductActivityItemStateEnum activityItemState = activityItem.getActivityState();
//                    String itemActivityCode = activityItem.getActivityCode();
//                    Long activityItemId = activityItem.getId();
//
//                    // 已被取消的活动，算全部出单
//                    if (ProductActivityItemStateEnum.Canceled.equals(activityItemState)) {
//                        Integer itemQuantityTotal = activityItem.getQuantityTotal();
//                        totalValidSold += itemQuantityTotal;
//                    } else {
//                        Integer validSold = iOrderItemService.queryValidQuantityByActivity(itemActivityCode);
//                        totalValidSold += validSold;
//
//                        // 完成活动（供应商）锁货还需要查询是否有转了圈货的记录，如有，不需要统计是否出单
//                        if (ProductActivityTypeEnum.StockLock.name().equals(activityType)) {
//                            ProductActivityBuyoutItem activityBuyoutItem = iProductActivityBuyoutItemService.queryByParentActivityItemId(activityItemId);
//                            if (activityBuyoutItem != null) {
//                                Integer buyoutQuantity = activityBuyoutItem.getBuyoutQuantity();
//                                totalValidSold += buyoutQuantity;
//                            }
//                        }
//                    }
//                }
//                Integer quantitySurplus = activity.getQuantityAlready();
//
//                // 判断总售出数量是否与活动总数量一致，若一致，则判定活动已完成
//                log.info("id为：{} 活动，已售出{}，活动总数", totalValidSold, quantitySurplus);
//                if(NumberUtil.compare(totalValidSold, quantitySurplus) >= 0){
//                    activity.setActivityState(ProductActivityStateEnum.Completed);
//                    TenantHelper.ignore(() -> iProductActivityService.updateById(activity));
//
//                    esProductSupport.productSkuUpload(productSkuCode);
//                    log.info("id为：{} 活动已完成", activity.getId());
//                }
//
//            }
//        }
    }

    /**
     * 定时回收订金任务（分销商已过期活已取消的活动）
     */
    @Override
    public void activityRecoveryDepositJob() {
        log.info("定时回收已过期、已取消的分销商锁货活动订金任务，开始执行，时间 = {}", DateUtil.now());
//        List<ProductActivityStockLockItem> stockLockItemList = iProductActivityStockLockItemService.queryCanceledOrExpiredAndExistsDeposit();
//        log.info("活动总数为：{}", CollUtil.size(stockLockItemList));
//        if(CollUtil.isNotEmpty(stockLockItemList)){
//            productActivitySupport.deductDeposit(stockLockItemList);
//        }
//        log.info("定时回收已过期、已取消的分销商锁货活动订金任务，结束执行，时间 = {}", DateUtil.now());
    }

    /**
     * 定时发送临期活动通知（邮件或者短信）
     */
    @Override
    public void activityAdventNoticeJob() {
//        productActivitySupport.sendActivityExpirationEmailOrSms();
    }

    /**
     * 定时生成仓储费任务
     */
    @Override
    public void storageFee() {
        log.info("定时生成仓储费任务，开始执行，时间 = {}", DateUtil.now());
        storageFeeSupport.storageFee();
        log.info("定时生成仓储费任务，结束执行，时间 = {}", DateUtil.now());
    }
}
