package com.zsmall.xxl.job.factory;

import com.zsmall.order.entity.domain.ThirdChannelFulfillmentRecord;
import com.zsmall.product.entity.domain.ProductMapping;
import org.springframework.beans.factory.InitializingBean;

import java.util.List;

/**
 * 第三方渠道工厂类接口
 * <AUTHOR>
 * @date 2023/6/25
 */
public interface ThirdChannelService extends InitializingBean {

    /**
     * 渠道通用定时事件
     */
    default void timedEvent() {}

    /**
     * 推送商品至渠道店铺
     */
    void pushProduct(List<ProductMapping> mappingList);

    /**
     * 更新商品
     */
    void updateProduct(List<ProductMapping> mappingList);

    /**
     * 取消同步商品
     */
    void cancelProduct(List<ProductMapping> mappingList);

    /**
     * 删除商品
     */
    void deleteProduct(List<ProductMapping> mappingList);

    /**
     * 从渠道店铺拉取订单
     */
    void pullOrder(String startDate, String endDate);

    /**
     * 推送履约信息至渠道店铺
     */
    void pushFulfillment(List<ThirdChannelFulfillmentRecord> fulfillmentRecordList);

    /**
     * 更新库存
     */
    void updateStock(List<ProductMapping> mappingList, Integer stockTotal);

    void pullOrderByJson(String voJson, String startDate, String endDate);

    void pullProduct(String json);
}
