package com.zsmall.xxl.job.service.impl;

import cn.hutool.extra.spring.SpringUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hengjian.common.tenant.helper.TenantHelper;
import com.zsmall.common.enums.product.StockManagerEnum;
import com.zsmall.product.entity.domain.ProductSku;
import com.zsmall.product.entity.iservice.IProductSkuService;
import com.zsmall.product.entity.mapper.ProductMapper;
import com.zsmall.warehouse.entity.domain.event.ThirdWarehouseEvent;
import com.zsmall.xxl.job.service.WarehouseXxlJobService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.stream.Collectors;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2024/3/18 16:33
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class WarehouseXxlJobServiceImpl implements WarehouseXxlJobService {
    private final IProductSkuService iProductSkuService;
    private final ProductMapper productMapper;
    @Override
    public void pullInventoryJob() {
        List<String> productSkuCodes = TenantHelper.ignore(productMapper::getAllBizArkProduct);

        ThreadPoolExecutor executor = SpringUtil.getBean("ioThreadPoolExecutor", ThreadPoolExecutor.class);
        List<String> productSkuCodesAble = new ArrayList<>();

        CountDownLatch downLatch = new CountDownLatch(productSkuCodes.size());
        for (String productSkuCode : productSkuCodes) {
            // 拿线程池
            executor.execute(() -> {
                try {
                    // 调用第三方仓库查询库存
                    ThirdWarehouseEvent.queryStock(StockManagerEnum.BizArk,productSkuCode);
                    productSkuCodesAble.add(productSkuCode);
                } catch (Exception e) {
                    log.error("查询库存失败,productSkuCode={}", productSkuCode);
                } finally {
                    downLatch.countDown();
                }
            });
        }
        try {
            downLatch.await();
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }
        log.info("库存拉取任务执行完毕,需拉取库存数:{},已拉取库存数:{}", productSkuCodes.size() , productSkuCodesAble.size());
        if(productSkuCodesAble.size()<productSkuCodes.size()){
            log.info("拉取库存失败的商品:{}}", productSkuCodes.stream().filter(s -> !productSkuCodesAble.contains(s)).collect(Collectors.toList()));
        }
    }

}
