package com.zsmall.xxl.job.jobHandler;

import com.xxl.job.core.handler.annotation.XxlJob;
import com.zsmall.order.biz.service.OrdersService;
import com.zsmall.xxl.job.service.OrderJobService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 订单定时任务-执行器
 *
 * <AUTHOR>
 * @date 2023/6/25
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class OrderJob {
    private final OrderJobService orderJobService;
    private final OrdersService ordersService;
    /**
     * 取消订单任务
     */
    @XxlJob("cancelOrders")
    public void cancelOrders() {
        orderJobService.cancelOrders();
    }

    @XxlJob("canceling2CancelFlow")
    public void canceling2CancelFlow() {
        ordersService.canceling2CancelFlow();
    }


}
