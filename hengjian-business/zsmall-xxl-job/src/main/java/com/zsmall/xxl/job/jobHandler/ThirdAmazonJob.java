package com.zsmall.xxl.job.jobHandler;


import cn.hutool.core.util.StrUtil;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.zsmall.xxl.job.service.ThirdAmazonJobService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;


/**
 * 第三方店铺，亚马逊专用
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ThirdAmazonJob {

    private final ThirdAmazonJobService thirdAmazonJobService;

    /**
     * 注册订单任务
     */
    @XxlJob("amazonOrderSyncRegisterJobHandler")
    public void orderSyncRegisterJobHandler() {
        String jobParam = XxlJobHelper.getJobParam();
        log.info("StoreAmazonSyncJob registerOrderSyncJobHandler start. jobParam = {}", jobParam);
        XxlJobHelper.log("StoreAmazonSyncJob registerOrderSyncJobHandler start. jobParam = {}", jobParam);

        int delays = 3600; // 默认60分钟
        String[] methodParams = jobParam.split(",");
        if(methodParams.length > 0) {
            String delaySeconds = methodParams[0];
            if(StrUtil.isNotBlank(delaySeconds)) {
                delays = Integer.parseInt(delaySeconds);
            }
        }

        LocalDateTime localDateTime = LocalDateTime.now();
        // 需延后一分钟，避免同步出现异常
        LocalDateTime newLocalDateTime = localDateTime.minusSeconds(delays);

        log.info("registerOrderSyncJobHandler localDateTime = {}, new = {}", localDateTime, newLocalDateTime);
        XxlJobHelper.log("registerOrderSyncJobHandler localDateTime = {}, new = {}", localDateTime, newLocalDateTime);
        thirdAmazonJobService.registerAmazonOrderSync(newLocalDateTime);
        log.info("StoreAmazonSyncJob registerOrderSyncJobHandler end.");
        XxlJobHelper.log("StoreAmazonSyncJob registerOrderSyncJobHandler end.");
    }

    /**
     * 获取亚马逊订单同步任务，并执行发起同步
     */
    @XxlJob("amazonOrdersSyncJobHandler")
    public void ordersSyncJobHandler() {
    }

    /**
     * 获取亚马逊订单详情同步任务，并执行发起同步
     */
    @XxlJob("amazonOrderItemSyncJobHandler")
    public void orderItemSyncJobHandler() {
        // TODO 亚马逊订单同步表中有未同步明细的sellerId ,根据卖家id数量开启线程。

    }


    /**
     * 获取亚马逊订单地址同步任务，并执行发起同步
     */
    @XxlJob("amazonOrderShippingAddressSyncJobHandler")
    public void orderShippingAddressSyncJobHandler() {
    }



}
