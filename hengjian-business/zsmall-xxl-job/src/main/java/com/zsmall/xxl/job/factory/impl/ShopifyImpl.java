package com.zsmall.xxl.job.factory.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.hengjian.common.core.exception.RStatusCodeException;
import com.hengjian.common.core.utils.SpringUtils;
import com.xxl.job.core.context.XxlJobHelper;
import com.zsmall.common.domain.LocaleMessage;
import com.zsmall.common.enums.BusinessCodeEnum;
import com.zsmall.common.enums.common.AttachmentTypeEnum;
import com.zsmall.common.enums.common.ChannelTypeEnum;
import com.zsmall.common.enums.order.FulfillmentPushStateEnum;
import com.zsmall.common.enums.order.LogisticsTypeEnum;
import com.zsmall.common.enums.order.OrderAddressType;
import com.zsmall.common.enums.order.OrderType;
import com.zsmall.common.enums.product.AttributeTypeEnum;
import com.zsmall.common.enums.product.WeightUnitEnum;
import com.zsmall.common.enums.productMapping.SyncStateEnum;
import com.zsmall.common.enums.statuscode.ZSMallStatusCodeEnum;
import com.zsmall.common.util.DecimalUtil;
import com.zsmall.extend.core.utils.ZonedDateUtil;
import com.zsmall.extend.shop.shopify.api.admin.*;
import com.zsmall.extend.shop.shopify.api.model.common.FulfillmentOrderLineItem;
import com.zsmall.extend.shop.shopify.api.model.common.InImage;
import com.zsmall.extend.shop.shopify.api.model.common.LineItemsByFulfillmentOrder;
import com.zsmall.extend.shop.shopify.api.model.fulfillment.InFulfillmentCreate2023;
import com.zsmall.extend.shop.shopify.api.model.fulfillment.InFulfillmentUpdate;
import com.zsmall.extend.shop.shopify.api.model.inventory.InInventoryItemUpdate;
import com.zsmall.extend.shop.shopify.api.model.inventory.InInventoryLevelSet;
import com.zsmall.extend.shop.shopify.api.model.order.InOrderPage;
import com.zsmall.extend.shop.shopify.api.model.product.InOptions;
import com.zsmall.extend.shop.shopify.api.model.product.InProductAdd;
import com.zsmall.extend.shop.shopify.api.model.product.InProductUpdate;
import com.zsmall.extend.shop.shopify.api.model.product.InProductVariant;
import com.zsmall.extend.shop.shopify.enums.FulfillmentStatus;
import com.zsmall.extend.shop.shopify.enums.OrderStatus;
import com.zsmall.extend.shop.shopify.enums.ProductStatusEnum;
import com.zsmall.extend.shop.shopify.exception.ShopifyClientException;
import com.zsmall.extend.shop.shopify.kit.ShopifyDelegate;
import com.zsmall.extend.shop.shopify.kit.ShopifyKit;
import com.zsmall.extend.shop.shopify.model.ShopifyPage;
import com.zsmall.extend.shop.shopify.model.fulfillment.Fulfillment;
import com.zsmall.extend.shop.shopify.model.fulfillment.FulfillmentOrder;
import com.zsmall.extend.shop.shopify.model.fulfillment.LineItem;
import com.zsmall.extend.shop.shopify.model.fulfillment.TrackingInfo;
import com.zsmall.extend.shop.shopify.model.order.BillingAddress;
import com.zsmall.extend.shop.shopify.model.order.Order;
import com.zsmall.extend.shop.shopify.model.order.OrderList;
import com.zsmall.extend.shop.shopify.model.product.ProductVariant;
import com.zsmall.extend.utils.ZSMallSystemEventUtils;
import com.zsmall.order.biz.support.OrderSupport;
import com.zsmall.order.entity.domain.*;
import com.zsmall.order.entity.domain.dto.OrderItemDTO;
import com.zsmall.order.entity.domain.event.GenerateOrderItemEvent;
import com.zsmall.order.entity.domain.event.RecalculateOrderAmountEvent;
import com.zsmall.order.entity.iservice.*;
import com.zsmall.product.entity.domain.*;
import com.zsmall.product.entity.iservice.*;
import com.zsmall.system.entity.domain.TenantSalesChannel;
import com.zsmall.system.entity.domain.vo.salesChannel.ShopifyExtraPropertiesVo;
import com.zsmall.system.entity.iservice.IShopifyExtraPropertiesService;
import com.zsmall.system.entity.iservice.ITenantSalesChannelService;
import com.zsmall.xxl.job.factory.ThirdChannelFactory;
import com.zsmall.xxl.job.factory.ThirdChannelService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * Shopify相关业务实现
 *
 * <AUTHOR>
 * @date 2023/6/25
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ShopifyImpl implements ThirdChannelService {

    private final IProductService iProductService;
    private final IProductSkuService iProductSkuService;
    private final IProductSkuPriceService iProductSkuPriceService;
    private final IProductSkuDetailService iProductSkuDetailService;
    private final IProductSkuAttributeService iProductSkuAttributeService;
    private final IProductSkuAttachmentService iProductSkuAttachmentService;
    private final IProductAttributeService iProductAttributeService;
    private final IProductCategoryService iProductCategoryService;
    private final IProductMappingService iProductMappingService;
    private final ITenantSalesChannelService iTenantSalesChannelService;
    private final IShopifyExtraPropertiesService iShopifyExtraPropertiesService;
    //private final IProductActivityItemService iProductActivityItemService;

    private final OrderCodeGenerator orderCodeGenerator;
    private final IOrdersService iOrdersService;
    private final IOrderLogisticsInfoService iOrderLogisticsInfoService;
    private final IOrderAddressInfoService iOrderAddressInfoService;
    private final IOrderItemService iOrderItemService;
    private final IOrderItemProductSkuService iOrderItemProductSkuService;
    private final IOrderItemTrackingRecordService iOrderItemTrackingRecordService;
    private final IOrderItemPriceService iOrderItemPriceService;
    private final IThirdChannelFulfillmentRecordService iThirdChannelFulfillmentRecordService;

    private final OrderSupport orderSupport;


    @Override
    public void afterPropertiesSet() throws Exception {
        ThirdChannelFactory.register(ChannelTypeEnum.Shopify, this);
    }


    /**
     * 推送商品至渠道店铺
     */
    @Override
    public void pushProduct(List<ProductMapping> mappingList) {
        log.info("Shopify定时任务 - 【推送商品至渠道店铺】 商品数量 = {}", CollUtil.size(mappingList));
        if (CollUtil.isNotEmpty(mappingList)) {
            for (ProductMapping productMapping : mappingList) {
                String tenantId = productMapping.getTenantId();
                String productSkuCode = productMapping.getProductSkuCode();
                ShopifyDelegate shopifyDelegate = null;
                String channelName = null;

                try {
                    Long channelId = productMapping.getChannelId();
                    TenantSalesChannel salesChannel = iTenantSalesChannelService.selectByIdNotTenant(channelId);
                    if (salesChannel == null) {
                        throw new RStatusCodeException(ZSMallStatusCodeEnum.SALES_CHANNEL_CANNOT_SYNC);
                    }

                    shopifyDelegate = ShopifyKit.create();
                    ShopifyExtraPropertiesVo shopifyExtraPropertiesVo = iShopifyExtraPropertiesService.selectByChannelId(channelId);

                    channelName = salesChannel.getChannelName();
                    String productCode = productMapping.getProductCode();
                    Product product = iProductService.queryByProductCodeNotTenant(productCode);
                    Long productId = product.getId();

                    ProductSku productSku = iProductSkuService.queryByProductSkuCode(productSkuCode);
                    Long productSkuId = productSku.getId();

                    String channelProductId = productMapping.getChannelProductId();
                    String channelSkuId = productMapping.getChannelSkuId();
                    String activityCode = productMapping.getActivityCode();

                    Integer stockTotal = 0;
                    if (StrUtil.isNotBlank(activityCode)) {
//                        ProductActivityItem activityItem = iProductActivityItemService.queryOneByEntity(ProductActivityItem.builder().activityCode(activityCode)
//                            .activityState(ProductActivityItemStateEnum.InProgress).build());
//                        if (activityItem != null) {
//                            stockTotal = activityItem.getQuantitySurplus();
//                        }
                    } else {
                        stockTotal = productSku.getStockTotal();
                    }

                    // 若第三方渠道商品ID不为空，说明之前已经同步过商品信息，本次只需要同步库存即可
                    if (channelProductId != null && channelSkuId != null) {
                        // 根据channelSkuId查询Shopify的Variant实体（Shopify那边的sku）
                        ProductVariant productVariant = shopifyDelegate.productVariantApi(channelName).getProductVariant(Long.parseLong(channelSkuId));
                        InInventoryLevelSet inventoryLevelSet = new InInventoryLevelSet();
                        inventoryLevelSet.setLocationId(shopifyExtraPropertiesVo.getLocationId());
                        inventoryLevelSet.setInventoryItemId(productVariant.getInventoryItemId());
                        inventoryLevelSet.setAvailable(stockTotal);
                        shopifyDelegate.inventoryLevelApi(channelName).setInventoryLevel(inventoryLevelSet);
                        productMapping.setSyncState(SyncStateEnum.Synced);
                    } else {  // 重新创建商品
                        ProductSkuDetail productSkuDetail = iProductSkuDetailService.queryByProductSkuCode(productSkuCode);
                        // 欧洲站需求暂时废弃
//                        ProductSkuPrice productSkuPrice = iProductSkuPriceService.queryByProductSkuCode(productSkuCode);
                        ProductSkuPrice productSkuPrice =null;
                        List<ProductSkuAttribute> skuAttributes = iProductSkuAttributeService.queryByProductSkuId(productSkuId);

                        // 不支持的重量单位
                        List<WeightUnitEnum> notSupportUnits = new ArrayList<>();
                        notSupportUnits.add(WeightUnitEnum.mg);
                        notSupportUnits.add(WeightUnitEnum.t);

                        // createFulfillmentService(shopifySdk, shopifyExtraProperties);
                        // String fulfillmentServiceHandle = shopifyExtraProperties.getFulfillmentServiceHandle();
                        // ProductDetail productDetail = productDetailRepository.findByProduct_Id(product.getId());

                        InProductAdd inProductAdd = new InProductAdd();
                        // 名称
                        inProductAdd.setTitle(productMapping.getProductName());
                        // 描述
                        inProductAdd.setBodyHtml(product.getDescription());
                        // 商家
                        inProductAdd.setVendor("ZSMall");

                        Long belongCategoryId = product.getBelongCategoryId();
                        // 分类
                        ProductCategory category = iProductCategoryService.getById(belongCategoryId);
                        String categoryName = category != null ? category.getCategoryName() : "";
                        inProductAdd.setProductType(categoryName);

                        // 卖点
                        List<ProductAttribute> featureList = iProductAttributeService.queryByProductIdAndAttributeType(productId, AttributeTypeEnum.Feature);
                        if (CollUtil.isNotEmpty(featureList)) {
                            String tags = featureList.stream().map(ProductAttribute::getAttributeValue).collect(Collectors.joining(","));
                            inProductAdd.setTags(tags);
                        }

                        BigDecimal markUpValue = productMapping.getMarkUpValue();
                        BigDecimal finalPrice = productMapping.getFinalPrice();

                        // sku集合
                        List<InProductVariant> variantList = new ArrayList<>();
                        if (markUpValue == null) {
                            throw new RStatusCodeException(ZSMallStatusCodeEnum.PRODUCT_SKU_MARK_UP_NOT_SET);
                        }

                        InProductVariant variant = new InProductVariant();
                        variant.setId(productMapping.getId());
                        variant.setSku(productMapping.getMappingSku());
                        variant.setBarcode(productSku.getUpc());
                        variant.setCompareAtPrice(DecimalUtil.bigDecimalToString(productSkuPrice.getPlatformDropShippingPrice()));
                        variant.setPrice(DecimalUtil.bigDecimalToString(finalPrice));

                        BigDecimal weight = productSkuDetail.getWeight();
                        WeightUnitEnum weightUnit = productSkuDetail.getWeightUnit();
                        // 单位转换
                        if (notSupportUnits.contains(weightUnit)) {
                            double convert = weightUnit.getConvert();
                            weight = NumberUtil.div(weight, convert, 2, RoundingMode.HALF_UP);
                            weightUnit = WeightUnitEnum.kg;
                        }
                        variant.setWeight(DecimalUtil.bigDecimalToString(weight));
                        variant.setWeightUnit(weightUnit.name());

                        List<InOptions> optionsList = new ArrayList<>();
                        for (int a = 0; a < skuAttributes.size(); a++) {
                            ProductSkuAttribute skuAttribute = skuAttributes.get(a);
                            String attributeName = skuAttribute.getAttributeName();
                            String attributeValue = skuAttribute.getAttributeValue();

                            InOptions options = new InOptions();
                            options.setName(attributeName);
                            options.setValues(CollUtil.newArrayList(attributeValue));
                            optionsList.add(options);

                            if (a == 0) {
                                variant.setOption1(attributeValue);
                            } else if (a == 1) {
                                variant.setOption2(attributeValue);
                            } else if (a == 2) {
                                variant.setOption3(attributeValue);
                            } else {
                                break;
                            }
                        }
                        variantList.add(variant);
                        inProductAdd.setVariants(variantList);
                        inProductAdd.setOptions(optionsList);

                        List<InImage> images = new ArrayList<>();
                        List<ProductSkuAttachment> skuAttachmentList = iProductSkuAttachmentService.queryBySkuIdAndAttachmentTypeOrderBySortAsc(productSkuId, AttachmentTypeEnum.Image);
                        if (CollectionUtils.isNotEmpty(skuAttachmentList)) {
                            images = skuAttachmentList.stream().map(vo -> new InImage().setSrc(vo.getAttachmentShowUrl())).collect(Collectors.toList());
                        }

                        inProductAdd.setImages(images);
                        inProductAdd.setStatus(ProductStatusEnum.active);
                        inProductAdd.setPublished(true);
                        inProductAdd.setMetafieldsGlobalDescriptionTag("");
                        inProductAdd.setMetafieldsGlobalTitleTag("");

                        ProductApi productApi = shopifyDelegate.productApi(channelName);
                        InventoryItemApi inventoryItemApi = shopifyDelegate.inventoryItemApi(channelName);
                        InventoryLevelApi inventoryLevelApi = shopifyDelegate.inventoryLevelApi(channelName);

                        log.info("Shopify商品推送，租户 = {}，SKU编号 = {}，准备推送 = {}", tenantId, productSkuCode, JSONUtil.toJsonStr(inProductAdd));
                        com.zsmall.extend.shop.shopify.model.product.Product shopifyProduct = productApi.addProduct(inProductAdd);
                        log.info("Shopify商品推送，租户 = {}，SKU编号 = {}，响应结果 = {}", tenantId, productSkuCode, JSONUtil.toJsonStr(shopifyProduct));
                        productMapping.setSyncState(SyncStateEnum.Synced);
                        productMapping.setSyncMessage(null);

                        Long shopifyProductId = shopifyProduct.getId();
                        productMapping.setChannelProductId(StrUtil.toStringOrNull(shopifyProductId));
                        List<ProductVariant> productVariants = shopifyProduct.getProductVariants();
                        for (ProductVariant productVariant : productVariants) {
                            productMapping.setChannelSkuId(StrUtil.toStringOrNull(productVariant.getId()));

                            Long inventoryItemId = productVariant.getInventoryItemId();
                            BigDecimal compareAtPrice = productVariant.getCompareAtPrice();

                            // 更新成本价
                            InInventoryItemUpdate inInventoryItemUpdate = new InInventoryItemUpdate();
                            inInventoryItemUpdate.setId(inventoryItemId);
                            inInventoryItemUpdate.setCost(compareAtPrice.doubleValue());
                            inInventoryItemUpdate.setTracked(true);
                            log.info("Shopify商品推送，租户 = {}，SKU编号 = {}，准备更新成本价 = {}", tenantId, productSkuCode, JSONUtil.toJsonStr(inInventoryItemUpdate));
                            inventoryItemApi.updateInventoryItem(inInventoryItemUpdate);

                            // 更新库存
                            InInventoryLevelSet inventoryLevelSet = new InInventoryLevelSet();
                            inventoryLevelSet.setLocationId(shopifyExtraPropertiesVo.getLocationId());
                            inventoryLevelSet.setInventoryItemId(inventoryItemId);
                            inventoryLevelSet.setAvailable(stockTotal);
                            inventoryLevelSet.setDisconnectIfNecessary(true);
                            log.info("Shopify商品推送，租户 = {}，SKU编号 = {}，准备更新库存 = {}", tenantId, productSkuCode, JSONUtil.toJsonStr(inventoryLevelSet));
                            inventoryLevelApi.setInventoryLevel(inventoryLevelSet);
                        }
                    }
                } catch (ShopifyClientException e) {
                    log.error("Shopify商品推送失败（Shopify错误），租户 = {}，SKU编号 = {}，原因 = {}", tenantId, productSkuCode, e.getMessage(), e);
                    productMapping.setSyncState(SyncStateEnum.SyncFailed);
                    productMapping.setSyncMessage(LocaleMessage.byStatusCodeToJSON(ZSMallStatusCodeEnum.SYNC_PRODUCT_UNKNOWN_ERROR));
                    deleteProductById(shopifyDelegate, channelName, Long.parseLong(productMapping.getChannelProductId()));
                    if (JSONUtil.isTypeJSON(e.getDescription())) {
                        JSONObject result = JSONUtil.parseObj(e.getDescription());
                        JSONObject respBody = result.getJSONObject("respBody");
                        if (respBody != null && StrUtil.isNotBlank(respBody.getStr("errors"))) {
                            String errors = respBody.getStr("errors");
                            productMapping.setSyncMessage(LocaleMessage.toJSON(errors, errors));
                        }
                    } else {
                        productMapping.setSyncMessage(LocaleMessage.toJSON(e.getMessage(), e.getMessage()));
                    }
                } catch (RStatusCodeException e) {
                    log.error("Shopify商品推送失败（业务错误），租户 = {}，SKU编号 = {}，原因 = {}", tenantId, productSkuCode, e.getMessage(), e);
                    productMapping.setSyncState(SyncStateEnum.SyncFailed);
                    productMapping.setSyncMessage(LocaleMessage.byStatusCodeToJSON(e.getStatusCode()));
                    deleteProductById(shopifyDelegate, channelName, Long.parseLong(productMapping.getChannelProductId()));
                } catch (Exception e) {
                    log.error("Shopify商品推送失败（未知错误），租户 = {}，SKU编号 = {}，原因 = {}", tenantId, productSkuCode, e.getMessage(), e);
                    productMapping.setSyncState(SyncStateEnum.SyncFailed);
                    productMapping.setSyncMessage(LocaleMessage.byStatusCodeToJSON(ZSMallStatusCodeEnum.SYNC_PRODUCT_UNKNOWN_ERROR));
                    deleteProductById(shopifyDelegate, channelName, Long.parseLong(productMapping.getChannelProductId()));
                }
                iProductMappingService.updateById(productMapping);
            }
        }
    }

    /**
     * 更新商品
     *
     * @param mappingList
     */
    @Override
    public void updateProduct(List<ProductMapping> mappingList) {
        log.info("Shopify定时任务 - 【更新商品】 商品数量 = {}", CollUtil.size(mappingList));
        for (ProductMapping productMapping : mappingList) {
            Long productMappingId = productMapping.getId();
            Long channelId = productMapping.getChannelId();

            TenantSalesChannel salesChannel = iTenantSalesChannelService.selectByIdNotTenant(channelId);
            if (salesChannel != null) {
                try {
                    ShopifyDelegate shopifyDelegate = ShopifyKit.create();
                    String channelName = salesChannel.getChannelName();
                    String productName = productMapping.getProductName();
                    String channelProductId = productMapping.getChannelProductId();
                    String channelSkuId = productMapping.getChannelSkuId();
                    BigDecimal finalPrice = productMapping.getFinalPrice();

                    String mappingSku = productMapping.getMappingSku();
                    if (mappingSku == null) {
                        mappingSku = productMapping.getProductSkuCode();
                    }

                    InProductUpdate inProductUpdate = new InProductUpdate();
                    inProductUpdate.setId(Long.parseLong(channelProductId));
                    inProductUpdate.setTitle(productName);

                    List<InProductVariant> variants = new ArrayList<>();
                    InProductVariant inProductVariant = new InProductVariant();
                    inProductVariant.setId(Long.parseLong(channelSkuId));
                    inProductVariant.setSku(mappingSku);
                    inProductVariant.setPrice(DecimalUtil.bigDecimalToString(finalPrice));
                    variants.add(inProductVariant);
                    inProductUpdate.setVariants(variants);
                    shopifyDelegate.productApi(channelName).updateProduct(inProductUpdate);

                    ShopifyExtraPropertiesVo shopifyExtraPropertiesVo = iShopifyExtraPropertiesService.selectByChannelId(channelId);
                    if (shopifyExtraPropertiesVo != null && shopifyExtraPropertiesVo.getLocationId() != null) {
                        String activityCode = productMapping.getActivityCode();
                        Integer stockTotal = 0;
                        if (StrUtil.isNotBlank(activityCode)) {
//                            ProductActivityItem activityItem = iProductActivityItemService.queryOneByEntity(ProductActivityItem.builder().activityCode(activityCode)
//                                .activityState(ProductActivityItemStateEnum.InProgress).build());
//                            if (activityItem != null) {
//                                stockTotal = activityItem.getQuantitySurplus();
//                            }
                        } else {
                            ProductSku productSku = iProductSkuService.queryByProductSkuCode(productMapping.getProductSkuCode());
                            stockTotal = productSku.getStockTotal();
                        }

                        Long locationId = shopifyExtraPropertiesVo.getLocationId();
                        ProductVariant productVariant = shopifyDelegate.productVariantApi(channelName).getProductVariant(Long.parseLong(channelSkuId));
                        InInventoryLevelSet inventoryLevelSet = new InInventoryLevelSet();
                        inventoryLevelSet.setLocationId(locationId);
                        inventoryLevelSet.setInventoryItemId(productVariant.getInventoryItemId());
                        inventoryLevelSet.setAvailable(stockTotal);
                        try {
                            shopifyDelegate.inventoryLevelApi(channelName).setInventoryLevel(inventoryLevelSet);
                        } catch (Exception e) {
                            log.error("Shopify商品更新任务 - 【更新库存】 productMappingId = {} 更新库存出现异常，原因：{}", productMappingId, e.getMessage(), e);
                            XxlJobHelper.log("Shopify商品更新任务 - 【更新库存】 productMappingId = {} 更新库存出现异常，原因：{}", productMappingId, e.getMessage());
                        }
                    }
                } catch (Exception e) {
                    log.info("Shopify商品更新失败 channelId = {} productMappingId = {} 原因 {}", channelId, productMappingId, e.getMessage(), e);
                }
            }
            productMapping.setSyncState(SyncStateEnum.Synced);
            iProductMappingService.updateById(productMapping);
        }
    }

    /**
     * 取消同步商品
     *
     * @param mappingList
     */
    @Override
    public void cancelProduct(List<ProductMapping> mappingList) {
        log.info("Shopify定时任务 - 【取消同步商品】 商品数量 = {}", CollUtil.size(mappingList));
        for (ProductMapping productMapping : mappingList) {

            Long channelId = productMapping.getChannelId();
            TenantSalesChannel salesChannel = iTenantSalesChannelService.selectByIdNotTenant(channelId);
            if (salesChannel != null) {
                ShopifyDelegate shopifyDelegate = ShopifyKit.create();
                String channelName = salesChannel.getChannelName();
                String channelProductId = productMapping.getChannelProductId();
                deleteProductById(shopifyDelegate, channelName, Long.parseLong(channelProductId));
            }

            productMapping.setSyncState(SyncStateEnum.NotSynced);
            productMapping.setChannelProductId(null);
            productMapping.setChannelSkuId(null);
            iProductMappingService.updateById(productMapping);
        }
    }

    /**
     * 删除商品
     *
     * @param mappingList
     */
    @Override
    public void deleteProduct(List<ProductMapping> mappingList) {
        log.info("Shopify定时任务 - 【删除商品】 商品数量 = {}", CollUtil.size(mappingList));
        for (ProductMapping productMapping : mappingList) {
            Long channelId = productMapping.getChannelId();
            TenantSalesChannel salesChannel = iTenantSalesChannelService.selectByIdNotTenant(channelId);
            if (salesChannel != null) {
                ShopifyDelegate shopifyDelegate = ShopifyKit.create();
                String channelName = salesChannel.getChannelName();
                String channelProductId = productMapping.getChannelProductId();
                if (StrUtil.isNotBlank(channelProductId)) {
                    deleteProductById(shopifyDelegate, channelName, Long.parseLong(channelProductId));
                }
            }
            iProductMappingService.removeById(productMapping);
        }
    }

    /**
     * 从渠道店铺拉取订单
     */
    @Override
    public void pullOrder(String startDate, String endDate) {
        log.info("Shopify定时任务 - 【拉取订单】 起始时间 = {} 截止时间 = {}", startDate, endDate);
        List<TenantSalesChannel> channelList = iTenantSalesChannelService.queryValidByChannelTypeNotTenant(ChannelTypeEnum.Shopify);
        log.info("Shopify定时任务 - 【拉取订单】 有效店铺数量 = {}", CollUtil.size(channelList));
        for (TenantSalesChannel salesChannel : channelList) {
            String tenantId = salesChannel.getTenantId();
            String channelName = salesChannel.getChannelName();
            try {
                ShopifyDelegate shopifyDelegate = ShopifyKit.create();
                OrderApi orderApi = shopifyDelegate.orderApi(channelName);

                InOrderPage orderPage = new InOrderPage();
                orderPage.setLimit(10);
                // 仅查询已付款的订单
                orderPage.setFinancialStatus("paid");
                if (StringUtils.isNotBlank(startDate) && StringUtils.isNotBlank(endDate)) {
                    String start = ZonedDateUtil.local2ZoneDateTime(startDate, ZoneId.of("-4")).toString();
                    String end = ZonedDateUtil.local2ZoneDateTime(endDate, ZoneId.of("-4")).toString();
                    orderPage.setUpdatedAtMin(start);
                    orderPage.setUpdatedAtMax(end);
                } else {  // 未设置起始结束时间，默认为当前时间减去72小时作为开始时间
                    LocalDateTime localEnd = LocalDateTime.now();
                    LocalDateTime localStart = localEnd.minusDays(3);
                    String start = ZonedDateUtil.local2ZoneDateTime(localStart, ZoneId.of("-4")).toString();
                    String end = ZonedDateUtil.local2ZoneDateTime(localEnd, ZoneId.of("-4")).toString();
                    orderPage.setUpdatedAtMin(start);
                    orderPage.setUpdatedAtMax(end);
                }

                log.info("Shopify定时任务 - 【拉取订单】 店铺 = {} 订单拉取请求参数 = {}", channelName, JSONUtil.toJsonStr(orderPage));
                List<Order> shopifyOrderList = new ArrayList<>();
                ShopifyPage<OrderList> orders = orderApi.getOrders(orderPage);
                OrderList result = orders.getResult();
                if (result != null) {
                    shopifyOrderList.addAll(result.getOrders());
                    while (orders.getNextPageInfo() != null) {
                        orders = orderApi.getOrders(orders.getNextPageInfo(), 10);
                        result = orders.getResult();
                        if (result != null) {
                            shopifyOrderList.addAll(result.getOrders());
                        }
                    }
                }

                List<Orders> ordersList = ordersBodyToEntity(shopifyOrderList, salesChannel);
                if (CollUtil.isNotEmpty(ordersList)) {
                    Boolean autoPayment = ZSMallSystemEventUtils.checkAutoPaymentEvent(tenantId);
                    if (autoPayment) {
                        try {
                            orderSupport.orderPayChain(tenantId, ordersList, true, true);
                        } catch (RStatusCodeException e) {
                            log.error("Shopify店铺[{}]，订单自动支付失败，原因 {}", channelName, e.getMessage(), e);
                        }
                    }
                }
            } catch (Exception e) {
                log.error("Shopify店铺[{}]，拉取订单失败，原因 {}", channelName, e.getMessage(), e);
            }
        }
    }

    /**
     * 推送履约信息至渠道店铺
     *
     * @param fulfillmentRecordList
     */
    @Override
    public void pushFulfillment(List<ThirdChannelFulfillmentRecord> fulfillmentRecordList) {
        log.info("Shopify定时任务 - 【推送履约信息至渠道店铺】 需要推送的数量 = {}", CollUtil.size(fulfillmentRecordList));
        for (ThirdChannelFulfillmentRecord fulfillmentRecord : fulfillmentRecordList) {

            String orderNo = fulfillmentRecord.getOrderNo();
            Long channelId = fulfillmentRecord.getChannelId();
            TenantSalesChannel salesChannel = iTenantSalesChannelService.selectByIdNotTenant(channelId);

            try {
                if (salesChannel != null) {
                    String channelOrderNo = fulfillmentRecord.getChannelOrderNo();
                    String channelItemNo = fulfillmentRecord.getChannelItemNo();
                    Long channelOrderId = Long.parseLong(channelOrderNo);

                    ShopifyDelegate shopifyDelegate = ShopifyKit.create();
                    FulfillmentOrderApi fulfillmentOrderApi = shopifyDelegate.fulfillmentOrderApi(salesChannel.getChannelName());
                    FulfillmentApi fulfillmentApi = shopifyDelegate.fulfillmentApi(salesChannel.getChannelName());

                    String channelFulfillmentId = fulfillmentRecord.getChannelFulfillmentId();

                    // 查询该订单所有履约单
                    List<FulfillmentOrder> fulfillmentOrderList = fulfillmentOrderApi.getFulfillmentOrders(channelOrderId);
                    log.info("Shopify订单[{}] - 【推送履约信息至渠道店铺】 - orderNo = {}, channelId = {}, fulfillmentOrderList = {}", orderNo, channelId, JSONUtil.toJsonStr(fulfillmentOrderList));

                    // 不存在履约单，抛出异常
                    if (CollUtil.isEmpty(fulfillmentOrderList)) {
                        throw new RStatusCodeException(ZSMallStatusCodeEnum.SHOPIFY_FULFILLMENT_ORDER_NOT_EXISTS);
                    }

                    // 筛选出开启状态的履约单
                    List<FulfillmentOrder> openFulfillmentOrder =
                        fulfillmentOrderList.stream().filter(fo -> OrderStatus.open.equals(fo.getStatus()))
                            .collect(Collectors.toList());
                    // 不存在履约单，抛出异常
                    if (CollUtil.isEmpty(openFulfillmentOrder)) {
                        throw new RStatusCodeException(ZSMallStatusCodeEnum.SHOPIFY_FULFILLMENT_ORDER_NOT_EXISTS);
                    }

                    FulfillmentOrder fulfillmentOrder = openFulfillmentOrder.get(0);
                    Long fulfillmentOrderId = fulfillmentOrder.getId();
                    List<LineItem> fo_LineItems = fulfillmentOrder.getLineItems();

                    // 构建Shopify履约相关参数
                    InFulfillmentCreate2023 inFulfillmentCreate2023 = new InFulfillmentCreate2023();

                    TrackingInfo trackingInfo = new TrackingInfo();
                    List<LineItemsByFulfillmentOrder> lineItemsByFulfillmentOrders = new ArrayList<>();
                    String orderItemNo = fulfillmentRecord.getOrderItemNo();
                    List<OrderItemTrackingRecord> trackingList = iOrderItemTrackingRecordService.getListByOrderItemNo(orderItemNo);
                    log.info("Shopify订单[{}] - 【推送履约信息至渠道店铺】 - orderItemNo = {}, trackingList.size = {}", orderNo, orderItemNo,
                        CollectionUtils.size(trackingList));

                    if (CollectionUtils.isNotEmpty(trackingList)) {
                        List<String> trackingNoList = new ArrayList<>();
                        String carrier = "";
                        for (OrderItemTrackingRecord trackingOrders : trackingList) {
                            carrier = trackingOrders.getLogisticsCarrier();
                            String trackingNo = trackingOrders.getLogisticsTrackingNo();
                            trackingNoList.add(trackingNo);
                        }

                        if (StrUtil.equals(carrier, "fedex")) {
                            carrier = "FedEx";
                        }
                        trackingInfo.setCompany(carrier);
                        trackingInfo.setNumber(CollUtil.join(trackingNoList, ","));
                    }

                    List<FulfillmentOrderLineItem> fulfillmentOrderLineItems = new ArrayList<>();
                    if (StringUtils.isNotBlank(channelItemNo) && CollUtil.isNotEmpty(fo_LineItems)) {
                        Long itemId = Long.parseLong(channelItemNo);
                        // 从履约单中去除该商品的履约商品信息
                        Optional<LineItem> firstLineItem =
                            fo_LineItems.stream().filter(fo_lineItem -> ObjectUtil.equals(fo_lineItem.getLineItemId(), itemId)).findFirst();
                        if (firstLineItem.isPresent()) {
                            LineItem lineItem = firstLineItem.get();
                            Integer fulfillableQuantity = lineItem.getFulfillableQuantity();
                            Long lineItemId = lineItem.getId();

                            FulfillmentOrderLineItem newLineItem = new FulfillmentOrderLineItem();
                            newLineItem.setId(lineItemId);
                            newLineItem.setQuantity(fulfillableQuantity);
                            fulfillmentOrderLineItems.add(newLineItem);
                        }
                    }

                    // 发起Shopify履约申请
                    if (channelFulfillmentId != null) {  // Shopify履约ID不为空，仅更新单号
                        InFulfillmentUpdate inFulfillmentUpdate = new InFulfillmentUpdate();
                        inFulfillmentUpdate.setTrackingInfo(trackingInfo);

                        log.info("Shopify订单[{}] - 【推送履约信息至渠道店铺】 - 请求前trackingInfo = {}", orderNo, JSONUtil.toJsonStr(trackingInfo));
                        Fulfillment updateFulfillment = fulfillmentApi.updateTracking(inFulfillmentUpdate, Long.parseLong(channelFulfillmentId));
                        log.info("Shopify订单[{}] - 【推送履约信息至渠道店铺】 - 请求后updateFulfillment = {}", orderNo, JSONUtil.toJsonStr(updateFulfillment));
                    } else {
                        LineItemsByFulfillmentOrder lineItemsByFulfillmentOrder = new LineItemsByFulfillmentOrder();
                        lineItemsByFulfillmentOrder.setFulfillmentOrderId(fulfillmentOrderId);
                        lineItemsByFulfillmentOrder.setFulfillmentOrderLineItems(fulfillmentOrderLineItems);
                        lineItemsByFulfillmentOrders.add(lineItemsByFulfillmentOrder);

                        inFulfillmentCreate2023.setTrackingInfo(trackingInfo);
                        inFulfillmentCreate2023.setLineItemsByFulfillmentOrder(lineItemsByFulfillmentOrders);

                        log.info("Shopify订单[{}] - 【推送履约信息至渠道店铺】 - 请求前createFulfillment = {}", orderNo, JSONUtil.toJsonStr(inFulfillmentCreate2023));
                        Fulfillment createFulfillment = fulfillmentApi.createFulfillment_2023(inFulfillmentCreate2023);
                        log.info("Shopify订单[{}] - 【推送履约信息至渠道店铺】 - 请求后createFulfillment = {}", orderNo, JSONUtil.toJsonStr(createFulfillment));

                        if (StringUtils.equalsAny(createFulfillment.getStatus().name(), FulfillmentStatus.success.name(),
                            FulfillmentStatus.pending.name())) {
                            Long fulfillmentId = createFulfillment.getId();
                            fulfillmentRecord.setChannelFulfillmentId(StrUtil.toStringOrNull(fulfillmentId));
                        }
                    }

                    fulfillmentRecord.setFulfillmentPushState(FulfillmentPushStateEnum.Pushed);
                    fulfillmentRecord.setChannelFulfillmentMessage(null);
                } else {
                    throw new RStatusCodeException(ZSMallStatusCodeEnum.SALES_CHANNEL_NOT_EXIST);
                }
            } catch (RStatusCodeException e) {
                log.info("Shopify订单[{}]，推送履约信息至渠道店铺出现业务异常，原因 {}", orderNo, e.getMessage(), e);
                fulfillmentRecord.setChannelFulfillmentMessage(LocaleMessage.byStatusCodeToJSON(e.getStatusCode()));
                fulfillmentRecord.setFulfillmentPushState(FulfillmentPushStateEnum.PushFailed);
            } catch (Exception e) {
                log.info("Shopify订单[{}]，推送履约信息至渠道店铺出现未知异常，原因 {}", orderNo, e.getMessage(), e);
                fulfillmentRecord.setChannelFulfillmentMessage(LocaleMessage.byStatusCodeToJSON(ZSMallStatusCodeEnum.PUSH_FULFILLMENT_TO_SALES_CHANNEL_ERROR));
                fulfillmentRecord.setFulfillmentPushState(FulfillmentPushStateEnum.PushFailed);
            }
            iThirdChannelFulfillmentRecordService.updateById(fulfillmentRecord);
        }
    }

    /**
     * 更新库存
     *
     * @param mappingList
     */
    @Override
    public void updateStock(List<ProductMapping> mappingList, Integer stockTotal) {
        log.info("Shopify定时任务 - 【更新库存】 需要推送的数量 = {}", CollUtil.size(mappingList));
        for (ProductMapping productMapping : mappingList) {
            Long productMappingId = productMapping.getId();
            Long channelId = productMapping.getChannelId();
            TenantSalesChannel salesChannel = iTenantSalesChannelService.selectByIdNotTenant(channelId);
            if (salesChannel != null) {
                ShopifyDelegate shopifyDelegate = ShopifyKit.create();
                String channelName = salesChannel.getChannelName();
                String activityCode = productMapping.getActivityCode();
                String channelProductId = productMapping.getChannelProductId();
                String channelSkuId = productMapping.getChannelSkuId();

                if (channelProductId == null) {
                    log.info("Shopify定时任务 - 【更新库存】 productMappingId = {} channelProductId为空，无法同步至Shopify", productMappingId);
                    XxlJobHelper.log("Shopify定时任务 - 【更新库存】 productMappingId = {} channelProductId为空，无法同步至Shopify", productMappingId);
                    continue;
                }

                // 如果是参加活动的，则不能取定时器传过来的通用库存，需要单独取活动库存
//                if (StrUtil.isNotBlank(activityCode)) {
//                    ProductActivityItem activityItem = iProductActivityItemService.queryOneByEntity(ProductActivityItem.builder().activityCode(activityCode).activityState(ProductActivityItemStateEnum.InProgress).build());
//                    if (activityItem != null) {
//                        stockTotal = activityItem.getQuantitySurplus();
//                    } else {
//                        stockTotal = 0;
//                    }
//                }

                ProductVariant productVariant = shopifyDelegate.productVariantApi(channelName).getProductVariant(Long.parseLong(channelSkuId));
                if (productVariant != null) {
                    ShopifyExtraPropertiesVo shopifyExtraPropertiesVo = iShopifyExtraPropertiesService.selectByChannelId(channelId);
                    if (shopifyExtraPropertiesVo != null) {
                        // 从商店额外参数中取出Shopify的location id 同步库存时需要用到
                        Long locationId = shopifyExtraPropertiesVo.getLocationId();
                        if (locationId != null) {
                            Long inventoryItemId = productVariant.getInventoryItemId();
                            InInventoryLevelSet inventoryLevelSet = new InInventoryLevelSet();
                            inventoryLevelSet.setLocationId(locationId);
                            inventoryLevelSet.setInventoryItemId(inventoryItemId);
                            inventoryLevelSet.setAvailable(stockTotal);
                            try {
                                shopifyDelegate.inventoryLevelApi(channelName).setInventoryLevel(inventoryLevelSet);
                            } catch (Exception e) {
                                log.error("Shopify定时任务 - 【更新库存】 productMappingId = {} 更新库存出现异常，原因：{}", productMappingId, e.getMessage(), e);
                                XxlJobHelper.log("Shopify定时任务 - 【更新库存】 productMappingId = {} 更新库存出现异常，原因：{}", productMappingId, e.getMessage());
                            }
                        }
                    } else {
                        log.info("Shopify定时任务 - 【更新库存】 productMappingId = {} Shopify额外配置信息不存在，无法同步至Shopify", productMappingId);
                        XxlJobHelper.log("Shopify定时任务 - 【更新库存】 productMappingId = {} Shopify额外配置信息不存在，无法同步至Shopify", productMappingId);
                    }
                } else {
                    log.info("Shopify定时任务 - 【更新库存】 productMappingId = {} productVariant查询失败，无法同步至Shopify", productMappingId);
                    XxlJobHelper.log("Shopify定时任务 - 【更新库存】 productMappingId = {} productVariant查询失败，无法同步至Shopify", productMappingId);
                }
            }
        }
    }

    @Override
    public void pullOrderByJson(String voJson, String startDate, String endDate) {

    }

    @Override
    public void pullProduct(String json) {

    }

    /**
     * 将Orders JSON响应信息转化为实体类
     *
     * @param shopifyOrderList
     * @param channel
     * @return
     */
    private List<Orders> ordersBodyToEntity(List<Order> shopifyOrderList, TenantSalesChannel channel) throws RStatusCodeException {
        List<Orders> orderList = new ArrayList<>();
        Long channelId = channel.getId();
        String tenantId = channel.getTenantId();

        for (Order shopifyOrder : shopifyOrderList) {
            String shopifyOrderName = shopifyOrder.getName();
            Long channelOrderNo = shopifyOrder.getId();

            Orders order = iOrdersService.queryValidOrder(channelId, tenantId, channelOrderNo.toString());

            if (order == null) {  // 数据库中没有存过该订单，准备创建实体类
                order = new Orders();
                String orderNo = orderCodeGenerator.codeGenerate(BusinessCodeEnum.OrderNo);
                order.setOrderNo(orderNo);
                order.setTenantId(tenantId);
                order.setOrderType(OrderType.Normal);
                order.setChannelType(ChannelTypeEnum.Shopify);
                order.setLogisticsType(LogisticsTypeEnum.DropShipping);
                order.setChannelId(channelId);
                order.setOrderNote(shopifyOrder.getNote());
                order.setChannelOrderNo(shopifyOrder.getId() != null ? shopifyOrder.getId().toString() : "");
                order.setChannelOrderName(shopifyOrderName);
                order.setChannelOrderTime(ZonedDateUtil.zoned2DateTime(shopifyOrder.getCreated_at(), ZoneId.of("-04:00")));

                // 物流信息
                OrderLogisticsInfo orderLogisticsInfo = new OrderLogisticsInfo();
                // Shopify目前都是代发
                // orderLogisticsInfo.setOrderId(orderId);
                orderLogisticsInfo.setOrderNo(orderNo);
                orderLogisticsInfo.setLogisticsType(LogisticsTypeEnum.DropShipping);

                // 收货地址
                BillingAddress shippingAddress = shopifyOrder.getShippingAddress();
                OrderAddressInfo orderAddressInfo = new OrderAddressInfo();
                if (shippingAddress != null) {
                    String zip = shippingAddress.getZip();

                    String countryCode = shippingAddress.getCountryCode();

                    orderAddressInfo.setOrderNo(orderNo);
                    orderAddressInfo.setAddressType(OrderAddressType.ShipAddress);
                    orderAddressInfo.setRecipient(shippingAddress.getName());
                    orderAddressInfo.setPhoneNumber(shippingAddress.getPhone());
                    orderAddressInfo.setCountry(countryCode);
                    orderAddressInfo.setCountryCode(countryCode);
                    orderAddressInfo.setState(shippingAddress.getProvinceCode());
                    orderAddressInfo.setStateCode(shippingAddress.getProvinceCode());
                    orderAddressInfo.setCity(shippingAddress.getCity());
                    orderAddressInfo.setAddress1(shippingAddress.getAddress1());
                    orderAddressInfo.setAddress2(shippingAddress.getAddress2());
                    orderAddressInfo.setZipCode(zip);

                    orderLogisticsInfo.setZipCode(StrUtil.trim(zip));
                    orderLogisticsInfo.setLogisticsZipCode(StrUtil.trim(zip));

                    if (StrUtil.contains(zip, "-")) {
                        // 存在-的邮编，需要分割出前面5位的主邮编
                        String mainZipCode = StrUtil.trim(StrUtil.split(zip, "-").get(0));
                        orderLogisticsInfo.setLogisticsZipCode(StrUtil.trim(mainZipCode));
                    }
                    orderLogisticsInfo.setLogisticsCountryCode(countryCode);
                }

                // 账单地址
                BillingAddress billingAddress = shopifyOrder.getBillingAddress();
                OrderAddressInfo orderBillingInfo = new OrderAddressInfo();
                if (billingAddress != null) {
                    orderBillingInfo.setOrderNo(orderNo);
                    orderBillingInfo.setAddressType(OrderAddressType.BillAddress);
                    orderBillingInfo.setRecipient(billingAddress.getName());
                    orderBillingInfo.setPhoneNumber(billingAddress.getPhone());
                    orderBillingInfo.setCountry(billingAddress.getCountryCode());
                    orderBillingInfo.setCountryCode(billingAddress.getCountryCode());
                    orderBillingInfo.setState(billingAddress.getProvinceCode());
                    orderBillingInfo.setStateCode(billingAddress.getProvinceCode());
                    orderBillingInfo.setCity(billingAddress.getCity());
                    orderBillingInfo.setAddress1(billingAddress.getAddress1());
                    orderBillingInfo.setAddress2(billingAddress.getAddress2());
                    orderBillingInfo.setZipCode(billingAddress.getZip());
                }

                List<LineItem> lineItems = shopifyOrder.getLineItems();
                List<OrderItem> orderItemList = new ArrayList<>();
                List<OrderItemPrice> orderItemPriceList = new ArrayList<>();

                LocaleMessage localeMessage = new LocaleMessage();
                Integer totalQuantity = 0;
                for (LineItem lineItem : lineItems) {
                    Long itemId = lineItem.getId();
                    Long variantId = lineItem.getVariantId();
                    String mappingSku = lineItem.getSku();
                    Integer quantity = lineItem.getQuantity();
                    totalQuantity += quantity;

                    ProductMapping productMapping = iProductMappingService.queryByTenantAndMappingSku(tenantId, channelId, mappingSku);
                    if (productMapping != null) {
                        GenerateOrderItemEvent generateOrderItemEvent = new GenerateOrderItemEvent();
                        generateOrderItemEvent.setDTenantId(tenantId);
                        generateOrderItemEvent.setOrder(order);
                        generateOrderItemEvent.setChannelTypeEnum(ChannelTypeEnum.Shopify);
                        generateOrderItemEvent.setLogisticsType(LogisticsTypeEnum.DropShipping);
                        generateOrderItemEvent.setCountry(orderAddressInfo.getCountryCode());
                        generateOrderItemEvent.setActivityCode(productMapping.getActivityCode());
                        generateOrderItemEvent.setProductSkuCode(productMapping.getProductSkuCode());
                        generateOrderItemEvent.setTotalQuantity(quantity);
                        SpringUtils.context().publishEvent(generateOrderItemEvent);

                        OrderItemDTO outDTO = generateOrderItemEvent.getOutDTO();
                        LocaleMessage message = outDTO.getLocaleMessage();
                        if (message.hasData()) {
                            localeMessage.append(message);
                        }

                        OrderItem orderItem = outDTO.getOrderItem();
                        OrderItemPrice orderItemPrice = outDTO.getOrderItemPrice();
                        OrderItemProductSku orderItemProductSku = outDTO.getOrderItemProductSku();

                        orderItem.setChannelItemNo(StrUtil.toStringOrNull(itemId));
                        orderItem.setTenantId(tenantId);
                        orderItemProductSku.setChannelVariantId(variantId);
                        orderItemProductSku.setTenantId(tenantId);

                        orderItem.setOrderItemPrice(orderItemPrice);
                        orderItem.setOrderItemProductSku(orderItemProductSku);

                        orderItemList.add(orderItem);
                        orderItemPriceList.add(orderItemPrice);
                    } else {
                        log.info("Shopify订单 {} 不存在映射SKU {}", shopifyOrderName, mappingSku);
                    }
                }

                log.info("Shopify订单 {} 有效的子订单数量 {}", shopifyOrderName, CollUtil.size(orderItemList));
                if (CollUtil.isNotEmpty(orderItemList)) {
                    order.setTotalQuantity(totalQuantity);

                    RecalculateOrderAmountEvent recalculateOrderAmountEvent = new RecalculateOrderAmountEvent();
                    recalculateOrderAmountEvent.setOrder(order);
                    recalculateOrderAmountEvent.setOrderItemPriceList(orderItemPriceList);
                    SpringUtils.publishEvent(recalculateOrderAmountEvent);

                    iOrdersService.save(order);
                    Long orderId = order.getId();

                    iOrderLogisticsInfoService.save(orderLogisticsInfo.setOrderId(orderId));

                    if (orderAddressInfo.getOrderNo() != null) {
                        iOrderAddressInfoService.save(orderAddressInfo.setOrderId(orderId));
                    }

                    if (orderBillingInfo.getOrderNo() != null) {
                        iOrderAddressInfoService.save(orderBillingInfo.setOrderId(orderId));
                    }

                    for (OrderItem orderItem : orderItemList) {
                        iOrderItemService.save(orderItem.setOrderId(orderId));
                        Long orderItemId = orderItem.getId();

                        OrderItemPrice orderItemPrice = orderItem.getOrderItemPrice();
                        OrderItemProductSku orderItemProductSku = orderItem.getOrderItemProductSku();

                        iOrderItemPriceService.save(orderItemPrice.setOrderItemId(orderItemId));
                        iOrderItemProductSkuService.save(orderItemProductSku.setOrderItemId(orderItemId));
                    }
                    orderList.add(order);
                }
            }
        }
        return orderList;
    }

    /**
     * 根据id删除第三方渠道商品
     *
     * @param channelProductId
     */
    private void deleteProductById(ShopifyDelegate shopifyDelegate, String channelName, Long channelProductId) {
        log.info("Shopify 根据id删除第三方渠道商品 channelName = {} channelProductId = {}", channelName, channelProductId);
        if (shopifyDelegate == null || StrUtil.isBlank(channelName) || channelProductId == null) {
            return;
        }

        try {
            shopifyDelegate.productApi(channelName).deleteProduct(channelProductId);
        } catch (Exception e) {
            log.info("Shopify 根据id删除第三方渠道商品失败，原因 {}", e.getMessage(), e);
        }
    }
}
