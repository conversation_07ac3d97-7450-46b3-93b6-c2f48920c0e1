package com.hengjian.common.mail.config;

import com.hengjian.common.mail.config.properties.MailProperties;
import com.hengjian.common.mail.core.JavaxMailTemplate;
import com.hengjian.common.mail.core.MailchimpMailTemplate;
import com.hengjian.common.mail.core.SendGridMailTemplate;
import com.hengjian.common.mail.utils.MailAccount;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * JavaMail 配置
 *
 * <AUTHOR>
 */
@AutoConfiguration
@EnableConfigurationProperties(MailProperties.class)
public class MailConfig {

    @Configuration
//    @ConditionalOnProperty(value = {"mail.enabled", "mail.javax.enabled"}, havingValue = "true")
//    @ConditionalOnMissingClass("com.sendgrid.SendGrid")
    @ConditionalOnExpression("${mail.enabled:false} && ${mail.javax.enabled:false}")
    static class JavaxMailConfig {

        @Bean
        public JavaxMailTemplate javaxMailTemplate(MailProperties mailProperties) {
            return new JavaxMailTemplate(mailProperties);
        }

        @Bean
        public MailAccount mailAccount(MailProperties mailProperties) {
            MailAccount account = new MailAccount();

            MailProperties.JavaxMail javaxMail = mailProperties.getJavax();
            account.setHost(javaxMail.getHost());
            account.setPort(javaxMail.getPort());
            account.setAuth(javaxMail.getAuth());
            account.setFrom(javaxMail.getFrom());
            account.setUser(javaxMail.getUser());
            account.setPass(javaxMail.getPass());
            account.setSocketFactoryPort(javaxMail.getPort());
            account.setStarttlsEnable(javaxMail.getStarttlsEnable());
            account.setSslEnable(javaxMail.getSslEnable());
            account.setTimeout(javaxMail.getTimeout());
            account.setConnectionTimeout(javaxMail.getConnectionTimeout());
            return account;
        }

    }

    @Configuration
//    @ConditionalOnProperty(value = {"mail.enabled", "mail.sendGrid.enabled"}, havingValue = "true")
//    @ConditionalOnClass(com.sendgrid.SendGrid.class)
    @ConditionalOnExpression("${mail.enabled:false} && ${mail.sendGrid.enabled:false}")
    static class SendGridMailConfig {
        @Bean
        public SendGridMailTemplate sendGridMailTemplate(MailProperties mailProperties) {
            return new SendGridMailTemplate(mailProperties);
        }
    }

    @Configuration
//    @ConditionalOnProperty(value = {"mail.enabled", "mail.sendGrid.enabled"}, havingValue = "true")
//    @ConditionalOnClass(com.sendgrid.SendGrid.class)
    @ConditionalOnExpression("${mail.enabled:false} && ${mail.mailchimp.enabled:false}")
    static class MailchimpMailConfig {
        @Bean
        public MailchimpMailTemplate mailchimpMailTemplate(MailProperties mailProperties) {
            return new MailchimpMailTemplate(mailProperties);
        }
    }
}
