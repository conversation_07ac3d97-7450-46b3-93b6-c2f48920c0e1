package com.hengjian.common.mail.api.mailchimp.bean;

import lombok.Data;

/**
 * metadata an associative array of user metadata.
 * Mandrill will store this metadata and make it available for retrieval.
 * In addition, you can select up to 10 metadata fields to index and make searchable using the Mandrill search api.
 */
@Data
public class Metadata {

    /**
     * a valid website url
     */
    private String website;

}
