package com.hengjian.common.mail.core;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.map.MapUtil;
import com.hengjian.common.mail.config.properties.MailProperties;
import com.hengjian.common.mail.utils.Mail;
import com.hengjian.common.mail.utils.MailAccount;
import com.hengjian.common.mail.utils.Utils;
import lombok.SneakyThrows;

import java.io.File;
import java.io.InputStream;
import java.util.Collection;
import java.util.Map;

/**
 * java 自带邮箱模板实现
 */
public class JavaxMailTemplate implements MailTemplate {

    private MailAccount mailAccount;

    private MailProperties.JavaxMail javaxMail;

    @SneakyThrows(Exception.class)
    public JavaxMailTemplate(MailProperties mailProperties) {
        this.javaxMail = mailProperties.getJavax();
        mailAccount = new MailAccount();
        mailAccount.setHost(javaxMail.getHost());
        mailAccount.setPort(javaxMail.getPort());
        mailAccount.setAuth(javaxMail.getAuth());
        mailAccount.setFrom(javaxMail.getFrom());
        mailAccount.setUser(javaxMail.getUser());
        mailAccount.setPass(javaxMail.getPass());
        mailAccount.setSocketFactoryPort(javaxMail.getPort());
        mailAccount.setStarttlsEnable(javaxMail.getStarttlsEnable());
        mailAccount.setSslEnable(javaxMail.getSslEnable());
        mailAccount.setTimeout(javaxMail.getTimeout());
        mailAccount.setConnectionTimeout(javaxMail.getConnectionTimeout());
    }

    @Override
    public String sendText(String to, String subject, String content, File... files) {
        return send(to, subject, content, false, files);
    }

    @Override
    public String sendHtml(String to, String subject, String content, File... files) {
        return send(to, subject, content, true, files);
    }

    @Override
    public String send(String to, String subject, String content, boolean isHtml, File... files) {
        return send(Utils.splitAddress(to), subject, content, isHtml, files);
    }

    @Override
    public String send(String to, String cc, String bcc, String subject, String content, boolean isHtml, File... files) {
        return send(Utils.splitAddress(to), Utils.splitAddress(cc), Utils.splitAddress(bcc), subject, content, isHtml, files);
    }

    @Override
    public String sendText(Collection<String> tos, String subject, String content, File... files) {
        return send(tos, subject, content, false, files);
    }

    @Override
    public String sendHtml(Collection<String> tos, String subject, String content, File... files) {
        return send(tos, subject, content, true, files);
    }

    @Override
    public String send(Collection<String> tos, String subject, String content, boolean isHtml, File... files) {
        return send(tos, null, null, subject, content, isHtml, files);
    }

    @Override
    public String send(Collection<String> tos, Collection<String> ccs, Collection<String> bccs, String subject, String content, boolean isHtml, File... files) {
        return send(mailAccount, true, tos, ccs, bccs, subject, content, null, isHtml, files);
    }

    @Override
    public String sendHtml(String to, String subject, String content, Map<String, InputStream> imageMap, File... files) {
        return send(to, subject, content, imageMap, true, files);
    }

    @Override
    public String send(String to, String subject, String content, Map<String, InputStream> imageMap, boolean isHtml, File... files) {
        return send(Utils.splitAddress(to), subject, content, imageMap, isHtml, files);
    }

    @Override
    public String send(String to, String cc, String bcc, String subject, String content, Map<String, InputStream> imageMap, boolean isHtml, File... files) {
        return send(Utils.splitAddress(to), Utils.splitAddress(cc), Utils.splitAddress(bcc), subject, content, imageMap, isHtml, files);
    }

    @Override
    public String sendHtml(Collection<String> tos, String subject, String content, Map<String, InputStream> imageMap, File... files) {
        return send(tos, subject, content, imageMap, true, files);
    }

    @Override
    public String send(Collection<String> tos, String subject, String content, Map<String, InputStream> imageMap, boolean isHtml, File... files) {
        return send(tos, null, null, subject, content, imageMap, isHtml, files);
    }

    @Override
    public String send(Collection<String> tos, Collection<String> ccs, Collection<String> bccs, String subject, String content, Map<String, InputStream> imageMap, boolean isHtml, File... files) {
        return send(mailAccount, true, tos, ccs, bccs, subject, content, imageMap, isHtml, files);
    }

    @Override
    public String send(String to, String subject, String templateId, Map<String, Object> params) {
        return null;
    }

    @Override
    public String send(String to, String cc, String bcc, String subject, String templateId, Map<String, Object> params) {
        return null;
    }

    @Override
    public String send(Collection<String> tos, String subject, String templateId, Map<String, Object> params) {
        return null;
    }

    @Override
    public String send(Collection<String> tos, Collection<String> ccs, Collection<String> bccs, String subject, String templateId, Map<String, Object> params) {
        return null;
    }

    /**
     * 发送邮件给多人
     *
     * @param mailAccount      邮件帐户信息
     * @param useGlobalSession 是否全局共享Session
     * @param tos              收件人列表
     * @param ccs              抄送人列表，可以为null或空
     * @param bccs             密送人列表，可以为null或空
     * @param subject          标题
     * @param content          正文
     * @param imageMap         图片与占位符，占位符格式为cid:${cid}
     * @param isHtml           是否为HTML格式
     * @param files            附件列表
     * @return message-id
     * @since 4.6.3
     */
    private static String send(MailAccount mailAccount, boolean useGlobalSession, Collection<String> tos, Collection<String> ccs, Collection<String> bccs, String subject, String content,
                              Map<String, InputStream> imageMap, boolean isHtml, File... files) {
        final Mail mail = Mail.create(mailAccount).setUseGlobalSession(useGlobalSession);

        // 可选抄送人
        if (CollUtil.isNotEmpty(ccs)) {
            mail.setCcs(ccs.toArray(new String[0]));
        }
        // 可选密送人
        if (CollUtil.isNotEmpty(bccs)) {
            mail.setBccs(bccs.toArray(new String[0]));
        }

        mail.setTos(tos.toArray(new String[0]));
        mail.setTitle(subject);
        mail.setContent(content);
        mail.setHtml(isHtml);
        mail.setFiles(files);

        // 图片
        if (MapUtil.isNotEmpty(imageMap)) {
            for (Map.Entry<String, InputStream> entry : imageMap.entrySet()) {
                mail.addImage(entry.getKey(), entry.getValue());
                // 关闭流
                IoUtil.close(entry.getValue());
            }
        }

        return mail.send();
    }
}
