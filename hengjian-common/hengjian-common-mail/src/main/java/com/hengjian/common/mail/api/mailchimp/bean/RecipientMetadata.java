package com.hengjian.common.mail.api.mailchimp.bean;

import cn.hutool.core.annotation.Alias;
import lombok.Data;

/**
 * Per-recipient metadata that will override the global values specified in the metadata parameter.
 */
@Data
public class RecipientMetadata {

    /**
     * the email address of the recipient that the metadata is associated with
     */
    private String rcpt;

    /**
     * an associated array containing the recipient's unique metadata.
     * If a key exists in both the per-recipient metadata and the global metadata,
     * the per-recipient metadata will be used.
     */
    private RecipientMetadataValue values;

    @Data
    static class RecipientMetadataValue {
        @Alias("user_id")
        private Integer userId;
    }

}
