package com.hengjian.common.mail.api.mailchimp.bean.out;

import cn.hutool.core.annotation.Alias;
import com.hengjian.common.mail.api.mailchimp.bean.SendResultStatusType;
import lombok.Data;
import lombok.ToString;

/**
 * 发送邮件结果
 */
@Data
@ToString
public class McSendResult {

    /**
     * the email address of the recipient
     */
    private String email;

    /**
     * the sending status of the recipient
     * Possible values:
     * "sent", "queued", "scheduled", "rejected", or "invalid".
     */
    private SendResultStatusType status;

    /**
     * the reason for the rejection if the recipient status is "rejected"
     * Possible values:
     * "hard-bounce", "soft-bounce", "spam", "unsub", "custom", "invalid-sender",
     * "invalid", "test-mode-limit", "unsigned", or "rule".
     */
    @Alias("reject_reason")
    private String rejectReason;

    /**
     * the reason for the email being queued if the response status is "queued"
     * Possible values:
     * "attachments", "multiple-recipients", "free-trial-sends-exhausted",
     * "hourly-quota-exhausted", "monthly-limit-reached", "sending-paused",
     * "sending-suspended", "account-suspended", or "sending-backlogged".
     */
    @Alias("queued_reason")
    private String queuedReason;

    /**
     * the message's unique id
     */
    private String _id;

}
