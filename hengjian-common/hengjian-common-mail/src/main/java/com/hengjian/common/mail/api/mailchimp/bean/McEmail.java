package com.hengjian.common.mail.api.mailchimp.bean;


import com.hengjian.common.mail.api.mailchimp.enums.ToType;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class McEmail {

    public McEmail(String email) {
        this.email = email;
    }

    public McEmail(String email, String type) {
        this.email = email;
        this.type = type;
    }

    /**
     * the email address of the recipient
     */
    private String email;

    /**
     * the optional display name to use for the recipient
     */
    private String name;

    /**
     * the header type to use for the recipient, defaults to "to" if not provided Possible values: "to", "cc", or "bcc".
     */
    private String type = ToType.to.name();

}
