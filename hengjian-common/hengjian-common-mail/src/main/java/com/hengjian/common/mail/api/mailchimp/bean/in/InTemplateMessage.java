package com.hengjian.common.mail.api.mailchimp.bean.in;

import cn.hutool.core.annotation.Alias;
import com.hengjian.common.mail.api.mailchimp.bean.GlobalMergeVar;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 模板信息
 */
@Data
@NoArgsConstructor
public class InTemplateMessage extends InMessage {

    public InTemplateMessage(String key) {
        super(key);
    }

    /**
     * REQUIRED
     * the immutable slug of a template that exists in the user's account.
     * Make sure you don't use the template name as this one might change.
     */
    @Alias("template_name")
    private String templateName;

    /**
     * REQUIRED
     * an array of template content to send.
     * Each item in the array should be a struct with two keys
     *    - name: the name of the content block to set the content for, and content: the actual content to put into the block
     */
    @Alias("template_content")
    private List<GlobalMergeVar> templateContent;

}
