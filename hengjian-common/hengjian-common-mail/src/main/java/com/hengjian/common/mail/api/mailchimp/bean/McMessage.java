package com.hengjian.common.mail.api.mailchimp.bean;

import cn.hutool.core.annotation.Alias;
import com.hengjian.common.mail.api.mailchimp.enums.MergeLanguageType;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class McMessage {

    public McMessage(String fromEmail, String fromName, String subaccount) {
        this.fromEmail = fromEmail;
        this.fromName = fromName;
        this.subaccount = subaccount;
    }

    /**
     * optional full HTML content to be sent if not in template
     */
    private String html;

    /**
     * optional full text content to be sent
     */
    private String text;

    /**
     * the message subject
     */
    private String subject;

    /**
     * Sender email address
     */
    @Alias("from_email")
    private String fromEmail;

    /**
     * optional from name to be used
     */
    @Alias("from_name")
    private String fromName;

    /**
     * List of message recipients
     */
    private List<McEmail> to;

    /**
     * optional extra headers to add to the message (most headers are allowed)
     */
    private Map<String, String> headers;

    /**
     * whether or not this message is important, and should be delivered ahead of non-important messages
     */
    private Boolean important;

    /**
     * whether or not to turn on open tracking for the message
     */
    @Alias("track_opens")
    private Boolean trackOpens;

    /**
     * whether or not to turn on click tracking for the message
     */
    @Alias("track_clicks")
    private Boolean trackClicks;

    /**
     * whether or not to automatically generate a text part for messages that are not given text
     */
    @Alias("auto_text")
    private Boolean autoText;

    /**
     * whether or not to automatically generate an HTML part for messages that are not given HTML
     */
    @Alias("auto_html")
    private Boolean autoHtml;

    /**
     * whether or not to automatically inline all CSS styles provided in the message HTML
     *  - only for HTML documents less than 256KB in size
     */
    @Alias("inline_css")
    private Boolean inlineCss;

    /**
     * whether or not to strip the query string from URLs when aggregating tracked URL data
     */
    @Alias("url_strip_qs")
    private Boolean urlStripQs;

    /**
     * whether or not to expose all recipients in to "To" header for each email
     */
    @Alias("preserve_recipients")
    private Boolean preserveRecipients;

    /**
     * view_content_link
     */
    @Alias("view_content_link")
    private Boolean viewContentLink;

    /**
     * an optional address to receive an exact copy of each recipient's email
     */
    @Alias("bcc_address")
    private String bccAddress;

    /**
     * a custom domain to use for tracking opens and clicks instead of mandrillapp.com
     */
    @Alias("tracking_domain")
    private String trackingDomain;

    /**
     * a custom domain to use for SPF/DKIM signing instead of mandrill (for "via" or "on behalf of" in email clients)
     */
    @Alias("signing_domain")
    private String signingDomain;

    /**
     * a custom domain to use for the messages's return-path
     */
    @Alias("return_path_domain")
    private String returnPathDomain;

    /**
     * whether to evaluate merge tags in the message.
     * Will automatically be set to true if either merge_vars or global_merge_vars are provided.
     */
    private Boolean merge;

    /**
     * the merge tag language to use when evaluating merge tags,
     * either mailchimp or handlebars Possible values: "mailchimp" or "handlebars".
     */
    @Alias("merge_language")
    private MergeLanguageType mergeLanguage;

    /**
     * global merge variables to use for all recipients. You can override these per recipient.
     */
    @Alias("global_merge_vars")
    private List<GlobalMergeVar> globalMergeVars;

    /**
     * per-recipient merge variables, which override global merge variables with the same name.
     */
    private List<MergeVar> mergeVars;

    /**
     * an array of string to tag the message with. Stats are accumulated using tags,
     * though we only store the first 100 we see, so this should not be unique or change frequently.
     * Tags should be 50 characters or less.
     * Any tags starting with an underscore are reserved for internal use and will cause errors.
     */
    private List<String> tags;

    /**
     * metadata an associative array of user metadata.
     * Mandrill will store this metadata and make it available for retrieval.
     * In addition, you can select up to 10 metadata fields to index and make searchable using the Mandrill search api.
     */
    private Metadata metadata;

    /**
     * the unique id of a subaccount for this message - must already exist or will fail with an error
     */
    private String subaccount;

    /**
     * an array of strings indicating for which any matching URLs will automatically
     * have Google Analytics parameters appended to their query string automatically.
     */
    @Alias("google_analytics_domains")
    private List<String> googleAnalyticsDomains;

    /**
     * an array of strings indicating for which any matching URLs will automatically
     * have Google Analytics parameters appended to their query string automatically.
     */
    @Alias("google_analytics_campaign")
    private List<String> googleAnalyticsCampaign;

    /**
     * Per-recipient metadata that will override the global values specified in the metadata parameter.
     */
    @Alias("recipient_metadata")
    private List<RecipientMetadata> recipientMetadata;

    /**
     * an array of supported attachments to add to the message
     */
    private List<McAttachment> attachments;

    /**
     * an array of embedded images to add to the message
     */
    private List<McImage> images;
}
