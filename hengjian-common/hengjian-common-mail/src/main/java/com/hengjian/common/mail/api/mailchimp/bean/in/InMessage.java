package com.hengjian.common.mail.api.mailchimp.bean.in;

import cn.hutool.core.annotation.Alias;
import com.hengjian.common.mail.api.mailchimp.bean.BaseSend;
import com.hengjian.common.mail.api.mailchimp.bean.McMessage;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 普通邮件发送
 */
@Data
@NoArgsConstructor
public class InMessage extends BaseSend {

    public InMessage(String key) {
        super(key);
    }

    /**
     * REQUIRED
     * the information on the message to send
     */
    private McMessage message;

    /**
     * enable a background sending mode that is optimized for bulk sending.
     * In async mode, messages/send will immediately return a status of "queued" for every recipient.
     * To handle rejections when sending in async mode, set up a webhook for the 'reject' event.
     * Defaults to false for messages with no more than 10 recipients;
     * messages with more than 10 recipients are always sent asynchronously, regardless of the value of async.
     */
    private boolean async;

    /**
     * the name of the dedicated ip pool that should be used to send the message.
     * If you do not have any dedicated IPs, this parameter has no effect.
     * If you specify a pool that does not exist, your default pool will be used instead.
     */
    @Alias("ip_pool")
    private String ipPool;

    /**
     * when this message should be sent as a UTC timestamp in YYYY-MM-DD HH:MM:SS format.
     * If you specify a time in the past, the message will be sent immediately;
     * for future dates, you're limited to one year from the date of scheduling.
     */
    @Alias("send_at")
    private String sendAt;
}
