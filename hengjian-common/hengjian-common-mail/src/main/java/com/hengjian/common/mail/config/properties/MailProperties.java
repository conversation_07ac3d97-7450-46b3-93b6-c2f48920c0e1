package com.hengjian.common.mail.config.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * JavaMail 配置属性
 *
 * <AUTHOR>
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "mail")
public class MailProperties {

    /**
     * 过滤开关
     */
    private Boolean enabled;
    /**
     * 默认邮箱发送配置
     */
    private JavaxMail javax;
    /**
     * SendGrid发送配置
     */
    private SendGridMail sendGrid;
    /**
     * Mailchimp 发送配置
     */
    private MailchimpMail mailchimp;

    /**
     * 每个邮箱每日最大发送量
     */
    private Integer dailyMax;

    /**
     * 每个邮箱每分钟最大发送量
     */
    private Integer minuteMax;

    /**
     * 默认邮箱发送配置
     */
    @Data
    public static class JavaxMail {
        // 默认邮箱独立开启
        private Boolean enabled;
        /**
         * SMTP服务器域名
         */
        private String host;

        /**
         * SMTP服务端口
         */
        private Integer port;

        /**
         * 是否需要用户名密码验证
         */
        private Boolean auth;

        /**
         * 用户名
         */
        private String user;

        /**
         * 密码
         */
        private String pass;

        /**
         * 发送方，遵循RFC-822标准
         */
        private String from;

        /**
         * 使用 STARTTLS安全连接，STARTTLS是对纯文本通信协议的扩展。它将纯文本连接升级为加密连接（TLS或SSL）， 而不是使用一个单独的加密通信端口。
         */
        private Boolean starttlsEnable;

        /**
         * 使用 SSL安全连接
         */
        private Boolean sslEnable;

        /**
         * SMTP超时时长，单位毫秒，缺省值不超时
         */
        private Long timeout;

        /**
         * Socket连接超时值，单位毫秒，缺省值不超时
         */
        private Long connectionTimeout;
    }

    /**
     * SendGrid发送配置
     */
    @Data
    public static class SendGridMail {
        // SendGrid独立开启
        private Boolean enabled;
        /**
         * SendGrid app key
         */
        private String appKey;
        /**
         * 发送者
         */
        private String from;
        /**
         * 发送者名称
         */
        private String fromName;
    }

    @Data
    public static class MailchimpMail {
        // Mailchimp独立开启
        private Boolean enabled;
        /**
         * api key
         */
        private String apiKey;
        /**
         * api send subaccount 子账号
         */
        private String subAccount;
        /**
         * 发送者
         */
        private String from;
        /**
         * 发送者名称
         */
        private String fromName;
    }
}
