package com.hengjian.common.mail.core;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ArrayUtil;
import com.hengjian.common.mail.config.properties.MailProperties;
import com.hengjian.common.mail.utils.MailException;
import com.hengjian.common.mail.utils.Utils;
import com.sendgrid.Method;
import com.sendgrid.Request;
import com.sendgrid.Response;
import com.sendgrid.SendGrid;
import com.sendgrid.helpers.mail.Mail;
import com.sendgrid.helpers.mail.objects.Attachments;
import com.sendgrid.helpers.mail.objects.Content;
import com.sendgrid.helpers.mail.objects.Email;
import com.sendgrid.helpers.mail.objects.Personalization;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.util.Collection;
import java.util.Map;
import java.util.Objects;

/**
 * SendGrid邮箱发送模板实现
 */
@Slf4j
public class SendGridMailTemplate implements MailTemplate {
    private static final String ENDPOINT_SEND = "mail/send";
    private SendGrid sendGrid;

    private Email from;

    @SneakyThrows(Exception.class)
    public SendGridMailTemplate(MailProperties mailProperties) {
        MailProperties.SendGridMail sendGridMail = mailProperties.getSendGrid();
        sendGrid = new SendGrid(sendGridMail.getAppKey());
        from = new Email(sendGridMail.getFrom(), sendGridMail.getFromName());
    }

    @Override
    public String sendText(String to, String subject, String content, File... files) {
        return send(to, subject, content, false, files);
    }

    @Override
    public String sendHtml(String to, String subject, String content, File... files) {
        return send(to, subject, content, true, files);
    }

    @Override
    public String send(String to, String subject, String content, boolean isHtml, File... files) {
        return send(to, null, null, subject, content, isHtml, files);
    }

    @Override
    public String send(String to, String cc, String bcc, String subject, String content, boolean isHtml, File... files) {
        return send(Utils.splitAddress(to), Utils.splitAddress(cc), Utils.splitAddress(bcc), subject, content, isHtml, files);
    }

    @Override
    public String sendText(Collection<String> tos, String subject, String content, File... files) {
        return send(tos, null, null, subject, content, false, files);
    }

    @Override
    public String sendHtml(Collection<String> tos, String subject, String content, File... files) {
        return send(tos, null, null, subject, content, true, files);
    }

    @Override
    public String send(Collection<String> tos, String subject, String content, boolean isHtml, File... files) {
        return send(tos, null, null, subject, content, isHtml, files);
    }

    @Override
    public String send(Collection<String> tos, Collection<String> ccs, Collection<String> bccs, String subject, String content, boolean isHtml, File... files) {
        String contentType = isHtml ? "text/html" : "text/pain";
        Content sendContent = new Content(contentType, content);

        Personalization personalization = new Personalization();

        addTos(personalization, tos);

        addCcs(personalization, ccs);

        addBccs(personalization, bccs);

        Mail mail = new Mail();
        mail.setFrom(from);
        mail.setSubject(subject);
        mail.addContent(sendContent);
        mail.addPersonalization(personalization);
        // 添加附件
        addFiles(mail, files);
        return send(mail);
    }

    // ------------------------------------------------------------------------------------------------------------------------------- Custom MailAccount
    @Override
    public String sendHtml(String to, String subject, String content, Map<String, InputStream> imageMap, File... files) {
        return null;
    }

    @Override
    public String send(String to, String subject, String content, Map<String, InputStream> imageMap, boolean isHtml, File... files) {
        return null;
    }

    @Override
    public String send(String to, String cc, String bcc, String subject, String content, Map<String, InputStream> imageMap, boolean isHtml, File... files) {
        return null;
    }

    @Override
    public String sendHtml(Collection<String> tos, String subject, String content, Map<String, InputStream> imageMap, File... files) {
        return null;
    }

    @Override
    public String send(Collection<String> tos, String subject, String content, Map<String, InputStream> imageMap, boolean isHtml, File... files) {
        return null;
    }

    @Override
    public String send(Collection<String> tos, Collection<String> ccs, Collection<String> bccs, String subject, String content, Map<String, InputStream> imageMap, boolean isHtml, File... files) {
        return null;
    }

    // ------------------------------------------------------------------------------------------------------------------------------- Custom MailAccount
    @Override
    public String send(String to, String subject, String templateId, Map<String, Object> params) {
        return send(to, null, null, subject, templateId, params);
    }

    @Override
    public String send(String to, String cc, String bcc, String subject, String templateId, Map<String, Object> params) {
        return send(Utils.splitAddress(to), Utils.splitAddress(cc), Utils.splitAddress(bcc), subject, templateId, params);
    }

    @Override
    public String send(Collection<String> tos, String subject, String templateId, Map<String, Object> params) {
        return send(tos, null, null, subject, templateId, params);
    }

    @Override
    public String send(Collection<String> tos, Collection<String> ccs, Collection<String> bccs, String subject, String templateId, Map<String, Object> params) {
        Personalization personalization = new Personalization();

        addTos(personalization, tos);

        addCcs(personalization, ccs);

        addBccs(personalization, bccs);

        for (Map.Entry<String, Object> kvEntry : params.entrySet()) {
            personalization.addDynamicTemplateData(kvEntry.getKey(), kvEntry.getValue());
        }

        Mail mail = new Mail();
        mail.setFrom(from);
        mail.setSubject(subject);
        mail.setTemplateId(templateId);
        mail.addPersonalization(personalization);
        return send(mail);
    }

    /**
     * 邮件发送
     *
     * @param mail
     * @throws MailException
     */
    private String send(Mail mail) {
        Response response = null;
        try {
            String bodyString = mail.build();
            log.info("bodyString = {}", bodyString);
            Request request = new Request();
            request.setMethod(Method.POST);
            request.setEndpoint(ENDPOINT_SEND);
            request.setBody(bodyString);
            response = sendGrid.api(request);
        } catch (IOException e) {
            throw new MailException(e);
        }

        log.info("response statusCode = {}, body = {}", response.getStatusCode(), response.getBody());
//        System.out.println(response.getStatusCode());
//        System.out.println(response.getBody());
//        System.out.println(response.getHeaders());
        if (Objects.equals(200, response.getStatusCode()) || Objects.equals(202, response.getStatusCode())) {
            log.info("mail send success");
            return response.getBody();
        } else {
            throw new MailException(response.getStatusCode() + "-" + response.getBody());
        }
    }

    /**
     * 添加附件
     * @param mail
     * @param files
     */
    private void addFiles(Mail mail, File[] files) {
        if (ArrayUtil.isNotEmpty(files)) {
            Attachments attachments = null;
            for (File file : files) {
                attachments = new Attachments();
                attachments.setFilename(file.getName());
                attachments.setContent(Base64.encode(file));
                mail.addAttachments(attachments);
            }
        }
    }


    private void addBccs(Personalization personalization, Collection<String> bccs) {
        if(CollUtil.isNotEmpty(bccs)) {
            bccs.forEach(to -> {
                Email mail = new Email(to);
                personalization.addBcc(mail);
            });
        }
    }

    private void addCcs(Personalization personalization, Collection<String> ccs) {
        if(CollUtil.isNotEmpty(ccs)) {
            ccs.forEach(to -> {
                Email mail = new Email(to);
                personalization.addCc(mail);
            });
        }
    }

    private void addTos(Personalization personalization, Collection<String> tos) {
        tos.forEach(to -> {
            Email mailTo = new Email(to);
            personalization.addTo(mailTo);
        });
    }

}
