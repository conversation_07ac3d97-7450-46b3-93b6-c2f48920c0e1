package com.hengjian.common.mail.core;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.img.ImgUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.json.JSONUtil;
import com.hengjian.common.mail.api.mailchimp.bean.*;
import com.hengjian.common.mail.api.mailchimp.bean.in.InMessage;
import com.hengjian.common.mail.api.mailchimp.bean.in.InTemplateMessage;
import com.hengjian.common.mail.api.mailchimp.enums.MailchimpUrls;
import com.hengjian.common.mail.api.mailchimp.enums.ToType;
import com.hengjian.common.mail.config.properties.MailProperties;
import com.hengjian.common.mail.utils.MailException;
import com.hengjian.common.mail.utils.Utils;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

import java.awt.image.BufferedImage;
import java.io.File;
import java.io.InputStream;
import java.util.*;

@Slf4j
public class MailchimpMailTemplate implements MailTemplate {
    // 设置超时时间，单位毫秒
    private int timeout = 60000;
    private MailProperties.MailchimpMail mailchimpMail;

    @SneakyThrows(Exception.class)
    public MailchimpMailTemplate(MailProperties mailProperties) {
        mailchimpMail = mailProperties.getMailchimp();
    }

    /**
     * 使用配置文件中设置的账户发送文本邮件，发送给单个或多个收件人<br>
     * 多个收件人可以使用逗号“,”分隔，也可以通过分号“;”分隔
     *
     * @param to      收件人
     * @param subject 标题
     * @param content 正文
     * @param files   附件列表
     * @return message-id
     * @since 3.2.0
     */
    @Override
    public String sendText(String to, String subject, String content,
                           File... files) {
        return send(to, subject, content, false, files);
    }

    /**
     * 使用配置文件中设置的账户发送HTML邮件，发送给单个或多个收件人<br>
     * 多个收件人可以使用逗号“,”分隔，也可以通过分号“;”分隔
     *
     * @param to      收件人
     * @param subject 标题
     * @param content 正文
     * @param files   附件列表
     * @return message-id
     * @since 3.2.0
     */
    @Override
    public String sendHtml(String to, String subject, String content,
                           File... files) {
        return send(to, subject, content, true, files);
    }

    /**
     * 使用配置文件中设置的账户发送邮件，发送单个或多个收件人<br>
     * 多个收件人可以使用逗号“,”分隔，也可以通过分号“;”分隔
     *
     * @param to      收件人
     * @param subject 标题
     * @param content 正文
     * @param isHtml  是否为HTML
     * @param files   附件列表
     * @return message-id
     */
    @Override
    public String send(String to, String subject, String content,
                       boolean isHtml, File... files) {
        return send(to, null, null, subject, content, isHtml, files);
    }

    /**
     * 使用配置文件中设置的账户发送邮件，发送单个或多个收件人<br>
     * 多个收件人、抄送人、密送人可以使用逗号“,”分隔，也可以通过分号“;”分隔
     *
     * @param to      收件人，可以使用逗号“,”分隔，也可以通过分号“;”分隔
     * @param cc      抄送人，可以使用逗号“,”分隔，也可以通过分号“;”分隔
     * @param bcc     密送人，可以使用逗号“,”分隔，也可以通过分号“;”分隔
     * @param subject 标题
     * @param content 正文
     * @param isHtml  是否为HTML
     * @param files   附件列表
     * @return message-id
     * @since 4.0.3
     */
    @Override
    public String send(String to, String cc, String bcc, String subject,
                       String content, boolean isHtml, File... files) {
        return send(Utils.splitAddress(to), Utils.splitAddress(cc), Utils.splitAddress(bcc), subject, content, isHtml, files);
    }

    /**
     * 使用配置文件中设置的账户发送文本邮件，发送给多人
     *
     * @param tos     收件人列表
     * @param subject 标题
     * @param content 正文
     * @param files   附件列表
     * @return message-id
     */
    @Override
    public String sendText(Collection<String> tos, String subject,
                           String content, File... files) {
        return send(tos, subject, content, false, files);
    }

    /**
     * 使用配置文件中设置的账户发送HTML邮件，发送给多人
     *
     * @param tos     收件人列表
     * @param subject 标题
     * @param content 正文
     * @param files   附件列表
     * @return message-id
     * @since 3.2.0
     */
    @Override
    public String sendHtml(Collection<String> tos, String subject,
                           String content, File... files) {
        return send(tos, subject, content, true, files);
    }

    /**
     * 使用配置文件中设置的账户发送邮件，发送给多人
     *
     * @param tos     收件人列表
     * @param subject 标题
     * @param content 正文
     * @param isHtml  是否为HTML
     * @param files   附件列表
     * @return message-id
     */
    @Override
    public String send(Collection<String> tos, String subject, String content,
                       boolean isHtml, File... files) {
        return send(tos, null, null, subject, content, isHtml, files);
    }

    /**
     * 使用配置文件中设置的账户发送邮件，发送给多人
     *
     * @param tos     收件人列表
     * @param ccs     抄送人列表，可以为null或空
     * @param bccs    密送人列表，可以为null或空
     * @param subject 标题
     * @param content 正文
     * @param isHtml  是否为HTML
     * @param files   附件列表
     * @return message-id
     * @since 4.0.3
     */
    @Override
    public String send(Collection<String> tos, Collection<String> ccs,
                       Collection<String> bccs, String subject, String content,
                       boolean isHtml, File... files) {
        return send(tos, ccs, bccs, subject, content, null, isHtml, files);
    }

    /**
     * 使用配置文件中设置的账户发送HTML邮件，发送给单个或多个收件人<br>
     * 多个收件人可以使用逗号“,”分隔，也可以通过分号“;”分隔
     *
     * @param to       收件人
     * @param subject  标题
     * @param content  正文
     * @param imageMap 图片与占位符，占位符格式为cid:$IMAGE_PLACEHOLDER
     * @param files    附件列表
     * @return message-id
     * @since 3.2.0
     */
    @Override
    public String sendHtml(String to, String subject, String content,
                           Map<String, InputStream> imageMap, File... files) {
        return send(to, subject, content, imageMap, true, files);
    }

    /**
     * 使用配置文件中设置的账户发送邮件，发送单个或多个收件人<br>
     * 多个收件人可以使用逗号“,”分隔，也可以通过分号“;”分隔
     *
     * @param to       收件人
     * @param subject  标题
     * @param content  正文
     * @param imageMap 图片与占位符，占位符格式为cid:$IMAGE_PLACEHOLDER
     * @param isHtml   是否为HTML
     * @param files    附件列表
     * @return message-id
     */
    @Override
    public String send(String to, String subject, String content,
                       Map<String, InputStream> imageMap, boolean isHtml,
                       File... files) {
        return send(to, null ,null, subject, content, imageMap, isHtml, files);
    }

    /**
     * 使用配置文件中设置的账户发送邮件，发送单个或多个收件人<br>
     * 多个收件人、抄送人、密送人可以使用逗号“,”分隔，也可以通过分号“;”分隔
     *
     * @param to       收件人，可以使用逗号“,”分隔，也可以通过分号“;”分隔
     * @param cc       抄送人，可以使用逗号“,”分隔，也可以通过分号“;”分隔
     * @param bcc      密送人，可以使用逗号“,”分隔，也可以通过分号“;”分隔
     * @param subject  标题
     * @param content  正文
     * @param imageMap 图片与占位符，占位符格式为cid:$IMAGE_PLACEHOLDER
     * @param isHtml   是否为HTML
     * @param files    附件列表
     * @return message-id
     * @since 4.0.3
     */
    @Override
    public String send(String to, String cc, String bcc, String subject,
                       String content, Map<String, InputStream> imageMap,
                       boolean isHtml, File... files) {
        return send(Utils.splitAddress(to), Utils.splitAddress(cc), Utils.splitAddress(bcc), subject,
            content, imageMap, isHtml, files);
    }

    /**
     * 使用配置文件中设置的账户发送HTML邮件，发送给多人
     *
     * @param tos      收件人列表
     * @param subject  标题
     * @param content  正文
     * @param imageMap 图片与占位符，占位符格式为cid:$IMAGE_PLACEHOLDER
     * @param files    附件列表
     * @return message-id
     * @since 3.2.0
     */
    @Override
    public String sendHtml(Collection<String> tos, String subject,
                           String content, Map<String, InputStream> imageMap,
                           File... files) {
        return send(tos, null, null, subject, content, imageMap, true, files);
    }

    /**
     * 使用配置文件中设置的账户发送邮件，发送给多人
     *
     * @param tos      收件人列表
     * @param subject  标题
     * @param content  正文
     * @param imageMap 图片与占位符，占位符格式为cid:$IMAGE_PLACEHOLDER
     * @param isHtml   是否为HTML
     * @param files    附件列表
     * @return message-id
     */
    @Override
    public String send(Collection<String> tos, String subject, String content,
                       Map<String, InputStream> imageMap, boolean isHtml,
                       File... files) {
        return send(tos, null, null, subject, content, imageMap, isHtml, files);
    }

    /**
     * 使用配置文件中设置的账户发送邮件，发送给多人
     *
     * @param tos      收件人列表
     * @param ccs      抄送人列表，可以为null或空
     * @param bccs     密送人列表，可以为null或空
     * @param subject  标题
     * @param content  正文
     * @param imageMap 图片与占位符，占位符格式为cid:$IMAGE_PLACEHOLDER
     * @param isHtml   是否为HTML
     * @param files    附件列表
     * @return message-id
     * @since 4.0.3
     */
    @Override
    public String send(Collection<String> tos, Collection<String> ccs,
                       Collection<String> bccs, String subject, String content,
                       Map<String, InputStream> imageMap, boolean isHtml,
                       File... files) {

        InMessage inMessage = new InMessage(mailchimpMail.getApiKey());
        McMessage message = new McMessage(mailchimpMail.getFrom(), mailchimpMail.getFromName(), mailchimpMail.getSubAccount());

        // 添加收件人等
        addTos(message, tos, ccs, bccs);
        message.setSubject(subject);

        // 判断消息主体
        if(StrUtil.isNotBlank(content)) {
            if(isHtml) {
                message.setHtml(content);
            } else {
                message.setText(content);
            }
        }

        // 发送附件
        addAttachments(message, files);

        // 图片
        addImages(message, imageMap);

        inMessage.setMessage(message);
        return send(inMessage);
    }

    /**
     * 添加图片
     * @param message
     * @param imageMap
     */
    private static void addImages(McMessage message, Map<String, InputStream> imageMap) {
        if (MapUtil.isNotEmpty(imageMap)) {
            List<McImage> images = new ArrayList<>();
            for (Map.Entry<String, InputStream> entry : imageMap.entrySet()) {
                String name = entry.getKey();
                InputStream value = entry.getValue();

                BufferedImage bufferedImage = ImgUtil.read(value);
                String encode = ImgUtil.toBase64(bufferedImage, null);

                McImage mcImage = new McImage();
                mcImage.setName(name);
                mcImage.setType("image/jpeg");
                mcImage.setContent(encode);
                images.add(mcImage);
            }
            message.setImages(images);
        }
    }

    /**
     * 添加附件
     * @param message
     * @param files
     */
    private static void addAttachments(McMessage message, File[] files) {
        if(ArrayUtil.isNotEmpty(files)) {
            List<McAttachment> attachments = new ArrayList<>();
            Arrays.stream(files).forEach(file -> {
                String type = FileUtil.getType(file);
                byte[] bytes = FileUtil.readBytes(file);
                String encode = Base64.encode(bytes);

                McAttachment mcAttachment = new McAttachment();
                mcAttachment.setName(file.getName());
                mcAttachment.setType(type);
                mcAttachment.setContent(encode);
                attachments.add(mcAttachment);
            });
            message.setAttachments(attachments);
        }
    }

    /**
     * 往请求体中添加收件人等
     * @param message
     * @param tos
     * @param ccs
     * @param bccs
     */
    private static void addTos(McMessage message, Collection<String> tos, Collection<String> ccs,
                               Collection<String> bccs) {
        List<McEmail> allTos = new ArrayList<>();
        tos.forEach(to -> {
            McEmail mcEmail = new McEmail(to);
            allTos.add(mcEmail);
        });
        // 可选抄送人
        if (CollUtil.isNotEmpty(ccs)) {
            ccs.forEach(to -> {
                McEmail mcEmail = new McEmail(to, ToType.cc.name());
                allTos.add(mcEmail);
            });
        }
        // 可选密送人
        if (CollUtil.isNotEmpty(bccs)) {
            bccs.forEach(to -> {
                McEmail mcEmail = new McEmail(to, ToType.bcc.name());
                allTos.add(mcEmail);
            });
        }
        message.setTo(allTos);
    }

    /**
     * 使用配置文件中设置的账户发送模板邮件
     *
     * @param to         收件人，可以使用逗号“,”分隔，也可以通过分号“;”分隔
     * @param subject    标题
     * @param templateId 模板Id
     * @param params     模板参数
     * @return
     */
    @Override
    public String send(String to, String subject, String templateId,
                       Map<String, Object> params) {
        return send(to, null ,null ,subject, templateId, params);
    }

    /**
     * 使用配置文件中设置的账户发送模板邮件，发送单个或多个收件人<br>
     * 多个收件人、抄送人、密送人可以使用逗号“,”分隔，也可以通过分号“;”分隔
     *
     * @param to         收件人，可以使用逗号“,”分隔，也可以通过分号“;”分隔
     * @param cc         抄送人，可以使用逗号“,”分隔，也可以通过分号“;”分隔
     * @param bcc        密送人，可以使用逗号“,”分隔，也可以通过分号“;”分隔
     * @param subject    标题
     * @param templateId 模板Id
     * @param params     模板参数
     * @return message-id
     */
    @Override
    public String send(String to, String cc, String bcc, String subject,
                       String templateId, Map<String, Object> params) {
        return send(Utils.splitAddress(to), Utils.splitAddress(cc), Utils.splitAddress(bcc), subject, templateId, params);
    }

    /**
     * 使用配置文件中设置的账户发送模板邮件，发送给多人
     *
     * @param tos        收件人列表
     * @param subject    标题
     * @param templateId 模板Id
     * @param params     模板参数
     * @return message-id
     */
    @Override
    public String send(Collection<String> tos, String subject,
                       String templateId, Map<String, Object> params) {
        return send(tos, null, null, subject, templateId, params);
    }

    /**
     * 使用配置文件中设置的账户发送邮件，发送给多人
     *
     * @param tos        收件人列表
     * @param ccs        抄送人列表，可以为null或空
     * @param bccs       密送人列表，可以为null或空
     * @param subject    标题
     * @param templateId 模板Id
     * @param params     模板参数
     * @return message-id
     * @since 4.0.3
     */
    @Override
    public String send(Collection<String> tos, Collection<String> ccs,
                       Collection<String> bccs, String subject,
                       String templateId, Map<String, Object> params) {
        InTemplateMessage inMessage = new InTemplateMessage(mailchimpMail.getApiKey());
        McMessage message = new McMessage(mailchimpMail.getFrom(), mailchimpMail.getFromName(), mailchimpMail.getSubAccount());

        // 添加收件人等
        addTos(message, tos, ccs, bccs);
        message.setSubject(subject);

        if(MapUtil.isNotEmpty(params)) {
            List<GlobalMergeVar> templateContent = new ArrayList<>();
            params.forEach((key, value) -> {
                GlobalMergeVar globalMergeVar = new GlobalMergeVar();
                globalMergeVar.setName(key);
                globalMergeVar.setContent(String.valueOf(value));
                templateContent.add(globalMergeVar);
            });
//            inMessage.setTemplateContent(templateContent);
            message.setGlobalMergeVars(templateContent);
        }
        inMessage.setTemplateContent(new ArrayList<>());
        inMessage.setTemplateName(templateId);
        inMessage.setMessage(message);
        return sendTemplate(inMessage);
    }


    /**
     * 发送普通邮件
     * @param inMessage
     * @return
     */
    private String send(InMessage inMessage) {
        String url = MailchimpUrls.Send.getUrl();

        return doSend(url, JSONUtil.toJsonStr(inMessage));
    }

    /**
     * 发送模板邮件
     * @param inTemplateMessage
     * @return
     */
    private String sendTemplate(InTemplateMessage inTemplateMessage) {
        String url = MailchimpUrls.SendTemplate.getUrl();

        return doSend(url, JSONUtil.toJsonStr(inTemplateMessage));
    }

    /**
     * 发送
     * @param url
     * @param body
     * @return
     */
    private String doSend(String url, String body) {
        log.info("邮件发送-url = {},邮箱处理线程:{}", url,Thread.currentThread().getName());
        log.info("邮件发送-body = {},邮箱处理线程:{}", body,Thread.currentThread().getName());
        HttpRequest httpRequest = HttpRequest.post(url).body(body)
                                             .timeout(timeout);
        try (HttpResponse response = httpRequest.execute()) {
            int status = response.getStatus();
            String respBody = response.body();
            log.info("邮件发送-code = {}, body = {},邮箱处理线程:{}", status, respBody,Thread.currentThread().getName());
            boolean ok = response.isOk();
            if (ok) {
                return respBody;
            } else {
                throw new MailException(status + "-" + respBody);
            }
        } catch (Exception e) {
            throw new MailException(e);
        }
    }


}
