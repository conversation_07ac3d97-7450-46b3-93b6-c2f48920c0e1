<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>hengjian-distribution</artifactId>
        <groupId>com.hengjian</groupId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <modules>
        <module>hengjian-common-bom</module>
        <module>hengjian-common-core</module>
        <module>hengjian-common-doc</module>
        <module>hengjian-common-excel</module>
        <module>hengjian-common-idempotent</module>
        <module>hengjian-common-job</module>
        <module>hengjian-common-log</module>
        <module>hengjian-common-mail</module>
        <module>hengjian-common-mybatis</module>
        <module>hengjian-common-oss</module>
        <module>hengjian-common-ratelimiter</module>
        <module>hengjian-common-redis</module>
        <module>hengjian-common-satoken</module>
        <module>hengjian-common-security</module>
        <module>hengjian-common-sms</module>
        <module>hengjian-common-web</module>
        <module>hengjian-common-translation</module>
        <module>hengjian-common-sensitive</module>
        <module>hengjian-common-json</module>
        <module>hengjian-common-encrypt</module>
        <module>hengjian-common-tenant</module>
        <module>hengjian-common-websocket</module>
        <module>hengjian-common-easyes</module>
    </modules>

    <artifactId>hengjian-common</artifactId>
    <packaging>pom</packaging>

    <description>
        common 通用模块
    </description>

</project>
